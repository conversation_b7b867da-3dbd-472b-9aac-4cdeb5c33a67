server{
        listen 80;
#charset koi8-r;
        access_log  /var/log/nginx/host.access.log  main;
        error_log  /var/log/nginx/error.log  error;
        location / {
                root   /usr/share/nginx/html;
                index  index.html index.htm;
                try_files $uri $uri/ /index.html;
                add_header Access-Control-Allow-Origin *;
                add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
                add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        }
#error_page  404              /404.html;
# redirect server error pages to the static page /50x.html
#error_page   500 502 503 504  /50x.html;
        location = /50x.html {
                root   /usr/share/nginx/html;
        }
	location /emergency-v2/ {
		rewrite ^/b/(.*)$ /$1 break;
		proxy_pass  http://**********:9925/emergency-v2/;
	}

  location /information-v2/ {
    rewrite ^/b/(.*)$ /$1 break;
    proxy_pass http://**********:9184/information-v2/;
  }
	location /schedule/ {
		rewrite ^/b/(.*)$ /$1 break;
		proxy_pass  http://**********:9008/schedule/;
	}
	location /security-v2/ {
		rewrite ^/b/(.*)$ /$1 break;
		proxy_pass  http://**********:9802/schedule/;
	}
  location /identification/ {
		rewrite ^/b/(.*)$ /$1 break;
		proxy_pass  http://10.0.28.93:9910/identification/;
	}




}
