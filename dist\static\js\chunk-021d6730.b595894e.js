(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-021d6730"],{"11ba":function(e,t,a){"use strict";a("5e9a")},"22cd":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"body"},[a("el-card",[a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v("数据筛选")])]),a("div",{staticClass:"center"},[a("div",{staticClass:"scarchIpt"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formInline}},[a("el-form-item",{attrs:{label:"班次名称："}},[a("el-input",{attrs:{placeholder:"请输入班次名称",clearable:"",maxlength:20},model:{value:e.formInline.className,callback:function(t){e.$set(e.formInline,"className",t)},expression:"formInline.className"}})],1),a("el-form-item",{attrs:{label:"班次类型："}},[a("el-select",{attrs:{placeholder:"请选择班次类型"},model:{value:e.formInline.classType,callback:function(t){e.$set(e.formInline,"classType",t)},expression:"formInline.classType"}},e._l(e.bctypeArr,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("div",{staticClass:"tabButton"},[a("div",[a("el-button",{staticClass:"searchBtn",staticStyle:{"font-size":"13px"},attrs:{icon:"el-icon-search",type:"primary",size:"mini"},on:{click:e.findList}},[e._v("搜索 ")]),a("el-button",{staticClass:"searchBtn",staticStyle:{"font-size":"13px"},attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetList}},[e._v("重置")])],1)])])]),a("el-card",{staticClass:"tab_card"},[a("div",{attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"tab_card_header"},[a("span",[e._v(" 班次管理展示列表 ")]),a("div",{staticClass:"btns"},[a("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-plus",type:"primary",size:"mini"},on:{click:e.goDetail}},[e._v("新建班次 ")]),a("el-radio-group",{staticStyle:{"margin-left":"15px"},on:{input:e.changeSelect},model:{value:e.SelectModel,callback:function(t){e.SelectModel=t},expression:"SelectModel"}},[a("el-radio-button",{attrs:{label:"列表式"}}),a("el-radio-button",{attrs:{label:"卡片式"}})],1)],1)])]),a("el-table",{directives:[{name:"show",rawName:"v-show",value:"列表式"==e.SelectModel,expression:"SelectModel=='列表式'"},{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"highlight-current-row":!0}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center","header-align":"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"name",label:"班次名称",width:"150",align:"left","header-align":"left","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"arrangementTypeName",width:"150",label:"班次类型",align:"center","header-align":"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"workTimeStr",label:"工作时间","min-width":"600",align:"center","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"work-time",style:{whiteSpace:"normal",overflow:"visible"}},e._l(t.row.arrangementTimeInfoList,(function(t,n){return a("el-tooltip",{attrs:{content:"点击关闭 tooltip 功能",placement:"top",effect:"light"}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._v(" "+e._s(t.workTimeStr)+" ")]),a("div",{staticClass:"one-work-line",class:"line"+t.code,style:e.getstyle(t)})])})),1)]}}])},[a("template",{slot:"header"},[a("div",[a("div",[e._v(" 工作时间 ")]),a("div",{staticClass:"time_line"},[e._l(e.timeline,(function(e,t){return a("div",{key:e.name,staticClass:"one_little",style:{left:4.16*e.value+"%"}})})),e._l(e.timeline,(function(t,n){return t.value/3==0||t.value/3==2||t.value/3==4||t.value/3==6||t.value/3==8?a("div",{key:t.value,staticClass:"name_little",class:{last_name_little:t.value/3==8},style:{left:4.16*t.value+"%",transform:"translateX(-50%)"}},[e._v(" "+e._s(t.name)+" ")]):e._e()}))],2)])])],2),a("el-table-column",{attrs:{prop:"workTimeAmount",label:"状态",align:"center","header-align":"center",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"1","inactive-value":"0"},on:{change:function(a){e.changeStatus(a,t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","header-align":"center",width:"250"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("span",{staticClass:"caozuo pointer",on:{click:function(a){return e.editData(t.$index,t.row)}}},[a("i",{staticClass:"el-icon-edit"}),e._v("编辑")]),a("span",{staticClass:"delete_btn pointer",on:{click:function(a){return e.deleteList(t.$index,t.row)}}},[a("i",{staticClass:"el-icon-delete"}),e._v("删除")])])]}}])})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"卡片式"==e.SelectModel,expression:"SelectModel=='卡片式'"}],staticClass:"showLIST"},e._l(e.tableData,(function(t,n){return a("div",{key:n,staticClass:"out_card"},[a("div",{staticClass:"card_con"},[a("div",{staticClass:"group_logo"},[e._v(" "+e._s(t.arrangementTypeName)+" ")]),a("div",{staticClass:"con_tit"},[e._v(" "+e._s(t.name)+" ")]),a("div",{staticClass:"con_member_num"},[a("div",{staticClass:"member_tit"},[e._v(" 工作时间 ")]),a("div",{staticClass:"memberNum"},[e._v(" "+e._s(t.workTimeStr)+" ")])]),a("div",{staticClass:"con_member_num"},[a("div",{staticClass:"member_tit"},[e._v(" 状态 ")]),a("div",{staticClass:"memberNum"},[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"1","inactive-value":"0"},on:{change:function(a){e.changeStatus(a,t)}},model:{value:t.status,callback:function(a){e.$set(t,"status",a)},expression:"item.status"}})],1)]),a("div",{staticClass:"card-btns"},[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.editData(n,t)}}},[e._v("编辑")]),a("el-button",{staticStyle:{color:"#8F97A2"},attrs:{type:"text"},on:{click:function(a){return e.deleteList(n,t)}}},[e._v("删除")])],1)])])})),0),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{limit:e.pageSize,page:e.pageNum,total:e.total},on:{"update:limit":function(t){e.pageSize=t},"update:page":function(t){e.pageNum=t},pagination:e.getPageLists}})],1)],1)],1)},r=[],s=(a("14d9"),a("d3b7"),a("159b"),a("a9e3"),a("dc01")),i={name:"",props:{},data:function(){return{SelectModel:"列表式",name:"环境数据",tabPosition:"1",formInline:{className:"",classType:""},loading:!1,tableData:[],pageSize:10,pageNum:1,isSearch:!1,total:0,isAdd:1,bctypeArr:[],minutesWidth:"",timeline:[{name:"00:00",value:0},{name:"03:00",value:3},{name:"06:00",value:6},{name:"09:00",value:9},{name:"12:00",value:12},{name:"15:00",value:15},{name:"18:00",value:18},{name:"21:00",value:21},{name:"24:00",value:24}]}},created:function(){},mounted:function(){var e=this;this.getPageLists(),this.getArrangementTypeLists(),this.getDict(),setTimeout((function(){e.getPageLists()}),10)},watch:{},filters:{},components:{},computed:{dictList:function(){return this.$store.state.dict}},methods:{changeSelect:function(e){this.getPageLists()},getstyle:function(e){var t,a,n=document.querySelectorAll(".work-time");return n.length>0&&(t=n[0].clientWidth,a=t/1440),{width:"".concat(a*e.width,"px"),left:"".concat(a*e.startLocal,"px")}},setdeleteByIdss:function(e){var t=this;this.loading=!0;var a={ids:e};1==this.tableData.length&&1!==this.pageNum&&(this.pageNum=this.pageNum-1),Object(s["q"])(a).then((function(e){console.log(e),t.$message({message:"删除成功",type:"success"}),t.getPageLists(),t.loading=!1})).catch((function(e){t.loading=!1}))},getArrangementTypeLists:function(){var e=this,t={};Object(s["w"])(t).then((function(t){e.bctypeArr=t.data}))},deleteList:function(e,t){var a=this;console.log(e,t),this.$confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.setdeleteByIdss(t.id)}))},editData:function(e,t){this.$router.push({name:"shiftmanaDetail",query:t})},goDetail:function(){this.$router.push({name:"shiftmanaDetail",query:{}})},getPageLists:function(e){var t=this;this.loading=!0;var a={};a="列表式"==this.SelectModel?this.isSearch?{styleType:"1",name:this.formInline.className,arrangementTypeId:this.formInline.classType,pageNum:this.pageNum,pageSize:this.pageSize}:{styleType:"1",pageNum:this.pageNum,pageSize:this.pageSize}:this.isSearch?{styleType:"2",name:this.formInline.className,arrangementTypeId:this.formInline.classType,pageNum:this.pageNum,pageSize:this.pageSize}:{styleType:"2",pageNum:this.pageNum,pageSize:this.pageSize},Object(s["y"])(a).then((function(e){t.tableData=e.data.list,t.total=e.data.total,console.log(t.tableData,"this.res"),t.tableData.forEach((function(e,t){e.arrangementTimeInfoList.forEach((function(e,t){var a=e.maxEndWorkTime.split(":"),n=e.minStartWorkTime.split(":");e.startLocal=60*Number(n[0])+Number(n[1]),e.endLocal=60*Number(a[0])+Number(a[1]),e.width=e.endLocal-e.startLocal}))})),console.log(t.tableData),t.loading=!1}))},getDict:function(){this.$store.dispatch("dict/setDict",{})},findList:function(){this.pageNum=1,this.isSearch=!0,this.getPageLists()},resetList:function(){this.pageNum=1,this.pageSize=10,this.formInline={},this.isSearch=!1,this.getPageLists()},changeStatus:function(e,t){var a=this,n={arrangementId:t.id};Object(s["p"])(n).then((function(e){a.getPageLists()}))}}},l=i,c=(a("11ba"),a("2877")),o=Object(c["a"])(l,n,r,!1,null,"5b9451b8",null);t["default"]=o.exports},"5e9a":function(e,t,a){},dc01:function(e,t,a){"use strict";a.d(t,"y",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"C",(function(){return i})),a.d(t,"B",(function(){return l})),a.d(t,"c",(function(){return c})),a.d(t,"m",(function(){return o})),a.d(t,"s",(function(){return u})),a.d(t,"z",(function(){return d})),a.d(t,"t",(function(){return m})),a.d(t,"A",(function(){return p})),a.d(t,"J",(function(){return h})),a.d(t,"K",(function(){return f})),a.d(t,"G",(function(){return g})),a.d(t,"M",(function(){return v})),a.d(t,"E",(function(){return b})),a.d(t,"w",(function(){return y})),a.d(t,"h",(function(){return w})),a.d(t,"f",(function(){return j})),a.d(t,"e",(function(){return S})),a.d(t,"q",(function(){return _})),a.d(t,"b",(function(){return O})),a.d(t,"r",(function(){return k})),a.d(t,"F",(function(){return L})),a.d(t,"k",(function(){return N})),a.d(t,"H",(function(){return C})),a.d(t,"l",(function(){return I})),a.d(t,"g",(function(){return T})),a.d(t,"D",(function(){return x})),a.d(t,"o",(function(){return D})),a.d(t,"I",(function(){return z})),a.d(t,"i",(function(){return A})),a.d(t,"j",(function(){return B})),a.d(t,"n",(function(){return $})),a.d(t,"x",(function(){return M})),a.d(t,"d",(function(){return P})),a.d(t,"u",(function(){return E})),a.d(t,"v",(function(){return q})),a.d(t,"p",(function(){return W})),a.d(t,"L",(function(){return J}));var n=a("b775");function r(e){return Object(n["a"])({url:"/schedule/arrangement/pageList",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/schedule/arrangement/save",method:"post",data:e})}function i(e){return Object(n["a"])({url:"/schedule/work-adjustment/pageList",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/schedule/schedule/mySchedule",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/schedule/work-adjustment/save",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/save",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/schedule/service-group/findList",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/schedule/service-group/getSelectList",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/schedule/member/findList",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/schedule/schedule/getShowData",method:"get",params:e})}function h(e){return Object(n["a"])({url:"/schedule/work-adjustment/detail",method:"get",params:e})}function f(e){return Object(n["a"])({url:"/schedule/work-adjustment/getActInfo",method:"get",params:e})}function g(e){return Object(n["a"])({url:"/schedule/work-adjustment/submitAct",method:"post",params:e})}function v(e){return Object(n["a"])({url:"/schedule/work-adjustment/withdrawAct",method:"get",params:e})}function b(e){return Object(n["a"])({url:"/schedule/schedule/pageList",method:"get",params:e})}function y(e){return Object(n["a"])({url:"/schedule/arrangement/getArrangementTypeList",method:"get",params:e})}function w(e){return Object(n["a"])({url:"/schedule/arrangement/findList",method:"get",params:e})}function j(e){return Object(n["a"])({url:"/schedule/arrangement/detail",method:"get",params:e})}function S(e){return Object(n["a"])({url:"/schedule/schedule/autoSetSchedule",method:"post",data:e})}function _(e){return Object(n["a"])({url:"/schedule/arrangement/deleteByIds",method:"post",params:e})}function O(e){return Object(n["a"])({url:"/schedule/schedule/save",method:"post",data:e})}function k(e){return Object(n["a"])({url:"/schedule/schedule/exportExcel",method:"get",params:e,responseType:"blob"})}function L(e){return Object(n["a"])({url:"/schedule/member-work/saveEntitys",method:"post",data:e})}function N(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/pageList",method:"get",params:e})}function C(e){return Object(n["a"])({url:"/schedule/work-adjustment/update",method:"post",data:e})}function I(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/update",method:"post",data:e})}function T(e){return Object(n["a"])({url:"/schedule/arrangement/update",method:"post",data:e})}function x(e){return Object(n["a"])({url:"/schedule/schedule/deleteByIds",method:"post",params:e})}function D(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/submitAct",method:"post",params:e})}function z(e){return Object(n["a"])({url:"/schedule/work-adjustment/deleteByIds",method:"post",params:e})}function A(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/deleteByIds",method:"post",params:e})}function B(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/detail",method:"get",params:e})}function $(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/getActInfo",method:"get",params:e})}function M(e){return Object(n["a"])({url:"/schedule/sysQuery/getLoginMemberInfo",method:"get",params:e})}function P(e){return Object(n["a"])({url:"/schedule/work-adjustment/approvalOperation",method:"post",params:e})}function E(e){return Object(n["a"])({url:"/schedule/schedule/getAllShowData",method:"get",params:e})}function q(e){return Object(n["a"])({url:"/schedule/service-group/findList",method:"get"})}function W(e){return Object(n["a"])({url:"/schedule/arrangement/updateStatus",method:"post",params:e})}function J(e){return Object(n["a"])({url:"/schedule/service-group/updateRemind",method:"post",data:e})}}}]);