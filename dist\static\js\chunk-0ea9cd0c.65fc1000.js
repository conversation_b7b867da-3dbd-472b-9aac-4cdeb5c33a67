(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-0ea9cd0c","chunk-4ff97d32"],{"1fb3":function(e,t,a){"use strict";a("7ff9")},"2d5e":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:24}},[a("div",{staticClass:"card_title"},[e._v("消防总览")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:24}},[a("div",{staticClass:"card_top"},e._l(e.cardTopNum,(function(t,o){return a("el-card",{key:o,staticClass:"box-card",attrs:{shadow:"hover","body-style":{padding:"20px 0"}}},[a("div",{staticClass:"title"},[e._v(" "+e._s(t.title)+" ")]),a("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"flex-end"}},[a("p",{staticClass:"num"},[e._v(e._s(t.num))]),a("p",{staticClass:"dan"},[e._v(e._s(t.dan))])])])})),1)])],1),a("div",{staticClass:"card_title"},[e._v("当前值班人员")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:10}},[a("el-carousel",{attrs:{interval:4e3,height:"200px"}},e._l(e.getDutyData,(function(t,o){return a("el-carousel-item",{key:o},[a("el-card",[a("div",{staticClass:"card_top"},[a("div",[a("p",[e._v("姓名："+e._s(t.name))]),a("p",[e._v("工号："+e._s(t.workNumber))]),a("p",[e._v("电话："+e._s(t.phone))])]),a("div",{staticStyle:{"text-align":"center"}},[a("p",{staticClass:"card_num"},[e._v(e._s(t.lncumbency))]),a("p",{staticStyle:{margin:"0"}},[e._v("在岗率")])])])])],1)})),1)],1),a("el-col",{attrs:{span:14}},[a("div",{staticStyle:{"margin-bottom":"20px"}},[a("span",[e._v("请选择消控室")]),a("el-select",{attrs:{placeholder:"请选择"},on:{change:e.fireControlChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.fireControlList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-carousel",{attrs:{interval:4e3,height:"200px"}},e._l(e.FlvData,(function(t,o){return a("el-carousel-item",{key:o},[a("jessibuca-player",{ref:"videoPlayer",refInFor:!0,attrs:{id:"videoElement1",videoUrl:e.videoUrl,error:e.videoError,message:e.videoError,height:!1,hasAudio:e.hasAudio,fluent:"",autoplay:"",live:""}})],1)})),1)],1)],1),a("div",{staticClass:"card_title"},[e._v("消防告警")]),a("el-card",{attrs:{shadow:"hover","body-style":{padding:"0px 20px"}}},[a("el-row",{staticStyle:{display:"flex","align-items":"center","padding-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:10,xs:10}},[a("el-date-picker",{staticStyle:{width:"20%"},attrs:{"value-format":"yyyy",format:"yyyy",type:"year",placeholder:"选择年","picker-options":e.pickerOptions,clearable:!1},on:{change:e.dateChange},model:{value:e.year,callback:function(t){e.year=t},expression:"year"}})],1)],1),a("div",{staticStyle:{display:"flex","margin-top":"10px"}},[a("div",{staticStyle:{width:"70%",height:"auto","min-height":"260px"},attrs:{id:"cardTwo"}}),a("div",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,border:"",height:"250px"}},[a("el-table-column",{attrs:{prop:"alarmTime",label:"时间",width:"180"}}),a("el-table-column",{attrs:{prop:"location",label:"区域",width:"120"}}),a("el-table-column",{attrs:{prop:"alarmStatus",label:"类型",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getTypeById(t.row))+" ")]}}])}),a("el-table-column",{attrs:{prop:"alarmStatus",label:"状态",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getNameById(t.row))+" ")]}}])})],1)],1)])],1),a("div",{staticClass:"card_title"},[e._v("消防耗材（灭火药剂）")]),a("el-card",{attrs:{shadow:"hover","body-style":{padding:"0px 20px"}}},[a("el-row",{staticStyle:{display:"flex","align-items":"center","padding-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:8,xs:6}},[a("el-select",{staticStyle:{width:"230px"},attrs:{placeholder:"请选择灭火剂种类",clearable:""},on:{change:e.changeType},model:{value:e.type,callback:function(t){e.type=t},expression:"type"}},e._l(e.dict.type.resource_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("div",{staticStyle:{width:"100%",height:"auto","min-height":"260px"},attrs:{id:"cardThree"}})],1),a("div",{staticClass:"card_title"},[e._v("消防设备")]),a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0}},[a("el-form-item",{attrs:{label:"设备类型"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"请选择设备类型",clearable:""},model:{value:e.queryParams.deviceType,callback:function(t){e.$set(e.queryParams,"deviceType",t)},expression:"queryParams.deviceType"}},e._l(e.dict.type.fire_device_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"安装位置"}},[a("el-cascader",{attrs:{options:e.options,props:{checkStrictly:!0,label:"name",value:"id",emitPath:!1},clearable:""},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),a("el-form-item",{attrs:{label:"设备状态"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"请选择设备状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.fire_device_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceList}},[a("el-table-column",{attrs:{label:"设备类型","show-overflow-tooltip":!0,prop:"deviceType",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.fire_device_type,value:t.row.deviceType,type:1}})]}}])}),a("el-table-column",{attrs:{label:"设备名称","show-overflow-tooltip":!0,prop:"deviceName",align:"center"}}),a("el-table-column",{attrs:{label:"所属部门","show-overflow-tooltip":!0,prop:"organizationId",align:"center"}}),a("el-table-column",{attrs:{label:"安装位置","show-overflow-tooltip":!0,prop:"location",align:"center"}}),a("el-table-column",{attrs:{label:"责任人","show-overflow-tooltip":!0,prop:"principal",align:"center"}}),a("el-table-column",{attrs:{label:"状态","show-overflow-tooltip":!0,prop:"status",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.fire_device_status,value:t.row.status,type:1}})]}}])}),a("el-table-column",{attrs:{label:"关联摄像头","show-overflow-tooltip":!0,prop:"camera",align:"center"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),a("div",{staticClass:"card_title"},[e._v("在岗率")]),a("el-row",{staticStyle:{"margin-top":"30px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("div",{attrs:{id:"ecahrts_pie"}})]),a("el-col",{attrs:{span:18}},[a("el-form",{ref:"queryForm",staticStyle:{"margin-left":"3%"},attrs:{model:e.cahrtsFoem,size:"small",inline:!0}},[a("el-form-item",{attrs:{label:"值班人员",prop:"qy"}},[a("el-select",{attrs:{multiple:"","multiple-limit":2,placeholder:"请选择"},on:{change:e.staffChange},model:{value:e.cahrtsFoem.inspectedPostUserId,callback:function(t){e.$set(e.cahrtsFoem,"inspectedPostUserId",t)},expression:"cahrtsFoem.inspectedPostUserId"}},e._l(e.staffOptions,(function(e){return a("el-option",{key:e.workNumber,attrs:{label:e.staffName,value:e.workNumber}})})),1)],1),a("el-form-item",{attrs:{label:"日期",prop:"qy"}},[a("el-radio-group",{on:{change:e.typeChange},model:{value:e.cahrtsFoem.dateType,callback:function(t){e.$set(e.cahrtsFoem,"dateType",t)},expression:"cahrtsFoem.dateType"}},[a("el-radio",{attrs:{label:"currentWeek"}},[e._v("本周")]),a("el-radio",{attrs:{label:"currentMonth"}},[e._v("本月")])],1)],1)],1),a("div",{attrs:{id:"ecahrts_line"}})],1)],1)],1)],1)],1)},i=[],n=(a("d3b7"),a("25f0"),a("d81d"),a("159b"),a("14d9"),a("4de4"),a("b0c0"),a("e9c4"),a("fb6a"),a("b775"));a("c38a");function r(){return Object(n["a"])({url:"/firecontrol-board/informationCount",method:"get"})}function l(e){return Object(n["a"])({url:"firecontrol-check-record/duty",megetthod:"get",params:e})}function s(e){return Object(n["a"])({url:"/firecontrol-device-alarm/selectAlarmKanbanBoard",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/firecontrol-board/firecontrolResourceCount",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/firecontrol-device/page",method:"get",params:e})}function u(){return Object(n["a"])({url:"/organization/tree",method:"get"})}function h(){return Object(n["a"])({url:"/area/tree",method:"get"})}function m(e){return Object(n["a"])({url:"/firecontrol-check_post/getDutyPerson",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/firecontrol-check_post/getDutyTime",method:"post",data:e})}function f(e){return Object(n["a"])({url:"/firecontrol-check_post/getOnDutyRate",method:"post",data:e})}function y(e){return Object(n["a"])({url:"/firecontrol-room/list",method:"get",params:e})}function g(e){return Object(n["a"])({url:"/firecontrol-room/relatedMonitor",method:"get",params:e})}function v(e){return Object(n["a"])({url:"/monitor/getVideoStreaming",method:"post",data:e})}var b=a("313e"),w=a.n(b),x=a("9ad9");a("817d");var S={name:"EnergyOver",components:{jessibucaPlayer:x["default"]},dicts:["fire_device_type","firecontrol_device_type","firecontrol_alarm_status","resource_type"],data:function(){return{hasAudio:!1,tableData:[],monthCount:[],deviceList:[],total:0,value:"",fireControlList:[],videoUrl:"",cahrtsFoem:{inspectedPostUserId:void 0,dateType:"currentWeek",dutyMonth:[]},showSearch:!0,queryParams:{current:1,size:10,deviceType:void 0,areaId:void 0,status:void 0},options:[],staffOptions:[],organization:[],loading:!1,pickerOptions:{disabledDate:function(e){return e.getTime()>=(new Date).getTime()}},cardTopNum:[{title:"人员配备",num:"0",num1:"0",dan:"位"},{title:"设备正常",num:"0",num1:"0",dan:"台"},{title:"设备离线",num:"0",num1:"0",dan:"台"},{title:"设备故障",num:"0",num1:"0",dan:"台"},{title:"消防耗材",num:"0",num1:"0",dan:"个"}],year:(new Date).getFullYear().toString(),type:"",eventCompletion:{year:(new Date).getFullYear().toString(),eventSceneId:"",eventTypeId:"",handleType:"",totalCountList:[],completedCountList:[]},eventJudge:{dateRange:["".concat(this.getDate((new Date).getTime()-2592e6)+" 00:00:00"),"".concat(this.getDate((new Date).getTime())+" 00:00:00")],eventDescribe:[],evaluateLevel:[],pointData:[{name:"未评价",value:20,percent:"20"},{name:"1星",value:20,percent:"20"},{name:"2星",value:20,percent:"20"},{name:"3星",value:20,percent:"20"},{name:"4星",value:20,percent:"20"},{name:"5星",value:20,percent:"20"}]},radioNumber:3,radioNumber1:3,radioNumber2:3,radioNumber3:3,valueNumber:[],dateNumber:[],getDutyData:[],FlvData:[]}},created:function(){var e=this;s({year:this.year}).then((function(t){console.log(t),e.tableData=t.data.page.records,e.monthCount[0]=t.data.map[0].one,e.monthCount[1]=t.data.map[0].too,e.monthCount[2]=t.data.map[0].three,e.monthCount[3]=t.data.map[0].four,e.monthCount[4]=t.data.map[0].five,e.monthCount[5]=t.data.map[0].six,e.monthCount[6]=t.data.map[0].seven,e.monthCount[7]=t.data.map[0].eight,e.monthCount[8]=t.data.map[0].nine,e.monthCount[9]=t.data.map[0].ten,e.monthCount[10]=t.data.map[0].eleven,e.monthCount[11]=t.data.map[0].twelve,console.log(e.monthCount,e.tableData,"ssssss"),e.rigtCardTwo()})),this.fireControl(),c({isDelete:0}).then((function(t){console.log(t,"www");var a=[],o=[],i=[];t.data.forEach((function(e){a.push(e.griddingName),o.push(e.remain),i.push(e.amount)})),e.rigtCardThree(a,o,i)})),h().then((function(t){e.options=t.data})),r().then((function(t){console.log(t),e.cardTopNum[0].num=t.data.humanCount,e.cardTopNum[1].num=t.data.normalDeviceCount,e.cardTopNum[2].num=t.data.offlineDeviceCount,e.cardTopNum[3].num=t.data.breakdownDeviceCount,e.cardTopNum[4].num=t.data.resourceCount})),this.getList()},mounted:function(){this.startTime=this.$moment().weekday(1).format("YYYY-MM-DD"),this.endTime=this.$moment().weekday(7).format("YYYY-MM-DD"),console.log(this.startTime,this.endTime),this.getDutyPerson()},methods:{getDuty:function(){var e=this;l().then((function(t){200==t.code&&(e.getDutyData=t.data),console.log(t,"值班人员")}))},fireControl:function(){var e=this;y(null).then((function(t){200==t.code&&(e.fireControlList=t.data,e.value=t.data[0].id),console.log(t,"消防控制"),e.fireControlChange(e.value)}))},getDutyPerson:function(){var e=this;this.staffOptions=[],console.log(this.startTime,this.endTime,"值班"),m({startTime:this.startTime,endTime:this.endTime,isDelete:0}).then((function(t){e.cahrtsFoem.inspectedPostUserId=[],e.cahrtsFoem.staffName=[],e.staffOptions=t.data,console.log(t.data,"值班人员"),t.data.forEach((function(t){e.cahrtsFoem.inspectedPostUserId.push(t.workNumber),e.cahrtsFoem.staffName.push(t.staffName)}));var a=[],o=[];e.cahrtsFoem.inspectedPostUserId.forEach((function(t){"currentWeek"==e.cahrtsFoem.dateType?(a.push({inspectedPostUserId:t,currentWeek:"1"}),o.push({inspectedPostUserId:t,currentWeek:"1"})):(a.push({inspectedPostUserId:t,currentMonth:"1"}),o.push({inspectedPostUserId:t,currentMonth:"1"}))})),console.log(a,"data1"),p(a).then((function(t){e.cahrtsFoem.dutyTime=t.data;var a=[];if(t.data[0]){for(var o=0;o<t.data[0].length;o++)a.push(parseInt(o+1)+"号");e.cahrtsFoem.dutyMonth=a}e.rightEcharts()})),f(o).then((function(t){e.leftEcharts(t.data)}))}))},fireControlChange:function(e){var t=this;console.log(e),g({id:e}).then((function(e){t.FlvData=e.data,console.log(e,t.FlvData,"测试1"),e.data.forEach((function(e){t.getvideoFlv(e.deviceId)}))}))},getvideoFlv:function(e){var t=this;v({equipmentIdList:[e]}).then((function(e){console.log(e.data[0].flvAddress),t.$nextTick((function(){t.videoUrl=e.data[0].flvAddress,t.$refs.videoPlayer.play(t.videoUrl)}))}))},getList:function(){var e=this;this.loading=!0,d(this.queryParams).then((function(t){u().then((function(a){e.organization=a.data,t.data.records.forEach((function(t){t.organizationId=e.findName(e.organization,t.organizationId)})),e.deviceList=t.data.records,e.total=t.data.total,e.loading=!1,console.log(t.data.records,a,e.loading)}))}))},getNameById:function(e){if(console.log(e,this.dict.type.firecontrol_alarm_status,"Name"),void 0!=e.alarmStatus&&""!=e.alarmStatus&&null!=e.alarmStatus)return this.dict.type.firecontrol_alarm_status.filter((function(t){return t.value==e.alarmStatus}))[0].label},getTypeById:function(e){if(console.log(e,this.dict.type.firecontrol_device_type,"Type"),void 0!=e.alarmType&&""!=e.alarmType&&null!=e.alarmType)return this.dict.type.firecontrol_device_type.filter((function(t){return t.value==e.alarmType}))[0].label},findName:function(e,t){for(var a="",o=0;o<e.length;o++){if(e[o].id==t){a=e[o].name;break}e[o].children&&(a=this.findName(e[o].children,t))}return a},handleQuery:function(){this.queryParams.current=1,this.getList()},resetQuery:function(){this.queryParams={current:1,size:10,deviceTyp:void 0,areaId:void 0,status:void 0},this.resetForm("queryForm"),this.handleQuery()},changeType:function(e){var t=this;e?c({type:e}).then((function(e){var a=[],o=[],i=[];e.data.forEach((function(e){a.push(e.griddingName),o.push(e.remain),i.push(e.amount)})),t.rigtCardThree(a,o,i)})):c({isDelete:0}).then((function(e){var a=[],o=[],i=[];e.data.forEach((function(e){a.push(e.griddingName),o.push(e.remain),i.push(e.amount)})),t.rigtCardThree(a,o,i)}))},radioChange:function(e){this.eventTimesCount={year:(new Date).getFullYear().toString(),eventSceneId:[],eventTypeId:[],handleType:[],countByYear1:[],countByYear2:[]}},dateChange:function(e){var t=this;s({year:e}).then((function(e){console.log(e),t.tableData=e.data.page.records,t.monthCount[0]=e.data.map[0].one,t.monthCount[1]=e.data.map[0].too,t.monthCount[2]=e.data.map[0].three,t.monthCount[3]=e.data.map[0].four,t.monthCount[4]=e.data.map[0].five,t.monthCount[5]=e.data.map[0].six,t.monthCount[6]=e.data.map[0].seven,t.monthCount[7]=e.data.map[0].eight,t.monthCount[8]=e.data.map[0].nine,t.monthCount[9]=e.data.map[0].ten,t.monthCount[10]=e.data.map[0].eleven,t.monthCount[11]=e.data.map[0].twelve,console.log(t.monthCount,t.tableData,"ssssss"),t.rigtCardTwo()}))},videoError:function(e){console.log("播放器错误："+JSON.stringify(e))},getDate:function(e){var t=new Date(e),a=t.getFullYear(),o=("0"+(t.getMonth()+1)).slice(-2),i=("0"+t.getDate()).slice(-2),n=a+"-"+o+"-"+i;return n},staffChange:function(e){var t=this;this.cahrtsFoem.staffName=[],e.forEach((function(e){t.staffOptions.forEach((function(a){a.workNumber==e&&t.cahrtsFoem.staffName.push(a.staffName)}))}));var a=[],o=[];e.forEach((function(e){"currentWeek"==t.cahrtsFoem.dateType?(a.push({inspectedPostUserId:e,currentWeek:"1"}),o.push({inspectedPostUserId:e})):(a.push({inspectedPostUserId:e,currentMonth:"1"}),o.push({inspectedPostUserId:e}))})),p(a).then((function(e){t.cahrtsFoem.dutyTime=e.data,t.rightEcharts()})),f(o).then((function(e){t.leftEcharts(e.data)}))},typeChange:function(e){this.cahrtsFoem.dateType=e,"currentWeek"==e?(this.startTime=this.$moment().weekday(1).format("YYYY-MM-DD"),this.endTime=this.$moment().weekday(7).format("YYYY-MM-DD")):(this.startTime=this.$moment().startOf("month").format("YYYY-MM-DD"),this.endTime=this.$moment().endOf("month").format("YYYY-MM-DD")),this.getDutyPerson()},rigtCardTwo:function(){var e=document.getElementById("cardTwo"),t=w.a.init(e),a=function(){for(var e=[],t=1;t<13;t++)e.push(t+"月");return e}(),o={backgroundColor:"#fff",tooltip:{trigger:"axis",axisPointer:{type:"shadow",textStyle:{color:"#fff"}}},grid:{borderWidth:0,top:20,bottom:60,right:20,left:50,textStyle:{color:"#fff"}},calculable:!0,xAxis:[{type:"category",axisLine:{lineStyle:{color:"#000"}},splitLine:{show:!1},axisTick:{show:!1},data:a}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{color:"#efefef"}},axisLine:{lineStyle:{color:"#000"}},minInterval:1}],series:[{name:"件数",type:"line",symbolSize:10,symbol:"circle",smooth:!0,itemStyle:{color:"#6f7de3"},data:this.monthCount}]};t.setOption(o)},rigtCardThree:function(e,t,a){var o=document.getElementById("cardThree"),i=w.a.init(o),n=e,r={backgroundColor:"#fff",tooltip:{trigger:"axis",axisPointer:{type:"shadow",textStyle:{color:"#fff"}}},grid:{borderWidth:0,top:20,bottom:60,right:20,left:50,textStyle:{color:"#fff"}},legend:{x:"center",top:"90%",textStyle:{color:"#000"},data:["剩余","总量"]},calculable:!0,xAxis:[{type:"category",axisLine:{lineStyle:{color:"#000"}},splitLine:{show:!1},axisTick:{show:!1},data:n}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{color:"#efefef"}},axisLine:{lineStyle:{color:"#000"}}}],series:[{name:"剩余",type:"bar",itemStyle:{color:"#6f7de3"},barWidth:30,data:t},{name:"总量",type:"bar",barWidth:30,itemStyle:{color:"rgba(17, 122, 242, 1)"},data:a}]};i.setOption(r)},cardRakingTwo:function(){var e=document.getElementById("card_raking_two"),t=w.a.init(e),a=[19,29,39,81,29,39],o={backgroundColor:"#fff",barWidth:15,grid:{borderWidth:0,top:20,bottom:30,right:55,left:180},xAxis:{type:"value",splitLine:{lineStyle:{color:"#efefef",type:"dashed"}},axisLine:{lineStyle:{color:"#D9D9D9"}},axisTick:{show:!1},axisLabel:{textStyle:{color:"#000",fontSize:12}}},yAxis:[{type:"category",data:this.eventSolveTime.eventDescribe,splitLine:{show:!1},axisTick:{show:!1},axisLine:{lineStyle:{color:"#D9D9D9"}},axisLabel:{textStyle:{color:"#000",fontSize:12}}},{type:"category",inverse:!0,axisTick:"none",axisLine:"none",show:!0,data:a}],series:[{type:"bar",name:"",itemStyle:{normal:{color:"rgba(60, 144, 247, 1)"}},data:this.eventSolveTime.solveTime,label:{normal:{show:!0,position:"right"},formatter:"{@value}"}}]};t.setOption(o)},appraiseOne:function(){var e=document.getElementById("appraise_one"),t=w.a.init(e),a=this.eventJudge.pointData;function o(e,t){for(var a={},o=0;o<e.length;o++)a[e[o][t]]=e[o];return a}var i=o(a,"name"),n={color:["rgba(60, 144, 247, 1)","rgba(85, 191, 192, 1)","rgba(94, 190, 103, 1)","rgba(244, 205, 73, 1)","rgba(224, 86, 103, 1)","rgba(124, 75, 216, 1)"],backgroundColor:"#fff",grid:{bottom:10,left:10,right:"10%"},legend:{orient:"vertical",top:"middle",left:"50%",textStyle:{color:"#000",fontSize:12},icon:"circle",data:a,formatter:function(e,t){return e+"   "+i[e].percent+"%   "+i[e].value}},series:[{radius:"80%",center:["29%","50%"],type:"pie",name:"",data:a,itemStyle:{normal:{label:{show:!1},borderColor:"#fff",borderWidth:4,labelLine:{show:!1}}}}]};t.setOption(n)},appraiseTwo:function(){var e=document.getElementById("appraise_two"),t=w.a.init(e),a={backgroundColor:"#fff",tooltip:{trigger:"axis",axisPointer:{type:"shadow",textStyle:{color:"#fff"}}},grid:{borderWidth:0,top:20,bottom:60,right:20,left:50,textStyle:{color:"#fff"}},calculable:!0,xAxis:{type:"category",axisLine:{lineStyle:{color:"#000"}},splitLine:{show:!1},axisTick:{show:!1},data:this.eventJudge.eventDescribe,axisLabel:{interval:0}},yAxis:{type:"value",splitLine:{show:!0,lineStyle:{color:"#efefef"}},axisLine:{lineStyle:{color:"#000"}}},series:[{name:"总数",type:"bar",itemStyle:{color:"#6f7de3"},barWidth:30,data:this.eventJudge.evaluateLevel}]};t.setOption(a)},leftEcharts:function(e){var t=w.a.init(document.getElementById("ecahrts_pie")),a=e,o={tooltip:{formatter:"{a} <br/>{b} : {c}%"},title:{show:!0,x:"center",y:"68%",text:"在岗率",textStyle:{fontSize:14,fontWeight:"bolder",fontStyle:"normal",color:"#31F3FF"}},series:[{name:"外部线",type:"gauge",radius:"95%",startAngle:225,endAngle:-45,axisLine:{lineStyle:{color:[[1,"#31F3FF"]],width:1}},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},detail:{show:!1},title:{show:!1}},{name:"外部刻度",type:"gauge",radius:"99%",min:0,max:100,splitNumber:10,startAngle:225,endAngle:-45,axisLine:{show:!1,lineStyle:{color:[[1,"rgba(0,0,0,0)"]]}},axisLabel:{show:!0,color:"#31F3FF",fontSize:10,distance:-20},axisTick:{show:!1},splitLine:{show:!1}},{name:"内部宽线条",type:"gauge",radius:"73%",startAngle:225,endAngle:-45,axisLine:{lineStyle:{color:[[1,"#122B3C"]],width:20}},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},detail:{show:!1},title:{show:!1}},{name:"内部细线条",type:"gauge",radius:"70%",startAngle:225,endAngle:-45,axisLine:{lineStyle:{color:[[1,"#122B3C"]],width:3}},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},detail:{show:!1},title:{show:!1}},{name:"间隔条形",type:"gauge",radius:"73%",z:4,splitNumber:35,startAngle:225,endAngle:-45,axisLine:{lineStyle:{opacity:0}},axisLabel:{show:!1},axisTick:{show:!0,length:20,splitNumber:1,lineStyle:{color:"#122B3C",width:1}},splitLine:{show:!1},detail:{show:!1},title:{show:!1}},{name:"数据",type:"gauge",radius:"72.5%",z:3,startAngle:225,max:100,endAngle:-45,axisLine:{lineStyle:{color:[[a/100,"#31F3FF"],[1,"#185363"]],width:20}},tooltip:{show:!1},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},detail:{formatter:function(e){return a+"%"},show:!0,fontWeight:"bold",fontSize:20},pointer:{show:!0,width:3},data:[{name:"",value:a}]},{name:"内圆环",type:"pie",radius:["4%","2%"],hoverAnimation:!1,tooltip:{show:!1},cursor:"default",labelLine:{normal:{show:!1}},itemStyle:{color:"#122B3C"},animation:!1,data:[1]},{name:"内圆环2",type:"pie",radius:"2%",hoverAnimation:!1,cursor:"default",tooltip:{show:!1},labelLine:{normal:{show:!1}},itemStyle:{color:"#31F3FF"},animation:!1,data:[1]}]};t.setOption(o)},rightEcharts:function(){var e=w.a.init(document.getElementById("ecahrts_line")),t={grid:{top:"5%",left:"3%",right:"4%",bottom:"13%",containLabel:!0},backgroundColor:"#fff",legend:{icon:"rect",bottom:10,textStyle:{color:"#000",data:["张珊珊","李师师"]},itemWidth:15,itemHeight:15},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:"currentWeek"==this.cahrtsFoem.dateType?["星期一","星期二","星期三","星期四","星期五","星期六","星期日"]:this.cahrtsFoem.dutyMonth,axisLabel:{textStyle:{fontSize:12,color:"#000"}},axisLine:{lineStyle:{color:"#000"}}},yAxis:{min:0,type:"value",splitLine:{show:!0,lineStyle:{type:"dashed"}},axisLabel:{textStyle:{fontSize:12,color:"#000"}},axisLine:{show:!1,lineStyle:{color:"#fff"}}},series:[{name:this.cahrtsFoem.staffName[0],data:this.cahrtsFoem.dutyTime[0],type:"line",smooth:!1,areaStyle:{opacity:0},itemStyle:{normal:{color:"#197CD8",lineStyle:{color:"#197CD8"}}},lineStyle:{normal:{width:3}}},{name:this.cahrtsFoem.staffName[1],data:this.cahrtsFoem.dutyTime[1],type:"line",smooth:!1,areaStyle:{opacity:0},itemStyle:{normal:{color:"#2B9F50",lineStyle:{color:"#2B9F50"}}},lineStyle:{normal:{width:3}}}]};e.setOption(t)}}},C=S,T=(a("1fb3"),a("2877")),_=Object(T["a"])(C,o,i,!1,null,"7e84e754",null);t["default"]=_.exports},"7ff9":function(e,t,a){},"817d":function(e,t,a){var o,i,n;(function(r,l){i=[t,a("313e")],o=l,n="function"===typeof o?o.apply(t,i):o,void 0===n||(e.exports=n)})(0,(function(e,t){var a=function(e){"undefined"!==typeof console&&console&&console.error&&console.error(e)};if(t){var o=["#2ec7c9","#b6a2de","#5ab1ef","#ffb980","#d87a80","#8d98b3","#e5cf0d","#97b552","#95706d","#dc69aa","#07a2a4","#9a7fd1","#588dd5","#f5994e","#c05050","#59678c","#c9ab00","#7eb00a","#6f5553","#c14089"],i={color:o,title:{textStyle:{fontWeight:"normal",color:"#008acd"}},visualMap:{itemWidth:15,color:["#5ab1ef","#e0ffff"]},toolbox:{iconStyle:{normal:{borderColor:o[0]}}},tooltip:{backgroundColor:"rgba(50,50,50,0.5)",axisPointer:{type:"line",lineStyle:{color:"#008acd"},crossStyle:{color:"#008acd"},shadowStyle:{color:"rgba(200,200,200,0.2)"}}},dataZoom:{dataBackgroundColor:"#efefff",fillerColor:"rgba(182,162,222,0.2)",handleColor:"#008acd"},grid:{borderColor:"#eee"},categoryAxis:{axisLine:{lineStyle:{color:"#008acd"}},splitLine:{lineStyle:{color:["#eee"]}}},valueAxis:{axisLine:{lineStyle:{color:"#008acd"}},splitArea:{show:!0,areaStyle:{color:["rgba(250,250,250,0.1)","rgba(200,200,200,0.1)"]}},splitLine:{lineStyle:{color:["#eee"]}}},timeline:{lineStyle:{color:"#008acd"},controlStyle:{color:"#008acd",borderColor:"#008acd"},symbol:"emptyCircle",symbolSize:3},line:{smooth:!0,symbol:"emptyCircle",symbolSize:3},candlestick:{itemStyle:{color:"#d87a80",color0:"#2ec7c9"},lineStyle:{width:1,color:"#d87a80",color0:"#2ec7c9"},areaStyle:{color:"#2ec7c9",color0:"#b6a2de"}},scatter:{symbol:"circle",symbolSize:4},map:{itemStyle:{color:"#ddd"},areaStyle:{color:"#fe994e"},label:{color:"#d87a80"}},graph:{itemStyle:{color:"#d87a80"},linkStyle:{color:"#2ec7c9"}},gauge:{axisLine:{lineStyle:{color:[[.2,"#2ec7c9"],[.8,"#5ab1ef"],[1,"#d87a80"]],width:10}},axisTick:{splitNumber:10,length:15,lineStyle:{color:"auto"}},splitLine:{length:22,lineStyle:{color:"auto"}},pointer:{width:5}}};t.registerTheme("macarons",i)}else a("ECharts is not Loaded")}))},"9ad9":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"auto",height:"300px"},attrs:{id:"jessibuca"}},[a("div",{ref:e.id,staticStyle:{width:"100%",height:"300px","background-color":"#000"},attrs:{id:e.id},on:{dblclick:e.fullscreenSwich}})])},i=[],n={name:"jessibuca",data:function(){return{jessibuca:null,playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:"",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!0}},props:["videoUrl","error","hasAudio","height","id"],mounted:function(){var e=this;window.onerror=function(e){};var t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){var a=document.getElementById(e.id);a.style.height=9/16*a.clientWidth+"px","undefined"==typeof e.videoUrl&&(e.videoUrl=t),console.log("初始化时的地址为: "+e.videoUrl),e.play(e.videoUrl)}))},created:function(){console.log(this.videoUrl)},methods:{create:function(){this.jessibuca=new JessibucaPro({container:"#"+this.id,decoder:"./decoder-pro.js",videoBuffer:.2,isResize:!1,text:"",loadingText:"加载中",debug:!0,isMulti:!0,useMSE:!0,useSIMD:!0,useWCS:!0,hasAudio:!1,useVideoRender:!0,controlAutoHide:!0,showBandwidth:!0,showPerformance:!1,operateBtns:{fullscreen:!0,screenshot:!0,play:!0,audio:!0},watermarkConfig:{text:{content:"摄像头"},right:10,top:10}}),this.jessibuca.on("fullscreen",(function(e){console.log("is fullscreen",index,e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var t=this;console.log(e),this.jessibuca&&this.destroy(),this.create(),this.jessibuca.on("play",(function(){t.playing=!0,t.loaded=!0,t.quieting=t.jessibuca.quieting})),this.jessibuca.hasLoaded()?this.jessibuca.play(e):this.jessibuca.on("load",(function(){console.log("load 播放"),t.jessibuca.play(e)}))},pause:function(){this.jessibuca&&this.jessibuca.pause(),this.playing=!1,this.err="",this.performance=""},destroy:function(){this.jessibuca&&this.jessibuca.destroy(),this.jessibuca=null,this.playing=!1,this.err="",this.performance=""},eventcallbacK:function(e,t){console.log("player 事件回调"),console.log(e),console.log(t)},fullscreenSwich:function(){var e=this.isFullscreen();this.jessibuca.setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}},destroyed:function(){this.jessibuca&&this.jessibuca.destroy(),this.playing=!1,this.loaded=!1,this.performance=""}},r=n,l=(a("a5fc"),a("2877")),s=Object(l["a"])(r,o,i,!1,null,null,null);t["default"]=s.exports},a5fc:function(e,t,a){"use strict";a("aa9e")},aa9e:function(e,t,a){}}]);