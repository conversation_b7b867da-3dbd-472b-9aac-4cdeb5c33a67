(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-13273ede"],{b59c:function(e,t,r){"use strict";r("bf9d")},bf9d:function(e,t,r){},dd96:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("span",[e._v("数据筛选")])]),r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticStyle:{display:"flex","justify-content":"space-between"},attrs:{model:e.queryParams,size:"small",inline:!0}},[r("div",[r("el-form-item",{attrs:{label:"危险源名称",prop:"name"}},[r("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入危险源名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),r("el-form-item",{attrs:{label:"所属企业",prop:"enterpriseName"}},[r("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入所属企业",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.enterpriseName,callback:function(t){e.$set(e.queryParams,"enterpriseName",t)},expression:"queryParams.enterpriseName"}})],1)],1),r("div",{staticStyle:{"min-width":"200px"}},[r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)])],1),r("el-card",{staticClass:"box-card"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("span",[e._v("危险源信息列表")]),r("el-button",{staticClass:"queryBtnT",attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增危险源")])],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roleList}},[r("el-table-column",{attrs:{label:"序号",type:"index",width:"70",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),r("el-table-column",{attrs:{label:"危险源名称","show-overflow-tooltip":!0,prop:"name",align:"center"}}),r("el-table-column",{attrs:{label:"所属企业",prop:"enterpriseName",align:"center"}}),r("el-table-column",{attrs:{label:"企业联系人",prop:"contact",align:"center"}}),r("el-table-column",{attrs:{label:"联系方式",prop:"phone",align:"center"}}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(r){return e.handleUpdate(t.row,"look")}}},[e._v("查看")]),r("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(r){return e.handleUpdate(t.row,"Update")}}},[e._v("编辑")]),r("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}})],1),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"960px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"150px"}},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"危险源名称 :",prop:"name"}},[r("el-input",{staticStyle:{width:"245px"},attrs:{disabled:e.disabled,placeholder:"请输入危险源名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"对应的MSDS库信息 :",prop:"msds"}},[r("el-select",{staticStyle:{width:"245px"},attrs:{filterable:"",placeholder:"对应的MSDS库信息",clearable:"",disabled:e.disabled},model:{value:e.form.msds,callback:function(t){e.$set(e.form,"msds",t)},expression:"form.msds"}},e._l(e.originArr,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"企业联系人 :",prop:"contact"}},[r("el-input",{staticStyle:{width:"245px"},attrs:{disabled:e.disabled,placeholder:"请输入企业联系人"},model:{value:e.form.contact,callback:function(t){e.$set(e.form,"contact",t)},expression:"form.contact"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"联系电话 :",prop:"phone"}},[r("el-input",{staticStyle:{width:"245px"},attrs:{disabled:e.disabled,placeholder:"请输入联系电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1)],1),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"所属企业名称 :",prop:"enterpriseName"}},[r("el-input",{attrs:{disabled:e.disabled,placeholder:"请输入所属企业名称"},model:{value:e.form.enterpriseName,callback:function(t){e.$set(e.form,"enterpriseName",t)},expression:"form.enterpriseName"}})],1)],1)],1),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"危险源详细描述 :",prop:"description"}},[r("el-input",{attrs:{disabled:e.disabled,type:"textarea",placeholder:"请输入危险源详细描述"},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1)],1)],1),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"应对措施 :",prop:"countermeasures"}},[r("el-input",{attrs:{disabled:e.disabled,type:"textarea",placeholder:"请输入应对措施"},model:{value:e.form.countermeasures,callback:function(t){e.$set(e.form,"countermeasures",t)},expression:"form.countermeasures"}})],1)],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary",disabled:e.disabled},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],s=(r("ac1f"),r("00b4"),r("d9e2"),r("d81d"),r("14d9"),r("a15b"),r("b64b"),r("e9c4"),r("b775"));function n(e){return Object(s["a"])({url:"/firecontrol-sources-danger/page",method:"get",params:e})}function o(e){return Object(s["a"])({url:"/firecontrol-sources-danger/save",method:"post",data:e})}function l(e){return Object(s["a"])({url:"/firecontrol-sources-danger/update",method:"post",data:e})}function c(e){return Object(s["a"])({url:"firecontrol-msds/list",method:"get",params:e})}function d(e){return Object(s["a"])({url:"/firecontrol-sources-danger/deleteById",method:"post",data:e})}var u=r("90c5"),m={name:"HiddenDangerRecord",dicts:["fault_type","handle_type","emergency_status"],data:function(){var e=function(e,t,r){var a=/^1[345789]\d{9}$/;a.test(t)?r():r(new Error("请输入11位手机号"))};return{loading:!0,showSearch:!0,total:0,roleList:[],title:"",open:!1,queryParams:{current:1,size:10,startTime:void 0,endTime:void 0,id:void 0,description:void 0,createUser:void 0,dangerType:void 0},uploadFile:"api/file/uploadFile",fileList:[],form:{},dateRange:[],originArr:[],rules:{name:[{required:!0,message:"请输入危险源名称",trigger:"blur"}],enterpriseName:[{required:!0,message:"请输入所属企业名称",trigger:"blur"}],description:[{required:!0,message:"请输入危险源详细描述",trigger:"blur"}],contact:[{required:!0,message:"请输入企业联系人",trigger:"blur"}],phone:[{type:"number",validator:e,message:"请输入正确的手机号",trigger:"change",required:!0}]},disabled:!1}},created:function(){this.getList()},methods:{getListpage:function(){var e=this;c().then((function(t){200==t.code&&(console.log(t),e.originArr=t.data)}))},getList:function(){var e=this;this.loading=!0,console.log(this.queryParams),n(this.queryParams).then((function(t){e.roleList=t.data.records,e.total=t.data.total,e.loading=!1,console.log(t)}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,dangerType:void 0,griddingId:void 0,location:void 0,description:void 0,attachment:void 0},this.resetForm("form")},handleQuery:function(){this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.queryParams.current=1,this.getList()},resetQuery:function(){this.dateRange=[],this.queryParams.startTime=void 0,this.queryParams.endTime=void 0,this.resetForm("queryForm"),this.handleQuery()},handleRemove:function(e,t){console.log(e,t);var r=[];t.map((function(e){r.push(e.url)})),this.form.attachment=r.join(",")},handlePreview:function(e,t,r){if(console.log(e,t,r),0==t.size)return this.$modal.msgWarning("当前文件大小不符合规范"),!0;var a=[];r.map((function(e){e.response?a.push(JSON.parse(u["a"].decryptAES(e.response,u["a"].aesKey))):a.push(e.url)})),this.form.attachment=a.join(",")},handleAdd:function(){this.disabled=!1,this.reset(),this.open=!0,this.title="新增危险源",this.fileList=[],this.getListpage()},handleUpdate:function(e,t){var r=this;if("look"==t?(this.disabled=!0,this.title="查看危险源"):(this.disabled=!1,this.title="编辑危险源"),this.reset(),this.open=!0,this.form=JSON.parse(JSON.stringify(e)),this.fileList=[],this.form.amount=null,null!=e.attachment){var a=[];a=JSON.parse(JSON.stringify(this.form.attachment.split(","))),a.map((function(e,t){var a=e.split("/");r.fileList.push({name:Date.now()+"_"+a[a.length-1],url:e})}))}},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(console.log(e.form),void 0!=e.form.id?l(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):o(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return d({id:e.id})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},p=m,f=(r("b59c"),r("2877")),h=Object(f["a"])(p,a,i,!1,null,"f7f01cd4",null);t["default"]=h.exports}}]);