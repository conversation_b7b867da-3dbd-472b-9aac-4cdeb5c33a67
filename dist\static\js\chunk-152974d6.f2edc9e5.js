(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-152974d6"],{"50b7":function(e,t,a){},9808:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入设备名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceName,callback:function(t){e.$set(e.queryParams,"deviceName",t)},expression:"queryParams.deviceName"}})],1),"2"==e.btnDia?a("el-form-item",{attrs:{label:"数据类型",prop:"deviceType"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"数据类型",clearable:""},model:{value:e.queryParams.deviceType,callback:function(t){e.$set(e.queryParams,"deviceType",t)},expression:"queryParams.deviceType"}},e._l(e.dict.type.firecontrol_device_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),"1"==e.btnDia?a("el-form-item",{attrs:{label:"设备状态"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"设备状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.fire_device_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"创建时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"3"==e.btnDia,expression:"btnDia == '3'"}],attrs:{label:"报警类型"}},[a("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"报警类型",clearable:""},model:{value:e.queryParams.emergencyType,callback:function(t){e.$set(e.queryParams,"emergencyType",t)},expression:"queryParams.emergencyType"}},e._l(e.dict.type.emergency_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"3"==e.btnDia,expression:"btnDia == '3'"}],attrs:{label:"处理状态"}},[a("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"处理状态",clearable:""},model:{value:e.queryParams.emergencyStatus,callback:function(t){e.$set(e.queryParams,"emergencyStatus",t)},expression:"queryParams.emergencyStatus"}},e._l(e.dict.type.emergency_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button-group",[a("el-button",{attrs:{type:"1"==e.btnDia?"primary":"",size:"mini"},on:{click:function(t){return e.diaDeatilBtn("1")}}},[e._v("设备状态")]),a("el-button",{attrs:{type:"2"==e.btnDia?"primary":"",size:"mini"},on:{click:function(t){return e.diaDeatilBtn("2")}}},[e._v("运行数据")])],1)],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"},{name:"show",rawName:"v-show",value:"1"==e.btnDia,expression:"btnDia == '1'"}],attrs:{data:e.roleList1}},[a("el-table-column",{attrs:{label:"设备名称",prop:"deviceName",align:"center"}}),a("el-table-column",{attrs:{label:"时间",prop:"createTime",align:"center"}}),a("el-table-column",{attrs:{prop:"status",label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.fire_device_status,value:t.row.status,type:1}})]}}])})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"},{name:"show",rawName:"v-show",value:"2"==e.btnDia,expression:"btnDia == '2'"}],attrs:{data:e.roleList2}},[a("el-table-column",{attrs:{label:"序号",type:"index",width:"70",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),a("el-table-column",{attrs:{label:"数据类型",type:"index",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getNameById(e.queryParams.deviceType))+" ")]}}])}),a("el-table-column",{attrs:{label:"设备名称",prop:"deviceName",align:"center"}}),a("el-table-column",{attrs:{label:"时间",prop:"collectTime",align:"center"}}),a("el-table-column",{attrs:{label:"参数",prop:"dataType",align:"center"}}),a("el-table-column",{attrs:{label:"参数值(mg/m³)",prop:"dataValue",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"300","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(a){return e.handleDetail(t.row)}}},[e._v("详情")])]}}])})],1),a("pagination",{attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"历史数据详情",visible:e.detailOpen,width:"1000px","append-to-body":""},on:{"update:visible":function(t){e.detailOpen=t}}},[a("el-form",{ref:"diaFrom",staticStyle:{"margin-top":"10px"},attrs:{model:e.diaFrom,size:"small",inline:!0,"label-width":"68px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"设备标签"}},[a("el-tag",{attrs:{type:"info"}},[e._v(e._s(e.diaFrom.deviceName))])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.diaChange},model:{value:e.diaFrom.dateRange,callback:function(t){e.$set(e.diaFrom,"dateRange",t)},expression:"diaFrom.dateRange"}})],1)],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingDia,expression:"loadingDia"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"350",border:""}},["2"==e.btnDia?a("el-table-column",{attrs:{prop:"collectTime",label:"时间",align:"center"}}):e._e(),"2"==e.btnDia?a("el-table-column",{attrs:{prop:"dataType",label:"参数",align:"center"}}):e._e(),"2"==e.btnDia?a("el-table-column",{attrs:{prop:"dataValue",label:"参数值",align:"center"}}):e._e()],1),a("pagination",{attrs:{total:e.totalDia,page:e.diaFrom.current,limit:e.diaFrom.size},on:{"update:page":function(t){return e.$set(e.diaFrom,"current",t)},"update:limit":function(t){return e.$set(e.diaFrom,"size",t)},pagination:e.historyPageDevice}})],1)],1)},r=[],l=(a("4de4"),a("d3b7"),a("b775"));function n(e){return Object(l["a"])({url:"/firecontrol-real-time/pageForDeviceType",method:"get",params:e})}function s(e){return Object(l["a"])({url:"/firecontrol-device-status/page",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/firecontrol-history/pageForDeviceId",method:"get",params:e})}var c={name:"OperationData",dicts:["firecontrol_device_type","emergency_status","symbol","fire_device_status","emergency_type","param_type"],data:function(){return{loading:!0,showSearch:!0,totalDia:0,loadingDia:!1,total:0,roleList1:[],roleList2:[],detailOpen:!1,dateRange1:[],tableData:[],queryParams:{current:1,size:10,deviceId:void 0,deviceName:void 0,deviceType:"4011201",emergencyStatus:void 0,startTime:void 0,endTime:void 0,emergencyType:void 0,status:void 0},diaFrom:{},dateRange:[],btnDia:"2"}},created:function(){},mounted:function(){this.getList()},methods:{getList:function(){"1"==this.btnDia?this.pageStatus():"2"==this.btnDia&&this.pageDevice()},handleDetail:function(e){console.log(e),this.detailOpen=!0,this.diaFrom.deviceName=e.deviceName,this.diaFrom.deviceId=e.deviceId,this.historyPageDevice()},pageDevice:function(){var e=this;this.loading=!0,this.roleList2=[],console.log(this.queryParams.deviceType),n(this.queryParams).then((function(t){console.log(t),e.roleList2=t.data.records,e.total=t.data.total,e.loading=!1}))},historyPageDevice:function(){var e=this;this.loadingDia=!0,this.tableData=[],console.log(this.diaFrom),o(this.diaFrom).then((function(t){console.log(t),e.tableData=t.data.records,e.totalDia=t.data.total,e.loadingDia=!1}))},diaChange:function(){this.diaFrom.dateRange.length>0&&(this.diaFrom.startTime=this.diaFrom.dateRange[0],this.diaFrom.endTime=this.diaFrom.dateRange[1]),console.log(this.diaFrom),this.historyPageDevice()},getNameById:function(e){if(void 0!=e&&""!=e&&null!=e)return this.dict.type.firecontrol_device_type.filter((function(t){return t.value==e}))[0].label},pageStatus:function(){var e=this;this.loading=!0,this.roleList1=[],s(this.queryParams).then((function(t){console.log(t),e.roleList1=t.data.records,e.total=t.data.total,e.loading=!1}))},diaDeatilBtn:function(e){this.btnDia=e,this.resetQuery()},handleQuery:function(){this.queryParams.current=1,this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.getList()},resetQuery:function(){this.dateRange=[],this.queryParams.startTime=void 0,this.queryParams.endTime=void 0,this.resetForm("queryForm"),this.handleQuery()}}},d=c,u=(a("f02e"),a("2877")),m=Object(u["a"])(d,i,r,!1,null,"5ffb81a4",null);t["default"]=m.exports},f02e:function(e,t,a){"use strict";a("50b7")}}]);