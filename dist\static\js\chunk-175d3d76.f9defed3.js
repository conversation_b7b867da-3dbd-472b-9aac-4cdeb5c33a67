(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-175d3d76"],{3645:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAstJREFUaEPtWltu2zAQ5DK1kUPUhvIV+xSNb2KfJMlJkp4k7insfFVweojCKbgFBakVVImcXa7zVQH+Mrnc2Zl9iDY5w+ewmG090RdmrhxR5ZyLn/jUjrkmon0I4dv6x6+91bFkYeh1Ob9n57Y9h7NmmXnnrq726/pnnV2cWFAE4PD50x15/yRxfOBLzcyP67f3Zy0INYA26g/ag/v7yLmH29P5UWNLBeC4mL04ojvNgVN7tCDEACwjPwSjASEC0Gr+xTLyQ1sxuSU5IQJwXM4Zdp55T0QxSbuSGrd2ZTVlpmbvN2h1ggGg0knJAC23EinBAIDo1xzCLtek0NK7Op0h36BFh+q6ohC+p3jnEDY557v9rb2YS5OSYu9vEBlBAHLykVDegbCyCQHI1X2U7iGDx+U8sjrOAvN+9fa+yRUNDEDqIOfq1el8kzto7PskANAuCmCyfGrk05PRUzsEjuJHmEUBTFJNzj3fns47DQO5PPgQAA7U6qiE0jMVJM1iBqJjaMkbSeJUZ7cD8LqcJ7WqyYOcfFCbEAPIECdhwbIxQgAi9ZmSF5dAQ1gTDKL7zPsEJJ94KAwgR3mn70h98P7r2BggsYG+ocEAQBb+5mk7TjdJ7lx8e0NG6YZJSWMUAUAjqOkJfQbR6Isk1B0A5EKJ/w5pXv0DRAzEjZdkAS2dRQCQWV5LgTT6Kgm1LCQbmwaAJvpqAJdgQdIIiySEjsIiFgoGQnESd44h4wUKQht9tYT+lFSbK0ZR4xoGRc1ANGTBguQ2Y4zRIgDi8eJfD4qiXyyhhoXFbEtE8TcC8SO9B70IAwUsFEffhAHteKFtXKZJ3DcmHPJMom/GgJQFq+ibAhDkgln0zQEgo7Zl9M0BICxoRuZUfS5uZEPjKRaso38RBiZZKJg4P5SBpjtX15UPIf79oLmNKLkAzrV3cwnlDrT+/j8A64hK7f0GP++HQL/vRA8AAAAASUVORK5CYII="},a832:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:24}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("数据筛选")])]),a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticStyle:{display:"flex","justify-content":"space-between"},attrs:{model:e.queryParams,size:"small",inline:!0,"label-position":"left"}},[a("div",{staticStyle:{width:"80%"}},[a("el-form-item",{attrs:{label:"演练名称"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入演练名称",clearable:"",maxlength:"20"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.planName,callback:function(t){e.$set(e.queryParams,"planName",t)},expression:"queryParams.planName"}})],1),a("el-form-item",{attrs:{label:"演练时间"}},[a("el-date-picker",{staticStyle:{width:"10vw"},attrs:{format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1)],1),a("div",{staticStyle:{"min-width":"166px"}},[a("el-form-item",[a("el-button",{staticStyle:{"font-size":"13px"},attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{staticStyle:{"font-size":"13px"},attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)])],1),a("el-card",{staticClass:"box-card mapBox"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("演练计划展示列表")])]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.eventListDialog,expression:"!eventListDialog"}],staticStyle:{position:"absolute","z-index":"1"},attrs:{type:"primary",plain:""},on:{click:function(t){e.eventListDialog=!0}}},[e._v("事件列表")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.eventListDialog,expression:"eventListDialog"}],staticClass:"eventListBox"},[a("div",{staticClass:"boxTitle"},[a("div",{staticClass:"content"},[e._v("演练计划列表")]),a("i",{staticClass:"el-icon-close",staticStyle:{color:"#fff",cursor:"pointer"},on:{click:function(t){e.eventListDialog=!1}}})]),a("div",{staticClass:"boxContent"},e._l(e.eventList,(function(t,s){return a("div",{key:s,staticClass:"event",on:{click:function(a){return e.eventClick(t)}}},[a("div",{staticClass:"overlength",attrs:{title:t.planName}},[a("span",{staticClass:"name"},[e._v("演练名称：")]),e._v(e._s(t.planName)+" ")]),a("div",[a("span",{staticClass:"name"},[e._v("事件类型：")]),a("el-tag",{attrs:{type:"success"}},[e._v(e._s(t.eventTypeName))])],1),a("div",[a("span",{staticClass:"name"},[e._v("演练状态：")]),e._v(e._s(t.drillStatus?e.dict.type.drill_status.find((function(e){return e.value==t.drillStatus})).label:"")+" ")]),a("div",{staticClass:"overlength",attrs:{title:t.drillTime}},[a("span",{staticClass:"name"},[e._v("演练时间：")]),e._v(e._s(t.drillTime)+" ")])])})),0)]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.eventDetaildialog,expression:"eventDetaildialog"}],staticClass:"eventDetailBox"},[a("div",{staticClass:"boxTitle"},[a("div",{staticClass:"content"},[e._v("演练名称："+e._s(e.eventDetail.planName))]),a("i",{staticClass:"el-icon-close",staticStyle:{color:"#fff",cursor:"pointer"},on:{click:function(t){e.eventDetaildialog=!1}}})]),a("div",{staticClass:"detailContent"},[a("div",{staticClass:"eventDetail"},[a("div",[a("span",{staticClass:"name"},[e._v("事件类型：")]),e._v(e._s(e.eventDetail.eventTypeName)+" ")]),a("div",[a("span",{staticClass:"name"},[e._v("关联预案：")]),e._v(e._s(e.eventDetail.externalPlanName)+" ")]),a("div",[a("span",{staticClass:"name"},[e._v("事件位置：")]),e._v(e._s(e.eventDetail.longitude+","+e.eventDetail.latitude)+" ")]),a("div",[a("span",{staticClass:"name"},[e._v("演练时间：")]),e._v(e._s(e.eventDetail.drillTime))])]),a("div",{staticClass:"btnBox"},[a("el-button",{attrs:{type:"primary",plain:""},on:{click:e.handleCommand}},[e._v("指挥调度")]),a("el-button",{staticStyle:{"margin-left":"30%"},attrs:{type:"primary",plain:""},on:{click:e.handleRecord}},[e._v("执行记录")])],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.resourcefulSearch,expression:"resourcefulSearch"}],staticClass:"searchBox"},[a("div",{staticClass:"boxTitle"},[a("div",{staticClass:"content"},[e._v("周边资源搜索")]),a("i",{staticClass:"el-icon-close",staticStyle:{color:"#fff",cursor:"pointer"},on:{click:function(t){e.resourcefulSearch=!1}}})]),a("div",{staticClass:"searchContent"},[a("div",{staticClass:"searchTitle"},[e._v("距离范围筛选")]),a("div",{staticStyle:{margin:"20px 0"}},[a("el-radio-group",{on:{change:e.rangeChange},model:{value:e.range,callback:function(t){e.range=t},expression:"range"}},[a("el-radio-button",{attrs:{label:"200"}},[e._v("200m")]),a("el-radio-button",{attrs:{label:"500"}},[e._v("500m")]),a("el-radio-button",{attrs:{label:"1000"}},[e._v("1km")]),a("el-radio-button",{attrs:{label:"5000"}},[e._v("5km")])],1)],1),a("div",{staticClass:"searchTitle"},[e._v("资源筛选")]),a("div",{staticStyle:{"margin-top":"20px"}},[a("el-checkbox-group",{on:{change:e.checkListChange},model:{value:e.checkList,callback:function(t){e.checkList=t},expression:"checkList"}},[a("el-checkbox",{attrs:{label:"5013003"}},[e._v("避难场所")]),a("el-checkbox",{attrs:{label:"5013005"}},[e._v("应急广播")]),a("el-checkbox",{attrs:{label:"5013004"}},[e._v("周边监控")]),a("el-checkbox",{attrs:{label:"5013008"}},[e._v("防护目标")]),a("el-checkbox",{attrs:{label:"5013009"}},[e._v("医疗机构")]),a("el-checkbox",{attrs:{label:"5013006"}},[e._v("风险隐患")]),a("el-checkbox",{attrs:{label:"5013007"}},[e._v("通讯保障")])],1)],1),a("div",[a("el-radio-group",{model:{value:e.dispatch,callback:function(t){e.dispatch=t},expression:"dispatch"}},[a("el-radio-button",{attrs:{label:"rank"}},[e._v("队伍调度")]),a("el-radio-button",{attrs:{label:"good"}},[e._v("物资调度")])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"rank"==e.dispatch,expression:"dispatch == 'rank'"}],staticClass:"rankAndgood"},e._l(e.ranksList,(function(t,s){return a("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"item.show"}],key:s,staticClass:"dispatch"},[a("div",{staticClass:"rankName"},[e._v(e._s(t.name))]),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.rankPosition(t)}}},[e._v("定位")]),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.rankDispatch(t)}}},[e._v("调度")])],1)])})),0),a("div",{directives:[{name:"show",rawName:"v-show",value:"good"==e.dispatch,expression:"dispatch == 'good'"}],staticClass:"rankAndgood"},e._l(e.goodsList,(function(t,s){return a("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"item.show"}],key:s,staticClass:"dispatch"},[a("div",{staticClass:"rankName"},[e._v(" "+e._s(t.name)+" ")]),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.goodPosition(t)}}},[e._v("定位")]),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.goodDispatch(t)}}},[e._v("调度")])],1)])})),0)])]),a("Map",{ref:"mapRef",attrs:{url:e.url},on:{detail:e.detailDialog}})],1)],1)],1),a("el-dialog",{attrs:{width:"960px","append-to-body":"",visible:e.commandDialog,"show-close":!1,"close-on-click-modal":!1,title:"指挥调度"},on:{"update:visible":function(t){e.commandDialog=t}}},[a("el-tabs",{on:{"tab-click":e.commandClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"预案响应",name:"first"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("div",{staticClass:"planList"},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.eventDetail.isEndRescue,expression:"!eventDetail.isEndRescue"}],attrs:{type:"primary"},on:{click:e.launchPlan}},[e._v("启动预案")]),e._l(e.planBox,(function(t,s){return a("div",{key:s,staticClass:"plan",on:{click:function(a){return e.planClick(t)}}},[a("div",[e._v(e._s(t.planName))]),a("el-tag",{staticStyle:{margin:"10px 0"},attrs:{type:"danger"}},[e._v(e._s(t.responseName))]),a("div",{staticStyle:{"white-space":"nowrap"}},[e._v("启动时间："+e._s(t.createTime))])],1)}))],2)]),a("el-col",{attrs:{span:18}},[a("el-row",[a("el-col",{attrs:{span:16}},[a("div",{staticClass:"stepBox"},e._l(e.stepList,(function(t,s){return a("div",{key:s,staticClass:"step"},[a("span",[e._v("步骤"+e._s(s+1))]),a("div",{staticClass:"rank"},[a("div",[e._v(" 响应名称:"+e._s(t.responseName)+" "+e._s(e.dict.type.step_status.find((function(e){return e.value==t.processStatus})).label)+" ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5011801==t.processStatus,expression:"item.processStatus == 5011801"}],attrs:{type:"text"},on:{click:function(a){return e.processUpdate(t.id,"5011802")}}},[e._v("开始处置")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5011802==t.processStatus,expression:"item.processStatus == 5011802"}],attrs:{type:"text"},on:{click:function(a){return e.processUpdate(t.id,"5011803")}}},[e._v("结束步骤")])],1),a("div",[e._v("注意事项:"+e._s(t.announcement))]),e._l(t.contingentList,(function(s,n){return a("div",{key:n,staticClass:"rank"},[a("div",[e._v(" 响应队伍:"+e._s(s.contingentName)+" "+e._s(e.dict.type.contingent_status.find((function(e){return e.value==s.contingentStatus})).label)+" ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5011901==s.contingentStatus&&5011802==t.processStatus,expression:"\n                          ele.contingentStatus == 5011901 &&\n                          item.processStatus == 5011802\n                        "}],attrs:{type:"text"},on:{click:function(t){return e.assign(s)}}},[e._v("指派")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5011902==s.contingentStatus&&5011802==t.processStatus,expression:"\n                          ele.contingentStatus == 5011902 &&\n                          item.processStatus == 5011802\n                        "}],attrs:{type:"text"},on:{click:function(t){return e.finish(s,"5011902")}}},[e._v("完成")])],1)}))],2)})),0)]),a("el-col",{attrs:{span:8}},[a("el-timeline",e._l(e.activities,(function(t,s){return a("el-timeline-item",{key:s,attrs:{timestamp:"步骤"+(s+1),placement:"top"}},[e._v(" 开始时间："+e._s(t.startTime)+" "),a("br"),e._l(t.contingentList,(function(t,s){return a("div",{key:s},[e._v(" 指派队伍："+e._s(t.contingentName)+" "),a("br"),e._v(" 任务描述："+e._s(t.taskDescription)+" "),a("br"),a("span",{directives:[{name:"show",rawName:"v-show",value:"5011902"==t.contingentStatus,expression:"ite.contingentStatus == '5011902'"}]},[e._v(" 预期完成时间："+e._s(t.finishTime)+" ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:"5011903"==t.contingentStatus,expression:"ite.contingentStatus == '5011903'"}]},[e._v(" 完成时间："+e._s(t.endTime)+" ")])])})),a("br"),a("el-tag",{directives:[{name:"show",rawName:"v-show",value:5011803==t.processStatus,expression:"activity.processStatus == 5011803"}],attrs:{type:"success"}},[e._v(" "+e._s(e.dict.type.step_status.find((function(e){return e.value==t.processStatus})).label)+" ")]),a("el-tag",{directives:[{name:"show",rawName:"v-show",value:5011802==t.processStatus,expression:"activity.processStatus == 5011802"}],attrs:{type:"warning"}},[e._v(" "+e._s(e.dict.type.step_status.find((function(e){return e.value==t.processStatus})).label)+" ")]),a("el-tag",{directives:[{name:"show",rawName:"v-show",value:5011801==t.processStatus,expression:"activity.processStatus == 5011801"}],attrs:{type:"info"}},[e._v(" "+e._s(e.dict.type.step_status.find((function(e){return e.value==t.processStatus})).label)+" ")])],2)})),1)],1)],1)],1)],1)],1),a("el-tab-pane",{attrs:{label:"临时任务",name:"second"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.sendTask}},[e._v("增派临时任务")]),a("el-table",{staticStyle:{width:"100%",margin:"20px 0"},attrs:{data:e.tasksData,border:""}},[a("el-table-column",{attrs:{prop:"updateTime",label:"创建时间",align:"center"}}),a("el-table-column",{attrs:{prop:"taskType",label:"任务类型",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(" "+e._s(e.dict.type.task_type.find((function(e){return e.value==t.row.taskType}))?e.dict.type.task_type.find((function(e){return e.value==t.row.taskType})).label:"")+" ")])]}}])}),a("el-table-column",{attrs:{prop:"leader",label:"负责人",align:"center"}}),a("el-table-column",{attrs:{prop:"taskRemark",label:"任务备注",align:"center"}}),a("el-table-column",{attrs:{prop:"finishTime",label:"期望完成时间",align:"center"}}),a("el-table-column",{attrs:{prop:"taskStatus",label:"任务状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(" "+e._s(e.dict.type.task_status.find((function(e){return e.value==t.row.taskStatus}))?e.dict.type.task_status.find((function(e){return e.value==t.row.taskStatus})).label:"")+" ")])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleTaskDetail(t.row)}}},[e._v("详情")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5012601==t.row.taskStatus,expression:"scope.row.taskStatus == 5012601"}],attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleTaskUpdate(t.row,"5012602")}}},[e._v("完成")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5012601==t.row.taskStatus,expression:"scope.row.taskStatus == 5012601"}],attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleTaskUpdate(t.row,"5012603")}}},[e._v("取消")])]}}])})],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.eventDetail.isEndRescue,expression:"!eventDetail.isEndRescue"}],attrs:{type:"primary",plain:""},on:{click:e.endRescue}},[e._v("结束演练")]),a("el-button",{on:{click:function(t){e.commandDialog=!1}}},[e._v("返 回")])],1)],1),a("el-dialog",{attrs:{width:"720px","append-to-body":"",visible:e.launchPlanDialog,"show-close":!1,"close-on-click-modal":!1,title:"启动预案"},on:{"update:visible":function(t){e.launchPlanDialog=t}}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.planLoading,expression:"planLoading"}],staticStyle:{display:"flex","flex-wrap":"wrap"}},[a("el-radio",{staticStyle:{width:"320px",margin:"0 0 10px 10px"},attrs:{label:e.planList.id,border:""},on:{input:e.planInput},model:{value:e.planRadio,callback:function(t){e.planRadio=t},expression:"planRadio"}},[e._v(e._s(e.planList.planName)+"-"+e._s(e.planList.planDescription))])],1),a("el-form",{ref:"levelFrom",staticStyle:{"margin-top":"20px"},attrs:{model:e.levelFrom,rules:e.levelRules,inline:!0}},[a("el-form-item",{attrs:{label:"响应等级",prop:"level"}},[a("el-select",{attrs:{placeholder:"请选择响应等级"},model:{value:e.levelFrom.level,callback:function(t){e.$set(e.levelFrom,"level",t)},expression:"levelFrom.level"}},e._l(e.levelBox,(function(e,t){return a("el-option",{key:t,attrs:{label:e.responseName,value:e.id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确 认")]),a("el-button",{on:{click:function(t){e.launchPlanDialog=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"指派任务",visible:e.assignDialog,width:"560px","append-to-body":""},on:{"update:visible":function(t){e.assignDialog=t}}},[a("el-form",{ref:"assignFrom",attrs:{model:e.assignFrom,rules:e.assignRules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"任务描述",prop:"taskDescription"}},[a("el-input",{attrs:{type:"textarea",maxlength:"200"},model:{value:e.assignFrom.taskDescription,callback:function(t){e.$set(e.assignFrom,"taskDescription",t)},expression:"assignFrom.taskDescription"}})],1),a("el-form-item",{attrs:{label:"期望完成时间",prop:"finishTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss"},model:{value:e.assignFrom.finishTime,callback:function(t){e.$set(e.assignFrom,"finishTime",t)},expression:"assignFrom.finishTime"}})],1),a("el-form-item",{attrs:{label:"是否短信通知",prop:"isSmsNotice"}},[a("el-radio-group",{model:{value:e.assignFrom.isSmsNotice,callback:function(t){e.$set(e.assignFrom,"isSmsNotice",t)},expression:"assignFrom.isSmsNotice"}},[a("el-radio",{attrs:{label:"true"}},[e._v("是")]),a("el-radio",{attrs:{label:"false"}},[e._v("否")])],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.assignConfirm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.assignDialog=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:e.sendTitle,visible:e.sendDialog,width:"960px","append-to-body":""},on:{"update:visible":function(t){e.sendDialog=t}}},[a("el-radio-group",{staticStyle:{"margin-bottom":"20px"},model:{value:e.sendFlag,callback:function(t){e.sendFlag=t},expression:"sendFlag"}},[a("el-radio-button",{attrs:{label:"rank"}},[e._v("队伍调度")]),a("el-radio-button",{attrs:{label:"good"}},[e._v("物资调度")])],1),"rank"==e.sendFlag?a("el-form",{ref:"sendrankFrom",attrs:{model:e.sendrankFrom,rules:e.sendrankRules,"label-width":"120px"}},[a("el-form-item",{key:"rankId",attrs:{label:"指派队伍",prop:"contingentId"}},[a("el-select",{attrs:{placeholder:"请选择指派队伍",disabled:e.rankDisabled},model:{value:e.sendrankFrom.contingentId,callback:function(t){e.$set(e.sendrankFrom,"contingentId",t)},expression:"sendrankFrom.contingentId"}},e._l(e.ranksOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.contingentName,value:e.id}})})),1)],1),a("el-form-item",{key:"rankRemark",attrs:{label:"任务备注",prop:"taskRemark"}},[a("el-input",{staticStyle:{width:"60%"},attrs:{type:"textarea",maxlength:"200",disabled:e.rankDisabled},model:{value:e.sendrankFrom.taskRemark,callback:function(t){e.$set(e.sendrankFrom,"taskRemark",t)},expression:"sendrankFrom.taskRemark"}})],1),a("el-form-item",{key:"rankFinish",attrs:{label:"期望完成时间",prop:"finishTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss",disabled:e.rankDisabled},model:{value:e.sendrankFrom.finishTime,callback:function(t){e.$set(e.sendrankFrom,"finishTime",t)},expression:"sendrankFrom.finishTime"}})],1),a("el-form-item",{key:"rankisIn",attrs:{label:"短信通知负责人",prop:"isInform"}},[a("el-radio-group",{attrs:{disabled:e.rankDisabled},model:{value:e.sendrankFrom.isInform,callback:function(t){e.$set(e.sendrankFrom,"isInform",t)},expression:"sendrankFrom.isInform"}},[a("el-radio",{attrs:{label:"1"}},[e._v("是")]),a("el-radio",{attrs:{label:"0"}},[e._v("否")])],1)],1)],1):e._e(),"good"==e.sendFlag?a("el-form",{ref:"sendgoodFrom",attrs:{model:e.sendgoodFrom,rules:e.sendgoodRules,"label-width":"120px"}},[a("el-form-item",{key:"goodDepot",attrs:{label:"调度仓库",prop:"depotId"}},[a("el-select",{attrs:{placeholder:"请选择调度仓库",disabled:e.goodDisabled},on:{change:e.depotChange},model:{value:e.sendgoodFrom.depotId,callback:function(t){e.$set(e.sendgoodFrom,"depotId",t)},expression:"sendgoodFrom.depotId"}},e._l(e.depotOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"调度物资"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.goodsData,border:""}},[a("el-table-column",{attrs:{align:"center",prop:"materialName",label:"物资名称","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"materialType",align:"center",label:"物资类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",[e._v(e._s(e.dict.type.materiel_type.find((function(e){return e.value==t.row.materialType})).label))])]}}],null,!1,662894432)}),this.goodDisabled?e._e():a("el-table-column",{attrs:{prop:"inventory",align:"center",label:"库存量"}}),a("el-table-column",{attrs:{prop:"dispatchQuantity",align:"center",label:"调度量"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{type:"number",maxlength:"20",disabled:e.goodDisabled},model:{value:t.row.dispatchQuantity,callback:function(a){e.$set(t.row,"dispatchQuantity",a)},expression:"scope.row.dispatchQuantity"}})]}}],null,!1,1421396730)})],1)],1),a("el-form-item",{key:"goodRemark",attrs:{label:"任务备注",prop:"taskRemark"}},[a("el-input",{attrs:{type:"textarea",disabled:e.goodDisabled,maxlength:"200"},model:{value:e.sendgoodFrom.taskRemark,callback:function(t){e.$set(e.sendgoodFrom,"taskRemark",t)},expression:"sendgoodFrom.taskRemark"}})],1),a("el-form-item",{key:"goodFinish",attrs:{label:"期望完成时间",prop:"finishTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss",disabled:e.goodDisabled},model:{value:e.sendgoodFrom.finishTime,callback:function(t){e.$set(e.sendgoodFrom,"finishTime",t)},expression:"sendgoodFrom.finishTime"}})],1),a("el-form-item",{key:"goodisIn",attrs:{label:"是否短信通知",prop:"isInform"}},[a("el-radio-group",{attrs:{disabled:e.goodDisabled},model:{value:e.sendgoodFrom.isInform,callback:function(t){e.$set(e.sendgoodFrom,"isInform",t)},expression:"sendgoodFrom.isInform"}},[a("el-radio",{attrs:{label:"1"}},[e._v("是")]),a("el-radio",{attrs:{label:"0"}},[e._v("否")])],1)],1)],1):e._e(),a("span",{directives:[{name:"show",rawName:"v-show",value:!e.goodDisabled,expression:"!goodDisabled"}],staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.sendConfirm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.sendDialog=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"执行记录",visible:e.recordDialog,width:"720px","append-to-body":""},on:{"update:visible":function(t){e.recordDialog=t}}},[a("div",{staticClass:"block"},[a("el-timeline",[a("el-timeline-item",{attrs:{timestamp:"事件接报",placement:"top"}},[a("el-card",[a("div",[e._v(" 上报时间："+e._s(e.eventReport.reportTime?e.eventReport.reportTime:"")+" ")]),a("div",[e._v(" 上报人："+e._s(e.eventReport.submitPerson)+" "+e._s(e.eventReport.contactNumber)+" ")])])],1),a("el-timeline-item",{attrs:{timestamp:"预案启动",placement:"top"}},[a("el-card",e._l(e.executionRecord.planStarts,(function(t,s){return a("div",{key:s,staticStyle:{margin:"10px 0"}},[a("div",[e._v("预案名称："+e._s(t.planName))]),a("div",[e._v("开始时间："+e._s(t.startTime))]),a("div",[e._v("处理人员："+e._s(t.handler))])])})),0)],1),a("el-timeline-item",{attrs:{timestamp:"持续调度",placement:"top"}},[a("el-card",e._l(e.executionRecord.continuousDispatch,(function(t,s){return a("div",{key:s,staticStyle:{margin:"10px 0"}},[a("div",{style:5012901==t.assignTaskType?"color:red":"color:green"},[e._v(" "+e._s(e.dict.type.assign_task_type.find((function(e){return e.value==t.assignTaskType}))?e.dict.type.assign_task_type.find((function(e){return e.value==t.assignTaskType})).label:"")+" ")]),a("div",[e._v("任务开始时间："+e._s(t.startTime?t.startTime:""))]),a("div",[e._v("指派队伍："+e._s(t.contingentName))]),a("div",[e._v(" 任务"+e._s("5012603"==t.taskStatus?"取消":"结束")+"时间："+e._s("5012601"==t.taskStatus?"":t.updateTime)+" ")])])})),0)],1),a("el-timeline-item",{attrs:{timestamp:"结束演练",placement:"top"}},[a("el-card",[a("div",[e._v(" 结束时间："+e._s(e.executionRecord.endRescue?e.executionRecord.endRescue.endTime:"")+" ")])])],1)],1)],1)])],1)},n=[],i=(a("d3b7"),a("159b"),a("25f0"),a("7db0"),a("14d9"),a("b775"));function o(e){return Object(i["a"])({url:"/emergency-drill-plan/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/emergency-event-type/tree",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/emergency-resource-overview/list",method:"get",params:e})}function d(e){return Object(i["a"])({url:"/emergency_plan/detail",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/emergency-plan-response-level/list",method:"get",params:e})}function u(e){return Object(i["a"])({url:"/emergency-plan-response/save",method:"post",data:e})}function m(e){return Object(i["a"])({url:"/emergency-plan-response/show-plan",method:"get",params:e})}function p(e){return Object(i["a"])({url:"/emergency-plan-response/show-process",method:"get",params:e})}function f(e){return Object(i["a"])({url:"/emergency-plan-response/show-process-right",method:"get",params:e})}function v(e){return Object(i["a"])({url:"/emergency-plan-response/update",method:"post",data:e})}function g(e){return Object(i["a"])({url:"/emergency-plan-response-contingent/update",method:"post",data:e})}function h(e){return Object(i["a"])({url:"/emergency_expert_contingent/list",method:"get",params:e})}function k(e){return Object(i["a"])({url:"/emergency-temporary-task/save",method:"post",data:e})}function b(e){return Object(i["a"])({url:"/emergency-supply-depot/list",method:"get",params:e})}function y(e){return Object(i["a"])({url:"/emergency-material/listOfDepot",method:"get",params:e})}function _(e){return Object(i["a"])({url:"/emergency-temporary-task/list",method:"get",params:e})}function D(e){return Object(i["a"])({url:"/emergency-temporary-task/detail",method:"get",params:e})}function w(e){return Object(i["a"])({url:"/emergency-temporary-task/update",method:"post",data:e})}function x(e){return Object(i["a"])({url:"/emergency-execution-record/save",method:"post",data:e})}function T(e){return Object(i["a"])({url:"/emergency-execution-record/detail",method:"get",params:e})}var R=a("344e"),S={name:"EmergencySupplies",dicts:["plan_deduction","response_level","step_status","contingent_status","materiel_type","task_type","task_status","assign_task_type","event_level","drill_type","drill_status"],components:{Map:R["default"]},data:function(){return{url:"".concat(a("3645")),rankUrl:"".concat(a("9a55")),goodUrl:"".concat(a("bdb4")),treeData:[],defaultProps:{children:"children",label:"nodeName"},loading:!1,planLoading:!1,showSearch:!0,resourcefulSearch:!1,eventList:[],text:void 0,dateRange:[],commandDialog:!1,activeName:"first",launchPlanDialog:!1,planList:[],queryParams:{current:1,size:1e3,eventNo:void 0,eventName:void 0,eventTypeName:void 0,eventTypeId:void 0,eventLevel:void 0,startTime:"",endTime:""},eventListDialog:!0,eventDetaildialog:!1,eventDetail:{},range:"200m",checkList:[],dispatch:"rank",ranksList:[],goodsList:[],planRadio:"",levelFrom:{level:void 0},levelBox:[],levelRules:{level:[{required:!0,message:"请选择响应等级",trigger:"blur"}]},assignDialog:!1,assignFrom:{taskDescription:void 0,finishTime:void 0,isSmsNotice:void 0},assignRules:{taskDescription:[{required:!0,message:"请输入任务描述",trigger:"blur"}],time:[{required:!0,message:"请选择完成时间",trigger:"blur"}],isSmsNotice:[{required:!0,message:"请选择是否短信通知",trigger:"blur"}]},planBox:[],planRow:void 0,stepList:[],activities:[],tasksData:[],sendDialog:!1,rankDisabled:!1,goodDisabled:!1,sendTitle:"",sendFlag:"rank",ranksOptions:[],sendrankFrom:{contingentId:void 0,taskRemark:void 0,finishTime:void 0,isInform:void 0},sendrankRules:{contingentId:[{required:!0,message:"请选择指派队伍",trigger:"change"}],taskRemark:[{required:!0,message:"请输入任务备注",trigger:"blur"}],finishTime:[{required:!0,message:"请选择期望时间",trigger:"change"}],isInform:[{required:!0,message:"请选择是否通知",trigger:"change"}]},depotOptions:[],sendgoodFrom:{depotId:void 0,taskRemark:void 0,finishTime:void 0,isInform:void 0},goodsData:[],sendgoodRules:{depotId:[{required:!0,message:"请选择指派队伍",trigger:"change"}],taskRemark:[{required:!0,message:"请输入任务备注",trigger:"blur"}],finishTime:[{required:!0,message:"请选择期望时间",trigger:"change"}],isInform:[{required:!0,message:"请选择是否通知",trigger:"change"}]},recordDialog:!1,executionRecord:{},eventReport:{}}},watch:{},created:function(){this.getList()},mounted:function(){var e=this;this.getTree(),this.$refs.mapRef.initMap(),l({resourceType:5013001}).then((function(t){e.goodsList=t.data.list})),l({resourceType:5013002}).then((function(t){e.ranksList=t.data.list}))},methods:{rangeChange:function(e){var t=this;this.$refs.mapRef.circleRange(this.eventDetail.longitude,this.eventDetail.latitude,e),this.$refs.mapRef.clearAllList(),this.checkList=[],this.ranksList.forEach((function(t){t.distance<e?t.show=!0:t.show=!1})),this.goodsList.forEach((function(t){t.distance<e?t.show=!0:t.show=!1})),l({resourceType:5013003}).then((function(a){a.data.list.forEach((function(a){a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,t.eventDetail.longitude,t.eventDetail.latitude),a.distance<e&&t.$refs.mapRef.markerList(a.longitude,a.latitude,"shelter",a)}))})),l({resourceType:5013004}).then((function(a){a.data.list.forEach((function(a){a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,t.eventDetail.longitude,t.eventDetail.latitude),a.distance<e&&t.$refs.mapRef.markerList(a.longitude,a.latitude,"monitor",a)}))})),l({resourceType:5013005}).then((function(a){a.data.list.forEach((function(a){a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,t.eventDetail.longitude,t.eventDetail.latitude),a.distance<e&&t.$refs.mapRef.markerList(a.longitude,a.latitude,"broadcast",a)}))})),l({resourceType:5013006}).then((function(a){a.data.list.forEach((function(a){a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,t.eventDetail.longitude,t.eventDetail.latitude),a.distance<e&&t.$refs.mapRef.markerList(a.longitude,a.latitude,"risk",a)}))})),l({resourceType:5013007}).then((function(a){a.data.list.forEach((function(a){a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,t.eventDetail.longitude,t.eventDetail.latitude),a.distance<e&&t.$refs.mapRef.markerList(a.longitude,a.latitude,"communicate",a)}))})),l({resourceType:5013008}).then((function(a){a.data.list.forEach((function(a){a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,t.eventDetail.longitude,t.eventDetail.latitude),a.distance<e&&t.$refs.mapRef.markerList(a.longitude,a.latitude,"protection",a)}))})),l({resourceType:5013009}).then((function(a){a.data.list.forEach((function(a){a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,t.eventDetail.longitude,t.eventDetail.latitude),a.distance<e&&t.$refs.mapRef.markerList(a.longitude,a.latitude,"medical",a)}))})),this.$forceUpdate()},rankPosition:function(e){this.$refs.mapRef.marker(e.longitude,e.latitude,this.rankUrl,!1,!1,e)},goodPosition:function(e){this.$refs.mapRef.marker(e.longitude,e.latitude,this.goodUrl,!1,!1,e)},rankDispatch:function(e){this.sendTask(),this.sendrankFrom.contingentId=e.id.toString()},goodDispatch:function(e){this.sendTask(),this.sendFlag="good",this.sendgoodFrom.depotId=e.id.toString(),this.depotChange(e.id)},checkListChange:function(e){-1!=e.indexOf("5013003")?this.$refs.mapRef.markerListAdd("shelter"):this.$refs.mapRef.markerListRemove("shelter"),-1!=e.indexOf("5013005")?this.$refs.mapRef.markerListAdd("broadcast"):this.$refs.mapRef.markerListRemove("broadcast"),-1!=e.indexOf("5013004")?this.$refs.mapRef.markerListAdd("monitor"):this.$refs.mapRef.markerListRemove("monitor"),-1!=e.indexOf("5013008")?this.$refs.mapRef.markerListAdd("protection"):this.$refs.mapRef.markerListRemove("protection"),-1!=e.indexOf("5013009")?this.$refs.mapRef.markerListAdd("medical"):this.$refs.mapRef.markerListRemove("medical"),-1!=e.indexOf("5013006")?this.$refs.mapRef.markerListAdd("risk"):this.$refs.mapRef.markerListRemove("risk"),-1!=e.indexOf("5013007")?this.$refs.mapRef.markerListAdd("communicate"):this.$refs.mapRef.markerListRemove("communicate")},getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){console.log(t),null!=t.data&&(e.eventList=t.data),e.loading=!1}))},getTree:function(){var e=this;r().then((function(t){200==t.code&&(e.recursion(t.data),e.treeData=t.data)}))},handleNodeClick:function(e,t,a){t?(this.$refs.Addtree.setCheckedNodes([e]),this.queryParams.eventTypeId=e.id,this.queryParams.eventTypeName=e.nodeName):(this.queryParams.eventTypeId=void 0,this.queryParams.eventTypeName=void 0)},eventLabelChange:function(e){this.$forceUpdate()},recursion:function(e){var t=this;e.forEach((function(e,a){if(e.children)return e.disabled=!0,t.recursion(e.children);e.disabled=!1}))},eventClick:function(e){var t=this;this.eventDetail=e,this.range="",this.checkList=[],this.$refs.mapRef.clearCircle(),this.stepList=[],this.activities=[],this.$refs.mapRef.marker(e.longitude,e.latitude,this.url,!0,!0),this.$refs.mapRef.marker(e.longitude,e.latitude,this.url,!1,!0),this.ranksList.forEach((function(a){a.show=!0,a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,e.longitude,e.latitude)})),this.goodsList.forEach((function(a){a.show=!0,a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,e.longitude,e.latitude)})),l({resourceType:5013003}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"shelter",e)}))})),l({resourceType:5013004}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"monitor",e)}))})),l({resourceType:5013005}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"broadcast",e)}))})),l({resourceType:5013006}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"risk",e)}))})),l({resourceType:5013007}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"communicate",e)}))})),l({resourceType:5013008}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"protection",e)}))})),l({resourceType:5013009}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"medical",e)}))})),this.resourcefulSearch=!0},detailDialog:function(e){this.eventDetaildialog=e},handleCommand:function(){var e=this;this.commandDialog=!0,m({businessTypeId:this.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(t){t.responseName=e.dict.type.response_level.find((function(e){return e.value==t.responseLevel})).label})),e.planBox=t.data})),_({businessTypeId:this.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),e.tasksData=t.data}))},planClick:function(e){var t=this;this.planRow=e,p({responseLevelId:e.responseLevelId,businessTypeId:this.eventDetail.businessTypeId}).then((function(e){t.stepList=e.data})),f({responseLevelId:e.responseLevelId,businessTypeId:this.eventDetail.businessTypeId}).then((function(e){console.log(e.data),t.activities=e.data}))},commandClick:function(e,t){console.log(e,t)},endRescue:function(){var e=this,t=this;this.$modal.confirm("是否确认结束当前演练").then((function(){return x({businessTypeId:t.eventDetail.businessTypeId})})).then((function(t){200==t.code&&(e.$modal.msgSuccess(t.msg),e.commandDialog=!1,e.eventDetaildialog=!1,e.$refs.mapRef.clearMap(),e.getList())})).catch((function(e){}))},launchPlan:function(){var e=this;this.planList=[],this.planRadio=void 0,this.launchPlanDialog=!0,d({id:this.eventDetail.planId}).then((function(t){e.planList=t.data}))},planInput:function(e){var t=this;c({planId:e}).then((function(e){e.data.forEach((function(e){e.responseName=t.dict.type.response_level.find((function(t){return t.value==e.responseLevel})).label})),t.levelBox=e.data}))},confirm:function(){var e=this;this.planRadio?this.$refs["levelFrom"].validate((function(t){if(!t)return console.log("error submit!!"),!1;var a={};a.responseLevelId=e.levelFrom.level,a.businessTypeId=e.eventDetail.businessTypeId,u(a).then((function(t){200==t.code&&(e.launchPlanDialog=!1,e.$modal.msgSuccess("预案启动成功！"),m({businessTypeId:e.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(t){t.responseName=e.dict.type.response_level.find((function(e){return e.value==t.responseLevel})).label})),e.planBox=t.data})))}))})):this.$modal.msgWarning("请先选择预案！")},processUpdate:function(e,t){var a=this;v({id:e,processStatus:t}).then((function(e){5011802==t?a.$modal.msgSuccess("当前流程开始处置！"):a.$modal.msgSuccess("当前流程结束步骤！"),a.planClick(a.planRow)}))},assign:function(e){this.$refs.assignFrom&&this.$refs.assignFrom.resetFields(),this.assignFrom.id=e.id,this.assignFrom.contingentId=e.contingentId,this.assignDialog=!0},finish:function(e,t){var a=this;this.$modal.confirm("是否确认完成当前指派").then((function(){return g({id:e.id,contingentId:e.contingentId,contingentStatus:5011903})})).then((function(){a.planClick(a.planRow),a.$modal.msgSuccess("指派已完成!")})).catch((function(e){}))},assignConfirm:function(){var e=this;this.$refs["assignFrom"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.assignFrom.contingentStatus="5011902",g(e.assignFrom).then((function(t){200==t.code&&(e.assignDialog=!1,e.$modal.msgSuccess("任务指派成功！"),e.planClick(e.planRow))}))}))},sendTask:function(){var e=this;this.sendTitle="增派临时任务",this.sendFlag="rank",this.rankDisabled=!1,this.goodDisabled=!1,this.sendrankFrom={contingentId:void 0,taskRemark:void 0,finishTime:void 0,isInform:void 0},this.sendgoodFrom={depotId:void 0,taskRemark:void 0,finishTime:void 0,isInform:void 0},this.goodsData=[],this.sendDialog=!0,h().then((function(t){e.ranksOptions=t.data})),b().then((function(t){e.depotOptions=t.data}))},depotChange:function(e){var t=this;this.goodsData=[],y({supplyDepotId:e}).then((function(e){t.goodsData=e.data,t.goodsData.forEach((function(e){t.$set(e,"dispatchQuantity","")}))}))},sendConfirm:function(){var e=this;"rank"==this.sendFlag?this.$refs["sendrankFrom"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.ranksOptions.forEach((function(t){e.sendrankFrom.contingentId==t.id&&(e.sendrankFrom.phone=t.phone)})),k({businessTypeId:e.eventDetail.businessTypeId,contingentVo:e.sendrankFrom,taskType:5012401}).then((function(t){200==t.code&&(e.$modal.msgSuccess("队伍指派成功！"),e.sendDialog=!1,_({businessTypeId:e.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),e.tasksData=t.data})))}))})):this.$refs["sendgoodFrom"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.sendgoodFrom.materialVos=[],e.goodsData.forEach((function(t){t.dispatchQuantity&&e.sendgoodFrom.materialVos.push({materialId:t.id,dispatchQuantity:t.dispatchQuantity})})),k({businessTypeId:e.eventDetail.businessTypeId,depotVo:e.sendgoodFrom,taskType:5012402}).then((function(t){200==t.code&&(e.$modal.msgSuccess("物资调度成功！"),e.sendDialog=!1,_({businessTypeId:e.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),e.tasksData=t.data})))}))}))},handleTaskDetail:function(e){var t=this;this.sendTitle="临时任务详情",h().then((function(e){t.ranksOptions=e.data})),b().then((function(e){t.depotOptions=e.data})),D({id:e.id,taskType:e.taskType}).then((function(a){t.rankDisabled=!0,t.goodDisabled=!0,5012401==e.taskType?(t.sendFlag="rank",t.sendDialog=!0,t.sendrankFrom=a.data.contingentVo,t.sendrankFrom.contingentId=t.sendrankFrom.contingentId.toString()):(t.sendFlag="good",t.sendDialog=!0,t.sendgoodFrom=a.data.depotVo,t.sendgoodFrom.depotId=t.sendgoodFrom.depotId.toString(),t.goodsData=a.data.depotVo.materialVos)}))},handleTaskUpdate:function(e,t){var a=this;5012602==t?this.$modal.confirm("是否确认完成当前任务").then((function(){return w({id:e.id,taskStatus:t})})).then((function(){a.$modal.msgSuccess("任务已完成!"),_({businessTypeId:a.eventDetail.businessTypeId}).then((function(e){e.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),a.tasksData=e.data}))})).catch((function(e){})):5012603==t&&this.$modal.confirm("是否确认取消当前任务").then((function(){return w({id:e.id,taskStatus:t})})).then((function(){a.$modal.msgSuccess("任务已取消!"),_({businessTypeId:a.eventDetail.businessTypeId}).then((function(e){e.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),a.tasksData=e.data}))})).catch((function(e){}))},handleRecord:function(){var e=this;T({businessTypeId:this.eventDetail.businessTypeId}).then((function(t){console.log(t.data),e.recordDialog=!0,e.executionRecord=t.data,e.eventReport=t.data.eventReport}))},handleQuery:function(){this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.getList()},resetQuery:function(){this.dateRange=[],this.queryParams={current:1,size:10,eventNo:void 0,eventName:void 0,eventTypeName:void 0,eventTypeId:void 0,eventLevel:void 0,startTime:"",endTime:""},this.resetForm("queryForm"),this.handleQuery()}}},F=S,I=(a("d01f"),a("2877")),L=Object(I["a"])(F,s,n,!1,null,"32022a06",null);t["default"]=L.exports},a9e7:function(e,t,a){},d01f:function(e,t,a){"use strict";a("a9e7")}}]);