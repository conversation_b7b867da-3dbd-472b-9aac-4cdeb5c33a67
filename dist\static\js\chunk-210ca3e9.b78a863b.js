(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-210ca3e9"],{"061b":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],style:"height:"+e.height},[n("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:e.src,frameborder:"no",scrolling:"auto"}})])},r=[],u={props:{src:{type:String,required:!0}},data:function(){return{height:document.documentElement.clientHeight-94.5+"px;",loading:!0,url:this.src}},mounted:function(){var e=this;setTimeout((function(){e.loading=!1}),300);var t=this;window.onresize=function(){t.height=document.documentElement.clientHeight-94.5+"px;"}}},a=u,o=n("2877"),l=Object(o["a"])(a,i,r,!1,null,null,null);t["a"]=l.exports},5194:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("i-frame",{attrs:{src:e.url}})},r=[],u=n("061b"),a={name:"Druid",components:{iFrame:u["a"]},data:function(){return{url:"/druid/login.html"}}},o=a,l=n("2877"),c=Object(l["a"])(o,i,r,!1,null,null,null);t["default"]=c.exports}}]);