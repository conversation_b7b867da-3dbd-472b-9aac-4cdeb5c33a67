(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-289a53c6"],{"3ba8":function(e,t,a){},5110:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("数据筛选")])]),a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticStyle:{display:"flex","justify-content":"space-between"},attrs:{model:e.queryParams,size:"small",inline:!0}},[a("div",[a("el-form-item",{attrs:{label:"重点建筑名称",prop:"name"}},[a("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入关键装置名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"所属区域 :",prop:"areaId"}},[a("el-select",{staticStyle:{width:"245px"},attrs:{filterable:"",placeholder:"请选择所属区域",clearable:""},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}},e._l(e.originArr,(function(e){return a("el-option",{key:e.areaId,attrs:{label:e.areaName,value:e.areaId}})})),1)],1),a("el-form-item",{attrs:{label:"联系人",prop:"contact"}},[a("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入联系人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.contact,callback:function(t){e.$set(e.queryParams,"contact",t)},expression:"queryParams.contact"}})],1)],1),a("div",{staticStyle:{"min-width":"200px"}},[a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)])],1),a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("重点建筑信息列表")]),a("el-button",{staticClass:"queryBtnT",attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增重点建筑")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roleList}},[a("el-table-column",{attrs:{label:"序号",type:"index",width:"70",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),a("el-table-column",{attrs:{label:"重点建筑名称","show-overflow-tooltip":!0,prop:"name",align:"center"}}),a("el-table-column",{attrs:{label:"所属区域",prop:"areaName",align:"center"}}),a("el-table-column",{attrs:{label:"联系人",prop:"contact",align:"center"}}),a("el-table-column",{attrs:{label:"联系方式",prop:"phone",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row,"look")}}},[e._v("查看")]),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row,"Update")}}},[e._v("编辑")]),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}})],1),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"960px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"150px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"重点建筑名称 :",prop:"name"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{disabled:e.disabled,placeholder:"请输入重点建筑名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"所属区域 :",prop:"areaId"}},[a("el-select",{staticStyle:{width:"245px"},attrs:{filterable:"",placeholder:"请选择所属区域",clearable:"",disabled:e.disabled},model:{value:e.form.areaId,callback:function(t){e.$set(e.form,"areaId",t)},expression:"form.areaId"}},e._l(e.originArr,(function(e){return a("el-option",{key:e.areaId,attrs:{label:e.areaName,value:e.areaId}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系人 :",prop:"contact"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{disabled:e.disabled,placeholder:"请输入联系人"},model:{value:e.form.contact,callback:function(t){e.$set(e.form,"contact",t)},expression:"form.contact"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系电话 :",prop:"phone"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{disabled:e.disabled,placeholder:"请输入联系电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"具体位置描述 :",prop:"locationDescription"}},[a("el-input",{attrs:{disabled:e.disabled,type:"textarea",placeholder:"请输入具体位置描述"},model:{value:e.form.locationDescription,callback:function(t){e.$set(e.form,"locationDescription",t)},expression:"form.locationDescription"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"详细描述 :",prop:"detailedDescription"}},[a("el-input",{attrs:{disabled:e.disabled,type:"textarea",placeholder:"请输入详细描述"},model:{value:e.form.detailedDescription,callback:function(t){e.$set(e.form,"detailedDescription",t)},expression:"form.detailedDescription"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",disabled:e.disabled},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],n=(a("ac1f"),a("00b4"),a("d9e2"),a("d81d"),a("14d9"),a("a15b"),a("b64b"),a("e9c4"),a("4de4"),a("d3b7"),a("b775"));function l(e){return Object(n["a"])({url:"/firecontrol-key-buildings/page",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/firecontrol-key-buildings/save",method:"post",data:e})}function s(e){return Object(n["a"])({url:"/firecontrol-key-buildings/update",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/firecontrol-area/list",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/firecontrol-key-buildings/deleteById",method:"post",data:e})}var u=a("90c5"),m={name:"HiddenDangerRecord",dicts:["fault_type","handle_type","emergency_status"],data:function(){var e=function(e,t,a){var r=/^1[345789]\d{9}$/;r.test(t)?a():a(new Error("请输入11位手机号"))};return{loading:!0,showSearch:!0,total:0,roleList:[],title:"",open:!1,queryParams:{current:1,size:10,areaId:void 0,contact:void 0,name:void 0},uploadFile:"api/file/uploadFile",fileList:[],form:{},dateRange:[],originArr:[],rules:{name:[{required:!0,message:"请输入重点建筑名称",trigger:"blur"}],detailedDescription:[{required:!0,message:"请输入所属企业名称",trigger:"blur"}],areaId:[{required:!0,message:"请选择所属区域",trigger:"blur"}],contact:[{required:!0,message:"请输入企业联系人",trigger:"blur"}],phone:[{type:"number",validator:e,message:"请输入正确的手机号",trigger:"change",required:!0}]},disabled:!1}},created:function(){this.getList(),this.getListpage()},methods:{getListpage:function(){var e=this;c().then((function(t){200==t.code&&(console.log(t,"res"),e.originArr=t.data)}))},getList:function(){var e=this;this.loading=!0,console.log(this.queryParams),l(this.queryParams).then((function(t){e.roleList=t.data.records,e.total=t.data.total,e.loading=!1,console.log(t)}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,dangerType:void 0,griddingId:void 0,location:void 0,description:void 0,attachment:void 0},this.resetForm("form")},handleQuery:function(){this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.queryParams.current=1,this.getList()},resetQuery:function(){this.dateRange=[],this.queryParams.startTime=void 0,this.queryParams.endTime=void 0,this.resetForm("queryForm"),this.handleQuery()},handleRemove:function(e,t){console.log(e,t);var a=[];t.map((function(e){a.push(e.url)})),this.form.attachment=a.join(",")},handlePreview:function(e,t,a){if(console.log(e,t,a),0==t.size)return this.$modal.msgWarning("当前文件大小不符合规范"),!0;var r=[];a.map((function(e){e.response?r.push(JSON.parse(u["a"].decryptAES(e.response,u["a"].aesKey))):r.push(e.url)})),this.form.attachment=r.join(",")},handleAdd:function(){this.disabled=!1,this.reset(),this.open=!0,this.title="新增危险源",this.fileList=[]},handleUpdate:function(e,t){var a=this;if("look"==t?(this.disabled=!0,this.title="查看危险源"):(this.disabled=!1,this.title="编辑危险源"),this.reset(),this.open=!0,this.form=JSON.parse(JSON.stringify(e)),this.fileList=[],this.form.amount=null,null!=e.attachment){var r=[];r=JSON.parse(JSON.stringify(this.form.attachment.split(","))),r.map((function(e,t){var r=e.split("/");a.fileList.push({name:Date.now()+"_"+r[r.length-1],url:e})}))}},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null==e.form.areaId&&void 0==e.form.areaId&&""==e.form.areaId||(e.form.areaName=e.originArr.filter((function(t){return t.areaId==e.form.areaId}))[0].areaName),console.log(e.form),void 0!=e.form.id?s(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):o(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return d({id:e.id})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},p=m,f=(a("f04a"),a("2877")),h=Object(f["a"])(p,r,i,!1,null,"258b6ee5",null);t["default"]=h.exports},f04a:function(e,t,a){"use strict";a("3ba8")}}]);