(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-2e348616"],{"0c4a":function(e,t,a){},"2e39":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-card",{staticClass:"left-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e._v("事件类型")]),a("el-input",{staticClass:"search",attrs:{maxlength:"20",placeholder:"搜索"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),a("el-tree",{ref:"tree",attrs:{"current-node-key":e.currentKey,data:e.treeData,props:e.defaultProps,"filter-node-method":e.filterNode},on:{"node-click":e.handleNodeClick}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e._v("事件标签")]),e._l(e.tagList,(function(t){return a("el-tag",{key:t.id,staticClass:"tag-item",attrs:{closable:"","disable-transitions":!1},on:{close:function(a){return e.clearTag(t)}}},[e._v(e._s(t.label))])})),a("div",{staticClass:"tag-add-box"},[a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",placeholder:"请输入"},model:{value:e.iptTemp,callback:function(t){e.iptTemp=t},expression:"iptTemp"}}),a("el-button",{attrs:{round:"",type:"primary"},on:{click:e.addTag}},[e._v("添加")])],1)],2)],1)],1)],1)},i=[],r=(a("4de4"),a("d3b7"),a("45c8")),s={data:function(){return{currentKey:"",filterText:"",treeData:[],defaultProps:{children:"children",label:"nodeName"},tagList:[],iptTemp:"",treeItemId:""}},watch:{filterText:function(e){this.$refs.tree.filter(e)}},created:function(){this.getTreeData()},methods:{filterNode:function(e,t){return!e||-1!==t.nodeName.indexOf(e)},addTag:function(){var e=this;""!==this.treeItemId?this.iptTemp?Object(r["c"])(this.treeItemId,this.iptTemp).then((function(t){200==(null===t||void 0===t?void 0:t.code)&&(e.$message({type:"success",message:"添加成功"}),e.getLabels()),e.iptTemp=""})):this.$message({type:"info",message:"请输入标签内容"}):this.$message({type:"info",message:"请先选择左侧事件类型"})},getLabels:function(){var e=this;Object(r["a"])(this.treeItemId).then((function(t){200==(null===t||void 0===t?void 0:t.code)&&(e.tagList=t.data,console.log(e.tagList))}))},clearTag:function(e){var t=this;this.$modal.confirm("是否确认删除当前标签").then((function(){return Object(r["d"])(e.id)})).then((function(){t.getLabels(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleNodeClick:function(e){this.tagList=[],e.children&&e.children.length>0?this.treeItemId="":(this.treeItemId=e.id,this.getLabels())},getTreeData:function(){var e=this;Object(r["b"])().then((function(t){200==(null===t||void 0===t?void 0:t.code)&&(e.treeData=t.data)}))}}},c=s,l=(a("39ca"),a("2877")),o=Object(l["a"])(c,n,i,!1,null,"68e71b6b",null);t["default"]=o.exports},"39ca":function(e,t,a){"use strict";a("0c4a")},"45c8":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return r})),a.d(t,"c",(function(){return s})),a.d(t,"d",(function(){return c}));var n=a("b775");a("c38a");function i(){return Object(n["a"])({url:"/emergency-event-type/tree",method:"get"})}function r(e){return Object(n["a"])({url:"/emergency-event-type-label/selectById",method:"get",params:{eventTypeId:e}})}function s(e,t){return Object(n["a"])({url:"/emergency-event-type-label/save",method:"post",data:{eventTypeId:e,label:t}})}function c(e){return Object(n["a"])({url:"/emergency-event-type-label/deleteById",method:"post",data:{id:e}})}}}]);