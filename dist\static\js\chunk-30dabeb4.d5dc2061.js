(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-30dabeb4"],{"036f":function(e,s,t){"use strict";t.r(s);var i=function(){var e=this,s=e.$createElement,t=e._self._c||s;return t("div",{staticStyle:{width:"auto",height:"auto"},attrs:{id:"jessibuca"}},[t("div",{ref:"container",staticStyle:{width:"100%",height:"10rem","background-color":"#000"},attrs:{id:"container"},on:{dblclick:e.fullscreenSwich}})])},n=[],o={name:"jessibuca",data:function(){return{jessibuca:null,playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:"",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!0}},props:["videoUrl","error","hasAudio","height"],mounted:function(){var e=this;window.onerror=function(e){};var s=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){var t=document.getElementById("container");t.style.height=9/16*t.clientWidth+"px","undefined"==typeof e.videoUrl&&(e.videoUrl=s),console.log("初始化时的地址为: "+e.videoUrl),e.play(e.videoUrl)}))},created:function(){console.log(this.videoUrl)},methods:{create:function(){console.log(this.$refs.container),console.log("hasAudio  "+this.hasAudio),this.jessibuca=new JessibucaPro({container:"#container",decoder:"./decoder-pro.js",videoBuffer:.2,isResize:!1,text:"",loadingText:"加载中",debug:!0,isMulti:!0,useMSE:!0,useSIMD:!0,useWCS:!0,hasAudio:!1,useVideoRender:!0,controlAutoHide:!0,showBandwidth:!0,showPerformance:!1,operateBtns:{fullscreen:!0,screenshot:!0,play:!0,audio:!0},watermarkConfig:{text:{content:"摄像头"},right:10,top:10}}),this.jessibuca.on("fullscreen",(function(e){console.log("is fullscreen",index,e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var s=this;console.log(e),this.jessibuca&&this.destroy(),this.create(),this.jessibuca.on("play",(function(){s.playing=!0,s.loaded=!0,s.quieting=s.jessibuca.quieting})),this.jessibuca.hasLoaded()?this.jessibuca.play(e):this.jessibuca.on("load",(function(){console.log("load 播放"),s.jessibuca.play(e)}))},pause:function(){this.jessibuca&&this.jessibuca.pause(),this.playing=!1,this.err="",this.performance=""},destroy:function(){this.jessibuca&&this.jessibuca.destroy(),this.jessibuca=null,this.playing=!1,this.err="",this.performance=""},eventcallbacK:function(e,s){console.log("player 事件回调"),console.log(e),console.log(s)},fullscreenSwich:function(){var e=this.isFullscreen();this.jessibuca.setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}},destroyed:function(){this.jessibuca&&this.jessibuca.destroy(),this.playing=!1,this.loaded=!1,this.performance=""}},c=o,l=(t("6bc5"),t("2877")),u=Object(l["a"])(c,i,n,!1,null,null,null);s["default"]=u.exports},"20f6":function(e,s,t){},"6bc5":function(e,s,t){"use strict";t("20f6")}}]);