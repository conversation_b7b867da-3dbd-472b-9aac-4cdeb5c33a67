(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-318fa96d"],{5866:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:24}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("数据筛选")])]),a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticStyle:{display:"flex","justify-content":"space-between"},attrs:{model:e.queryParams,size:"small",inline:!0,"label-position":"left"}},[a("div",[a("el-form-item",{attrs:{label:"技术规范名称"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入技术规范名称",clearable:"",maxlength:"20"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.technicalSpecificationsName,callback:function(t){e.$set(e.queryParams,"technicalSpecificationsName",e._n(t))},expression:"queryParams.technicalSpecificationsName"}})],1),a("el-form-item",{attrs:{label:"技术规范大类"}},[a("el-select",{staticStyle:{width:"245px"},attrs:{placeholder:"请选择技术规范大类"},on:{change:e.largeCategoryChange},model:{value:e.queryParams.parentId,callback:function(t){e.$set(e.queryParams,"parentId",t)},expression:"queryParams.parentId"}},e._l(e.largeCategoryData,(function(e){return a("el-option",{key:e.id,attrs:{label:e.nodeName,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"技术规范小类"}},[a("el-select",{staticStyle:{width:"245px"},attrs:{placeholder:"请选择技术规范小类"},model:{value:e.queryParams.technicalSpecificationsTypeId,callback:function(t){e.$set(e.queryParams,"technicalSpecificationsTypeId",t)},expression:"queryParams.technicalSpecificationsTypeId"}},e._l(e.subclassData,(function(e){return a("el-option",{key:e.id,attrs:{label:e.className,value:e.id}})})),1)],1)],1),a("div",{staticStyle:{"min-width":"200px"}},[a("el-form-item",[a("el-button",{staticClass:"resetQueryStyle",attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{staticClass:"resetQueryStyle",attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)])],1),a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("技术规范列表")]),a("el-button",{staticClass:"queryBtnT",attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增技术规范")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.shelter}},[a("el-table-column",{attrs:{label:"序号",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),e._v(" :row-style=\"{height: '48px'}\" /> "),a("el-table-column",{attrs:{label:"技术规范名称",align:"center",prop:"technicalSpecificationsName","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"技术规范大类",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getNameById(t.row))+" ")]}}])}),a("el-table-column",{attrs:{label:"技术规范小类",align:"center",prop:"name"}}),a("el-table-column",{attrs:{label:"发布时间",align:"center",prop:"publishTime","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"发布单位",align:"center",prop:"publishingUnit","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"更新日期",align:"center",prop:"updateTime","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"220","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",icon:"el-icon-view"},on:{click:function(a){return e.handleLook(t.row)}}},[e._v("查看")]),a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")]),a("el-button",{attrs:{type:"text",icon:"el-icon-download"},on:{click:function(a){return e.handledownload(t.row)}}},[e._v("下载")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}})],1)],1)],1),a("el-dialog",{attrs:{title:e.title,visible:e.abilityOpen,width:"960px","append-to-body":""},on:{"update:visible":function(t){e.abilityOpen=t}}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.abilityForm,rules:e.abilityRules,"label-width":"150px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"技术规范名称 :",prop:"technicalSpecificationsName"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入技术规范名称",maxlength:"20",disabled:e.disabled},model:{value:e.abilityForm.technicalSpecificationsName,callback:function(t){e.$set(e.abilityForm,"technicalSpecificationsName",t)},expression:"abilityForm.technicalSpecificationsName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"发布单位 :",prop:"publishingUnit"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入发布单位",maxlength:"20",disabled:e.disabled},model:{value:e.abilityForm.publishingUnit,callback:function(t){e.$set(e.abilityForm,"publishingUnit",t)},expression:"abilityForm.publishingUnit"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"技术规范大类 :",prop:"parentId"}},[a("el-select",{staticStyle:{width:"245px"},attrs:{disabled:e.disabled,placeholder:"请选择技术规范大类"},on:{change:e.largeCategoryChange},model:{value:e.abilityForm.parentId,callback:function(t){e.$set(e.abilityForm,"parentId",t)},expression:"abilityForm.parentId"}},e._l(e.largeCategoryData,(function(e){return a("el-option",{key:e.id,attrs:{label:e.nodeName,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"技术规范小类 :",prop:"technicalSpecificationsTypeId"}},[a("el-select",{staticStyle:{width:"245px"},attrs:{disabled:e.disabled,placeholder:"请选择技术规范小类"},model:{value:e.abilityForm.technicalSpecificationsTypeId,callback:function(t){e.$set(e.abilityForm,"technicalSpecificationsTypeId",t)},expression:"abilityForm.technicalSpecificationsTypeId"}},e._l(e.subclassData,(function(e){return a("el-option",{key:e.id,attrs:{label:e.className,value:e.id}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"发布时间",prop:"publishTime"}},[a("el-date-picker",{staticStyle:{width:"245px"},attrs:{type:"date",placeholder:"选择日期",disabled:e.disabled,format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.abilityForm.publishTime,callback:function(t){e.$set(e.abilityForm,"publishTime",t)},expression:"abilityForm.publishTime"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"标准号 :",prop:"standardNumber"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入标准号",maxlength:"20",disabled:e.disabled},model:{value:e.abilityForm.standardNumber,callback:function(t){e.$set(e.abilityForm,"standardNumber",t)},expression:"abilityForm.standardNumber"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"详细内容 :",prop:"details"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入详细内容",type:"textarea",disabled:e.disabled,maxlength:"200"},model:{value:e.abilityForm.details,callback:function(t){e.$set(e.abilityForm,"details",t)},expression:"abilityForm.details"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"附件 :"}},[a("el-upload",{staticClass:"upload-demo",attrs:{action:e.uploadImgUrl,"on-success":e.handleAvatarSuccess,"file-list":e.fileList,disabled:e.disabled,"on-remove":e.handleRemove,limit:1,headers:e.headers,"on-error":e.handleError,"on-exceed":e.handleExceed,"before-upload":e.beforeAvatarUpload},model:{value:e.abilityForm.fileUrl,callback:function(t){e.$set(e.abilityForm,"fileUrl",t)},expression:"abilityForm.fileUrl"}},[a("el-button",{attrs:{size:"small",type:"primary",disabled:e.disabled}},[e._v("点击上传")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB")])])],1)],1)],1)],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.disabled?e._e():a("el-button",{staticClass:"popupButton",attrs:{type:"primary"},on:{click:function(t){return e.confirm("abilityForm")}}},[e._v("确 定")]),a("el-button",{staticClass:"popupButton",on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],r=a("c7eb"),s=a("1da1"),n=(a("b64b"),a("e9c4"),a("14d9"),a("d81d"),a("b0c0"),a("4de4"),a("d3b7"),a("5f87"),a("99af"),a("b775"));a("c38a");function o(e){return Object(n["a"])({url:"/emergency-technical-specifications/page",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/emergency-technical-specifications/save",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/file/downloadFile?bucket=".concat(e[1],"&path=").concat(e[2],"&fileName=").concat(e[3]),method:"get",responseType:"blob"})}function p(e){return Object(n["a"])({url:"/emergency-technical-specifications/deleteById",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/emergency-type/tree",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/emergency-type/list",method:"get",params:e})}var h={name:"EmergencySupplies",dicts:["material_type"],data:function(){return{container:{},lngAndLat:"",loading:!1,showSearch:!0,total:0,shelter:null,abilityOpen:!1,title:"新增技术规范",text:void 0,imgUrl:"".concat(a("8708")),queryParams:{current:1,size:10,technicalSpecificationsTypeId:void 0,parentId:void 0,technicalSpecificationsName:void 0},frequency:0,abilityForm:{},disabled:!1,abilityRules:{technicalSpecificationsName:[{required:!0,message:"技术规范名称不能为空",trigger:"blur"}],parentId:[{required:!0,message:"技术规范大类不能为空",trigger:"blur"}],technicalSpecificationsTypeId:[{required:!0,message:"技术规范小类不能为空",trigger:"blur"}]},headers:{Authorization:localStorage.getItem("token")},uploadImgUrl:"/emergency-v2/file/uploadFile",fileList:[],largeCategoryData:[],subclassData:[]}},watch:{},created:function(){this.getList()},mounted:function(){this.largeCategory()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){console.log(t),null!=t.data&&(e.shelter=t.data.records,e.total=t.data.total),e.loading=!1}))},handleLook:function(e){this.reset(),this.abilityOpen=!0,this.abilityForm=JSON.parse(JSON.stringify(e)),this.abilityForm.parentId=this.abilityForm.parentId+"",this.largeCategoryChange(e.parentId),this.title="查看技术规范",this.disabled=!0,this.lngAndLat=e.longitude+","+e.latitude,console.log(this.abilityForm);var t=[];""!=e.fileUrl&&(t=e.fileUrl.split("/"),this.fileList.push({name:t[t.length-1],url:e.fileUrl}))},handledownload:function(e){var t=this,a=this;this.$modal.confirm("是否确认下载附件").then((function(){var t=e.fileUrl.split(",");console.log(t),t.map((function(e){var t=e.split("/");d(t).then(function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(i){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a.handledownloadGet(t,i);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}))})).then((function(){t.getList(),t.$modal.msgSuccess("下载成功")})).catch((function(e){}))},handleAdd:function(){this.reset(),this.abilityOpen=!0,this.title="新增技术规范",this.disabled=!1},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return p({id:e.id})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(e){}))},cancel:function(){this.abilityOpen=!1,this.reset()},confirm:function(e){var t=this;this.$refs[e].validate((function(e){e&&(void 0!=t.abilityForm.id?(void 0)(t.abilityForm).then((function(e){200==e.code&&(t.$modal.msgSuccess("编辑成功"),t.abilityOpen=!1,t.getList())})):c(t.abilityForm).then((function(e){200==e.code&&(t.$modal.msgSuccess("新增成功"),t.abilityOpen=!1,t.getList())})))}))},handleQuery:function(){this.queryParams.current=1,this.getList()},reset:function(){this.abilityForm={id:void 0,refugeName:void 0,refugeArea:void 0,holdsNumber:void 0,liabilityUser:void 0,phone:void 0,remark:void 0},this.fileList=[],this.lngAndLat="",this.resetForm("abilityForm")},resetQuery:function(){this.queryParams={current:1,size:10,refugeName:void 0,liabilityUser:void 0,phone:void 0},this.resetForm("queryForm"),this.handleQuery()},handleAvatarSuccess:function(e,t,a){console.log(e,t,a),this.abilityForm.fileUrl=e,this.abilityForm.fileName=t.name},handleRemove:function(e,t){console.log(e,t,"sssssss"),this.abilityForm.fileUrl="",this.abilityForm.fileName=""},handleError:function(e,t,a){console.log(JSON.parse(e),t,a,"sssssss"),this.$modal.msgSuccess(JSON.parse(e).msg)},handleExceed:function(){this.$modal.msgSuccess("请不要上传多个文件")},beforeAvatarUpload:function(e){console.log(e);var t=["jpeg","jpg","png","gif","bmp","tiff","webp","svg","mp4","avi","mkv","mov","wmv","flv","webm","mpeg","mp3","wav","aac","flac","ogg","wma","pdf","word","excel","txt","doc","docx","xlsx","xls","pptx","ppt"],a=e.name.split("."),i=e.size/1024/1024<100,l=-1==t.indexOf(a[1]);return console.log(l),l&&this.$message.error("仅支持jpeg|jpg|png|gif|bmp|tiff|webp|svg|mp4|avi|mkv|mov|wmv|flv|webm|mpeg|mp3|wav|aac|flac|ogg|wma|pdf|word|excel|txt|doc|docx|xlsx|xls|pptx|ppt| 格式!"),i||this.$message.error("上传附件大小不能超过 100MB!"),!l&&i},largeCategory:function(){var e=this;u({classType:2}).then((function(t){200==t.code&&(e.largeCategoryData=t.data)}))},largeCategoryChange:function(e){var t=this;m({parentId:e,classType:2}).then((function(e){console.log(e,"小类"),200==e.code&&(t.subclassData=e.data)}))},getNameById:function(e){if(void 0!=e.parentId&&""!=e.parentId&&null!=e.parentId)return this.largeCategoryData.filter((function(t){return t.id==e.parentId}))[0].nodeName}}},f=h,b=(a("eae9"),a("2877")),y=Object(b["a"])(f,i,l,!1,null,"dc9d514c",null);t["default"]=y.exports},"83fb":function(e,t,a){},eae9:function(e,t,a){"use strict";a("83fb")}}]);