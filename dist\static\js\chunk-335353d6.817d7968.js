(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-335353d6"],{"08d6":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"物料种类",prop:"materialType"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"物料种类",clearable:""},model:{value:e.queryParams.materialType,callback:function(t){e.$set(e.queryParams,"materialType",t)},expression:"queryParams.materialType"}},e._l(e.dict.type.material_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"物料名称",prop:"materialName"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入设备名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.materialName,callback:function(t){e.$set(e.queryParams,"materialName",t)},expression:"queryParams.materialName"}})],1),a("el-form-item",{attrs:{label:"所属区域",prop:"griddingId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"安装区域"},model:{value:e.queryParams.griddingId,callback:function(t){e.$set(e.queryParams,"griddingId",t)},expression:"queryParams.griddingId"}},e._l(e.areaArr,(function(e){return a("el-option",{key:e.griddingId,attrs:{label:e.griddingName,value:e.griddingId}})})),1)],1),a("el-form-item",{attrs:{label:"详细位置",prop:"location"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入详细位置",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.location,callback:function(t){e.$set(e.queryParams,"location",t)},expression:"queryParams.location"}})],1),a("el-form-item",{attrs:{label:"更新时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roleList}},[a("el-table-column",{attrs:{label:"ID","show-overflow-tooltip":!0,prop:"griddingId",align:"center"}}),a("el-table-column",{attrs:{label:"所属区域",prop:"griddingName",align:"center"}}),a("el-table-column",{attrs:{label:"详细位置","show-overflow-tooltip":!0,prop:"location",align:"center"}}),a("el-table-column",{attrs:{label:"物种科类",prop:"materialType",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.material_type,value:t.row.materialType,type:1}})]}}])}),a("el-table-column",{attrs:{label:"储量",prop:"amount",align:"center"}}),a("el-table-column",{attrs:{label:"剩余量",prop:"remain",align:"center"}}),a("el-table-column",{attrs:{label:"联系电话",prop:"phone",align:"center"}}),a("el-table-column",{attrs:{label:"责任人",prop:"principal",align:"center"}}),a("el-table-column",{attrs:{label:"更新日期",prop:"createTime",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"所属区域",prop:"griddingId"}},[a("el-select",{ref:"selectDept",staticStyle:{width:"100%"},attrs:{placeholder:"请选择所属区域"},on:{change:function(t){return e.getAreaName()}},model:{value:e.form.griddingId,callback:function(t){e.$set(e.form,"griddingId",t)},expression:"form.griddingId"}},e._l(e.areaArr,(function(e){return a("el-option",{key:e.griddingId,attrs:{label:e.griddingName,value:e.griddingId}})})),1)],1),a("el-form-item",{attrs:{label:"详细位置",prop:"location"}},[a("el-input",{attrs:{placeholder:"请输入详细位置"},model:{value:e.form.location,callback:function(t){e.$set(e.form,"location",t)},expression:"form.location"}})],1),a("el-form-item",{attrs:{label:"物种科类",prop:"materialType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"物料种类",clearable:""},model:{value:e.form.materialType,callback:function(t){e.$set(e.form,"materialType",t)},expression:"form.materialType"}},e._l(e.dict.type.material_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"储量",prop:"amount"}},[a("el-input",{attrs:{placeholder:"请输入储量"},model:{value:e.form.amount,callback:function(t){e.$set(e.form,"amount",e._n(t))},expression:"form.amount"}})],1),a("el-form-item",{attrs:{label:"剩余量",prop:"remain"}},[a("el-input",{attrs:{placeholder:"请输入剩余量"},model:{value:e.form.remain,callback:function(t){e.$set(e.form,"remain",e._n(t))},expression:"form.remain"}})],1),a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1),a("el-form-item",{attrs:{label:"责任人",prop:"principal"}},[a("el-input",{attrs:{placeholder:"请输入责任人"},model:{value:e.form.principal,callback:function(t){e.$set(e.form,"principal",e._n(t))},expression:"form.principal"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],l=(a("d9e2"),a("ac1f"),a("00b4"),a("7db0"),a("d3b7"),a("b775"));function n(e){return Object(l["a"])({url:"/firecontrol-material/page",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/firecontrol-area/page",method:"get",params:e})}function s(e){return Object(l["a"])({url:"/firecontrol-material/save",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/firecontrol-material/update",method:"post",data:e})}var c={name:"MaterialData",dicts:["material_type"],data:function(){var e=function(e,t,a){""===t?a(new Error("请输入绑定的手机号码")):/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(t)?a():a(new Error("请输入正确的手机号码"))};return{loading:!0,showSearch:!0,total:0,roleList:[],queryParams:{current:1,size:10,materialType:void 0,materialName:void 0,griddingId:void 0,location:void 0,startTime:void 0,endTime:void 0},dateRange:[],areaArr:[],title:"",open:!1,form:{},rules:{griddingId:[{required:!0,message:"请选择所属区域",trigger:"change"}],principal:[{required:!0,message:"请选择联系人",trigger:"blur"}],phone:[{validator:e,required:!0,trigger:"blur"}],location:[{required:!0,message:"请输入详细位置",trigger:"blur"}],materialType:[{required:!0,message:"请选择物种科类",trigger:"change"}],amount:[{required:!0,message:"请输入储量",trigger:"blur"}],remain:[{required:!0,message:"请输入剩余量",trigger:"blur"}]}}},created:function(){this.getList(),this.getAreaPage()},methods:{getAreaPage:function(){var e=this;o({current:1,size:1e3}).then((function(t){e.areaArr=t.data.records}))},getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){e.roleList=t.data.records,e.total=t.data.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:void 0,griddingId:void 0,griddingName:void 0,principal:void 0,materialType:void 0,amount:void 0,remain:void 0,phone:void 0},this.resetForm("form")},handleQuery:function(){this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.queryParams.current=1,this.getList()},getAreaName:function(){var e=this,t=this.areaArr.find((function(t){return t.griddingId==e.form.griddingId}));this.form.griddingName=t.griddingName},handleAdd:function(){this.reset(),this.open=!0,this.title="新增物料数据"},handleUpdate:function(e){this.reset(),this.open=!0,this.title="修改物料数据",this.form=e},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.id?d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):s(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return d({id:e.id,isDeleted:1})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},m=c,u=(a("0ada"),a("2877")),p=Object(u["a"])(m,r,i,!1,null,"72da4012",null);t["default"]=p.exports},"0ada":function(e,t,a){"use strict";a("3eedd")},"3eedd":function(e,t,a){}}]);