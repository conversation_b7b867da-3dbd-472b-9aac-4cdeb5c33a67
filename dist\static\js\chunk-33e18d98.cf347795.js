(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-33e18d98"],{"3cd0":function(e,t,a){"use strict";a("e511")},"7add":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"body"},[a("el-card",{staticClass:"top_box"},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("数据筛选")])]),a("div",{staticClass:"center"},[a("div",{staticClass:"scarchIpt"},[a("el-form",{attrs:{inline:!0,model:e.formInline}},[a("el-form-item",{attrs:{label:"排班名称："}},[a("el-input",{attrs:{placeholder:"请输入排班名称",maxlength:20},model:{value:e.formInline.className,callback:function(t){e.$set(e.formInline,"className",t)},expression:"formInline.className"}})],1),a("el-form-item",{attrs:{label:"班次名称："}},[a("el-select",{attrs:{placeholder:"请选择班次类型"},model:{value:e.formInline.classType,callback:function(t){e.$set(e.formInline,"classType",t)},expression:"formInline.classType"}},e._l(e.bctypeArr,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"排班班组："}},[a("el-select",{attrs:{placeholder:"请选择排班班组"},model:{value:e.formInline.GroupName,callback:function(t){e.$set(e.formInline,"GroupName",t)},expression:"formInline.GroupName"}},e._l(e.serviceArr,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1),a("div",{staticClass:"tabButton"},[a("el-button",{staticClass:"searchBtn",staticStyle:{"font-size":"13px"},attrs:{icon:"el-icon-search",type:"primary",size:"mini"},on:{click:e.findList}},[e._v("搜索")]),a("el-button",{staticClass:"searchBtn",staticStyle:{"font-size":"13px"},attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetList}},[e._v("重置")])],1)])]),a("el-card",{staticClass:"tab_card"},[a("div",{attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"tab_card_header"},[a("span",[e._v(" 排班管理展示列表 ")]),a("div",{staticClass:"btns"},[a("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-plus",type:"primary",size:"mini"},on:{click:e.openDrawerBtn}},[e._v("新建排班 ")])],1)])]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"highlight-current-row":!0}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center","header-align":"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"name",label:"排班名称",width:"200",align:"left","header-align":"left","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"memberTotal",label:"排班人数",align:"center","header-align":"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"arrangementsName",label:"排班名次",align:"left","header-align":"left","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"serviceGroupName",label:"排班班组",align:"left","header-align":"left","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"scheduleWayName",label:"排班方式",align:"center","header-align":"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"操作",align:"center","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("span",{staticClass:"caozuo pointer",on:{click:function(a){return e.editData(t.$index,t.row)}}},[e._v("排班")]),a("span",{staticClass:"delete_btn pointer",on:{click:function(a){return e.deleteList(t.$index,t.row)}}},[a("i",{staticClass:"el-icon-delete"}),e._v("删除")])])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{limit:e.pageSize,page:e.pageNum,total:e.total},on:{"update:limit":function(t){e.pageSize=t},"update:page":function(t){e.pageNum=t},pagination:e.pbglPageLists}})],1)],1),a("el-dialog",{attrs:{title:"新建排班",visible:e.openDrawer,"before-close":e.handleClose,"append-to-body":""},on:{"update:visible":function(t){e.openDrawer=t}}},[a("el-form",{ref:"siteList",attrs:{"label-position":e.labelPosition,"label-width":"100px",model:e.siteList,rules:e.rules}},[a("el-form-item",{attrs:{label:"排班名称",prop:"addClassName"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:"",maxlength:20},model:{value:e.siteList.addClassName,callback:function(t){e.$set(e.siteList,"addClassName",t)},expression:"siteList.addClassName"}})],1),a("el-form-item",{attrs:{label:"班次名称",prop:"addClassType"}},[a("el-select",{attrs:{placeholder:"请选择班次",filterable:"",multiple:"","collapse-tags":""},model:{value:e.siteList.addClassType,callback:function(t){e.$set(e.siteList,"addClassType",t)},expression:"siteList.addClassType"}},e._l(e.bctypeArr,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"排班班组",prop:"addClassSer"}},[a("el-select",{attrs:{placeholder:"请选择服务组"},model:{value:e.siteList.addClassSer,callback:function(t){e.$set(e.siteList,"addClassSer",t)},expression:"siteList.addClassSer"}},e._l(e.serviceArr,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"排班方式"}},[a("el-radio-group",{model:{value:e.siteList.addpbType,callback:function(t){e.$set(e.siteList,"addpbType",t)},expression:"siteList.addpbType"}},[a("el-radio",{attrs:{label:1}},[e._v("统一排班")]),a("el-radio",{attrs:{label:2}},[e._v("轮换排班(考勤组不少于两人)")])],1)],1)],1),a("div",{staticClass:"up-btn",attrs:{slot:"footer"},slot:"footer"},[a("div",{staticClass:"up-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitList("siteList")}}},[e._v("确定")]),a("el-button",{on:{click:e.closeDialog}},[e._v("取消")])],1)])],1)],1)},s=[],r=(a("a15b"),a("14d9"),a("dc01")),i={name:"",props:{},data:function(){return{rules:{addClassName:[{required:!0,message:"请输入名称",trigger:"blur"}],addClassType:[{required:!0,message:"请选择班次",trigger:"blur"}],addClassSer:[{required:!0,message:"请选择服务组",trigger:"change"}]},labelPosition:"right",name:"环境数据",formInline:{className:"",classType:"",GroupName:""},loading:!1,tableData:[],pageSize:10,pageNum:1,isSearch:!1,total:0,targetList:[{name:"轻度",degree:"102001",startNum:"",startType:"1",endNum:"",endType:"1"},{name:"中度",degree:"102002",startNum:"",startType:"1",endNum:"",endType:"1"},{name:"重度",degree:"102003",startNum:"",startType:"1",endNum:"",endType:"1"}],serviceArr:[],bctypeArr:[],openDrawer:!1,siteList:{addClassName:"",addClassType:"",addClassSer:"",addpbType:1}}},created:function(){this.findLists(),this.bcfindLists(),this.pbglPageLists(),this.getDict(),this.getTeamsGroups()},mounted:function(){},watch:{},filters:{},components:{},computed:{dictList:function(){return this.$store.state.dict}},methods:{addpbs:function(){var e=this;this.loading=!0;var t={arrangementIds:this.siteList.addClassType.join(","),serviceGroupId:this.siteList.addClassSer,name:this.siteList.addClassName,scheduleWay:this.siteList.addpbType};console.log(t,"params"),Object(r["b"])(t).then((function(t){e.$message({message:"新增成功",type:"success"}),setTimeout((function(){e.loading=!1,e.openDrawer=!1,e.pbglPageLists()}),1e3)}))},submitList:function(e){var t=this;console.log(e),this.$refs[e].validate((function(e){if(console.log(e),!e)return console.log("error submit!!"),!1;t.addpbs()}))},openDrawerBtn:function(){this.isAdd=1,this.openDrawer=!0,this.siteList.addClassName="",this.siteList.addClassType="",this.siteList.addClassSer="",this.siteList.addpbType=1},goDetail:function(){this.$router.push({name:"schedulingmanaDetail",query:{}})},editData:function(e,t){console.log(t),this.$router.push({name:"schedulingmanaDetail",query:t})},onSubmit:function(){console.log("submit!")},handleClose:function(e){this.$confirm("确认关闭？").then((function(t){e()})).catch((function(e){}))},findLists:function(){var e=this,t={};Object(r["z"])(t).then((function(t){e.serviceArr=t.data}))},bcfindLists:function(){var e=this,t={};Object(r["h"])(t).then((function(t){e.bctypeArr=t.data}))},getTeamsGroups:function(){},pbglPageLists:function(){var e=this;this.loading=!0;var t={};t=this.isSearch?{serviceGroupId:this.formInline.GroupName,arrangementIdQuery:this.formInline.classType,name:this.formInline.className,pageNum:this.pageNum,pageSize:this.pageSize}:{pageNum:this.pageNum,pageSize:this.pageSize},Object(r["E"])(t).then((function(t){e.tableData=t.data.list,e.total=t.data.total,console.log(e.tableData,"this.res"),e.loading=!1}))},deleteList:function(e,t){var a=this;console.log(e,t),this.$confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.pbdeleteByIdss(t.id)}))},pbdeleteByIdss:function(e){var t=this;this.loading=!0;var a={ids:e};console.log(a,"params"),1==this.tableData.length&&1!==this.pageNum&&(this.pageNum=this.pageNum-1),Object(r["D"])(a).then((function(e){console.log(e),t.$message({message:"删除成功",type:"success"}),t.pbglPageLists(),t.loading=!1}))},getDict:function(){this.$store.dispatch("dict/setDict",{})},findList:function(){this.pageNum=1,this.isSearch=!0,this.pbglPageLists()},resetList:function(){this.tableData=[],this.pageNum=1,this.pageSize=10,this.formInline={},this.isSearch=!1,this.pbglPageLists()},closeDialog:function(){this.$refs.siteList.resetFields(),this.openDrawer=!1}}},l=i,o=(a("3cd0"),a("2877")),u=Object(o["a"])(l,n,s,!1,null,"806dda16",null);t["default"]=u.exports},dc01:function(e,t,a){"use strict";a.d(t,"y",(function(){return s})),a.d(t,"a",(function(){return r})),a.d(t,"C",(function(){return i})),a.d(t,"B",(function(){return l})),a.d(t,"c",(function(){return o})),a.d(t,"m",(function(){return u})),a.d(t,"s",(function(){return c})),a.d(t,"z",(function(){return d})),a.d(t,"t",(function(){return p})),a.d(t,"A",(function(){return m})),a.d(t,"J",(function(){return h})),a.d(t,"K",(function(){return f})),a.d(t,"G",(function(){return g})),a.d(t,"M",(function(){return b})),a.d(t,"E",(function(){return v})),a.d(t,"w",(function(){return y})),a.d(t,"h",(function(){return L})),a.d(t,"f",(function(){return j})),a.d(t,"e",(function(){return w})),a.d(t,"q",(function(){return O})),a.d(t,"b",(function(){return C})),a.d(t,"r",(function(){return N})),a.d(t,"F",(function(){return k})),a.d(t,"k",(function(){return T})),a.d(t,"H",(function(){return S})),a.d(t,"l",(function(){return I})),a.d(t,"g",(function(){return D})),a.d(t,"D",(function(){return x})),a.d(t,"o",(function(){return _})),a.d(t,"I",(function(){return $})),a.d(t,"i",(function(){return A})),a.d(t,"j",(function(){return z})),a.d(t,"n",(function(){return B})),a.d(t,"x",(function(){return G})),a.d(t,"d",(function(){return P})),a.d(t,"u",(function(){return q})),a.d(t,"v",(function(){return E})),a.d(t,"p",(function(){return J})),a.d(t,"L",(function(){return F}));var n=a("b775");function s(e){return Object(n["a"])({url:"/schedule/arrangement/pageList",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/schedule/arrangement/save",method:"post",data:e})}function i(e){return Object(n["a"])({url:"/schedule/work-adjustment/pageList",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/schedule/schedule/mySchedule",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/schedule/work-adjustment/save",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/save",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/schedule/service-group/findList",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/schedule/service-group/getSelectList",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/schedule/member/findList",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/schedule/schedule/getShowData",method:"get",params:e})}function h(e){return Object(n["a"])({url:"/schedule/work-adjustment/detail",method:"get",params:e})}function f(e){return Object(n["a"])({url:"/schedule/work-adjustment/getActInfo",method:"get",params:e})}function g(e){return Object(n["a"])({url:"/schedule/work-adjustment/submitAct",method:"post",params:e})}function b(e){return Object(n["a"])({url:"/schedule/work-adjustment/withdrawAct",method:"get",params:e})}function v(e){return Object(n["a"])({url:"/schedule/schedule/pageList",method:"get",params:e})}function y(e){return Object(n["a"])({url:"/schedule/arrangement/getArrangementTypeList",method:"get",params:e})}function L(e){return Object(n["a"])({url:"/schedule/arrangement/findList",method:"get",params:e})}function j(e){return Object(n["a"])({url:"/schedule/arrangement/detail",method:"get",params:e})}function w(e){return Object(n["a"])({url:"/schedule/schedule/autoSetSchedule",method:"post",data:e})}function O(e){return Object(n["a"])({url:"/schedule/arrangement/deleteByIds",method:"post",params:e})}function C(e){return Object(n["a"])({url:"/schedule/schedule/save",method:"post",data:e})}function N(e){return Object(n["a"])({url:"/schedule/schedule/exportExcel",method:"get",params:e,responseType:"blob"})}function k(e){return Object(n["a"])({url:"/schedule/member-work/saveEntitys",method:"post",data:e})}function T(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/pageList",method:"get",params:e})}function S(e){return Object(n["a"])({url:"/schedule/work-adjustment/update",method:"post",data:e})}function I(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/update",method:"post",data:e})}function D(e){return Object(n["a"])({url:"/schedule/arrangement/update",method:"post",data:e})}function x(e){return Object(n["a"])({url:"/schedule/schedule/deleteByIds",method:"post",params:e})}function _(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/submitAct",method:"post",params:e})}function $(e){return Object(n["a"])({url:"/schedule/work-adjustment/deleteByIds",method:"post",params:e})}function A(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/deleteByIds",method:"post",params:e})}function z(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/detail",method:"get",params:e})}function B(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/getActInfo",method:"get",params:e})}function G(e){return Object(n["a"])({url:"/schedule/sysQuery/getLoginMemberInfo",method:"get",params:e})}function P(e){return Object(n["a"])({url:"/schedule/work-adjustment/approvalOperation",method:"post",params:e})}function q(e){return Object(n["a"])({url:"/schedule/schedule/getAllShowData",method:"get",params:e})}function E(e){return Object(n["a"])({url:"/schedule/service-group/findList",method:"get"})}function J(e){return Object(n["a"])({url:"/schedule/arrangement/updateStatus",method:"post",params:e})}function F(e){return Object(n["a"])({url:"/schedule/service-group/updateRemind",method:"post",data:e})}},e511:function(e,t,a){}}]);