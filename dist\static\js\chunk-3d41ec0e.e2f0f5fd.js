(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-3d41ec0e"],{"279e":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"body flexs"},[s("el-card",[s("div",{staticClass:"body-left"},[s("div",{staticClass:"left-line"},[s("div",{staticClass:"left-lineTitle"},[t._v("员工信息")]),s("div",{staticClass:"left-lineBody"},[s("div",{staticClass:"left-txt"},[s("p",[t._v("员工姓名：")]),s("span",[t._v(t._s(t.detailList.applicantUserName))])]),s("div",{staticClass:"left-txt"},[s("p",[t._v("所属部门：")]),s("span",[t._v(t._s(t.detailList.applicantDeptName))])]),s("div",{staticClass:"left-txt"},[s("p",[t._v("所属班组：")]),s("span",[t._v(t._s(t.detailList.serviceGroupName))])])])]),s("div",{staticClass:"left-line"},[s("div",{staticClass:"left-lineTitle"},[t._v("调班信息")]),s("div",{staticClass:"left-lineBody"},[s("div",{staticClass:"left-txt"},[t._v("调班方式："),s("span",[t._v(t._s(t.detailList.typeName))])]),"104003"==t.detailList.type?s("div",{staticClass:"left-txt"},[s("p",[t._v("调班时间：")]),s("span",[t._v(t._s(t.detailList.workDatetime))])]):s("div",{staticClass:"left-txt"},[s("p",[t._v("调班时间：")]),s("span",[t._v(t._s(t.detailList.restDatetime))])]),"104001"==t.detailList.type?s("div",{staticClass:"left-txt maxW"},[s("p",[t._v("补班时间：")]),t._v(" "),s("span",[t._v(t._s(t.detailList.workDatetime))])]):t._e(),"104002"==t.detailList.type?s("div",{staticClass:"left-txt maxW"},[s("p",[t._v("代班人：")]),t._v(" "),s("span",[t._v(t._s(t.detailList.applicantUserName))])]):t._e(),"104003"==t.detailList.type?s("div",{staticClass:"left-txt"},[s("p",[t._v("代班人：")]),t._v(" "),s("span",[t._v(t._s(t.detailList.applicantUserName))])]):t._e(),s("div",{staticClass:"left-txt maxW"},[s("p",[t._v("调班原因：")]),t._v(" "),s("span",[t._v(t._s(t.detailList.reason))])])])]),s("div",{staticClass:"left-down"})]),t.processList.length>0?s("div",{staticClass:"body-right"},[s("div",{staticClass:"timeline"},t._l(t.processList,(function(e,a){return s("div",{key:a,staticClass:"time-item"},[s("div",{staticClass:"time-line"},[e.duration?s("svg",{staticClass:"icon",attrs:{t:"1686707594449",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"3557",width:"28",height:"28"}},[s("path",{attrs:{d:"M852.8 261.6l2.3 1.2-2.3-1.2z",fill:"#68BB8D","p-id":"3558"}}),s("path",{attrs:{d:"M514.2 99.9c-228.5 0-413.7 185.2-413.7 413.7s185.2 413.7 413.7 413.7S927.8 742 927.8 513.5 742.5 99.9 514.2 99.9zM712 430.7L553 587l-77 75.3c-0.3 0.4-0.7 0.8-1.2 1.3-0.6 0.6-1.3 1.2-2 1.8-4.8 4.6-11.1 7.1-17.8 7.1h-1.1c-7 0-13.5-2.6-18.3-7.4-0.7-0.6-1.3-1.2-1.9-1.7-0.4-0.4-0.7-0.7-1-1.1L304.3 533.9c-10.4-10.4-9.7-28 1.5-39.2 5.7-5.7 13.3-8.9 21-8.9 7 0 13.5 2.6 18.3 7.4l109.4 109.4 58.1-56.8 159.1-156.3c4.8-4.7 11.2-7.2 18.1-7.2 7.8 0 15.5 3.3 21.2 9.1 11 11.4 11.6 29 1 39.3z",fill:"#68BB8D","p-id":"3559"}})]):s("svg",{staticClass:"icon",attrs:{t:"1686707548580",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"2273",width:"28",height:"28"}},[s("path",{attrs:{d:"M511.563 65.144c-246.87 0-446.998 200.128-446.998 446.998S264.693 959.14 511.563 959.14 958.56 759.012 958.56 512.141 758.433 65.144 511.563 65.144z m178.524 582.517c11.716 11.716 11.716 30.71 0 42.426-5.857 5.858-13.535 8.787-21.213 8.787s-15.355-2.929-21.213-8.787L513.535 555.961 379.41 690.087c-5.858 5.858-13.536 8.787-21.213 8.787s-15.355-2.929-21.213-8.787c-11.715-11.716-11.715-30.71 0-42.426L471.11 513.535 336.983 379.41c-11.715-11.716-11.715-30.711 0-42.427 11.716-11.716 30.711-11.716 42.427 0l134.126 134.126 134.126-134.126c11.715-11.716 30.711-11.716 42.426 0 11.716 11.716 11.716 30.711 0 42.427L555.961 513.535l134.126 134.126z",fill:"#d81e06","p-id":"2274"}})]),a!==t.processList.length-1?s("el-divider"):t._e()],1),s("div",{staticClass:"time-con"},["startEvent"==e.activityType||"endEvent"==e.activityType?s("div",[s("div",{staticClass:"cards cardsm"},[s("span",{staticClass:"cards-left"},[t._v("时间：")]),s("span",[t._v(t._s(e.createTime))])]),s("div",{staticClass:"cards"},[s("span",{staticClass:"cards-left"},[t._v("流程状态：")]),s("span",[t._v(t._s("startEvent"==e.activityType?"开始":"结束"))])])]):s("div",[s("div",{staticClass:"cards cardsm"},[s("span",{staticClass:"cards-left"},[t._v("时间：")]),s("span",[t._v(t._s(e.createTime))])]),s("div",{staticClass:"cards cardsm"},[s("span",{staticClass:"cards-left"},[t._v("人员：")]),s("span",[t._v(t._s(e.assigneeName))])]),!e.commentList||e.commentList.length<2?s("div",[s("div",{staticClass:"cards cardsm"},[s("span",{staticClass:"cards-left"},[t._v("审批意见：")]),t._l(e.commentList,(function(e,a){return s("div",{key:a,staticStyle:{"text-align":"left"}},[s("span",[t._v(t._s(e.message))])])}))],2),s("div",{staticClass:"cards"},[s("span",{staticClass:"cards-left"},[t._v("流程状态：")]),t._l(e.commentList,(function(e,a){return s("div",{key:a,staticStyle:{"text-align":"left"}},[0==e.type?s("span",{staticClass:"cards-dian",staticStyle:{"background-color":"#FF0000"}}):t._e(),1==e.type?s("span",{staticClass:"cards-dian",staticStyle:{"background-color":"#0bbd87"}}):t._e(),2==e.type?s("span",{staticClass:"cards-dian",staticStyle:{"background-color":"#ffae00"}}):t._e(),3==e.type?s("span",{staticClass:"cards-dian",staticStyle:{"background-color":"#FF0000"}}):t._e(),4==e.type?s("span",{staticClass:"cards-dian",staticStyle:{"background-color":"#1890ff"}}):t._e(),5==e.type?s("span",{staticClass:"cards-dian",staticStyle:{"background-color":"#13ce66"}}):t._e(),0==e.type?s("span",[t._v("驳回")]):t._e(),1==e.type?s("span",[t._v("通过")]):t._e(),2==e.type?s("span",[t._v("退回")]):t._e(),3==e.type?s("span",[t._v("拒绝")]):t._e(),4==e.type?s("span",[t._v("委派")]):t._e(),5==e.type?s("span",[t._v("转办")]):t._e()])}))],2)]):t._e()]),e.commentList&&e.commentList.length>1?s("div",[s("el-timeline",{attrs:{reverse:t.reverse}},t._l(e.commentList,(function(e,a){return s("el-timeline-item",{key:a,attrs:{icon:e.icon,type:e.type,color:1==e.type?"#0bbd87":2==e.type?"#ffae00":3==e.type?"#FF0000":4==e.type?"#1890ff":5==e.type?"#13ce66":"",size:e.size,timestamp:e.time}},[t._v(" "+t._s(1==e.type?"通过":2==e.type?"退回":3==e.type?"拒绝":4==e.type?"委派":5==e.type?"转办":"")+" ："+t._s(e.message)+" ")])})),1)],1):t._e()])])})),0)]):t._e()])],1)},n=[],r=(s("d3b7"),s("159b"),s("3c65"),s("dc01")),i={name:"",props:{},data:function(){return{detailList:{},processList:[],opinion:"",reverse:!0,activities:[{name:"马振兵",state:"提交",stateRight:"通过",timestamp:"2018-04-12 20:46",color:"#1890ff"},{name:"马振兵",state:"提交",stateRight:"通过",timestamp:"2018-04-03 20:46",color:"#0bbd87"},{name:"马振兵",state:"提交",stateRight:"驳回",timestamp:"2018-04-03 20:46",color:"#FF0000"},{name:"马振兵",state:"提交",stateRight:"通过",timestamp:"2018-04-03 20:46"},{content:"支持使用图标",timestamp:"2018-04-12 20:46",size:"large",type:"primary",icon:"el-icon-more"},{content:"支持自定义颜色",timestamp:"2018-04-03 20:46",color:"#0bbd87"},{content:"支持自定义尺寸",timestamp:"2018-04-03 20:46",size:"large"},{content:"默认样式的节点",timestamp:"2018-04-03 20:46"}]}},created:function(){this.tbglDetails()},mounted:function(){},watch:{},filters:{},components:{},computed:{},methods:{tbglDetails:function(){var t=this;this.loading=!0,console.log(this.$route.query);var e=this.$route.query.id,s={id:e};console.log(s,"params"),Object(r["J"])(s).then((function(e){console.log(e),t.detailList=e.data,t.loading=!1,t.detailList.procinsId&&t.tbgllcDetails()}))},tbgllcDetails:function(){var t=this;this.loading=!0,console.log(this.$route.query);var e=this.$route.query.id,s={workAdjustmentId:e};console.log(s,"params"),Object(r["K"])(s).then((function(e){console.log(e),e.data.historyProcNodeList.forEach((function(e){t.processList.unshift(e)})),t.loading=!1}))}}},c=i,u=(s("df82"),s("2877")),d=Object(u["a"])(c,a,n,!1,null,"469c2566",null);e["default"]=d.exports},"3c65":function(t,e,s){"use strict";var a=s("23e7"),n=s("7b0b"),r=s("07fa"),i=s("3a34"),c=s("083a"),u=s("3511"),d=1!==[].unshift(0),o=!function(){try{Object.defineProperty([],"length",{writable:!1}).unshift()}catch(t){return t instanceof TypeError}}();a({target:"Array",proto:!0,arity:1,forced:d||o},{unshift:function(t){var e=n(this),s=r(e),a=arguments.length;if(a){u(s+a);var d=s;while(d--){var o=d+a;d in e?e[o]=e[d]:c(e,o)}for(var l=0;l<a;l++)e[l]=arguments[l]}return i(e,s+a)}})},"6e41":function(t,e,s){},dc01:function(t,e,s){"use strict";s.d(e,"y",(function(){return n})),s.d(e,"a",(function(){return r})),s.d(e,"C",(function(){return i})),s.d(e,"B",(function(){return c})),s.d(e,"c",(function(){return u})),s.d(e,"m",(function(){return d})),s.d(e,"s",(function(){return o})),s.d(e,"z",(function(){return l})),s.d(e,"t",(function(){return p})),s.d(e,"A",(function(){return m})),s.d(e,"J",(function(){return f})),s.d(e,"K",(function(){return h})),s.d(e,"G",(function(){return v})),s.d(e,"M",(function(){return g})),s.d(e,"E",(function(){return _})),s.d(e,"w",(function(){return y})),s.d(e,"h",(function(){return b})),s.d(e,"f",(function(){return j})),s.d(e,"e",(function(){return L})),s.d(e,"q",(function(){return C})),s.d(e,"b",(function(){return O})),s.d(e,"r",(function(){return w})),s.d(e,"F",(function(){return k})),s.d(e,"k",(function(){return x})),s.d(e,"H",(function(){return S})),s.d(e,"l",(function(){return B})),s.d(e,"g",(function(){return D})),s.d(e,"D",(function(){return A})),s.d(e,"o",(function(){return z})),s.d(e,"I",(function(){return I})),s.d(e,"i",(function(){return T})),s.d(e,"j",(function(){return E})),s.d(e,"n",(function(){return F})),s.d(e,"x",(function(){return N})),s.d(e,"d",(function(){return M})),s.d(e,"u",(function(){return q})),s.d(e,"v",(function(){return R})),s.d(e,"p",(function(){return $})),s.d(e,"L",(function(){return J}));var a=s("b775");function n(t){return Object(a["a"])({url:"/schedule/arrangement/pageList",method:"get",params:t})}function r(t){return Object(a["a"])({url:"/schedule/arrangement/save",method:"post",data:t})}function i(t){return Object(a["a"])({url:"/schedule/work-adjustment/pageList",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/schedule/schedule/mySchedule",method:"get",params:t})}function u(t){return Object(a["a"])({url:"/schedule/work-adjustment/save",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/schedule/repair-attend-apply/save",method:"post",data:t})}function o(t){return Object(a["a"])({url:"/schedule/service-group/findList",method:"get",params:t})}function l(t){return Object(a["a"])({url:"/schedule/service-group/getSelectList",method:"get",params:t})}function p(t){return Object(a["a"])({url:"/schedule/member/findList",method:"get",params:t})}function m(t){return Object(a["a"])({url:"/schedule/schedule/getShowData",method:"get",params:t})}function f(t){return Object(a["a"])({url:"/schedule/work-adjustment/detail",method:"get",params:t})}function h(t){return Object(a["a"])({url:"/schedule/work-adjustment/getActInfo",method:"get",params:t})}function v(t){return Object(a["a"])({url:"/schedule/work-adjustment/submitAct",method:"post",params:t})}function g(t){return Object(a["a"])({url:"/schedule/work-adjustment/withdrawAct",method:"get",params:t})}function _(t){return Object(a["a"])({url:"/schedule/schedule/pageList",method:"get",params:t})}function y(t){return Object(a["a"])({url:"/schedule/arrangement/getArrangementTypeList",method:"get",params:t})}function b(t){return Object(a["a"])({url:"/schedule/arrangement/findList",method:"get",params:t})}function j(t){return Object(a["a"])({url:"/schedule/arrangement/detail",method:"get",params:t})}function L(t){return Object(a["a"])({url:"/schedule/schedule/autoSetSchedule",method:"post",data:t})}function C(t){return Object(a["a"])({url:"/schedule/arrangement/deleteByIds",method:"post",params:t})}function O(t){return Object(a["a"])({url:"/schedule/schedule/save",method:"post",data:t})}function w(t){return Object(a["a"])({url:"/schedule/schedule/exportExcel",method:"get",params:t,responseType:"blob"})}function k(t){return Object(a["a"])({url:"/schedule/member-work/saveEntitys",method:"post",data:t})}function x(t){return Object(a["a"])({url:"/schedule/repair-attend-apply/pageList",method:"get",params:t})}function S(t){return Object(a["a"])({url:"/schedule/work-adjustment/update",method:"post",data:t})}function B(t){return Object(a["a"])({url:"/schedule/repair-attend-apply/update",method:"post",data:t})}function D(t){return Object(a["a"])({url:"/schedule/arrangement/update",method:"post",data:t})}function A(t){return Object(a["a"])({url:"/schedule/schedule/deleteByIds",method:"post",params:t})}function z(t){return Object(a["a"])({url:"/schedule/repair-attend-apply/submitAct",method:"post",params:t})}function I(t){return Object(a["a"])({url:"/schedule/work-adjustment/deleteByIds",method:"post",params:t})}function T(t){return Object(a["a"])({url:"/schedule/repair-attend-apply/deleteByIds",method:"post",params:t})}function E(t){return Object(a["a"])({url:"/schedule/repair-attend-apply/detail",method:"get",params:t})}function F(t){return Object(a["a"])({url:"/schedule/repair-attend-apply/getActInfo",method:"get",params:t})}function N(t){return Object(a["a"])({url:"/schedule/sysQuery/getLoginMemberInfo",method:"get",params:t})}function M(t){return Object(a["a"])({url:"/schedule/work-adjustment/approvalOperation",method:"post",params:t})}function q(t){return Object(a["a"])({url:"/schedule/schedule/getAllShowData",method:"get",params:t})}function R(t){return Object(a["a"])({url:"/schedule/service-group/findList",method:"get"})}function $(t){return Object(a["a"])({url:"/schedule/arrangement/updateStatus",method:"post",params:t})}function J(t){return Object(a["a"])({url:"/schedule/service-group/updateRemind",method:"post",data:t})}},df82:function(t,e,s){"use strict";s("6e41")}}]);