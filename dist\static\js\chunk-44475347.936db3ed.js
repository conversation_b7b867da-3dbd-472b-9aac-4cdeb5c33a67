(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-44475347"],{"18a5":function(e,i,s){"use strict";s("5f3d")},"5f3d":function(e,i,s){},eae2:function(e,i,s){"use strict";s.r(i);var t=function(){var e=this,i=e.$createElement,s=e._self._c||i;return s("div",{staticStyle:{width:"auto",height:"300px"},attrs:{id:"jessibuca"}},[s("div",{ref:e.id,staticStyle:{width:"100%",height:"300px","background-color":"#000"},attrs:{id:e.id},on:{dblclick:e.fullscreenSwich}})])},n=[],o={name:"jessibuca",data:function(){return{jessibuca:null,playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:"",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!0}},props:["videoUrl","error","hasAudio","height","id"],mounted:function(){var e=this;window.onerror=function(e){};var i=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){var s=document.getElementById(e.id);s.style.height=9/16*s.clientWidth+"px","undefined"==typeof e.videoUrl&&(e.videoUrl=i),console.log("初始化时的地址为: "+e.videoUrl),e.play(e.videoUrl)}))},created:function(){console.log(this.videoUrl)},methods:{create:function(){this.jessibuca=new JessibucaPro({container:"#"+this.id,decoder:"./decoder-pro.js",videoBuffer:.2,isResize:!1,text:"",loadingText:"加载中",debug:!0,isMulti:!0,useMSE:!0,useSIMD:!0,useWCS:!0,hasAudio:!1,useVideoRender:!0,controlAutoHide:!0,showBandwidth:!0,showPerformance:!1,operateBtns:{fullscreen:!0,screenshot:!0,play:!0,audio:!0},watermarkConfig:{text:{content:"摄像头"},right:10,top:10}}),this.jessibuca.on("fullscreen",(function(e){console.log("is fullscreen",index,e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var i=this;console.log(e),this.jessibuca&&this.destroy(),this.create(),this.jessibuca.on("play",(function(){i.playing=!0,i.loaded=!0,i.quieting=i.jessibuca.quieting})),this.jessibuca.hasLoaded()?this.jessibuca.play(e):this.jessibuca.on("load",(function(){console.log("load 播放"),i.jessibuca.play(e)}))},pause:function(){this.jessibuca&&this.jessibuca.pause(),this.playing=!1,this.err="",this.performance=""},destroy:function(){this.jessibuca&&this.jessibuca.destroy(),this.jessibuca=null,this.playing=!1,this.err="",this.performance=""},eventcallbacK:function(e,i){console.log("player 事件回调"),console.log(e),console.log(i)},fullscreenSwich:function(){var e=this.isFullscreen();this.jessibuca.setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}},destroyed:function(){this.jessibuca&&this.jessibuca.destroy(),this.playing=!1,this.loaded=!1,this.performance=""}},c=o,l=(s("18a5"),s("2877")),u=Object(l["a"])(c,t,n,!1,null,null,null);i["default"]=u.exports}}]);