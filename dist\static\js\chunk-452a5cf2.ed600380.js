(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-452a5cf2"],{"49a1":function(e,t,a){},"9e9a":function(e,t,a){"use strict";a("49a1")},a63a:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:24}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("数据筛选")])]),a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticStyle:{display:"flex","justify-content":"space-between"},attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"100px"}},[a("div",[a("el-form-item",{attrs:{label:"ID :",prop:"deviceId"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入ID",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceId,callback:function(t){e.$set(e.queryParams,"deviceId",t)},expression:"queryParams.deviceId"}})],1),a("el-form-item",{attrs:{label:"设备名称 :",prop:"deviceName"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入设备名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceName,callback:function(t){e.$set(e.queryParams,"deviceName",t)},expression:"queryParams.deviceName"}})],1),a("el-form-item",{attrs:{label:"告警状态 :",prop:"alarmStatus"}},[a("el-select",{staticStyle:{width:"10vw"},attrs:{placeholder:"告警状态",clearable:""},model:{value:e.queryParams.alarmStatus,callback:function(t){e.$set(e.queryParams,"alarmStatus",t)},expression:"queryParams.alarmStatus"}},e._l(e.dict.type.firecontrol_alarm_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"告警时间 :"}},[a("el-date-picker",{staticStyle:{width:"10vw"},attrs:{format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",{attrs:{label:"告警等级 :",prop:"alarmLevel"}},[a("el-select",{staticStyle:{width:"10vw"},attrs:{placeholder:"告警等级",clearable:""},model:{value:e.queryParams.alarmLevel,callback:function(t){e.$set(e.queryParams,"alarmLevel",t)},expression:"queryParams.alarmLevel"}},e._l(e.dict.type.firecontrol_alarm_level,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("div",{staticStyle:{"min-width":"200px"}},[a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)])],1),a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("设备告警列表")])]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userList}},[a("el-table-column",{attrs:{type:"index",width:"50",label:"序号"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),a("el-table-column",{key:"deviceId",attrs:{label:"设备ID",align:"center",prop:"deviceId"}}),a("el-table-column",{key:"deviceName",attrs:{label:"设备名称",align:"center",prop:"deviceName","show-overflow-tooltip":!0}}),a("el-table-column",{key:"location",attrs:{label:"安装位置",align:"center",prop:"location","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"告警类型",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getNameById1(t.row))+" ")]}}])}),a("el-table-column",{attrs:{label:"告警状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:e.getstatusByClass(t.row)},[e._v(e._s(e.getNameById(t.row)))])]}}])}),a("el-table-column",{key:"alarmTime",attrs:{label:"告警时间",align:"center",prop:"alarmTime"}}),a("el-table-column",{attrs:{label:"告警等级",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getNameById2(t.row))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDetail(t.row)}}},[e._v("详情")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}})],1)],1)],1),a("el-dialog",{attrs:{title:e.titleDetail,visible:e.openDetail,width:"1200px","append-to-body":""},on:{"update:visible":function(t){e.openDetail=t}}},[a("div",{staticClass:"diaTil"},[a("p",[e._v("处理记录")])]),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"200",border:""}},[a("el-table-column",{attrs:{type:"index",width:"50",label:"序号"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),a("el-table-column",{attrs:{prop:"approvalUser",label:"处理人",align:"center"}}),a("el-table-column",{attrs:{prop:"result",label:"审批状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getNameById3(t.row))+" ")]}}])}),a("el-table-column",{attrs:{prop:"approvalTime",label:"时间",align:"center"}})],1),a("div",{staticClass:"diaTil",staticStyle:{"padding-top":"10px"}},[a("p",[e._v("设备信息")])]),a("el-card",{attrs:{shadow:"hover"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:14}},[a("div",{staticClass:"diadevice_top",staticStyle:{"margin-bottom":"30px"}},[a("div",[e._v("ID："+e._s(e.deptOptions.id))]),a("div",[e._v("设备名称："+e._s(e.deptOptions.deviceName))]),a("div",[e._v("所属部门："+e._s(e.deptOptions.departmentName))])]),a("div",{staticClass:"diadevice_top",staticStyle:{"margin-bottom":"30px"}},[a("div",[e._v("当前状态："+e._s(e.deptOptions.deviceStatus))]),a("div",[e._v("安装位置："+e._s(e.deptOptions.location))]),a("div",[e._v("责任人："+e._s(e.deptOptions.principal))])]),a("div",{staticClass:"diadevice_top"},[a("div",[e._v("联系电话："+e._s(e.deptOptions.phone))])])])],1)],1),a("div",{staticClass:"diaTil",staticStyle:{"padding-top":"10px"}},[a("p",[e._v("告警信息")])]),a("el-card",{attrs:{shadow:"hover"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8,xs:8}},[a("div",[e._v("告警类型："+e._s(e.getNameById1(e.deptOptions)))])]),a("el-col",{attrs:{span:8,xs:8}},[a("div",[e._v("告警值："+e._s(e.deptOptions.alarmValue))])]),a("el-col",{attrs:{span:8,xs:8}},[a("div",[e._v("告警时间："+e._s(e.deptOptions.alarmTime))])])],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancelDetail}},[e._v("关 闭")])],1)],1)],1)},r=[],i=(a("4de4"),a("d3b7"),a("e9c4"),a("b775"));function s(e){return Object(i["a"])({url:"/firecontrol-device-alarm/page",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/firecontrol-device-alarm/detail",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/monitor/getVideoStreaming",method:"post",data:e})}var c={name:"AlarmLog",components:{},dicts:["firecontrol_alarm_status","firecontrol_alarm_level","firecontrol_device_type","firecontrol_approve_status"],data:function(){return{loading:!1,showSearch:!0,total:0,userList:null,title:"",deptOptions:{},open:!1,deptName:void 0,dateRange:[],form:{},activeNames:["1"],defaultProps:{children:"children",label:"label"},queryParams:{current:1,size:10},tableData:[],rules:{},titleDetail:"设备详情",openDetail:!1,formDetail:{},videoUrl:"",hasAudio:!1}},watch:{deptName:function(e){this.$refs.tree.filter(e)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,console.log(this.queryParams),s(this.queryParams).then((function(t){200==t.code&&(e.userList=t.data.records,e.total=t.data.total),console.log(t),e.loading=!1})),this.userList=[{}]},getDetail:function(e){var t=this;console.log(e),n({id:e}).then((function(e){console.log(e),t.titleDetail="告警详情-".concat(t.getNameById(e.data)),t.deptOptions=e.data,t.tableData=e.data.workTickets}))},filterNode:function(e,t){return!e||-1!==t.label.indexOf(e)},handleNodeClick:function(e){this.queryParams.deptId=e.id,this.handleQuery()},getstatusByClass:function(e){switch(e.alarmStatus){case"4012501":return"blue";case"4012503":return"green";default:break}},getNameById:function(e){if(void 0!=e.alarmStatus&&""!=e.alarmStatus&&null!=e.alarmStatus)return this.dict.type.firecontrol_alarm_status.filter((function(t){return t.value==e.alarmStatus}))[0].label},getNameById1:function(e){if(void 0!=e.alarmType&&""!=e.alarmType&&null!=e.alarmType)return this.dict.type.firecontrol_device_type.filter((function(t){return t.value==e.alarmType}))[0].label},getNameById2:function(e){if(void 0!=e.alarmLevel&&""!=e.alarmLevel&&null!=e.alarmLevel)return this.dict.type.firecontrol_alarm_level.filter((function(t){return t.value==e.alarmLevel}))[0].label},getNameById3:function(e){if(void 0!=e.result&&""!=e.result&&null!=e.result)return this.dict.type.firecontrol_approve_status.filter((function(t){return t.value==e.result}))[0].label},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={},this.resetForm("form")},handleQuery:function(){this.queryParams.current=1,this.queryParams.size=10,this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1],this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.queryParams={},this.handleQuery()},handleDetail:function(e){this.openDetail=!0,this.getDetail(e.id)},cancelDetail:function(){this.openDetail=!1,this.resetDetail()},resetDetail:function(){this.formDetail={}},handleRemove:function(e,t){console.log(e,t)},handlePreview:function(e){console.log(e)},submitForm:function(){},handleDelete:function(e){var t=this,a=e.userId||this.ids;this.$modal.confirm('是否确认删除用户编号为"'+a+'"的数据项？').then((function(){})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},videoError:function(e){console.log("播放器错误："+JSON.stringify(e))},getvideoFlv:function(e){var t=this;console.log(e),this.dialogVisible=!0,o({equipmentIdList:[e]}).then((function(e){console.log(e),t.$nextTick((function(){t.videoUrl=e.data[0].flvAddress,t.$refs.videoPlayer.play(t.videoUrl)}))}))}}},d=c,u=(a("9e9a"),a("2877")),m=Object(u["a"])(d,l,r,!1,null,"06218ced",null);t["default"]=m.exports}}]);