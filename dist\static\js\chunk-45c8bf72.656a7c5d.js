(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-45c8bf72","chunk-347193c5"],{1493:function(e,t,a){},"2c13":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:24}},[a("el-card",{staticClass:"box-card mapBox"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("应急事件展示列表")])]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.eventListDialog,expression:"!eventListDialog"}],staticStyle:{position:"absolute","z-index":"1"},attrs:{type:"primary",plain:""},on:{click:function(t){e.eventListDialog=!0}}},[e._v("事件列表 ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.eventListDialog,expression:"eventListDialog"}],staticClass:"eventListBox"},[a("div",{staticClass:"boxTitle"},[a("div",{staticClass:"content"},[e._v("事件列表")]),a("i",{staticClass:"el-icon-close",staticStyle:{color:"#fff",cursor:"pointer"},on:{click:function(t){e.eventListDialog=!1}}})]),a("div",{staticClass:"boxContent"},e._l(e.eventList,(function(t,i){return a("div",{key:i,staticClass:"event",on:{click:function(a){return e.eventClick(t)}}},[a("div",{staticClass:"overlength",attrs:{title:t.eventName}},[a("span",{staticClass:"name"},[e._v("事件名称：")]),e._v(e._s(t.eventName)+" ")]),a("div",[a("span",{staticClass:"name"},[e._v("事件类型：")]),a("el-tag",{attrs:{type:"success"}},[e._v(e._s(t.eventTypeName))])],1),a("div",[a("span",{staticClass:"name"},[e._v("事件等级：")]),e._v(e._s(t.eventLevel?e.dict.type.event_level.find((function(e){return e.value==t.eventLevel})).label:"")+" ")]),a("div",{staticClass:"overlength",attrs:{title:t.occurrenceTime}},[a("span",{staticClass:"name"},[e._v("事件时间：")]),e._v(e._s(t.occurrenceTime)+" ")]),a("div",{staticClass:"overlength"},[a("span",{staticClass:"name"},[e._v("调度进度：")]),e._v(e._s(t.dispatchState?e.dict.type.emergency_dispatch_state.find((function(e){return e.value==t.dispatchState})).label:"")+" ")])])})),0)]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.eventDetaildialog,expression:"eventDetaildialog"}],staticClass:"eventDetailBox"},[a("div",{staticClass:"boxTitle"},[a("div",{staticClass:"content"},[e._v("事件名称："+e._s(e.eventDetail.eventName))]),a("i",{staticClass:"el-icon-close",staticStyle:{color:"#fff",cursor:"pointer"},on:{click:function(t){e.eventDetaildialog=!1}}})]),a("div",{staticClass:"detailContent"},[a("div",{staticClass:"eventDetail"},[a("div",[a("span",{staticClass:"name"},[e._v("事件类型：")]),e._v(e._s(e.eventDetail.eventTypeName)+" ")]),a("div",[a("span",{staticClass:"name"},[e._v("事件描述：")]),e._v(e._s(e.eventDetail.eventDescription)+" ")]),a("div",[a("span",{staticClass:"name"},[e._v("事件位置：")]),e._v(e._s(e.eventDetail.longitude+","+e.eventDetail.latitude)+" ")]),a("div",[a("span",{staticClass:"name"},[e._v("事件时间：")]),e._v(e._s(e.eventDetail.occurrenceTime)+" ")])]),a("div",{staticClass:"btnBox1"},[a("el-button",{staticClass:"queryBtnAcc",attrs:{type:"primary"},on:{click:e.handleReport}},[e._v("事故报告")]),a("el-button",{staticClass:"queryBtnAcc",attrs:{type:"primary"},on:{click:e.handleCommand}},[e._v("预案调度 ")]),a("el-button",{staticClass:"queryBtnAcc",attrs:{type:"primary"},on:{click:e.handleRecord}},[e._v("执行记录")]),a("el-button",{staticClass:"queryBtnAcc",attrs:{type:"primary"},on:{click:e.clickAcc}},[e._v("事故模拟 ")])],1),a("div",{staticClass:"btnBox"})])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.resourcefulSearch,expression:"resourcefulSearch"}],staticClass:"searchBox"},[a("div",{staticClass:"boxTitle"},[a("div",{staticClass:"content"},[e._v("周边资源搜索")]),a("i",{staticClass:"el-icon-close",staticStyle:{color:"#fff",cursor:"pointer"},on:{click:function(t){e.resourcefulSearch=!1}}})]),a("div",{staticClass:"searchContent"},[a("div",{staticClass:"searchTitle"},[e._v("距离范围筛选")]),a("div",{staticStyle:{margin:"20px 0"}},[a("el-radio-group",{on:{change:e.rangeChange},model:{value:e.range,callback:function(t){e.range=t},expression:"range"}},[a("el-radio-button",{attrs:{label:"200"}},[e._v("200m")]),a("el-radio-button",{attrs:{label:"500"}},[e._v("500m")]),a("el-radio-button",{attrs:{label:"1000"}},[e._v("1km")]),a("el-radio-button",{attrs:{label:"5000"}},[e._v("5km")])],1)],1),a("div",{staticClass:"searchTitle"},[e._v("资源筛选")]),a("div",{staticStyle:{"margin-top":"20px"}},[a("el-checkbox-group",{on:{change:e.checkListChange},model:{value:e.checkList,callback:function(t){e.checkList=t},expression:"checkList"}},[a("el-checkbox",{attrs:{label:"5013003"}},[e._v("避难场所")]),a("el-checkbox",{attrs:{label:"5013005"}},[e._v("应急广播")]),a("el-checkbox",{attrs:{label:"5013004"}},[e._v("周边监控")]),a("el-checkbox",{attrs:{label:"5013008"}},[e._v("防护目标")]),a("el-checkbox",{attrs:{label:"5013009"}},[e._v("医疗机构")]),a("el-checkbox",{attrs:{label:"5013006"}},[e._v("风险隐患")]),a("el-checkbox",{attrs:{label:"5013007"}},[e._v("通讯保障")])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.eventDetail.isEndRescue,expression:"!eventDetail.isEndRescue"}]},[a("el-radio-group",{model:{value:e.dispatch,callback:function(t){e.dispatch=t},expression:"dispatch"}},[a("el-radio-button",{attrs:{label:"rank"}},[e._v("队伍调度")]),a("el-radio-button",{attrs:{label:"good"}},[e._v("物资调度")])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"rank"==e.dispatch&&!e.eventDetail.isEndRescue,expression:"dispatch == 'rank' && !eventDetail.isEndRescue"}],staticClass:"rankAndgood"},e._l(e.ranksList,(function(t,i){return a("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"item.show"}],key:i,staticClass:"dispatch"},[a("div",{staticClass:"rankName"},[e._v(" "+e._s(t.name)+" ")]),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.rankPosition(t)}}},[e._v("定位")]),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.rankDispatch(t)}}},[e._v("调度")])],1)])})),0),a("div",{directives:[{name:"show",rawName:"v-show",value:"good"==e.dispatch,expression:"dispatch == 'good'"}],staticClass:"rankAndgood"},e._l(e.goodsList,(function(t,i){return a("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"item.show"}],key:i,staticClass:"dispatch"},[a("div",{staticClass:"rankName"},[e._v(" "+e._s(t.name)+" ")]),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.goodPosition(t)}}},[e._v("定位")]),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.goodDispatch(t)}}},[e._v("调度")])],1)])})),0)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.resourcefulSearch,expression:"resourcefulSearch"}],staticClass:"searchBox-2"},[a("div",{staticClass:"searchContent"},[a("div",{staticStyle:{margin:"10px 0"}},[a("el-radio-group",{on:{change:e.rangeChangeNew},model:{value:e.changeNew,callback:function(t){e.changeNew=t},expression:"changeNew"}},[a("el-radio-button",{staticStyle:{"margin-top":"8px"},attrs:{label:"93001"}},[e._v("实时视频")]),a("el-radio-button",{staticStyle:{"margin-top":"8px"},attrs:{label:"93002"}},[e._v("实时气象")]),a("el-radio-button",{staticStyle:{"margin-top":"8px"},attrs:{label:"93003"}},[e._v("气体监测")]),a("el-radio-button",{staticStyle:{"margin-top":"8px"},attrs:{label:"93004"}},[e._v("人员定位")])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"93001"==e.changeNew,expression:"changeNew == '93001'"}],staticClass:"rankAndgood"},e._l(e.videoDetailData,(function(t,i){return a("div",{key:i,staticClass:"dispatch"},[a("div",{staticClass:"rankName"},[e._v(" "+e._s(t.name)+" ")]),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.videoPosition(t)}}},[e._v("定位")]),a("el-button",{attrs:{type:"text",disabled:0==t.isOnline},on:{click:function(a){return e.clickVideo(t)}}},[e._v("查看")])],1)])})),0),a("div",{directives:[{name:"show",rawName:"v-show",value:"93003"==e.changeNew,expression:"changeNew == '93003' "}],staticClass:"rankAndgood"},[e.monitorStationList&&e.monitorStationList.length>0?a("div",e._l(e.monitorStationList,(function(t,i){return a("div",{key:i,staticClass:"dispatch"},[a("div",{staticClass:"rankName"},[e._v(" "+e._s(t.name)+" ")]),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.gasPosition(t)}}},[e._v("定位")]),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.clickGas(t)}}},[e._v("查看")])],1)])})),0):a("div",{staticClass:"no-text"},[e._v("暂无数据")])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"93002"==e.changeNew,expression:"changeNew == '93002' "}],staticClass:"rankAndgood"},[a("el-descriptions",{attrs:{column:2}},[a("el-descriptions-item",{attrs:{label:"城市/区"}},[e._v(e._s(e.wea.city))]),a("el-descriptions-item",{attrs:{label:"天气"}},[e._v(e._s(e.wea.weather))]),a("el-descriptions-item",{attrs:{label:"温度"}},[e._v(e._s(e.wea.temperature)+"℃")]),a("el-descriptions-item",{attrs:{label:"风向"}},[e._v(" "+e._s(e.wea.windDirection)+" ")]),a("el-descriptions-item",{attrs:{label:"风力"}},[e._v(e._s(e.wea.windPower))]),a("el-descriptions-item",{attrs:{label:"空气湿度"}},[e._v(e._s(e.wea.humidity))])],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"93004"==e.changeNew,expression:"changeNew == '93004'"}],staticClass:"rankAndgood"},e._l(e.ranksList,(function(t,i){return a("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"item.show"}],key:i,staticClass:"dispatch"},[a("div",{staticClass:"rankName"},[e._v(" "+e._s(t.name)+" ")]),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.rankPosition(t)}}},[e._v("定位")]),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.rankDispatch(t)}}},[e._v("调度")])],1)])})),0)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.gasDialog,expression:"gasDialog"}],staticClass:"eventDetailBox"},[a("div",{staticClass:"boxTitle"},[a("div",{staticClass:"content"},[e._v("气体监测")]),a("i",{staticClass:"el-icon-close",staticStyle:{color:"#fff",cursor:"pointer"},on:{click:function(t){e.gasDialog=!1}}})]),e.gasDetailData&&e.gasDetailData.length>0?a("div",{staticClass:"new"},[a("div",{staticClass:"rankAndgood"},[a("el-descriptions",{staticClass:"margin-top",attrs:{column:1}},e._l(e.gasDetailData,(function(t,i){return a("el-descriptions-item",{key:i,staticClass:"dispatch",attrs:{label:t.factorName}},[e._v(" "+e._s(t.monitorValue)+e._s(t.unit)+" ")])})),1)],1)]):e._e(),a("el-empty",{staticClass:"new",attrs:{description:"暂无数据"}})],1),a("el-dialog",{staticStyle:{margin:"30vh 0 0 0vw"},attrs:{title:"实时视频",visible:e.videoVisible,width:"560px","destroy-on-close":"",modal:!1},on:{"update:visible":function(t){e.videoVisible=t}}},[a("div",{staticStyle:{width:"100%",height:"100%"},attrs:{id:"videoElement"}},[a("jessibuca-player",{ref:"videoPlayer",attrs:{id:"videoElement1",videoUrl:e.videoUrl,error:e.videoError,message:e.videoError,height:!1,hasAudio:e.hasAudio,fluent:"",autoplay:"",live:""}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.videoVisible=!1}}},[e._v("取 消")])],1)]),a("Map",{ref:"mapRef",attrs:{url:e.url},on:{detail:e.detailDialog,weather:e.weather,toDetile:e.toDetile}})],1)],1)],1),a("el-dialog",{attrs:{width:"960px","append-to-body":"",visible:e.reportDialog,title:"事故报告"},on:{"update:visible":function(t){e.reportDialog=t}}},[a("el-descriptions",{attrs:{title:"基本信息"}},[a("el-descriptions-item",{attrs:{label:"事件名称"}},[e._v(e._s(e.reportDetail.eventName)+" ")]),a("el-descriptions-item",{attrs:{label:"上报人"}},[e._v(e._s(e.reportDetail.submitPerson)+" ")]),a("el-descriptions-item",{attrs:{label:"联系电话"}},[e._v(e._s(e.reportDetail.contactNumber)+" ")])],1),a("el-descriptions",{staticStyle:{margin:"20px 0"},attrs:{title:"事件详情"}},[a("el-descriptions-item",{attrs:{label:"事件描述"}},[e._v(e._s(e.reportDetail.eventDescription)+" ")]),a("el-descriptions-item",{attrs:{label:"发生时间"}},[e._v(e._s(e.reportDetail.occurrenceTime)+" ")]),a("el-descriptions-item",{attrs:{label:"事件附件"}},[e.reportDetail.attachmentAddress?a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handledownload(e.reportDetail)}}},[e._v("下载附件 ")]):e._e()],1)],1),a("el-descriptions",{attrs:{title:"事件报告"}},[a("el-descriptions-item",{attrs:{label:"事件类型"}},[a("el-tag",{staticClass:"tag",attrs:{size:"small"}},[e._v(e._s(e.reportDetail.eventTypeName))])],1),a("el-descriptions-item",{attrs:{label:"事件等级"}},[e._v(e._s(e.reportDetail.eventLevel?e.dict.type.event_level.find((function(t){return t.value==e.reportDetail.eventLevel})).label:"")+" ")]),a("el-descriptions-item",{attrs:{label:"经济损失"}},[e._v(e._s(e.reportDetail.economicLoss)+" ")]),a("el-descriptions-item",{attrs:{label:"受灾面积"}},[e._v(e._s(e.reportDetail.disasterArea)+"公顷 ")]),a("el-descriptions-item",{attrs:{label:"死亡人数"}},[e._v(e._s(e.reportDetail.deathNumber)+" ")]),a("el-descriptions-item",{attrs:{label:"受伤人数"}},[e._v(e._s(e.reportDetail.injuredNumber)+" ")]),a("el-descriptions-item",{attrs:{label:"失踪人数"}},[e._v(e._s(e.reportDetail.missingNumber)+" ")]),a("el-descriptions-item",{attrs:{label:"受困人数"}},[e._v(e._s(e.reportDetail.trappedNumber)+" ")]),a("el-descriptions-item",{attrs:{label:"事件标签"}},e._l(e.reportDetail.eventTypeLabels,(function(t){return a("el-tag",{staticClass:"tag",attrs:{size:"small"}},[e._v(e._s(t.label)+" ")])})),1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancel}},[e._v("返 回")])],1)],1),a("el-dialog",{attrs:{width:"960px","append-to-body":"",visible:e.commandDialog,"show-close":!1,"close-on-click-modal":!1,title:"指挥调度"},on:{"update:visible":function(t){e.commandDialog=t}}},[a("el-tabs",{on:{"tab-click":e.commandClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"预案响应",name:"first"}},[a("el-row",[a("el-col",{attrs:{span:7}},[a("div",{staticClass:"planList"},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.eventDetail.isEndRescue,expression:"!eventDetail.isEndRescue"}],attrs:{type:"primary"},on:{click:e.launchPlan}},[e._v("启动预案 ")]),a("div",{staticClass:"planCard"},e._l(e.planBox,(function(t,i){return a("div",{key:i,staticClass:"plan",on:{click:function(a){return e.planClick(t)}}},[a("div",[e._v(e._s(t.planName))]),a("el-tag",{staticStyle:{margin:"10px 0"},attrs:{type:"danger"}},[e._v(e._s(t.responseName)+" ")]),a("div",{staticStyle:{"white-space":"nowrap"}},[e._v("启动时间："+e._s(t.createTime))])],1)})),0)],1)]),a("el-col",{attrs:{span:17}},[a("el-row",[a("el-col",{attrs:{span:16}},[a("div",{staticClass:"stepBox"},e._l(e.stepList,(function(t,i){return a("div",{key:i,staticClass:"step"},[a("span",[e._v("步骤"+e._s(i+1))]),a("div",{staticClass:"rank"},[a("div",[e._v(" 响应名称:"+e._s(t.responseName)+" "+e._s(e.dict.type.step_status.find((function(e){return e.value==t.processStatus})).label)+" ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5011801==t.processStatus,expression:"item.processStatus == 5011801"}],attrs:{type:"text"},on:{click:function(a){return e.processUpdate(t.id,"5011802")}}},[e._v("开始处置 ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5011802==t.processStatus,expression:"item.processStatus == 5011802"}],attrs:{type:"text"},on:{click:function(a){return e.processUpdate(t.id,"5011803")}}},[e._v("结束步骤 ")])],1),a("div",[e._v("注意事项:"+e._s(t.announcement))]),e._l(t.contingentList,(function(i,n){return a("div",{key:n,staticClass:"rank"},[a("div",[e._v(" 响应队伍:"+e._s(i.contingentName)+" "+e._s(e.dict.type.contingent_status.find((function(e){return e.value==i.contingentStatus})).label)+" ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5011901==i.contingentStatus&&5011802==t.processStatus,expression:"\n                          ele.contingentStatus == 5011901 &&\n                          item.processStatus == 5011802\n                        "}],attrs:{type:"text"},on:{click:function(t){return e.assign(i)}}},[e._v("指派 ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5011902==i.contingentStatus&&5011802==t.processStatus,expression:"\n                          ele.contingentStatus == 5011902 &&\n                          item.processStatus == 5011802\n                        "}],attrs:{type:"text"},on:{click:function(t){return e.finish(i,"5011902")}}},[e._v("完成 ")])],1)}))],2)})),0)]),a("el-col",{attrs:{span:8}},[a("el-timeline",{staticStyle:{height:"54vh","overflow-y":"auto"}},e._l(e.activities,(function(t,i){return a("el-timeline-item",{key:i,attrs:{timestamp:"步骤"+(i+1),placement:"top"}},[e._v(" 开始时间："+e._s(t.startTime)+" "),a("br"),e._l(t.contingentList,(function(t,i){return a("div",{key:i},[e._v(" 指派队伍："+e._s(t.contingentName)+" "),a("br"),e._v(" 任务描述："+e._s(t.taskDescription)+" "),a("br"),a("span",{directives:[{name:"show",rawName:"v-show",value:"5011902"==t.contingentStatus,expression:"ite.contingentStatus == '5011902'"}]},[e._v(" 预期完成时间："+e._s(t.finishTime)+" ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:"5011903"==t.contingentStatus,expression:"ite.contingentStatus == '5011903'"}]},[e._v(" 完成时间："+e._s(t.endTime)+" ")])])})),a("br"),a("el-tag",{directives:[{name:"show",rawName:"v-show",value:5011803==t.processStatus,expression:"activity.processStatus == 5011803"}],attrs:{type:"success"}},[e._v(" "+e._s(e.dict.type.step_status.find((function(e){return e.value==t.processStatus})).label)+" ")]),a("el-tag",{directives:[{name:"show",rawName:"v-show",value:5011802==t.processStatus,expression:"activity.processStatus == 5011802"}],attrs:{type:"warning"}},[e._v(" "+e._s(e.dict.type.step_status.find((function(e){return e.value==t.processStatus})).label)+" ")]),a("el-tag",{directives:[{name:"show",rawName:"v-show",value:5011801==t.processStatus,expression:"activity.processStatus == 5011801"}],attrs:{type:"info"}},[e._v(" "+e._s(e.dict.type.step_status.find((function(e){return e.value==t.processStatus})).label)+" ")])],2)})),1)],1)],1)],1)],1)],1),a("el-tab-pane",{attrs:{label:"临时任务",name:"second"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.sendTask}},[e._v("增派临时任务")]),a("el-table",{staticStyle:{width:"100%",margin:"20px 0"},attrs:{data:e.tasksData,border:""}},[a("el-table-column",{attrs:{prop:"createTime",label:"创建时间",align:"center"}}),a("el-table-column",{attrs:{prop:"updateTime",label:"更新时间",align:"center"}}),a("el-table-column",{attrs:{prop:"taskType",label:"任务类型",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(" "+e._s(e.dict.type.task_type.find((function(e){return e.value==t.row.taskType}))?e.dict.type.task_type.find((function(e){return e.value==t.row.taskType})).label:"")+" ")])]}}])}),a("el-table-column",{attrs:{prop:"leader",label:"负责人",align:"center"}}),a("el-table-column",{attrs:{prop:"taskRemark",label:"任务备注",align:"center"}}),a("el-table-column",{attrs:{prop:"finishTime",label:"期望完成时间",align:"center"}}),a("el-table-column",{attrs:{prop:"taskStatus",label:"任务状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(" "+e._s(e.dict.type.task_status.find((function(e){return e.value==t.row.taskStatus}))?e.dict.type.task_status.find((function(e){return e.value==t.row.taskStatus})).label:"")+" ")])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleTaskDetail(t.row)}}},[e._v("详情 ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5012601==t.row.taskStatus,expression:"scope.row.taskStatus == 5012601"}],attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleTaskUpdate(t.row,"5012602")}}},[e._v("完成 ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:5012601==t.row.taskStatus,expression:"scope.row.taskStatus == 5012601"}],attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleTaskUpdate(t.row,"5012603")}}},[e._v("取消 ")])]}}])})],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.eventDetail.isEndRescue,expression:"!eventDetail.isEndRescue"}],attrs:{type:"primary",plain:""},on:{click:e.endRescue}},[e._v("结束救援 ")]),a("el-button",{on:{click:function(t){e.commandDialog=!1}}},[e._v("返 回")])],1)],1),a("el-dialog",{attrs:{width:"720px","append-to-body":"",visible:e.launchPlanDialog,"show-close":!1,"close-on-click-modal":!1,title:"启动预案"},on:{"update:visible":function(t){e.launchPlanDialog=t}}},[a("el-form",{attrs:{inline:!0,model:e.planForm}},[a("el-form-item",{attrs:{label:"预案名称"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入预案名称",maxlength:"20"},model:{value:e.planForm.planName,callback:function(t){e.$set(e.planForm,"planName",t)},expression:"planForm.planName"}})],1),a("el-form-item",{attrs:{label:"预案类型"}},[a("el-select",{staticStyle:{width:"10vw"},attrs:{placeholder:"请选择预案类型"},model:{value:e.planForm.planType,callback:function(t){e.$set(e.planForm,"planType",t)},expression:"planForm.planType"}},e._l(e.dict.type.plan_deduction,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.planSearch}},[e._v("搜索")]),a("el-button",{attrs:{size:"mini"},on:{click:e.planReset}},[e._v("重置")])],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.planLoading,expression:"planLoading"}],staticStyle:{display:"flex","flex-wrap":"wrap"}},e._l(e.planList,(function(t,i){return a("el-radio",{key:i,staticStyle:{width:"320px",margin:"0 0 10px 10px"},attrs:{label:t.id,border:""},on:{input:e.planInput},model:{value:e.planRadio,callback:function(t){e.planRadio=t},expression:"planRadio"}},[[a("el-tooltip",{attrs:{content:t.planName+"-"+t.planDescription,placement:"top"}},[a("span",{staticClass:"tooltip"},[e._v(e._s(t.planName)+"-"+e._s(t.planDescription))])])]],2)})),1),a("el-form",{ref:"levelFrom",staticStyle:{"margin-top":"20px"},attrs:{model:e.levelFrom,rules:e.levelRules,inline:!0}},[a("el-form-item",{attrs:{label:"响应等级",prop:"level"}},[a("el-select",{attrs:{placeholder:"请选择响应等级"},model:{value:e.levelFrom.level,callback:function(t){e.$set(e.levelFrom,"level",t)},expression:"levelFrom.level"}},e._l(e.levelBox,(function(e,t){return a("el-option",{key:t,attrs:{label:e.responseName,value:e.id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确 认")]),a("el-button",{on:{click:function(t){e.launchPlanDialog=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"指派任务",visible:e.assignDialog,width:"560px","append-to-body":""},on:{"update:visible":function(t){e.assignDialog=t}}},[a("el-form",{ref:"assignFrom",attrs:{model:e.assignFrom,rules:e.assignRules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"任务描述",prop:"taskDescription"}},[a("el-input",{attrs:{type:"textarea",maxlength:"200"},model:{value:e.assignFrom.taskDescription,callback:function(t){e.$set(e.assignFrom,"taskDescription",t)},expression:"assignFrom.taskDescription"}})],1),a("el-form-item",{attrs:{label:"期望完成时间",prop:"finishTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss"},model:{value:e.assignFrom.finishTime,callback:function(t){e.$set(e.assignFrom,"finishTime",t)},expression:"assignFrom.finishTime"}})],1),a("el-form-item",{attrs:{label:"是否短信通知",prop:"isSmsNotice"}},[a("el-radio-group",{model:{value:e.assignFrom.isSmsNotice,callback:function(t){e.$set(e.assignFrom,"isSmsNotice",t)},expression:"assignFrom.isSmsNotice"}},[a("el-radio",{attrs:{label:"true"}},[e._v("是")]),a("el-radio",{attrs:{label:"false"}},[e._v("否")])],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.assignConfirm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.assignDialog=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:e.sendTitle,visible:e.sendDialog,width:"960px","append-to-body":""},on:{"update:visible":function(t){e.sendDialog=t}}},[a("el-radio-group",{staticStyle:{"margin-bottom":"20px"},model:{value:e.sendFlag,callback:function(t){e.sendFlag=t},expression:"sendFlag"}},[a("el-radio-button",{attrs:{label:"rank"}},[e._v("队伍调度")]),a("el-radio-button",{attrs:{label:"good"}},[e._v("物资调度")])],1),"rank"==e.sendFlag?a("el-form",{ref:"sendrankFrom",attrs:{model:e.sendrankFrom,rules:e.sendrankRules,"label-width":"120px"}},[a("el-form-item",{key:"rankId",attrs:{label:"指派队伍",prop:"contingentId"}},[a("el-select",{attrs:{placeholder:"请选择指派队伍",disabled:e.rankDisabled},model:{value:e.sendrankFrom.contingentId,callback:function(t){e.$set(e.sendrankFrom,"contingentId",t)},expression:"sendrankFrom.contingentId"}},e._l(e.ranksOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.contingentName,value:e.id}})})),1)],1),a("el-form-item",{key:"rankRemark",attrs:{label:"任务备注",prop:"taskRemark"}},[a("el-input",{staticStyle:{width:"60%"},attrs:{type:"textarea",maxlength:"200",disabled:e.rankDisabled},model:{value:e.sendrankFrom.taskRemark,callback:function(t){e.$set(e.sendrankFrom,"taskRemark",t)},expression:"sendrankFrom.taskRemark"}})],1),a("el-form-item",{attrs:{label:"是否即刻完成",prop:"taskDescription"}},[a("el-radio",{attrs:{label:"1"},model:{value:e.ranksRadio,callback:function(t){e.ranksRadio=t},expression:"ranksRadio"}},[e._v("是")]),a("el-radio",{attrs:{label:"2"},model:{value:e.ranksRadio,callback:function(t){e.ranksRadio=t},expression:"ranksRadio"}},[e._v("否")])],1),"2"==e.ranksRadio?a("el-form-item",{key:"rankFinish",attrs:{label:"期望完成时间",prop:"finishTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss",disabled:e.rankDisabled},model:{value:e.sendrankFrom.finishTime,callback:function(t){e.$set(e.sendrankFrom,"finishTime",t)},expression:"sendrankFrom.finishTime"}})],1):e._e(),a("el-form-item",{key:"rankisIn",attrs:{label:"是否短信通知",prop:"isInform"}},[a("el-radio-group",{attrs:{disabled:e.rankDisabled},model:{value:e.sendrankFrom.isInform,callback:function(t){e.$set(e.sendrankFrom,"isInform",t)},expression:"sendrankFrom.isInform"}},[a("el-radio",{attrs:{label:"1"}},[e._v("是")]),a("el-radio",{attrs:{label:"0"}},[e._v("否")])],1)],1)],1):e._e(),"good"==e.sendFlag?a("el-form",{ref:"sendgoodFrom",attrs:{model:e.sendgoodFrom,rules:e.sendgoodRules,"label-width":"120px"}},[a("el-form-item",{key:"goodDepot",attrs:{label:"调度仓库",prop:"depotId"}},[a("el-select",{attrs:{placeholder:"请选择调度仓库",disabled:e.goodDisabled},on:{change:e.depotChange},model:{value:e.sendgoodFrom.depotId,callback:function(t){e.$set(e.sendgoodFrom,"depotId",t)},expression:"sendgoodFrom.depotId"}},e._l(e.depotOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"调度物资"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.goodsData,border:""}},[a("el-table-column",{attrs:{align:"center",prop:"materialName",label:"物资名称","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"materialType",align:"center",label:"物资类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",[e._v(e._s(e.dict.type.materiel_type.find((function(e){return e.value==t.row.materialType})).label)+" ")])]}}],null,!1,2967836331)}),this.goodDisabled?e._e():a("el-table-column",{attrs:{prop:"inventory",align:"center",label:"库存量"}}),a("el-table-column",{attrs:{prop:"dispatchQuantity",align:"center",label:"调度量"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{type:"number",maxlength:"20",disabled:e.goodDisabled},on:{input:function(a){return e.checkNum(t.row)}},model:{value:t.row.dispatchQuantity,callback:function(a){e.$set(t.row,"dispatchQuantity",a)},expression:"scope.row.dispatchQuantity"}})]}}],null,!1,1610549567)})],1)],1),a("el-form-item",{key:"goodRemark",attrs:{label:"任务备注",prop:"taskRemark"}},[a("el-input",{attrs:{maxlength:"200",type:"textarea",disabled:e.goodDisabled},model:{value:e.sendgoodFrom.taskRemark,callback:function(t){e.$set(e.sendgoodFrom,"taskRemark",t)},expression:"sendgoodFrom.taskRemark"}})],1),a("el-form-item",{attrs:{label:"是否即刻完成",prop:"taskDescription"}},[a("el-radio",{attrs:{label:"1"},model:{value:e.goodsRadio,callback:function(t){e.goodsRadio=t},expression:"goodsRadio"}},[e._v("是")]),a("el-radio",{attrs:{label:"2"},model:{value:e.goodsRadio,callback:function(t){e.goodsRadio=t},expression:"goodsRadio"}},[e._v("否")])],1),"2"==e.goodsRadio?a("el-form-item",{key:"goodFinish",attrs:{label:"期望完成时间",prop:"finishTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss",disabled:e.goodDisabled},model:{value:e.sendgoodFrom.finishTime,callback:function(t){e.$set(e.sendgoodFrom,"finishTime",t)},expression:"sendgoodFrom.finishTime"}})],1):e._e(),a("el-form-item",{key:"goodisIn",attrs:{label:"是否短信通知",prop:"isInform"}},[a("el-radio-group",{attrs:{disabled:e.goodDisabled},model:{value:e.sendgoodFrom.isInform,callback:function(t){e.$set(e.sendgoodFrom,"isInform",t)},expression:"sendgoodFrom.isInform"}},[a("el-radio",{attrs:{label:"1"}},[e._v("是")]),a("el-radio",{attrs:{label:"0"}},[e._v("否")])],1)],1)],1):e._e(),a("span",{directives:[{name:"show",rawName:"v-show",value:!e.goodDisabled,expression:"!goodDisabled"}],staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.sendConfirm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.sendDialog=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"次生分析",visible:e.secondDialog,width:"960px","append-to-body":""},on:{"update:visible":function(t){e.secondDialog=t}}},[a("el-button",{staticStyle:{"margin-bottom":"20px"},attrs:{type:"primary",plain:"",size:"mini"},on:{click:e.secondDataAdd}},[e._v("新增次生分析 ")]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.secondData,border:""}},[a("el-table-column",{attrs:{prop:"createTime",align:"center",label:"上传时间"}}),a("el-table-column",{attrs:{prop:"name",align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleSecondDetail(t.row)}}},[e._v("查看 ")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleSecondDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.secondQueryParams.current,limit:e.secondQueryParams.size},on:{"update:page":function(t){return e.$set(e.secondQueryParams,"current",t)},"update:limit":function(t){return e.$set(e.secondQueryParams,"size",t)},pagination:e.getSecondData}})],1),a("el-dialog",{attrs:{title:e.secondaryAnalysisTitle,visible:e.analysisDialog,width:"560px","append-to-body":""},on:{"update:visible":function(t){e.analysisDialog=t}}},[a("el-form",{ref:"analysisForm",attrs:{model:e.analysisForm,rules:e.analysisRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"次生灾害",prop:"secondaryDisaster"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入次生灾害",maxlength:"20",disabled:e.analysisDisabled},model:{value:e.analysisForm.secondaryDisaster,callback:function(t){e.$set(e.analysisForm,"secondaryDisaster",t)},expression:"analysisForm.secondaryDisaster"}})],1),a("el-form-item",{attrs:{label:"通知人员",prop:"noticePersonId"}},[a("el-select",{staticStyle:{width:"245px"},attrs:{placeholder:"请选择通知人员",disabled:e.analysisDisabled},model:{value:e.analysisForm.noticePersonId,callback:function(t){e.$set(e.analysisForm,"noticePersonId",t)},expression:"analysisForm.noticePersonId"}},[a("el-option",{attrs:{label:"人员一",value:"shanghai"}}),a("el-option",{attrs:{label:"人员二",value:"beijing"}})],1)],1),a("el-form-item",{attrs:{label:"要素分析",prop:"elementAnalysis"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{type:"textarea",maxlength:"200",disabled:e.analysisDisabled},model:{value:e.analysisForm.elementAnalysis,callback:function(t){e.$set(e.analysisForm,"elementAnalysis",t)},expression:"analysisForm.elementAnalysis"}})],1),a("el-form-item",{attrs:{label:"承载体情况",prop:"bearingSituation"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{type:"textarea",maxlength:"200",disabled:e.analysisDisabled},model:{value:e.analysisForm.bearingSituation,callback:function(t){e.$set(e.analysisForm,"bearingSituation",t)},expression:"analysisForm.bearingSituation"}})],1),a("el-form-item",{attrs:{label:"应急措施",prop:"emergencyMeasures"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{type:"textarea",maxlength:"200",disabled:e.analysisDisabled},model:{value:e.analysisForm.emergencyMeasures,callback:function(t){e.$set(e.analysisForm,"emergencyMeasures",t)},expression:"analysisForm.emergencyMeasures"}})],1)],1),a("span",{directives:[{name:"show",rawName:"v-show",value:!e.analysisDisabled,expression:"!analysisDisabled"}],staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.analysisSubmit}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.analysisDialog=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"执行记录",visible:e.recordDialog,width:"720px","append-to-body":""},on:{"update:visible":function(t){e.recordDialog=t}}},[a("div",{staticClass:"block"},[a("el-timeline",[a("el-timeline-item",{attrs:{timestamp:"事件接报",placement:"top"}},[a("el-card",[a("div",[e._v(" 上报时间："+e._s(e.eventReport.reportTime?e.eventReport.reportTime:"")+" ")]),a("div",[e._v(" 上报人："+e._s(e.eventReport.submitPerson)+" "+e._s(e.eventReport.contactNumber)+" ")]),a("div",[e._v("审核时间："+e._s(e.eventReport.auditTime))]),a("div",[e._v("审核人："+e._s(e.eventReport.auditPerson))])])],1),a("el-timeline-item",{attrs:{timestamp:"预案启动",placement:"top"}},[a("el-card",e._l(e.executionRecord.planStarts,(function(t,i){return a("div",{key:i,staticStyle:{margin:"10px 0"}},[a("div",[e._v("预案名称："+e._s(t.planName))]),a("div",[e._v("开始时间："+e._s(t.startTime))]),a("div",[e._v("处理人员："+e._s(t.handler))])])})),0)],1),a("el-timeline-item",{attrs:{timestamp:"持续调度",placement:"top"}},[a("el-card",e._l(e.executionRecord.continuousDispatch,(function(t,i){return a("div",{key:i,staticStyle:{margin:"10px 0"}},[a("div",{style:5012901==t.assignTaskType?"color:red":"color:green"},[e._v(" "+e._s(e.dict.type.assign_task_type.find((function(e){return e.value==t.assignTaskType}))?e.dict.type.assign_task_type.find((function(e){return e.value==t.assignTaskType})).label:"")+" ")]),a("div",[e._v("任务开始时间："+e._s(t.startTime?t.startTime:""))]),a("div",[e._v("指派队伍："+e._s(t.contingentName))]),a("div",[e._v(" 任务"+e._s("5012603"==t.taskStatus?"取消":"结束")+"时间："+e._s("5012601"==t.taskStatus?"":t.updateTime)+" ")])])})),0)],1),a("el-timeline-item",{attrs:{timestamp:"结束救援",placement:"top"}},[a("el-card",[a("div",[e._v(" 结束时间："+e._s(e.executionRecord.endRescue?e.executionRecord.endRescue.endTime:"")+" ")])])],1)],1)],1)]),a("el-dialog",{attrs:{title:"信息发布",visible:e.informationDialog,width:"560px","append-to-body":""},on:{"update:visible":function(t){e.informationDialog=t}}},[a("div",{staticStyle:{"margin-bottom":"20px"}},[a("el-button",{attrs:{type:"primary",plain:e.plain1},on:{click:function(t){return e.programClick(1)}}},[e._v("信息发布节目 ")]),a("el-button",{attrs:{type:"primary",plain:e.plain2},on:{click:function(t){return e.programClick(2)}}},[e._v("广播节目 ")])],1),a("el-form",{ref:"informationForm",attrs:{model:e.informationForm,rules:e.rules,"label-width":"120px"}},[e.plain2?a("el-form-item",{attrs:{label:"应急发布名称",prop:"name"}},[a("el-input",{attrs:{maxlength:"20",placeholder:"请输入应急发布名称",disabled:e.disabled},model:{value:e.informationForm.name,callback:function(t){e.$set(e.informationForm,"name",t)},expression:"informationForm.name"}})],1):e._e(),e.plain1?a("el-form-item",{attrs:{label:"应急广播名称",prop:"emergencyName"}},[a("el-input",{attrs:{maxlength:"20",placeholder:"请输入应急发布名称",disabled:e.disabled},model:{value:e.informationForm.emergencyName,callback:function(t){e.$set(e.informationForm,"emergencyName",t)},expression:"informationForm.emergencyName"}})],1):e._e(),e.plain2?a("el-form-item",{attrs:{label:"发布素材",prop:"materialId"}},[a("div",{staticStyle:{display:"flex"}},[a("el-select",{staticStyle:{width:"100%","margin-right":"10px"},attrs:{placeholder:"发布素材",clearable:"",disabled:e.disabled},on:{change:e.getUrl},model:{value:e.informationForm.materialId,callback:function(t){e.$set(e.informationForm,"materialId",t)},expression:"informationForm.materialId"}},e._l(e.materialData,(function(e){return a("el-option",{key:e.id,attrs:{label:e.materialName,value:e.id}})})),1),a("el-button",{attrs:{type:"primary",size:"mini",disabled:e.disabled},on:{click:e.upload}},[e._v("上 传 ")])],1)]):e._e(),e.plain1?a("el-row",[a("el-col",{attrs:{span:18}},[e.textFlag?e._e():a("el-form-item",{attrs:{label:"发布素材",prop:"audioMaterialId"}},[a("div",{staticStyle:{display:"flex"}},[a("el-select",{staticStyle:{width:"100%","margin-right":"10px"},attrs:{placeholder:"发布素材",clearable:"",disabled:e.disabled},on:{change:e.getUrl},model:{value:e.informationForm.audioMaterialId,callback:function(t){e.$set(e.informationForm,"audioMaterialId",t)},expression:"informationForm.audioMaterialId"}},e._l(e.materialData,(function(e){return a("el-option",{key:e.id,attrs:{label:e.materialName,value:e.id}})})),1),a("el-button",{attrs:{type:"primary",size:"mini",disabled:e.disabled},on:{click:e.upload}},[e._v("上 传 ")])],1)]),e.textFlag?a("el-form-item",{attrs:{label:"输入文字素材",prop:"textMaterial"}},[a("el-input",{attrs:{type:"textarea",maxlength:"200",placeholder:"输入文字素材",disabled:e.disabled},model:{value:e.informationForm.textMaterial,callback:function(t){e.$set(e.informationForm,"textMaterial",t)},expression:"informationForm.textMaterial"}})],1):e._e()],1),a("el-col",{staticStyle:{"text-align":"center","margin-top":"3px"},attrs:{span:6}},[a("el-button",{attrs:{type:"primary",size:"mini",disabled:e.disabled},on:{click:e.textConten}},[e._v(e._s(e.textFlag?"发布素材":"文字内容")+" ")])],1)],1):e._e(),e.plain2?a("el-form-item",{attrs:{label:"发布终端",prop:"terminalIdArr"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"发布终端",clearable:"",multiple:"",disabled:e.disabled},model:{value:e.informationForm.terminalIdArr,callback:function(t){e.$set(e.informationForm,"terminalIdArr",t)},expression:"informationForm.terminalIdArr"}},e._l(e.terminalList,(function(e){return a("el-option",{key:e.terminalId,attrs:{label:e.terminalName,value:e.terminalId}})})),1)],1):e._e(),e.plain1?a("el-form-item",{attrs:{label:"发布终端",prop:"terminalIdArr1"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"发布终端",clearable:"",multiple:"",disabled:e.disabled},model:{value:e.informationForm.terminalIdArr1,callback:function(t){e.$set(e.informationForm,"terminalIdArr1",t)},expression:"informationForm.terminalIdArr1"}},e._l(e.terminalList1,(function(e){return a("el-option",{key:e.terminalId,attrs:{label:e.terminalName,value:e.terminalId}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"发布时间",prop:"dange"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetimerange","value-format":"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:e.disabled},model:{value:e.informationForm.dange,callback:function(t){e.$set(e.informationForm,"dange",t)},expression:"informationForm.dange"}})],1),e.plain2?a("el-form-item",{attrs:{label:"应急发布原因",prop:"emergencyReason"}},[a("el-input",{attrs:{type:"textarea",maxlength:"200",placeholder:"应急发布原因",disabled:e.disabled},model:{value:e.informationForm.emergencyReason,callback:function(t){e.$set(e.informationForm,"emergencyReason",t)},expression:"informationForm.emergencyReason"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.disabled,expression:"!disabled"}],attrs:{type:"primary"},on:{click:function(t){return e.submitForm("6010703")}}},[e._v("创 建 ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.disabled,expression:"!disabled"}],on:{click:e.informationCancel}},[e._v("取 消")])],1),a("el-dialog",{attrs:{title:"新增素材",visible:e.openmaterial,width:"560px","append-to-body":""},on:{"update:visible":function(t){e.openmaterial=t}}},[a("el-form",{ref:"formmaterial",attrs:{model:e.formmaterial,rules:e.rulesmaterial,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"素材名称",prop:"materialName"}},[a("el-input",{attrs:{placeholder:"请输入素材名称",maxlength:"20"},model:{value:e.formmaterial.materialName,callback:function(t){e.$set(e.formmaterial,"materialName",t)},expression:"formmaterial.materialName"}})],1),a("el-form-item",{attrs:{label:"素材类型",prop:"materialType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"素材类型",clearable:""},model:{value:e.formmaterial.materialType,callback:function(t){e.$set(e.formmaterial,"materialType",t)},expression:"formmaterial.materialType"}},e._l(e.dict.type.information_material_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"素材上传",prop:"url"}},[a("el-upload",{ref:"my-upload",attrs:{action:e.uploadFile,"on-remove":e.handleRemove,"on-success":e.handlePreview,"file-list":e.fileList,limit:1,"before-upload":e.beforeUpload,disabled:!e.formmaterial.materialType}},[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-upload2"}},[e._v("选择素材(*只能上传一份) ")])],1),a("span",{directives:[{name:"show",rawName:"v-show",value:"6010101"==e.formmaterial.materialType,expression:"formmaterial.materialType == '6010101'"}],staticClass:"limit"},[e._v(" 图片(支持:jpeg/jpg、png、gif、bmp、tiff、webp、svg ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:"6010102"==e.formmaterial.materialType,expression:"formmaterial.materialType == '6010102'"}],staticClass:"limit"},[e._v(" 视频(支持:mp4、avi、mkv、mov、wmv、flv、webm、mpeg ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:"6010103"==e.formmaterial.materialType,expression:"formmaterial.materialType == '6010103'"}],staticClass:"limit"},[e._v("音频(支持:mp3、wav、aac、flac、ogg、wma")]),a("span",{directives:[{name:"show",rawName:"v-show",value:"6010104"==e.formmaterial.materialType,expression:"formmaterial.materialType == '6010104'"}],staticClass:"limit"},[e._v("文档(支持:pdf、word、excel、ppt、txt")])],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFormmaterial}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelmaterial}},[e._v("取 消")])],1)],1)],1),e.open?a("el-dialog",{attrs:{title:e.titleFire+"影响分析",visible:e.open,width:"1000px","append-to-body":""},on:{"update:visible":function(t){e.open=t},close:e.clickClose}},[a("el-radio-group",{staticStyle:{"margin-bottom":"10px"},model:{value:e.titleFire,callback:function(t){e.titleFire=t},expression:"titleFire"}},[a("el-radio",{attrs:{label:"火灾"}},[e._v("火灾")]),a("el-radio",{attrs:{label:"物理爆炸"}},[e._v("物理爆炸")]),a("el-radio",{attrs:{label:"气体泄露"}},[e._v("气体泄露")])],1),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"火灾"==e.titleFire?"230px":"170px"}},["火灾"==e.titleFire?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日最高温度(T)",prop:"temperature"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入大于0-50的日最高温度"},model:{value:e.form.temperature,callback:function(t){e.$set(e.form,"temperature",t)},expression:"form.temperature"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日最小湿度(h)",prop:"humidity"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入0-80内的日最小湿度"},on:{input:function(t){return e.checkMaxHumidity(e.form.humidity)}},model:{value:e.form.humidity,callback:function(t){e.$set(e.form,"humidity",t)},expression:"form.humidity"}})],1)],1)],1):e._e(),"火灾"==e.titleFire?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日平均风级(W)",prop:"windScale"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入0-12的日平均风级"},on:{input:function(t){return e.checkMaxWindScale(e.form.windScale)}},model:{value:e.form.windScale,callback:function(t){e.$set(e.form,"windScale",t)},expression:"form.windScale"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"风速(m/min)",prop:"windSpeed"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入0-20风速"},model:{value:e.form.windSpeed,callback:function(t){e.$set(e.form,"windSpeed",t)},expression:"form.windSpeed"}})],1)],1)],1):e._e(),"火灾"==e.titleFire?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"地形坡度(W)",prop:"slope"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入小于等于60的地形坡度"},on:{input:function(t){return e.checkMaxSlope(e.form.slope)}},model:{value:e.form.slope,callback:function(t){e.$set(e.form,"slope",t)},expression:"form.slope"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"风向",prop:"windDirection"}},[a("el-select",{staticStyle:{width:"10vw"},attrs:{placeholder:"请选择风向",clearable:""},model:{value:e.form.windDirection,callback:function(t){e.$set(e.form,"windDirection",t)},expression:"form.windDirection"}},e._l(e.dict.type.fire_wind,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1):e._e(),"火灾"==e.titleFire?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"时间",prop:"time"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入时间"},model:{value:e.form.time,callback:function(t){e.$set(e.form,"time",t)},expression:"form.time"}})],1)],1)],1):e._e(),"物理爆炸"==e.titleFire?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"爆炸物料总质量(Wf)",prop:"weight"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入小于10000的爆炸物料总质量"},model:{value:e.form.weight,callback:function(t){e.$set(e.form,"weight",t)},expression:"form.weight"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"爆炸物料名称",prop:"matterId"}},[a("el-select",{staticClass:"text-case-type-select",staticStyle:{width:"245px"},attrs:{filterable:"",placeholder:"请选择爆炸物料名称"},model:{value:e.form.matterId,callback:function(t){e.$set(e.form,"matterId",t)},expression:"form.matterId"}},e._l(e.optoion1,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1):e._e(),"气体泄露"==e.titleFire?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日最高温度(T)",prop:"temperature"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入0-50的日最高温度"},model:{value:e.form.temperature,callback:function(t){e.$set(e.form,"temperature",t)},expression:"form.temperature"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"容积(m³)",prop:"weight"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入小于10000的容积"},model:{value:e.form.weight,callback:function(t){e.$set(e.form,"weight",t)},expression:"form.weight"}})],1)],1)],1):e._e(),"气体泄露"==e.titleFire?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"爆炸物料名称",prop:"matterId"}},[a("el-select",{staticClass:"text-case-type-select",staticStyle:{width:"245px"},attrs:{filterable:"",placeholder:"请选择爆炸物料名称"},model:{value:e.form.matterId,callback:function(t){e.$set(e.form,"matterId",t)},expression:"form.matterId"}},e._l(e.optoion,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"风向",prop:"windDirection"}},[a("el-select",{staticStyle:{width:"10vw"},attrs:{placeholder:"请选择风向",clearable:""},model:{value:e.form.windDirection,callback:function(t){e.$set(e.form,"windDirection",t)},expression:"form.windDirection"}},e._l(e.dict.type.fire_wind,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1):e._e(),"气体泄露"==e.titleFire?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"风速(m/min)",prop:"windSpeed"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入0-20的风速"},model:{value:e.form.windSpeed,callback:function(t){e.$set(e.form,"windSpeed",t)},expression:"form.windSpeed"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"时间",prop:"time"}},[a("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入时间"},model:{value:e.form.time,callback:function(t){e.$set(e.form,"time",t)},expression:"form.time"}})],1)],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFormAcc}},[e._v("确 定")]),a("el-button",{on:{click:e.clickCloseAcc}},[e._v("取 消")])],1)],1):e._e(),a("el-dialog",{attrs:{title:"疏解人员",visible:e.mitigateDialog,width:"560px","append-to-body":""},on:{"update:visible":function(t){e.mitigateDialog=t}}},[a("el-form",{ref:"mitigateForm",attrs:{model:e.mitigateForm,rules:e.mitigateRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"疏解人数",prop:"personCount"}},[a("el-input",{staticStyle:{width:"245px"},model:{value:e.mitigateForm.personCount,callback:function(t){e.$set(e.mitigateForm,"personCount",t)},expression:"mitigateForm.personCount"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.mitigateSubmit}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.mitigateDialog=!1}}},[e._v("取 消")])],1)],1)],1)},n=[],s=a("c7eb"),o=a("1da1"),r=a("5530"),l=a("ade3"),c=(a("d81d"),a("d3b7"),a("159b"),a("ac1f"),a("00b4"),a("e9c4"),a("14d9"),a("a9e3"),a("25f0"),a("a15b"),a("7db0"),a("fb6a"),a("b0c0"),a("b64b"),a("802f")),d=a("450d"),u=a("cf65"),m=a("58b9"),p=a("3eed"),f=a("5c21"),g={name:"EmergencySupplies",dicts:["plan_deduction","response_level","step_status","contingent_status","materiel_type","task_type","task_status","assign_task_type","event_level","information_material_type","emergency_dispatch_state","fire_wind"],components:{Map:f["default"],jessibucaPlayer:m["default"]},data:function(){var e;return e={hasAudio:!1,ranksRadio:"1",goodsRadio:"1",wea:{},videoVisible:!1,videoUrl:null,url:"".concat(a("3645")),rankUrl:"".concat(a("9a55")),goodUrl:"".concat(a("bdb4")),gasUrl:"".concat(a("d4b3")),monitorUrl:"".concat(a("2310")),monitorUrl1:"".concat(a("da78")),treeData:[],defaultProps:{children:"children",label:"nodeName"},loading:!1,planLoading:!1,showSearch:!0,resourcefulSearch:!1,eventList:[],text:void 0,dateRange:[],reportDialog:!1,reportDetail:{},commandDialog:!1,activeName:"first",launchPlanDialog:!1,planForm:{planName:void 0,planType:void 0},planList:[],queryParams:{current:1,size:1e3,eventNo:void 0,eventName:void 0,eventTypeName:void 0,eventTypeId:void 0,eventLevel:void 0,startTime:"",endTime:"",dispatchStateList:[]},eventListDialog:!0,eventDetaildialog:!1,eventDetail:{},range:"200m",checkList:[],dispatch:"rank",changeNew:"93001",ranksList:[],monitorStationList:[],gasDialog:!1,gasDetailData:[],videoDetailData:[],goodsList:[],planRadio:"",levelFrom:{level:void 0},levelBox:[],levelRules:{level:[{required:!0,message:"请选择响应等级",trigger:"blur"}]},assignDialog:!1,assignFrom:{taskDescription:void 0,finishTime:void 0,isSmsNotice:void 0},assignRules:{taskDescription:[{required:!0,message:"请输入任务描述",trigger:"blur"}],time:[{required:!0,message:"请选择完成时间",trigger:"blur"}],isSmsNotice:[{required:!0,message:"请选择是否短信通知",trigger:"blur"}]},planBox:[],planRow:void 0,stepList:[],activities:[],tasksData:[],sendDialog:!1,rankDisabled:!1,goodDisabled:!1,sendTitle:"",sendFlag:"rank",ranksOptions:[],sendrankFrom:{contingentId:void 0,taskRemark:void 0,finishTime:void 0,isInform:void 0},sendrankRules:{contingentId:[{required:!0,message:"请选择指派队伍",trigger:"change"}],taskRemark:[{required:!0,message:"请输入任务备注",trigger:"blur"}],finishTime:[{required:!0,message:"请选择期望时间",trigger:"change"}],isInform:[{required:!0,message:"请选择是否通知",trigger:"change"}]},depotOptions:[],sendgoodFrom:{depotId:void 0,taskRemark:void 0,finishTime:void 0,isInform:void 0},goodsData:[],sendgoodRules:{depotId:[{required:!0,message:"请选择指派队伍",trigger:"change"}],taskRemark:[{required:!0,message:"请输入任务备注",trigger:"blur"}],finishTime:[{required:!0,message:"请选择期望时间",trigger:"change"}],isInform:[{required:!0,message:"请选择是否通知",trigger:"change"}]},secondDialog:!1,analysisDialog:!1,secondaryAnalysisTitle:void 0,secondData:[],secondQueryParams:{current:1,size:10},total:0,analysisDisabled:!1,analysisForm:{secondaryDisaster:void 0,noticePersonId:[],elementAnalysis:void 0,bearingSituation:void 0,emergencyMeasures:void 0},analysisRules:{},mitigateForm:{},mitigateRules:{},mitigateDialog:!1,routeForm:{type:1},ranksRouteForm:{type:1},goodsRouteForm:{type:1},recordDialog:!1,executionRecord:{},eventReport:{},informationDialog:!1,informationForm:{},formmaterial:{},rulesmaterial:{materialName:[{required:!0,message:"素材名称不能为空",trigger:"blur"}],materialType:[{required:!0,message:"素材类型不能为空",trigger:"blur"}]},rules:{name:[{required:!0,message:"请输入应急发布名称",trigger:"blur"}],emergencyName:[{required:!0,message:"请输入应急发布名称",trigger:"blur"}],materialId:[{required:!0,message:"发布素材不能为空",trigger:"change"}],audioMaterialId:[{required:!0,message:"发布素材不能为空",trigger:"change"}],terminalIdArr:[{required:!0,message:"发布终端不能为空",trigger:"blur"}],terminalIdArr1:[{required:!0,message:"发布终端不能为空",trigger:"blur"}],dange:[{required:!0,message:"发布时间不能为空",trigger:"change"}],emergencyReason:[{required:!0,message:"应急发布原因不能为空",trigger:"blur"}],textMaterial:[{required:!0,message:"文字素材不能为空",trigger:"blur"}]},plain1:!1,plain2:!0,textFlag:!1,fileList:[],uploadFile:"/file/informationUploadFile",terminalList:[],terminalList1:[],openmaterial:!1,disabled:!1,materialData:[],accidentPoint:{longitude:null,latitude:null},initialPoint:{longitude:115.128757,latitude:37.611534},longAdd:4e-4,latAdd:37e-5,saveData:[],titleFire:"火灾",open:!1,form:{}},Object(l["a"])(e,"rules",{temperature:[{required:!0,message:"请输入",trigger:"blur"},{min:1,max:20,message:"长度限制在1到20个字符",trigger:"blur"},{pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message:"请输入非负数",trigger:"blur"}],humidity:[{required:!0,message:"请输入",trigger:"blur"},{min:1,max:20,message:"长度限制在1到20个字符",trigger:"blur"},{pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message:"请输入非负数",trigger:"blur"}],ks:[{required:!0,message:"请选择",trigger:"select"}],windSpeed:[{required:!0,message:"请输入",trigger:"blur"},{min:1,max:20,message:"长度限制在1到20个字符",trigger:"blur"},{pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message:"请输入非负数",trigger:"blur"}],slope:[{required:!0,message:"请输入",trigger:"blur"},{min:1,max:20,message:"长度限制在1到20个字符",trigger:"blur"},{pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message:"请输入非负数",trigger:"blur"}],KW:[{required:!0,message:"请选择",trigger:"select"}],kd:[{required:!0,message:"请输入",trigger:"blur"},{min:1,max:20,message:"长度限制在1到20个字符",trigger:"blur"},{pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message:"请输入非负数",trigger:"blur"}],weight:[{required:!0,message:"请输入",trigger:"blur"},{min:1,max:20,message:"长度限制在1到20个字符",trigger:"blur"},{pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message:"请输入非负数",trigger:"blur"}],matterId:[{required:!0,message:"请选择",trigger:"select"}],windScale:[{required:!0,message:"请输入",trigger:"blur"},{pattern:/^(0|[1-9]\d{0,1})$/,message:"请输入2位以内非负整数",trigger:"blur"}]}),Object(l["a"])(e,"KS",null),Object(l["a"])(e,"KW",null),Object(l["a"])(e,"selectOptionWidth",null),Object(l["a"])(e,"returnData",[]),Object(l["a"])(e,"ranksPoint",[]),Object(l["a"])(e,"goodsPoint",[]),e},watch:{},created:function(){this.getList()},mounted:function(){var e=this;this.getDict(),console.log(this.dict.type.event_level,"ssssssssssssssss"),this.getTree(),this.$refs.mapRef.initMap(),Object(c["o"])({resourceType:5013001}).then((function(t){e.goodsList=t.data.list})),Object(c["o"])({resourceType:5013002}).then((function(t){e.ranksList=t.data.list})),Object(u["d"])().then((function(t){e.monitorStationList=t.data})),Object(c["o"])({resourceType:5013004}).then((function(t){e.videoDetailData=t.data.list,console.log(e.videoDetailData)}))},methods:{clickAcc:function(){this.open=!0,this.form={},this.simulatedAccident()},clickRun:function(){this.$refs.mapRef.runpolyline()},clickClose:function(){},clickCloseAcc:function(){this.open=!1,this.choose=null},submitFormAcc:function(){var e=this,t=parseInt((this.accidentPoint.longitude-this.initialPoint.longitude)/this.longAdd),a=parseInt((this.initialPoint.latitude-this.accidentPoint.latitude)/this.latAdd);t<=0?this.$message.error("请选择园区内事件进行事故模拟"):(this.$refs.form.validate((function(i){if(!i)return!1;e.$set(e.form,"longitude",e.accidentPoint.longitude),e.$set(e.form,"latitude",e.accidentPoint.latitude),e.$set(e.form,"x",t),e.$set(e.form,"y",a);var n={"火灾":2,"物理爆炸":3,"气体泄露":1};Object(d["a"])(Object(r["a"])(Object(r["a"])({},e.form),{},{analysisModel:n[e.titleFire]})).then((function(t){if(console.log(t),e.$refs.mapRef.clearAllMap(),"火灾"==e.titleFire||"气体泄露"==e.titleFire)e.returnData=t.data.spreadModel,e.analyzPointLocations();else{e.accidentPoint.longitude,e.accidentPoint.latitude;e.$refs.mapRef.clickExplosion(e.accidentPoint.longitude,e.accidentPoint.latitude,t.data)}e.$refs.mapRef.marker(e.accidentPoint.longitude,e.accidentPoint.latitude,e.url,!0,!0),e.open=!1}))})),this.choose=null)},getDict:function(){var e=this;Object(d["b"])({type:"KS"}).then((function(t){e.KS=t.data})),Object(d["b"])({type:"KW"}).then((function(t){e.KW=t.data})),Object(d["c"])({analysisType:1}).then((function(t){e.optoion1=t.data})),Object(d["c"])({analysisType:2}).then((function(t){e.optoion=t.data}))},toMercator:function(e,t){var a=proj4("EPSG:4326","EPSG:3857",[e,t]);return[a[0],a[1]]},toWGS84:function(e,t){var a=proj4("EPSG:3857","EPSG:4326",[e,t]);return[a[0],a[1]]},analyzPointLocations:function(){console.log(this.returnData),this.saveData=[],this.saveData=this.returnData.map((function(e){return{lnglat:e.split(",")}})),this.$refs.mapRef.clickMass(this.saveData),console.log(this.saveData)},simulatedAccident:function(){var e=parseInt((this.accidentPoint.longitude-this.initialPoint.longitude)/this.longAdd),t=parseInt((this.accidentPoint.latitude-this.initialPoint.latitude)/this.latAdd);console.log(e),console.log(t)},rangeChange:function(e){this.$refs.mapRef.circleRange(this.eventDetail.longitude,this.eventDetail.latitude,e),this.$refs.mapRef.clearAllList(),this.checkList=[],this.ranksList.forEach((function(t){t.distance<e?t.show=!0:t.show=!1})),this.goodsList.forEach((function(t){t.distance<e?t.show=!0:t.show=!1})),this.overview(e),this.$forceUpdate()},overview:function(e){for(var t=this,a=["shelter","monitor","broadcast","risk","communicate","protection","medical"],i=function(i){Object(c["o"])({resourceType:5013003+i}).then((function(n){n.data.list.forEach((function(n){n.distance=t.$refs.mapRef.getDistance(n.longitude,n.latitude,t.eventDetail.longitude,t.eventDetail.latitude),e&&n.distance<e&&t.$refs.mapRef.markerList(n.longitude,n.latitude,a[i],n)}))}))},n=0;n<7;n++)i(n)},checkNum:function(e){var t=/^\+?[1-9][0-9]*$/;t.test(e.dispatchQuantity)||(e.dispatchQuantity=""),console.log(e)},rankPosition:function(e){this.$refs.mapRef.marker1(e.longitude,e.latitude,this.rankUrl,!1,!1,e)},gasPosition:function(e){this.$refs.mapRef.marker1(e.longitude,e.latitude,this.gasUrl,!1,!1,e)},videoPosition:function(e){1==e.isOnline?this.$refs.mapRef.marker1(e.longitude,e.latitude,this.monitorUrl,!1,!1,e):this.$refs.mapRef.marker1(e.longitude,e.latitude,this.monitorUrl1,!1,!1,e)},clickVideo:function(e){var t=this;console.log(e),this.videoVisible=!0,Object(p["a"])({equipmentIdList:[e.deviceId]}).then((function(e){console.log(e.data[0].flvAddress,"测试123"),t.$nextTick((function(){t.videoUrl=t.changeUrl(e.data[0].flvAddress),t.$refs.videoPlayer.play(t.videoUrl)}))}))},videoError:function(e){console.log("播放器错误："+JSON.stringify(e))},clickGas:function(e){var t=this;console.log(e),this.gasDialog=!0,this.gasDetailData=[],Object(u["c"])({monitoringId:e.id}).then((function(e){for(var a in console.log(e.data),e.data)e.data[a].forEach((function(e){console.log(e),t.gasDetailData.push(e)}));console.log(t.gasDetailData)}))},goodPosition:function(e){this.$refs.mapRef.marker(e.longitude,e.latitude,this.goodUrl,!1,!1,e)},getLocation:function(){AMap.plugin("AMap.CitySearch",(function(){var e=new AMap.CitySearch;e.getLocalCity((function(e,t){"complete"===e&&"OK"===t.info&&(address.value=t.city,console.log("hsdkjshdfk",t),AMap.plugin("AMap.Weather",(function(){var e=new AMap.Weather;e.getForecast(t.city,(function(e,t){var a=t.forecasts[0];console.log(a),temperature.low=a.nightTemp,temperature.high=a.dayTemp,weather.value=a.dayWeather,console.log(weather)}))})))}))}))},weather:function(e){console.log(e),this.wea=e},toDetile:function(e){console.log(e),this.$set(this.mitigateForm,"id",e.id),this.$set(this.routeForm,"destination",e.longitude+","+e.latitude),this.mitigateDialog=!0},rankDispatch:function(e){console.log("duiwu---------------------",e),null!=e.longitude&&null!=e.latitude&&(this.ranksPoint=[Number(e.longitude),Number(e.latitude)],console.log(this.ranksPoint),this.$set(this.ranksRouteForm,"origin",+e.longitude+","+e.latitude)),this.sendTask(),this.sendrankFrom.contingentId=e.id.toString()},goodDispatch:function(e){null!=e.longitude&&null!=e.latitude&&(console.log(0x650e124ef1c7,e),this.goodsPoint=[Number(e.longitude),Number(e.latitude)],this.$set(this.goodsRouteForm,"origin",+e.longitude+","+e.latitude)),this.sendTask(),this.sendFlag="good",this.sendgoodFrom.depotId=e.id.toString(),this.depotChange(e.id)},checkListChange:function(e){-1!=e.indexOf("5013003")?this.$refs.mapRef.markerListAdd("shelter"):this.$refs.mapRef.markerListRemove("shelter"),-1!=e.indexOf("5013005")?this.$refs.mapRef.markerListAdd("broadcast"):this.$refs.mapRef.markerListRemove("broadcast"),-1!=e.indexOf("5013004")?this.$refs.mapRef.markerListAdd("monitor"):this.$refs.mapRef.markerListRemove("monitor"),-1!=e.indexOf("5013008")?this.$refs.mapRef.markerListAdd("protection"):this.$refs.mapRef.markerListRemove("protection"),-1!=e.indexOf("5013009")?this.$refs.mapRef.markerListAdd("medical"):this.$refs.mapRef.markerListRemove("medical"),-1!=e.indexOf("5013006")?this.$refs.mapRef.markerListAdd("risk"):this.$refs.mapRef.markerListRemove("risk"),-1!=e.indexOf("5013007")?this.$refs.mapRef.markerListAdd("communicate"):this.$refs.mapRef.markerListRemove("communicate")},rangeChangeNew:function(e){console.log(e),"93002"==e&&(console.log("xcxcxcxccc"),this.$refs.mapRef.nowWeather())},searchWea:function(){},getList:function(){var e=this;this.loading=!0,Object(c["p"])(Object(r["a"])(Object(r["a"])({},this.queryParams),{},{dispatchStateList:this.queryParams.dispatchStateList.join(",")})).then((function(t){console.log(t,"response"),null!=t.data&&(e.eventList=t.data),e.loading=!1}))},getTree:function(){var e=this;Object(c["j"])().then((function(t){console.log(t,"测试杀杀杀"),200==t.code&&(e.recursion(t.data),e.treeData=t.data)}))},handleNodeClick:function(e,t,a){t?(this.$refs.Addtree.setCheckedNodes([e]),this.queryParams.eventTypeId=e.id,this.queryParams.eventTypeName=e.nodeName):(this.queryParams.eventTypeId=void 0,this.queryParams.eventTypeName=void 0)},eventLabelChange:function(e){this.$forceUpdate()},recursion:function(e){var t=this;e.forEach((function(e,a){if(e.children)return e.disabled=!0,t.recursion(e.children);e.disabled=!1}))},eventClick:function(e){var t=this;this.range="",this.checkList=[],this.$refs.mapRef.clearCircle(),this.stepList=[],this.activities=[],this.eventDetail=e,console.log(e),this.$set(this.mitigateForm,"drillId",e.id),this.accidentPoint.longitude=e.longitude,this.accidentPoint.latitude=e.latitude,this.$set(this.routeForm,"origin",+e.longitude+","+e.latitude),this.$set(this.ranksRouteForm,"destination",+e.longitude+","+e.latitude),this.$set(this.goodsRouteForm,"destination",+e.longitude+","+e.latitude),console.log(this.accidentPoint),console.log(this.eventDetail.isEndRescue,"ssssss"),this.$refs.mapRef.marker(e.longitude,e.latitude,this.url,!0,!0),this.$refs.mapRef.removeLine(),this.ranksList.forEach((function(a){a.show=!0,a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,e.longitude,e.latitude)})),this.goodsList.forEach((function(a){a.show=!0,a.distance=t.$refs.mapRef.getDistance(a.longitude,a.latitude,e.longitude,e.latitude)})),Object(c["o"])({resourceType:5013003}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"shelter",e)}))})),Object(c["o"])({resourceType:5013004}).then((function(e){e.data.list.forEach((function(e){console.log(e,"element"),0==e.isOnline?t.$refs.mapRef.markerList(e.longitude,e.latitude,"monitor",e):1==e.isOnline&&t.$refs.mapRef.markerList(e.longitude,e.latitude,"monitor1",e)}))})),Object(c["o"])({resourceType:5013005}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"broadcast",e)}))})),Object(c["o"])({resourceType:5013006}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"risk",e)}))})),Object(c["o"])({resourceType:5013007}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"communicate",e)}))})),Object(c["o"])({resourceType:5013008}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"protection",e)}))})),Object(c["o"])({resourceType:5013009}).then((function(e){e.data.list.forEach((function(e){t.$refs.mapRef.markerList(e.longitude,e.latitude,"medical",e)}))})),this.resourcefulSearch=!0},detailDialog:function(e){this.eventDetaildialog=e},handleReport:function(){var e=this;this.reportDialog=!0,Object(c["u"])({id:this.eventDetail.id}).then((function(t){console.log(t),e.reportDetail=t.data}))},handleCommand:function(){var e=this;this.commandDialog=!0,console.log(this.eventDetail.businessTypeId,"ssss"),Object(c["x"])({businessTypeId:this.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(t){t.responseName=e.dict.type.response_level.find((function(e){return e.value==t.responseLevel})).label})),console.log(t.data,"sssssswww"),e.planBox=t.data})),Object(c["E"])({businessTypeId:this.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),console.log(t.data),e.tasksData=t.data}))},planClick:function(e){var t=this;this.planRow=e,console.log(this.eventDetail.businessTypeId),Object(c["y"])({responseLevelId:e.responseLevelId,businessTypeId:this.eventDetail.businessTypeId}).then((function(e){t.stepList=e.data})),Object(c["z"])({responseLevelId:e.responseLevelId,businessTypeId:this.eventDetail.businessTypeId}).then((function(e){console.log(e.data),t.activities=e.data}))},commandClick:function(e,t){console.log(e,t)},endRescue:function(){var e=this,t=this;this.$modal.confirm("是否确认结束当前救援").then((function(){return Object(c["t"])({businessTypeId:t.eventDetail.businessTypeId})})).then((function(t){console.log(t),"请确认临时任务状态不是未开始"!=t.msg&&"无法结束救援"!=t.msg&&"步骤状态不能是进行中"!=t.msg?(e.reportDialog=!1,e.$modal.msgSuccess("救援已成功结束！"),e.commandDialog=!1,e.eventDetaildialog=!1,e.$refs.mapRef.clearMap(),e.getList()):e.$message.error(t.msg)})).catch((function(e){}))},cancel:function(){this.reportDialog=!1},launchPlan:function(){var e=this;this.planList=[],this.planRadio=void 0,this.levelFrom={},this.launchPlanDialog=!0,Object(c["q"])({businessTypeId:this.eventDetail.businessTypeId}).then((function(t){e.planList=t.data}))},planSearch:function(){var e=this;this.planLoading=!0,Object(c["q"])(Object.assign({businessTypeId:this.eventDetail.businessTypeId},this.planForm)).then((function(t){e.planList=t.data,e.planLoading=!1}))},planReset:function(){var e=this;this.planForm.planName=void 0,this.planForm.planType=void 0,this.planLoading=!0,Object(c["q"])(this.planForm).then((function(t){e.planList=t.data,e.planLoading=!1}))},planInput:function(e){var t=this;Object(c["l"])({planId:e}).then((function(e){e.data.forEach((function(e){e.responseName=t.dict.type.response_level.find((function(t){return t.value==e.responseLevel})).label})),t.levelBox=e.data}))},confirm:function(){var e=this;this.planRadio?this.$refs["levelFrom"].validate((function(t){if(!t)return console.log("error submit!!"),!1;var a={};a.responseLevelId=e.levelFrom.level,a.businessTypeId=e.eventDetail.businessTypeId,Object(c["r"])(a).then((function(t){200==t.code&&(e.launchPlanDialog=!1,e.$modal.msgSuccess("预案启动成功！"),console.log(e.eventDetail.businessTypeId,"sssssss"),Object(c["x"])({businessTypeId:e.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(t){console.log(t,"planBox"),t.responseName=e.dict.type.response_level.find((function(e){return e.value==t.responseLevel})).label})),e.planBox=t.data})))}))})):this.$modal.msgWarning("请先选择预案！")},processUpdate:function(e,t){var a=this;Object(c["A"])({id:e,processStatus:t}).then((function(e){5011802==t?a.$modal.msgSuccess("当前流程开始处置！"):a.$modal.msgSuccess("当前流程结束步骤！"),a.planClick(a.planRow)}))},assign:function(e){this.$refs.assignFrom&&this.$refs.assignFrom.resetFields(),this.assignFrom.id=e.id,this.assignFrom.contingentId=e.contingentId,this.assignDialog=!0},finish:function(e,t){var a=this;this.$modal.confirm("是否确认完成当前指派").then((function(){return Object(c["h"])({id:e.id,contingentId:e.contingentId,contingentStatus:5011903})})).then((function(){a.planClick(a.planRow),a.$modal.msgSuccess("指派已完成!")})).catch((function(e){}))},assignConfirm:function(){var e=this;this.$refs["assignFrom"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.assignFrom.contingentStatus="5011902",Object(c["h"])(e.assignFrom).then((function(t){200==t.code&&(e.assignDialog=!1,e.$modal.msgSuccess("任务指派成功！"),e.planClick(e.planRow))}))}))},sendTask:function(){var e=this;this.sendTitle="增派临时任务",this.sendFlag="rank",this.rankDisabled=!1,this.goodDisabled=!1,this.sendrankFrom={contingentId:void 0,taskRemark:void 0,finishTime:void 0,isInform:void 0},this.sendgoodFrom={depotId:void 0,taskRemark:void 0,finishTime:void 0,isInform:void 0},this.goodsData=[],this.sendDialog=!0,Object(c["g"])().then((function(t){console.log(t,"手机号"),e.ranksOptions=t.data})),Object(c["i"])().then((function(t){e.depotOptions=t.data}))},depotChange:function(e){var t=this;Object(c["m"])({supplyDepotId:e}).then((function(e){t.goodsData=e.data,t.goodsData.forEach((function(e){t.$set(e,"dispatchQuantity","")}))}))},sendConfirm:function(){var e=this;"rank"==this.sendFlag?this.$refs["sendrankFrom"].validate((function(t){if(!t)return console.log("error submit!!"),!1;if(e.ranksOptions.forEach((function(t){e.sendrankFrom.contingentId==t.id&&(e.sendrankFrom.phone=t.phone)})),console.log(e.ranksRadio),"1"==e.ranksRadio){var a=new Date,i=a.getFullYear(),n=("0"+(a.getMonth()+1)).slice(-2),s=("0"+a.getDate()).slice(-2),o=("0"+a.getHours()).slice(-2),r=("0"+a.getMinutes()).slice(-2),l=("0"+a.getSeconds()).slice(-2),d=i+"-"+n+"-"+s+" "+o+":"+r+":"+l;console.log(d),e.$set(e.sendrankFrom,"finishTime",d)}Object(c["C"])({businessTypeId:e.eventDetail.businessTypeId,contingentVo:e.sendrankFrom,taskType:5012401}).then((function(t){200==t.code&&(e.$modal.msgSuccess("队伍指派成功！"),e.sendDialog=!1,Object(c["E"])({businessTypeId:e.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),e.tasksData=t.data})),"1"==e.ranksRadio&&Object(u["y"])(e.ranksRouteForm).then((function(t){if(t.data.steps&&t.data.steps.length>0){console.log(t.data.steps),console.log("///////////**************");var a=[];a.push(e.ranksPoint),t.data.steps.forEach((function(e){console.log(e.polyline.split(";"));var t=[];t=e.polyline.split(";");for(var i=0;i<t.length;i++)t[i]=t[i].split(","),a.push([Number(t[i][0]),Number(t[i][1])]);console.log(t,"resA")})),console.log(e.accidentPoint.longitude),a.push([Number(e.accidentPoint.longitude),Number(e.accidentPoint.latitude)]),console.log(a,"middlePath"),e.$refs.mapRef.runpolyline(a,e.ranksPoint,t.data.duration)}})))}))})):this.$refs["sendgoodFrom"].validate((function(t){if(!t)return console.log("error submit!!"),!1;if("1"==e.goodsRadio){var a=new Date,i=a.getFullYear(),n=("0"+(a.getMonth()+1)).slice(-2),s=("0"+a.getDate()).slice(-2),o=("0"+a.getHours()).slice(-2),r=("0"+a.getMinutes()).slice(-2),l=("0"+a.getSeconds()).slice(-2),d=i+"-"+n+"-"+s+" "+o+":"+r+":"+l;console.log(d),e.$set(e.sendgoodFrom,"finishTime",d)}e.sendgoodFrom.materialVos=[];var m=!1;e.goodsData.forEach((function(t){t.dispatchQuantity&&(console.log(t.dispatchQuantity),t.dispatchQuantity>t.inventory&&(m=!0),e.sendgoodFrom.materialVos.push({materialId:t.id,dispatchQuantity:t.dispatchQuantity}))})),console.log(m),m?e.$message({message:"调度量不能大于库存量",type:"warning"}):Object(c["C"])({businessTypeId:e.eventDetail.businessTypeId,depotVo:e.sendgoodFrom,taskType:5012402}).then((function(t){200==t.code&&(e.$modal.msgSuccess("物资调度成功！"),e.sendDialog=!1,Object(c["E"])({businessTypeId:e.eventDetail.businessTypeId}).then((function(t){t.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),e.tasksData=t.data})),"1"==e.goodsRadio&&Object(u["y"])(e.goodsRouteForm).then((function(t){if(t.data.steps&&t.data.steps.length>0){console.log(t.data.steps);var a=[];console.log("csxcsdcsdfdsf",a),a.push(e.goodsPoint),t.data.steps.forEach((function(e){console.log(e.polyline.split(";"));var t=[];t=e.polyline.split(";");for(var i=0;i<t.length;i++)t[i]=t[i].split(","),a.push([Number(t[i][0]),Number(t[i][1])]);console.log(t)})),a.push([Number(e.accidentPoint.longitude),Number(e.accidentPoint.latitude)]),console.log(a),e.$refs.mapRef.runpolyline(a,e.goodsPoint,t.data.duration)}})))}))}))},handleTaskDetail:function(e){var t=this;this.sendTitle="临时任务详情",Object(c["g"])().then((function(e){t.ranksOptions=e.data})),Object(c["i"])().then((function(e){t.depotOptions=e.data})),Object(c["B"])({id:e.id,taskType:e.taskType}).then((function(a){t.rankDisabled=!0,t.goodDisabled=!0,5012401==e.taskType?(t.sendFlag="rank",t.sendDialog=!0,t.sendrankFrom=a.data.contingentVo,t.sendrankFrom.contingentId=t.sendrankFrom.contingentId.toString()):(t.sendFlag="good",t.sendDialog=!0,t.sendgoodFrom=a.data.depotVo,t.sendgoodFrom.depotId=t.sendgoodFrom.depotId.toString(),t.goodsData=a.data.depotVo.materialVos)}))},handleTaskUpdate:function(e,t){var a=this;5012602==t?this.$modal.confirm("是否确认完成当前任务").then((function(){return Object(c["D"])({id:e.id,taskStatus:t})})).then((function(){a.$modal.msgSuccess("任务已完成!"),Object(c["E"])({businessTypeId:a.eventDetail.businessTypeId}).then((function(e){e.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),a.tasksData=e.data}))})).catch((function(e){})):5012603==t&&this.$modal.confirm("是否确认取消当前任务").then((function(){return Object(c["D"])({id:e.id,taskStatus:t})})).then((function(){a.$modal.msgSuccess("任务已取消!"),Object(c["E"])({businessTypeId:a.eventDetail.businessTypeId}).then((function(e){e.data.forEach((function(e){e.depotVo?e=Object.assign(e,e.depotVo):e.contingentVo&&(e=Object.assign(e,e.contingentVo))})),a.tasksData=e.data}))})).catch((function(e){}))},handleSecond:function(){this.secondDialog=!0,this.getSecondData()},getSecondData:function(){var e=this;this.secondQueryParams.businessTypeId=this.eventDetail.businessTypeId,this.loading=!0,Object(c["d"])(this.secondQueryParams).then((function(t){null!=t.data&&(e.secondData=t.data.records,e.total=t.data.total),e.loading=!1}))},handleSecondDetail:function(e){var t=this;Object(c["c"])({id:e.id}).then((function(e){console.log(e.data),t.analysisDialog=!0,t.analysisDisabled=!0,t.analysisForm=e.data}))},handleSecondDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前分析").then((function(){return Object(c["b"])({id:e.id})})).then((function(){t.$modal.msgSuccess("次生分析删除成功!"),t.getSecondData()})).catch((function(e){}))},secondDataAdd:function(){this.secondaryAnalysisTitle="新增次生分析",this.analysisDialog=!0,this.analysisDisabled=!1,this.analysisForm={secondaryDisaster:void 0,noticePersonId:[],elementAnalysis:void 0,bearingSituation:void 0,emergencyMeasures:void 0}},analysisSubmit:function(){var e=this;this.analysisForm.businessTypeId=this.eventDetail.businessTypeId,Object(c["e"])(this.analysisForm).then((function(t){200==t.code&&(e.$modal.msgSuccess("次生分析新增成功!"),e.analysisDialog=!1,e.getSecondData())}))},handleRecord:function(){var e=this;Object(c["s"])({businessTypeId:this.eventDetail.businessTypeId}).then((function(t){console.log(t.data),e.recordDialog=!0,e.executionRecord=t.data,e.eventReport=t.data.eventReport}))},handledownload:function(e){var t=this,a=this;this.$modal.confirm("是否确认下载附件").then((function(){console.log(e,"wwwwwww");var t=e.attachmentAddress.split(",");t.map((function(e){var t=e.split("/");Object(c["k"])(t).then(function(){var e=Object(o["a"])(Object(s["a"])().mark((function e(i){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a.handledownloadGet(t,i);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}))})).then((function(){t.getList(),t.$modal.msgSuccess("下载成功")})).catch((function(e){console.log(e)}))},handleInformation:function(){this.plain1=!1,this.plain2=!0,this.informationForm={},this.informationDialog=!0,this.getpagematerial(),this.pagesterminal(),this.$refs.informationForm.resetFields()},upload:function(){this.openmaterial=!0,this.fileList=[]},textConten:function(){this.textFlag=!this.textFlag},getUrl:function(){var e=this;this.materialData.map((function(t){t.id==e.informationForm.materialId&&(e.informationForm.url=t.url)}))},getpagematerial:function(){var e=this;Object(c["n"])({isEmergency:1}).then((function(t){console.log(t),e.materialData=t.data}))},pagesterminal:function(){var e=this;Object(c["a"])().then((function(t){console.log(t,"信息发布终端"),e.terminalList=t.data}))},broadcastList:function(){var e=this;Object(c["f"])().then((function(t){console.log(t,"广播终端"),e.terminalList1=t.data}))},beforeUpload:function(e){if("6010101"==this.formmaterial.materialType){var t=/(jpeg|jpg|png|gif|bmp|tiff|webp|svg)$/.test(e.name);t||this.$message.error("上传素材格式只能是下列格式!");var a=e.size/1024/1024<50;return a||this.$message.error("上传素材大小不能超过 50MB!"),t&&a}if("6010102"==this.formmaterial.materialType){var i=/(mp4|avi|mkv|mov|wmv|flv|webm|mpeg)$/.test(e.name);i||this.$message.error("上传素材格式只能是下列格式!");var n=e.size/1024/1024<50;return n||this.$message.error("上传素材大小不能超过 50MB!"),i&&n}if("6010103"==this.formmaterial.materialType){var s=/(mp3|wav|aac|flac|ogg|wma)$/.test(e.name);s||this.$message.error("上传素材格式只能是下列格式!");var o=e.size/1024/1024<50;return o||this.$message.error("上传素材大小不能超过 50MB!"),s&&o}if("6010104"==this.formmaterial.materialType){var r=/(pdf|word|excel|txt|ppt)$/.test(e.name);r||this.$message.error("上传素材格式只能是下列格式!");var l=e.size/1024/1024<50;return l||this.$message.error("上传素材大小不能超过 50MB!"),r&&l}},handleRemove:function(e,t){this.form.url=void 0},handlePreview:function(e,t,a){if(console.log(e,t,a),0==t.size)return this.$modal.msgWarning("当前文件大小不符合规范"),!0;this.formmaterial.url=a[0].response},submitFormmaterial:function(){var e=this;this.$refs["formmaterial"].validate((function(t){t&&(e.formmaterial.isEmergency=1,console.log(e.formmaterial),Object(c["w"])(e.formmaterial).then((function(t){e.$modal.msgSuccess("新增成功"),e.openmaterial=!1,e.getpagematerial()})))}))},submitForm:function(e){var t=this,a=this;this.$refs["informationForm"].validate((function(i){if(i){if(t.formmaterial.isEmergency=1,t.informationForm.startTime=t.informationForm.dange[0],t.informationForm.endTime=t.informationForm.dange[1],t.informationForm.saveFlag=e,t.plain1){var n=JSON.parse(JSON.stringify(t.informationForm.terminalIdArr1));t.informationForm.deviceIds=n.toString(),t.informationForm.typeFlag="6010602"}else{t.informationForm.deviceCount=t.informationForm.terminalIdArr.length;var s=JSON.parse(JSON.stringify(t.informationForm.terminalIdArr));t.informationForm.terminalId=s.toString(),t.informationForm.typeFlag="6010601"}console.log(t.informationForm),void 0!=t.informationForm.id?update(t.informationForm).then((function(e){t.$modal.msgSuccess("修改成功"),t.informationDialog=!1,t.getList()})):2==e?t.$modal.confirm("发起创建的节目将直接发起审核,是否继续创建").then((function(){var e=this;return Object(c["v"])(a.informationForm).then((function(t){e.$modal.msgSuccess("新增成功"),e.informationDialog=!1,e.getList()}))})):Object(c["v"])(t.informationForm).then((function(e){t.$modal.msgSuccess("新增成功"),t.informationDialog=!1,t.getList()}))}}))},cancelmaterial:function(){this.openmaterial=!1,this.resetmaterial()},resetmaterial:function(){this.formmaterial={materialName:void 0,materialType:void 0,url:void 0},this.resetForm("formmaterial")},informationCancel:function(){this.informationDialog=!1,this.reset()},programClick:function(e){this.informationForm={},1==e?(this.plain1=!1,this.plain2=!0,this.textFlag=!1,this.getpagematerial(),this.pagesterminal()):(this.plain1=!0,this.plain2=!1,this.getpagematerial(),this.broadcastList()),this.$refs.informationForm.resetFields()},handleQuery:function(){this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.getList()},resetQuery:function(){this.dateRange=[],this.queryParams={current:1,size:10,eventNo:void 0,eventName:void 0,eventTypeName:void 0,eventTypeId:void 0,eventLevel:void 0,startTime:"",endTime:"",dispatchStateList:[]},this.resetForm("queryForm"),this.handleQuery()},mitigateSubmit:function(){var e=this;console.log(this.mitigateForm),Object(u["g"])(this.mitigateForm).then((function(t){t.success?(e.$message.success("疏解成功"),console.log(e.routeForm),Object(u["y"])(e.routeForm).then((function(t){if(t.data.steps&&t.data.steps.length>0){console.log(t.data.steps);var a=[[Number(e.accidentPoint.longitude),Number(e.accidentPoint.latitude)]];t.data.steps.forEach((function(e){console.log(e.polyline.split(";"));var t=[];t=e.polyline.split(";");for(var i=0;i<t.length;i++)t[i]=t[i].split(","),a.push([Number(t[i][0]),Number(t[i][1])]);console.log(t)}));var i=[Number(e.accidentPoint.longitude),Number(e.accidentPoint.latitude)];console.log(a),e.$refs.mapRef.runpolyline(a,i,t.data.duration)}}))):e.$message.error("疏解失败"),e.mitigateDialog=!1}))}}},h=g,v=(a("e09b"),a("2877")),b=Object(v["a"])(h,i,n,!1,null,"49bc9f9d",null);t["default"]=b.exports},3645:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAstJREFUaEPtWltu2zAQ5DK1kUPUhvIV+xSNb2KfJMlJkp4k7insfFVweojCKbgFBakVVImcXa7zVQH+Mrnc2Zl9iDY5w+ewmG090RdmrhxR5ZyLn/jUjrkmon0I4dv6x6+91bFkYeh1Ob9n57Y9h7NmmXnnrq726/pnnV2cWFAE4PD50x15/yRxfOBLzcyP67f3Zy0INYA26g/ag/v7yLmH29P5UWNLBeC4mL04ojvNgVN7tCDEACwjPwSjASEC0Gr+xTLyQ1sxuSU5IQJwXM4Zdp55T0QxSbuSGrd2ZTVlpmbvN2h1ggGg0knJAC23EinBAIDo1xzCLtek0NK7Op0h36BFh+q6ohC+p3jnEDY557v9rb2YS5OSYu9vEBlBAHLykVDegbCyCQHI1X2U7iGDx+U8sjrOAvN+9fa+yRUNDEDqIOfq1el8kzto7PskANAuCmCyfGrk05PRUzsEjuJHmEUBTFJNzj3fns47DQO5PPgQAA7U6qiE0jMVJM1iBqJjaMkbSeJUZ7cD8LqcJ7WqyYOcfFCbEAPIECdhwbIxQgAi9ZmSF5dAQ1gTDKL7zPsEJJ94KAwgR3mn70h98P7r2BggsYG+ocEAQBb+5mk7TjdJ7lx8e0NG6YZJSWMUAUAjqOkJfQbR6Isk1B0A5EKJ/w5pXv0DRAzEjZdkAS2dRQCQWV5LgTT6Kgm1LCQbmwaAJvpqAJdgQdIIiySEjsIiFgoGQnESd44h4wUKQht9tYT+lFSbK0ZR4xoGRc1ANGTBguQ2Y4zRIgDi8eJfD4qiXyyhhoXFbEtE8TcC8SO9B70IAwUsFEffhAHteKFtXKZJ3DcmHPJMom/GgJQFq+ibAhDkgln0zQEgo7Zl9M0BICxoRuZUfS5uZEPjKRaso38RBiZZKJg4P5SBpjtX15UPIf79oLmNKLkAzrV3cwnlDrT+/j8A64hK7f0GP++HQL/vRA8AAAAASUVORK5CYII="},"3eed":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return s}));var i=a("b775");a("c38a");function n(e){return Object(i["a"])({url:"/emergency-resource-overview/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/emergency-monitor/getVideoStreaming",method:"post",data:e})}},"450d":function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return s})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return l}));var i=a("b775");a("c38a");function n(e){return Object(i["a"])({url:"/emergency-assistant-decision/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/emergency-event-type/tree",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/dict/getDict",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/dict/getMatterMsds",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/emergency-assistant-decision/analysis",method:"get",params:e})}},"54f2":function(e,t,a){},"58b9":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"auto",height:"auto"},attrs:{id:"jessibuca"}},[a("div",{ref:"container",staticStyle:{width:"100%",height:"10rem","background-color":"#000"},attrs:{id:"container"},on:{dblclick:e.fullscreenSwich}})])},n=[],s={name:"jessibuca",data:function(){return{jessibuca:null,playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:"",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!0}},props:["videoUrl","error","hasAudio","height"],mounted:function(){var e=this;window.onerror=function(e){};var t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){var a=document.getElementById("container");a.style.height=9/16*a.clientWidth+"px","undefined"==typeof e.videoUrl&&(e.videoUrl=t),console.log("初始化时的地址为: "+e.videoUrl),e.play(e.videoUrl)}))},created:function(){console.log(this.videoUrl)},methods:{create:function(){console.log(this.$refs.container),console.log("hasAudio  "+this.hasAudio),this.jessibuca=new JessibucaPro({container:"#container",decoder:"./decoder-pro.js",videoBuffer:.2,isResize:!1,text:"",loadingText:"加载中",debug:!0,isMulti:!0,useMSE:!0,useSIMD:!0,useWCS:!0,hasAudio:!1,useVideoRender:!0,controlAutoHide:!0,showBandwidth:!0,showPerformance:!1,operateBtns:{fullscreen:!0,screenshot:!0,play:!0,audio:!0},watermarkConfig:{text:{content:"摄像头"},right:10,top:10}}),this.jessibuca.on("fullscreen",(function(e){console.log("is fullscreen",index,e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var t=this;console.log(e),this.jessibuca&&this.destroy(),this.create(),this.jessibuca.on("play",(function(){t.playing=!0,t.loaded=!0,t.quieting=t.jessibuca.quieting})),this.jessibuca.hasLoaded()?this.jessibuca.play(e):this.jessibuca.on("load",(function(){console.log("load 播放"),t.jessibuca.play(e)}))},pause:function(){this.jessibuca&&this.jessibuca.pause(),this.playing=!1,this.err="",this.performance=""},destroy:function(){this.jessibuca&&this.jessibuca.destroy(),this.jessibuca=null,this.playing=!1,this.err="",this.performance=""},eventcallbacK:function(e,t){console.log("player 事件回调"),console.log(e),console.log(t)},fullscreenSwich:function(){var e=this.isFullscreen();this.jessibuca.setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}},destroyed:function(){this.jessibuca&&this.jessibuca.destroy(),this.playing=!1,this.loaded=!1,this.performance=""}},o=s,r=(a("d8f9"),a("2877")),l=Object(r["a"])(o,i,n,!1,null,null,null);t["default"]=l.exports},"802f":function(e,t,a){"use strict";a.d(t,"p",(function(){return n})),a.d(t,"j",(function(){return s})),a.d(t,"o",(function(){return o})),a.d(t,"u",(function(){return r})),a.d(t,"q",(function(){return l})),a.d(t,"l",(function(){return c})),a.d(t,"r",(function(){return d})),a.d(t,"x",(function(){return u})),a.d(t,"y",(function(){return m})),a.d(t,"z",(function(){return p})),a.d(t,"A",(function(){return f})),a.d(t,"h",(function(){return g})),a.d(t,"g",(function(){return h})),a.d(t,"C",(function(){return v})),a.d(t,"i",(function(){return b})),a.d(t,"m",(function(){return y})),a.d(t,"E",(function(){return k})),a.d(t,"B",(function(){return w})),a.d(t,"D",(function(){return x})),a.d(t,"e",(function(){return D})),a.d(t,"d",(function(){return F})),a.d(t,"c",(function(){return A})),a.d(t,"b",(function(){return j})),a.d(t,"t",(function(){return S})),a.d(t,"s",(function(){return O})),a.d(t,"n",(function(){return N})),a.d(t,"w",(function(){return T})),a.d(t,"a",(function(){return I})),a.d(t,"f",(function(){return C})),a.d(t,"v",(function(){return _})),a.d(t,"k",(function(){return R}));a("99af");var i=a("b775");function n(e){return Object(i["a"])({url:"/emergency-event-submit-manage/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/emergency-event-type/tree",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/emergency-resource-overview/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/emergency-event-submit-manage/detail",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/emergency_plan/list",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/emergency-plan-response-level/list",method:"get",params:e})}function d(e){return Object(i["a"])({url:"/emergency-plan-response/save",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/emergency-plan-response/show-plan",method:"get",params:e})}function m(e){return Object(i["a"])({url:"/emergency-plan-response/show-process",method:"get",params:e})}function p(e){return Object(i["a"])({url:"/emergency-plan-response/show-process-right",method:"get",params:e})}function f(e){return Object(i["a"])({url:"/emergency-plan-response/update",method:"post",data:e})}function g(e){return Object(i["a"])({url:"/emergency-plan-response-contingent/update",method:"post",data:e})}function h(e){return Object(i["a"])({url:"/emergency_expert_contingent/list",method:"get",params:e})}function v(e){return Object(i["a"])({url:"/emergency-temporary-task/save",method:"post",data:e})}function b(e){return Object(i["a"])({url:"/emergency-supply-depot/list",method:"get",params:e})}function y(e){return Object(i["a"])({url:"/emergency-material/listOfDepot",method:"get",params:e})}function k(e){return Object(i["a"])({url:"/emergency-temporary-task/list",method:"get",params:e})}function w(e){return Object(i["a"])({url:"/emergency-temporary-task/detail",method:"get",params:e})}function x(e){return Object(i["a"])({url:"/emergency-temporary-task/update",method:"post",data:e})}function D(e){return Object(i["a"])({url:"/emergency-secondary-analysis/save",method:"post",data:e})}function F(e){return Object(i["a"])({url:"/emergency-secondary-analysis/page",method:"get",params:e})}function A(e){return Object(i["a"])({url:"/emergency-secondary-analysis/detail",method:"get",params:e})}function j(e){return Object(i["a"])({url:"/emergency-secondary-analysis/delete",method:"get",params:e})}function S(e){return Object(i["a"])({url:"/emergency-execution-record/save",method:"post",data:e})}function O(e){return Object(i["a"])({url:"/emergency-execution-record/detail",method:"get",params:e})}function N(e){return Object(i["a"])({url:"/information-emergency-material/list",method:"get",params:e})}function T(e){return Object(i["a"])({url:"/information-emergency-material/save",method:"post",data:e})}function I(e){return Object(i["a"])({url:"/information-emergency-terminal/InformationList",method:"get",params:e})}function C(e){return Object(i["a"])({url:"/information-emergency-terminal/broadcastList",method:"get",params:e})}function _(e){return Object(i["a"])({url:"/information-release-emergency/saveInformationRelease",method:"post",data:e})}function R(e){return Object(i["a"])({url:"/file/downloadFile?bucket=".concat(e[1],"&path=").concat(e[2],"&fileName=").concat(e[3]),method:"get",responseType:"blob"})}},cf65:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"J",(function(){return s})),a.d(t,"H",(function(){return o})),a.d(t,"h",(function(){return r})),a.d(t,"f",(function(){return l})),a.d(t,"I",(function(){return c})),a.d(t,"E",(function(){return d})),a.d(t,"s",(function(){return u})),a.d(t,"o",(function(){return m})),a.d(t,"n",(function(){return p})),a.d(t,"a",(function(){return f})),a.d(t,"G",(function(){return g})),a.d(t,"j",(function(){return h})),a.d(t,"m",(function(){return v})),a.d(t,"l",(function(){return b})),a.d(t,"t",(function(){return y})),a.d(t,"p",(function(){return k})),a.d(t,"k",(function(){return w})),a.d(t,"A",(function(){return x})),a.d(t,"B",(function(){return D})),a.d(t,"C",(function(){return F})),a.d(t,"D",(function(){return A})),a.d(t,"F",(function(){return j})),a.d(t,"w",(function(){return S})),a.d(t,"K",(function(){return O})),a.d(t,"e",(function(){return N})),a.d(t,"i",(function(){return T})),a.d(t,"v",(function(){return I})),a.d(t,"r",(function(){return C})),a.d(t,"d",(function(){return _})),a.d(t,"c",(function(){return R})),a.d(t,"g",(function(){return L})),a.d(t,"y",(function(){return E})),a.d(t,"z",(function(){return M})),a.d(t,"x",(function(){return P})),a.d(t,"u",(function(){return q})),a.d(t,"q",(function(){return U}));var i=a("b775");a("c38a");function n(e){return Object(i["a"])({url:"/emergencyCar/add",method:"post",data:e})}function s(e){return Object(i["a"])({url:"/emergencyCar/page",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/emergencyCar/detail",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/emergencyCar/dispatch",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/emergencyCar/delete",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/emergencyCar/record",method:"get",params:e})}function d(e){return Object(i["a"])({url:"/emergencyCar/return",method:"post",data:e})}function u(){return Object(i["a"])({url:"/emergencyCar/exportTemplate",method:"post",responseType:"blob"})}function m(e){return Object(i["a"])({url:"/emergencyCar/export",method:"post",data:e,responseType:"blob"})}function p(e){return Object(i["a"])({url:"/enterpriseDrill/page",method:"get",params:e})}function f(e){return Object(i["a"])({url:"/enterpriseDrill/add",method:"post",data:e})}function g(e){return Object(i["a"])({url:"/enterpriseDrill/update",method:"post",data:e})}function h(e){return Object(i["a"])({url:"/emergencyArea/tree",method:"get",params:e})}function v(e){return Object(i["a"])({url:"/enterpriseDrill/detail",method:"get",params:e})}function b(e){return Object(i["a"])({url:"/enterpriseDrill/delete",method:"post",data:e})}function y(e){return Object(i["a"])({url:"/enterpriseDrill/exportTemplate",method:"post",data:e,responseType:"blob"})}function k(e){return Object(i["a"])({url:"/enterpriseDrill/export",method:"post",data:e,responseType:"blob"})}function w(e){return Object(i["a"])({url:"/emergencyOrganization/tree",method:"get",params:e})}function x(e){return Object(i["a"])({url:"/emergency-plan-manage-firm/overviewHead",method:"get",params:e})}function D(e){return Object(i["a"])({url:"/emergency-plan-manage-firm/overviewLeft",method:"get",params:e})}function F(e){return Object(i["a"])({url:"/emergency-plan-manage-firm/overviewRight",method:"get",params:e})}function A(e){return Object(i["a"])({url:"/emergency-plan-manage-firm/pageList",method:"post",data:e})}function j(e){return Object(i["a"])({url:"/emergency-plan-manage-firm/save",method:"post",data:e})}function S(e){return Object(i["a"])({url:"/dict/getDict",method:"get",params:e})}function O(e){return Object(i["a"])({url:"/emergency-plan-manage-firm/view/".concat(e),method:"get"})}function N(e){return Object(i["a"])({url:"/emergency-plan-manage-firm/del/".concat(e),method:"post"})}function T(e){return Object(i["a"])({url:"/emergency-plan-manage-firm/edit",method:"post",data:e})}function I(){return Object(i["a"])({url:"/emergency-plan-manage-firm/exportTemplate",method:"post",responseType:"blob"})}function C(e){return Object(i["a"])({url:"/emergency-plan-manage-firm/export",method:"post",data:e,responseType:"blob"})}function _(e){return Object(i["a"])({url:"/emergency-assistant-decision/airEquipment",method:"get",params:e})}function R(e){return Object(i["a"])({url:"/emergency-assistant-decision/airData",method:"get",params:e})}function L(e){return Object(i["a"])({url:"/emergency_refuge/dispatch",method:"post",data:e})}function E(e){return Object(i["a"])({url:"/map/getPath",method:"get",params:e})}function M(e){return Object(i["a"])({url:"/emergency_expert_contingent/getTeamList",method:"get",params:e})}function P(e){return Object(i["a"])({url:"/emergency-expert/getExpertList",method:"get",params:e})}function q(){return Object(i["a"])({url:"/emergency_expert_contingent/exportTemplate",method:"post",responseType:"blob"})}function U(e){return Object(i["a"])({url:"/emergency_expert_contingent/export",method:"post",data:e,responseType:"blob"})}},d4b3:function(e,t){e.exports="data:image/png;base64,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"},d8f9:function(e,t,a){"use strict";a("1493")},e09b:function(e,t,a){"use strict";a("54f2")}}]);