(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-485928b1"],{"972c":function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"e",(function(){return o})),r.d(t,"i",(function(){return i})),r.d(t,"l",(function(){return l})),r.d(t,"b",(function(){return s})),r.d(t,"f",(function(){return c})),r.d(t,"j",(function(){return u})),r.d(t,"d",(function(){return d})),r.d(t,"h",(function(){return m})),r.d(t,"k",(function(){return p})),r.d(t,"c",(function(){return f})),r.d(t,"g",(function(){return h}));var a=r("b775");function n(e){return Object(a["a"])({url:"/firecontrol-area/page",method:"get",params:e})}function o(e){return Object(a["a"])({url:"/firecontrol-water/page",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/firecontrol-water/save",method:"post",data:e})}function l(e){return Object(a["a"])({url:"/firecontrol-water/update",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/firecontrol-nature-water/page",method:"get",params:e})}function c(e){return Object(a["a"])({url:"/firecontrol-nature-water/save",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/firecontrol-nature-water/update",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/firecontrol-resource/page",method:"get",params:e})}function m(e){return Object(a["a"])({url:"/firecontrol-resource/save",method:"post",data:e})}function p(e){return Object(a["a"])({url:"/firecontrol-resource/update",method:"post",data:e})}function f(e){return Object(a["a"])({url:"/firecontrol-resource/record/page",method:"post",data:e})}function h(e){return Object(a["a"])({url:"/firecontrol-resource/record/save",method:"post",data:e})}},"9b31":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0}},[r("el-form-item",{attrs:{label:"联系人",prop:"principal"}},[r("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入联系人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.principal,callback:function(t){e.$set(e.queryParams,"principal",t)},expression:"queryParams.principal"}})],1),r("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[r("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入联系电话",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.phone,callback:function(t){e.$set(e.queryParams,"phone",t)},expression:"queryParams.phone"}})],1),r("el-form-item",{attrs:{label:"创建时间"}},[r("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增天然水源")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roleList}},[r("el-table-column",{attrs:{label:"ID",prop:"id",align:"center"}}),r("el-table-column",{attrs:{label:"所属区域",prop:"griddingName",align:"center"}}),r("el-table-column",{attrs:{label:"详细位置","show-overflow-tooltip":!0,prop:"detailLocation",align:"center"}}),r("el-table-column",{attrs:{label:"储量",prop:"amount",align:"center"}}),r("el-table-column",{attrs:{label:"联系人",prop:"principal",align:"center"}}),r("el-table-column",{attrs:{label:"联系方式",prop:"phone",align:"center"}}),r("el-table-column",{attrs:{label:"创建人",prop:"createUser",align:"center"}}),r("el-table-column",{attrs:{label:"创建日期",prop:"createTime",align:"center"}}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"650px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[r("el-form-item",{attrs:{label:"所属区域",prop:"griddingId"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择所属区域"},on:{change:function(t){return e.getAreaName()}},model:{value:e.form.griddingId,callback:function(t){e.$set(e.form,"griddingId",t)},expression:"form.griddingId"}},e._l(e.areaArr,(function(e){return r("el-option",{key:e.id,attrs:{label:e.griddingName,value:e.id}})})),1)],1),r("el-form-item",{attrs:{label:"详细位置",prop:"detailLocation"}},[r("el-input",{attrs:{placeholder:"请输入详细位置"},model:{value:e.form.detailLocation,callback:function(t){e.$set(e.form,"detailLocation",t)},expression:"form.detailLocation"}})],1),r("el-form-item",{attrs:{label:"储量",prop:"amount"}},[r("el-input",{attrs:{placeholder:"请输管径"},model:{value:e.form.amount,callback:function(t){e.$set(e.form,"amount",t)},expression:"form.amount"}})],1),r("el-form-item",{attrs:{label:"联系人",prop:"principal"}},[r("el-input",{attrs:{placeholder:"请输入联系人"},model:{value:e.form.principal,callback:function(t){e.$set(e.form,"principal",t)},expression:"form.principal"}})],1),r("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[r("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],o=(r("d9e2"),r("ac1f"),r("00b4"),r("7db0"),r("d3b7"),r("972c")),i={name:"FirewaterSource",data:function(){var e=function(e,t,r){""===t?r(new Error("请输入绑定的手机号码")):/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(t)?r():r(new Error("请输入正确的手机号码"))};return{loading:!0,showSearch:!0,total:0,roleList:[],title:"",open:!1,queryParams:{current:1,size:10,principal:void 0,phone:void 0,startTime:void 0,endTime:void 0},form:{},dateRange:[],rules:{griddingId:[{required:!0,message:"请选择所属区域",trigger:"change"}],detailLocation:[{required:!0,message:"请输入详细位置",trigger:"blur"}],amount:[{required:!0,message:"请输入储量",trigger:"blur"}],principal:[{required:!0,message:"请输入联系人",trigger:"blur"}],phone:[{validator:e,required:!0,trigger:"blur"}]},areaArr:[]}},created:function(){this.getList(),this.getAreaPage()},methods:{getList:function(){var e=this;this.loading=!0,Object(o["b"])(this.queryParams).then((function(t){e.roleList=t.data.records,e.total=t.data.total,e.loading=!1}))},getAreaPage:function(){var e=this;Object(o["a"])({current:1,size:1e3}).then((function(t){e.areaArr=t.data.records}))},getAreaName:function(){var e=this,t=this.areaArr.find((function(t){return t.id==e.form.griddingId}));this.form.griddingName=t.griddingName},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:void 0,griddingName:void 0,griddingId:void 0,detailLocation:void 0,amount:void 0,principal:void 0,phone:void 0,remark:void 0},this.resetForm("form")},handleQuery:function(){this.queryParams.current=1,this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(){this.reset(),this.open=!0,this.title="新增天然水源"},handleUpdate:function(e){this.reset(),this.open=!0,this.title="修改天然水源",this.form=e},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.id?Object(o["j"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(o["f"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;e.roleId;this.$modal.confirm("是否确认删除当前数据").then((function(){return Object(o["j"])({id:e.id,isDeleted:1})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},l=i,s=r("2877"),c=Object(s["a"])(l,a,n,!1,null,"5a993a16",null);t["default"]=c.exports}}]);