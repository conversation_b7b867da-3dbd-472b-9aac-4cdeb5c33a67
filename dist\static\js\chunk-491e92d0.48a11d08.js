(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-491e92d0","chunk-4ff97d32"],{1769:function(e,t,o){"use strict";o.r(t);var i=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:6}},[o("el-card",{staticClass:"left-card"},[o("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[o("span",{staticStyle:{"font-size":"18px","line-height":"36px"}},[e._v("监控目录")]),o("el-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:e.addGroup}},[e._v("添加分组")])],1),o("el-tree",{ref:"tree",attrs:{data:e.treeData,props:e.defaultProps,"expand-on-click-node":!1},on:{"node-click":e.handleClick},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.node,r=t.data;return o("span",{staticClass:"custom-tree-node"},[o("span",[e._v(e._s(i.label))]),o("span",[o("el-button",{directives:[{name:"show",rawName:"v-show",value:r.monitoringVos,expression:"data.monitoringVos"}],attrs:{icon:"el-icon-plus",type:"text",size:"mini"},on:{click:function(t){return t.stopPropagation(),function(){return e.groupAdd(r)}()}}}),o("el-button",{directives:[{name:"show",rawName:"v-show",value:r.monitoringVos,expression:"data.monitoringVos"}],attrs:{icon:"el-icon-edit",type:"text",size:"mini"},on:{click:function(t){return t.stopPropagation(),function(){return e.editGroup(r)}()}}}),o("el-button",{attrs:{icon:"el-icon-delete",type:"text",size:"mini"},on:{click:function(t){return t.stopPropagation(),function(){return e.removeGroup(r)}()}}})],1)])}}])})],1)],1),o("el-col",{attrs:{span:18}},[o("el-card",{staticClass:"right-card"},[o("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[o("span",{staticStyle:{"font-size":"18px"}},[e._v("实时视频")])]),o("div",{staticClass:"videoBox"},e._l(e.videoBox,(function(t,i){return o("el-card",{key:i,staticClass:"videoCard"},[o("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[o("span",[e._v(e._s(t.deviceName))]),o("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text",icon:"el-icon-close"},on:{click:function(t){return e.closeCard(i)}}})],1),o("div",{staticStyle:{width:"100%",height:"100%"},attrs:{id:"videoElement"}},[o("jessibuca-player",{ref:"videoPlayer"+i,refInFor:!0,attrs:{id:"container"+i,videoUrl:e.videoUrl[i],error:e.videoError,message:e.videoError,height:!1,hasAudio:e.hasAudio,fluent:"",autoplay:"",live:""}})],1)])})),1)])],1)],1),o("el-dialog",{attrs:{title:e.groupTitle,visible:e.groupDialog,width:"560px"},on:{"update:visible":function(t){e.groupDialog=t}}},[o("el-form",{ref:"groupForm",attrs:{model:e.groupForm,rules:e.rules,"label-width":"100px"}},[o("el-form-item",{attrs:{label:"分组名称",prop:"directoryName"}},[o("el-input",{attrs:{placeholder:"请输入分组名称"},model:{value:e.groupForm.directoryName,callback:function(t){e.$set(e.groupForm,"directoryName",t)},expression:"groupForm.directoryName"}})],1)],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")]),o("el-button",{on:{click:function(t){e.groupDialog=!1}}},[e._v("取 消")])],1)],1),o("el-dialog",{attrs:{title:"添加摄像头",visible:e.videoDialog,width:"960px"},on:{"update:visible":function(t){e.videoDialog=t}}},[o("el-form",{ref:"form",attrs:{"label-width":"88px",inline:""}},[o("el-form-item",{attrs:{label:"设备ID"}},[o("el-input",{attrs:{placeholder:"请输入设备ID"},on:{change:e.getVideoList},model:{value:e.query.deviceId,callback:function(t){e.$set(e.query,"deviceId",t)},expression:"query.deviceId"}})],1),o("el-form-item",{attrs:{label:"摄像头名称"}},[o("el-input",{attrs:{placeholder:"请输入摄像头名称"},on:{change:e.getVideoList},model:{value:e.query.deviceName,callback:function(t){e.$set(e.query,"deviceName",t)},expression:"query.deviceName"}})],1),o("el-form-item",{attrs:{label:"所属部门"}},[o("el-select",{attrs:{placeholder:"请选择所属部门",clearable:""},on:{change:e.getVideoList},model:{value:e.query.organizationName,callback:function(t){e.$set(e.query,"organizationName",t)},expression:"query.organizationName"}},[o("el-option",{staticClass:"option",attrs:{value:e.query.organizationId}},[o("el-tree",{ref:"tree",staticClass:"tree",attrs:{data:e.organizationTree,"show-checkbox":!0,"node-key":"id",props:e.organizationProps},on:{check:e.handleNodeClick}})],1)],1)],1),o("el-form-item",{attrs:{label:"安装位置"}},[o("el-input",{attrs:{placeholder:"请输入安装位置"},on:{change:e.getVideoList},model:{value:e.query.placementLocation,callback:function(t){e.$set(e.query,"placementLocation",t)},expression:"query.placementLocation"}})],1)],1),o("el-table",{attrs:{data:e.videoList},on:{"selection-change":e.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"55"}}),e._l(e.tableColumn,(function(t){return o("el-table-column",{key:t.prop,attrs:{label:t.label,prop:t.prop,align:"center","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(i){var r=i.row;return["expertLevel"===t.prop?o("span",[e._v(e._s((e.dict.type.emergency_expert_level.find((function(e){return e.value==r.expertLevel}))||{}).label))]):o("span",[e._v(e._s(r[t.prop]))])]}}],null,!0)})}))],2),o("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.query.current,limit:e.query.size},on:{"update:page":function(t){return e.$set(e.query,"current",t)},"update:limit":function(t){return e.$set(e.query,"size",t)},pagination:e.getVideoList}}),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:e.submitVideo}},[e._v("确 定")]),o("el-button",{on:{click:function(t){e.videoDialog=!1}}},[e._v("取 消")])],1)],1)],1)},r=[],n=(o("d3b7"),o("159b"),o("b64b"),o("e9c4"),o("14d9"),o("b0c0"),o("a434"),o("b775"));function a(){return Object(n["a"])({url:"/firecontrol-monitoring-directory/list",method:"get"})}function s(e){return Object(n["a"])({url:"/firecontrol-monitoring-directory/save",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/firecontrol-monitoring-directory/update",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/firecontrol-monitoring-directory/delete",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/firecontrol-monitoring-directory-monitor/delete",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/firecontrol-monitoring-directory-monitor/save",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/monitor/page",method:"get",params:e})}function h(e){return Object(n["a"])({url:"/monitor/getVideoStreaming",method:"post",data:e})}function f(e){return Object(n["a"])({url:"/organization/tree",method:"get",params:e})}var g=o("9ad9"),m={components:{jessibucaPlayer:g["default"]},data:function(){return{treeData:[],defaultProps:{children:"monitoringVos",label:"directoryName"},organizationProps:{children:"children",label:"name"},videoBox:[],hasAudio:!1,videoUrl:[],groupTitle:"",groupDialog:!1,groupForm:{directoryName:""},rules:{directoryName:[{required:!0,message:"请输入分组名称",trigger:"blur"}]},videoDialog:!1,organizationTree:[],multipleSelection:[],query:{current:1,size:10},total:0,directoryId:"",videoList:[],tableColumn:[{label:"ID",prop:"deviceId"},{label:"摄像头名称",prop:"deviceName"},{label:"所属部门",prop:"organizationName"},{label:"安装位置",prop:"placementLocation"}]}},watch:{},created:function(){this.getTreeData(),this.getOrganizationTree()},methods:{getTreeData:function(){var e=this;a().then((function(t){200==(null===t||void 0===t?void 0:t.code)&&(t.data.forEach((function(e){e.stringId="group"+e.id,e.monitoringVos.length>0&&e.monitoringVos.forEach((function(e){e.directoryName=e.deviceName,e.stringId="device"+e.id}))})),e.treeData=t.data)}))},handleClick:function(e){if(this.videoBox=[],e.monitoringVos)if(e.monitoringVos.length>=4){this.videoBox=JSON.parse(JSON.stringify(e.monitoringVos)),this.videoBox.length=4;var t=[];e.monitoringVos.forEach((function(e){t.push(e.deviceId)}))}else{this.videoBox=JSON.parse(JSON.stringify(e.monitoringVos));var o=[];e.monitoringVos.forEach((function(e){o.push(e.deviceId)}))}else this.videoBox.push(e),this.getvideoFlv([e.deviceId])},getOrganizationTree:function(){var e=this;f().then((function(t){e.organizationTree=t.data}))},handleNodeClick:function(e,t,o){t?(this.$refs.tree.setCheckedNodes([e]),this.query.organizationId=e.id,this.query.organizationName=e.name):(this.query.organizationId=void 0,this.query.organizationName=void 0)},addGroup:function(){this.$refs.groupForm&&this.$refs.groupForm.resetFields(),this.groupTitle="添加监控目录分组",this.groupDialog=!0},editGroup:function(e){this.groupTitle="编辑监控目录分组",this.groupDialog=!0,this.groupForm.id=e.id,this.groupForm.directoryName=e.directoryName},removeGroup:function(e){var t=this;e.monitoringVos?this.$modal.confirm("是否确认删除当前分组").then((function(){return c({id:e.id})})).then((function(){t.$modal.msgSuccess("删除成功"),t.getTreeData()})).catch((function(){})):this.$modal.confirm("是否确认删除当前分组下该设备").then((function(){return u({id:e.id})})).then((function(){t.$modal.msgSuccess("删除成功"),t.getTreeData()})).catch((function(){}))},submit:function(){var e=this;this.$refs["groupForm"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.groupForm.id?l(e.groupForm).then((function(t){200==(null===t||void 0===t?void 0:t.code)&&(e.$message({type:"success",message:"编辑成功"}),e.groupDialog=!1,e.getTreeData())})):s(e.groupForm).then((function(t){200==(null===t||void 0===t?void 0:t.code)&&(e.$message({type:"success",message:"添加成功"}),e.groupDialog=!1,e.getTreeData())}))}))},groupAdd:function(e){this.videoDialog=!0,this.query.directoryId=e.id,this.getVideoList()},getVideoList:function(){var e=this;p(this.query).then((function(t){e.videoList=t.data.records||[],e.total=t.data.total,console.log(t.data.records)}))},handleSelectionChange:function(e){this.multipleSelection=e},submitVideo:function(){var e=this;if(this.multipleSelection.length>0){var t=[];this.multipleSelection.forEach((function(o){t.push({monitorId:o.id,directoryId:e.query.directoryId})})),d(t).then((function(t){200==t.code&&(e.$message({type:"success",message:"摄像头添加成功"}),e.videoDialog=!1,e.getTreeData())}))}else this.$message({type:"warning",message:"请选择需要添加的摄像头！"})},closeCard:function(e){this.videoBox.splice(e,1)},videoError:function(e){console.log("播放器错误："+JSON.stringify(e))},getvideoFlv:function(e){var t=this;this.dialogVisible=!0,h({equipmentIdList:[e]}).then((function(e){console.log(e.data),t.$nextTick((function(){t.videoUrl=e.data[0].flvAddress,t.$refs.videoPlayer.play(t.videoUrl)}))}))},closeVideo:function(){this.$refs.videoPlayer.destroy()}}},v=m,y=(o("3d5c"),o("2877")),b=Object(y["a"])(v,i,r,!1,null,"5f6bca7a",null);t["default"]=b.exports},"3d5c":function(e,t,o){"use strict";o("67dd")},"67dd":function(e,t,o){},"9ad9":function(e,t,o){"use strict";o.r(t);var i=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticStyle:{width:"auto",height:"300px"},attrs:{id:"jessibuca"}},[o("div",{ref:e.id,staticStyle:{width:"100%",height:"300px","background-color":"#000"},attrs:{id:e.id},on:{dblclick:e.fullscreenSwich}})])},r=[],n={name:"jessibuca",data:function(){return{jessibuca:null,playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:"",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!0}},props:["videoUrl","error","hasAudio","height","id"],mounted:function(){var e=this;window.onerror=function(e){};var t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){var o=document.getElementById(e.id);o.style.height=9/16*o.clientWidth+"px","undefined"==typeof e.videoUrl&&(e.videoUrl=t),console.log("初始化时的地址为: "+e.videoUrl),e.play(e.videoUrl)}))},created:function(){console.log(this.videoUrl)},methods:{create:function(){this.jessibuca=new JessibucaPro({container:"#"+this.id,decoder:"./decoder-pro.js",videoBuffer:.2,isResize:!1,text:"",loadingText:"加载中",debug:!0,isMulti:!0,useMSE:!0,useSIMD:!0,useWCS:!0,hasAudio:!1,useVideoRender:!0,controlAutoHide:!0,showBandwidth:!0,showPerformance:!1,operateBtns:{fullscreen:!0,screenshot:!0,play:!0,audio:!0},watermarkConfig:{text:{content:"摄像头"},right:10,top:10}}),this.jessibuca.on("fullscreen",(function(e){console.log("is fullscreen",index,e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var t=this;console.log(e),this.jessibuca&&this.destroy(),this.create(),this.jessibuca.on("play",(function(){t.playing=!0,t.loaded=!0,t.quieting=t.jessibuca.quieting})),this.jessibuca.hasLoaded()?this.jessibuca.play(e):this.jessibuca.on("load",(function(){console.log("load 播放"),t.jessibuca.play(e)}))},pause:function(){this.jessibuca&&this.jessibuca.pause(),this.playing=!1,this.err="",this.performance=""},destroy:function(){this.jessibuca&&this.jessibuca.destroy(),this.jessibuca=null,this.playing=!1,this.err="",this.performance=""},eventcallbacK:function(e,t){console.log("player 事件回调"),console.log(e),console.log(t)},fullscreenSwich:function(){var e=this.isFullscreen();this.jessibuca.setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}},destroyed:function(){this.jessibuca&&this.jessibuca.destroy(),this.playing=!1,this.loaded=!1,this.performance=""}},a=n,s=(o("a5fc"),o("2877")),l=Object(s["a"])(a,i,r,!1,null,null,null);t["default"]=l.exports},a5fc:function(e,t,o){"use strict";o("aa9e")},aa9e:function(e,t,o){}}]);