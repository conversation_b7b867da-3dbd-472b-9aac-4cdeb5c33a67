(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-525612db","chunk-2d20955d"],{"0c74":function(t,e,a){"use strict";a("26b0")},"26b0":function(t,e,a){},2855:function(t,e,a){"use strict";a.r(e);var n,o,c=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container"},[a("div",{staticClass:"left-board"},[a("div",{staticClass:"logo-wrapper"},[a("div",{staticClass:"logo"},[a("img",{attrs:{src:t.logo,alt:"logo"}}),t._v(" Form Generator ")])]),a("el-scrollbar",{staticClass:"left-scrollbar"},[a("div",{staticClass:"components-list"},[a("div",{staticClass:"components-title"},[a("svg-icon",{attrs:{"icon-class":"component"}}),t._v("输入型组件 ")],1),a("draggable",{staticClass:"components-draggable",attrs:{list:t.inputComponents,group:{name:"componentsGroup",pull:"clone",put:!1},clone:t.cloneComponent,draggable:".components-item",sort:!1},on:{end:t.onEnd}},t._l(t.inputComponents,(function(e,n){return a("div",{key:n,staticClass:"components-item",on:{click:function(a){return t.addComponent(e)}}},[a("div",{staticClass:"components-body"},[a("svg-icon",{attrs:{"icon-class":e.tagIcon}}),t._v(" "+t._s(e.label)+" ")],1)])})),0),a("div",{staticClass:"components-title"},[a("svg-icon",{attrs:{"icon-class":"component"}}),t._v("选择型组件 ")],1),a("draggable",{staticClass:"components-draggable",attrs:{list:t.selectComponents,group:{name:"componentsGroup",pull:"clone",put:!1},clone:t.cloneComponent,draggable:".components-item",sort:!1},on:{end:t.onEnd}},t._l(t.selectComponents,(function(e,n){return a("div",{key:n,staticClass:"components-item",on:{click:function(a){return t.addComponent(e)}}},[a("div",{staticClass:"components-body"},[a("svg-icon",{attrs:{"icon-class":e.tagIcon}}),t._v(" "+t._s(e.label)+" ")],1)])})),0),a("div",{staticClass:"components-title"},[a("svg-icon",{attrs:{"icon-class":"component"}}),t._v(" 布局型组件 ")],1),a("draggable",{staticClass:"components-draggable",attrs:{list:t.layoutComponents,group:{name:"componentsGroup",pull:"clone",put:!1},clone:t.cloneComponent,draggable:".components-item",sort:!1},on:{end:t.onEnd}},t._l(t.layoutComponents,(function(e,n){return a("div",{key:n,staticClass:"components-item",on:{click:function(a){return t.addComponent(e)}}},[a("div",{staticClass:"components-body"},[a("svg-icon",{attrs:{"icon-class":e.tagIcon}}),t._v(" "+t._s(e.label)+" ")],1)])})),0)],1)])],1),a("div",{staticClass:"center-board"},[a("div",{staticClass:"action-bar"},[a("el-button",{attrs:{icon:"el-icon-download",type:"text"},on:{click:t.download}},[t._v(" 导出vue文件 ")]),a("el-button",{staticClass:"copy-btn-main",attrs:{icon:"el-icon-document-copy",type:"text"},on:{click:t.copy}},[t._v(" 复制代码 ")]),a("el-button",{staticClass:"delete-btn",attrs:{icon:"el-icon-delete",type:"text"},on:{click:t.empty}},[t._v(" 清空 ")])],1),a("el-scrollbar",{staticClass:"center-scrollbar"},[a("el-row",{staticClass:"center-board-row",attrs:{gutter:t.formConf.gutter}},[a("el-form",{attrs:{size:t.formConf.size,"label-position":t.formConf.labelPosition,disabled:t.formConf.disabled,"label-width":t.formConf.labelWidth+"px"}},[a("draggable",{staticClass:"drawing-board",attrs:{list:t.drawingList,animation:340,group:"componentsGroup"}},t._l(t.drawingList,(function(e,n){return a("draggable-item",{key:e.renderKey,attrs:{"drawing-list":t.drawingList,element:e,index:n,"active-id":t.activeId,"form-conf":t.formConf},on:{activeItem:t.activeFormItem,copyItem:t.drawingItemCopy,deleteItem:t.drawingItemDelete}})})),1),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.drawingList.length,expression:"!drawingList.length"}],staticClass:"empty-info"},[t._v(" 从左侧拖入或点选组件进行表单设计 ")])],1)],1)],1)],1),a("right-panel",{attrs:{"active-data":t.activeData,"form-conf":t.formConf,"show-field":!!t.drawingList.length},on:{"tag-change":t.tagChange}}),a("code-type-dialog",{attrs:{visible:t.dialogVisible,title:"选择生成类型","show-file-name":t.showFileName},on:{"update:visible":function(e){t.dialogVisible=e},confirm:t.generate}}),a("input",{attrs:{id:"copyNode",type:"hidden"}})],1)},i=[],l=a("53ca"),r=a("5530"),s=(a("ac1f"),a("5319"),a("14d9"),a("b64b"),a("e9c4"),a("d81d"),a("a434"),a("d3b7"),a("159b"),a("c740"),a("b76a")),d=a.n(s),p=a("e552"),u=a.n(p),f=a("b311"),m=a.n(f),h=a("a85b"),v=a("766b"),g=a("2e2a"),b=a("ed08");a("99af"),a("a15b"),a("b0c0");function y(t){return'<el-dialog v-bind="$attrs" v-on="$listeners" @open="onOpen" @close="onClose" title="Dialog Title">\n    '.concat(t,'\n    <div slot="footer">\n      <el-button @click="close">取消</el-button>\n      <el-button type="primary" @click="handleConfirm">确定</el-button>\n    </div>\n  </el-dialog>')}function w(t){return"<template>\n    <div>\n      ".concat(t,"\n    </div>\n  </template>")}function x(t){return"<script>\n    ".concat(t,"\n  <\/script>")}function C(t){return"<style>\n    ".concat(t,"\n  </style>")}function I(t,e,a){var n="";"right"!==t.labelPosition&&(n='label-position="'.concat(t.labelPosition,'"'));var c=t.disabled?':disabled="'.concat(t.disabled,'"'):"",i='<el-form ref="'.concat(t.formRef,'" :model="').concat(t.formModel,'" :rules="').concat(t.formRules,'" size="').concat(t.size,'" ').concat(c,' label-width="').concat(t.labelWidth,'px" ').concat(n,">\n      ").concat(e,"\n      ").concat(k(t,a),"\n    </el-form>");return o&&(i='<el-row :gutter="'.concat(t.gutter,'">\n        ').concat(i,"\n      </el-row>")),i}function k(t,e){var a="";return t.formBtns&&"file"===e&&(a='<el-form-item size="large">\n          <el-button type="primary" @click="submitForm">提交</el-button>\n          <el-button @click="resetForm">重置</el-button>\n        </el-form-item>',o&&(a='<el-col :span="24">\n          '.concat(a,"\n        </el-col>"))),a}function O(t,e){return o||24!==t.span?'<el-col :span="'.concat(t.span,'">\n      ').concat(e,"\n    </el-col>"):e}var D={colFormItem:function(t){var e="";t.labelWidth&&t.labelWidth!==n.labelWidth&&(e='label-width="'.concat(t.labelWidth,'px"'));var a=!g["e"][t.tag]&&t.required?"required":"",o=M[t.tag]?M[t.tag](t):null,c="<el-form-item ".concat(e,' label="').concat(t.label,'" prop="').concat(t.vModel,'" ').concat(a,">\n        ").concat(o,"\n      </el-form-item>");return c=O(t,c),c},rowFormItem:function(t){var e="default"===t.type?"":'type="'.concat(t.type,'"'),a="default"===t.type?"":'justify="'.concat(t.justify,'"'),n="default"===t.type?"":'align="'.concat(t.align,'"'),o=t.gutter?'gutter="'.concat(t.gutter,'"'):"",c=t.children.map((function(t){return D[t.layout](t)})),i="<el-row ".concat(e," ").concat(a," ").concat(n," ").concat(o,">\n      ").concat(c.join("\n"),"\n    </el-row>");return i=O(t,i),i}},M={"el-button":function(t){var e=N(t),a=(e.tag,e.disabled),n=t.type?'type="'.concat(t.type,'"'):"",o=t.icon?'icon="'.concat(t.icon,'"'):"",c=t.size?'size="'.concat(t.size,'"'):"",i=j(t);return i&&(i="\n".concat(i,"\n")),"<".concat(t.tag," ").concat(n," ").concat(o," ").concat(c," ").concat(a,">").concat(i,"</").concat(t.tag,">")},"el-input":function(t){var e=N(t),a=e.disabled,n=e.vModel,o=e.clearable,c=e.placeholder,i=e.width,l=t.maxlength?':maxlength="'.concat(t.maxlength,'"'):"",r=t["show-word-limit"]?"show-word-limit":"",s=t.readonly?"readonly":"",d=t["prefix-icon"]?"prefix-icon='".concat(t["prefix-icon"],"'"):"",p=t["suffix-icon"]?"suffix-icon='".concat(t["suffix-icon"],"'"):"",u=t["show-password"]?"show-password":"",f=t.type?'type="'.concat(t.type,'"'):"",m=t.autosize&&t.autosize.minRows?':autosize="{minRows: '.concat(t.autosize.minRows,", maxRows: ").concat(t.autosize.maxRows,'}"'):"",h=z(t);return h&&(h="\n".concat(h,"\n")),"<".concat(t.tag," ").concat(n," ").concat(f," ").concat(c," ").concat(l," ").concat(r," ").concat(s," ").concat(a," ").concat(o," ").concat(d," ").concat(p," ").concat(u," ").concat(m," ").concat(i,">").concat(h,"</").concat(t.tag,">")},"el-input-number":function(t){var e=N(t),a=e.disabled,n=e.vModel,o=e.placeholder,c=t["controls-position"]?"controls-position=".concat(t["controls-position"]):"",i=t.min?":min='".concat(t.min,"'"):"",l=t.max?":max='".concat(t.max,"'"):"",r=t.step?":step='".concat(t.step,"'"):"",s=t["step-strictly"]?"step-strictly":"",d=t.precision?":precision='".concat(t.precision,"'"):"";return"<".concat(t.tag," ").concat(n," ").concat(o," ").concat(r," ").concat(s," ").concat(d," ").concat(c," ").concat(i," ").concat(l," ").concat(a,"></").concat(t.tag,">")},"el-select":function(t){var e=N(t),a=e.disabled,n=e.vModel,o=e.clearable,c=e.placeholder,i=e.width,l=t.filterable?"filterable":"",r=t.multiple?"multiple":"",s=F(t);return s&&(s="\n".concat(s,"\n")),"<".concat(t.tag," ").concat(n," ").concat(c," ").concat(a," ").concat(r," ").concat(l," ").concat(o," ").concat(i,">").concat(s,"</").concat(t.tag,">")},"el-radio-group":function(t){var e=N(t),a=e.disabled,n=e.vModel,o='size="'.concat(t.size,'"'),c=_(t);return c&&(c="\n".concat(c,"\n")),"<".concat(t.tag," ").concat(n," ").concat(o," ").concat(a,">").concat(c,"</").concat(t.tag,">")},"el-checkbox-group":function(t){var e=N(t),a=e.disabled,n=e.vModel,o='size="'.concat(t.size,'"'),c=t.min?':min="'.concat(t.min,'"'):"",i=t.max?':max="'.concat(t.max,'"'):"",l=$(t);return l&&(l="\n".concat(l,"\n")),"<".concat(t.tag," ").concat(n," ").concat(c," ").concat(i," ").concat(o," ").concat(a,">").concat(l,"</").concat(t.tag,">")},"el-switch":function(t){var e=N(t),a=e.disabled,n=e.vModel,o=t["active-text"]?'active-text="'.concat(t["active-text"],'"'):"",c=t["inactive-text"]?'inactive-text="'.concat(t["inactive-text"],'"'):"",i=t["active-color"]?'active-color="'.concat(t["active-color"],'"'):"",l=t["inactive-color"]?'inactive-color="'.concat(t["inactive-color"],'"'):"",r=!0!==t["active-value"]?":active-value='".concat(JSON.stringify(t["active-value"]),"'"):"",s=!1!==t["inactive-value"]?":inactive-value='".concat(JSON.stringify(t["inactive-value"]),"'"):"";return"<".concat(t.tag," ").concat(n," ").concat(o," ").concat(c," ").concat(i," ").concat(l," ").concat(r," ").concat(s," ").concat(a,"></").concat(t.tag,">")},"el-cascader":function(t){var e=N(t),a=e.disabled,n=e.vModel,o=e.clearable,c=e.placeholder,i=e.width,l=t.options?':options="'.concat(t.vModel,'Options"'):"",r=t.props?':props="'.concat(t.vModel,'Props"'):"",s=t["show-all-levels"]?"":':show-all-levels="false"',d=t.filterable?"filterable":"",p="/"===t.separator?"":'separator="'.concat(t.separator,'"');return"<".concat(t.tag," ").concat(n," ").concat(l," ").concat(r," ").concat(i," ").concat(s," ").concat(c," ").concat(p," ").concat(d," ").concat(o," ").concat(a,"></").concat(t.tag,">")},"el-slider":function(t){var e=N(t),a=e.disabled,n=e.vModel,o=t.min?":min='".concat(t.min,"'"):"",c=t.max?":max='".concat(t.max,"'"):"",i=t.step?":step='".concat(t.step,"'"):"",l=t.range?"range":"",r=t["show-stops"]?':show-stops="'.concat(t["show-stops"],'"'):"";return"<".concat(t.tag," ").concat(o," ").concat(c," ").concat(i," ").concat(n," ").concat(l," ").concat(r," ").concat(a,"></").concat(t.tag,">")},"el-time-picker":function(t){var e=N(t),a=e.disabled,n=e.vModel,o=e.clearable,c=e.placeholder,i=e.width,l=t["start-placeholder"]?'start-placeholder="'.concat(t["start-placeholder"],'"'):"",r=t["end-placeholder"]?'end-placeholder="'.concat(t["end-placeholder"],'"'):"",s=t["range-separator"]?'range-separator="'.concat(t["range-separator"],'"'):"",d=t["is-range"]?"is-range":"",p=t.format?'format="'.concat(t.format,'"'):"",u=t["value-format"]?'value-format="'.concat(t["value-format"],'"'):"",f=t["picker-options"]?":picker-options='".concat(JSON.stringify(t["picker-options"]),"'"):"";return"<".concat(t.tag," ").concat(n," ").concat(d," ").concat(p," ").concat(u," ").concat(f," ").concat(i," ").concat(c," ").concat(l," ").concat(r," ").concat(s," ").concat(o," ").concat(a,"></").concat(t.tag,">")},"el-date-picker":function(t){var e=N(t),a=e.disabled,n=e.vModel,o=e.clearable,c=e.placeholder,i=e.width,l=t["start-placeholder"]?'start-placeholder="'.concat(t["start-placeholder"],'"'):"",r=t["end-placeholder"]?'end-placeholder="'.concat(t["end-placeholder"],'"'):"",s=t["range-separator"]?'range-separator="'.concat(t["range-separator"],'"'):"",d=t.format?'format="'.concat(t.format,'"'):"",p=t["value-format"]?'value-format="'.concat(t["value-format"],'"'):"",u="date"===t.type?"":'type="'.concat(t.type,'"'),f=t.readonly?"readonly":"";return"<".concat(t.tag," ").concat(u," ").concat(n," ").concat(d," ").concat(p," ").concat(i," ").concat(c," ").concat(l," ").concat(r," ").concat(s," ").concat(o," ").concat(f," ").concat(a,"></").concat(t.tag,">")},"el-rate":function(t){var e=N(t),a=e.disabled,n=e.vModel,o=(t.max&&":max='".concat(t.max,"'"),t["allow-half"]?"allow-half":""),c=t["show-text"]?"show-text":"",i=t["show-score"]?"show-score":"";return"<".concat(t.tag," ").concat(n," ").concat(o," ").concat(c," ").concat(i," ").concat(a,"></").concat(t.tag,">")},"el-color-picker":function(t){var e=N(t),a=e.disabled,n=e.vModel,o='size="'.concat(t.size,'"'),c=t["show-alpha"]?"show-alpha":"",i=t["color-format"]?'color-format="'.concat(t["color-format"],'"'):"";return"<".concat(t.tag," ").concat(n," ").concat(o," ").concat(c," ").concat(i," ").concat(a,"></").concat(t.tag,">")},"el-upload":function(t){var e=t.disabled?":disabled='true'":"",a=t.action?':action="'.concat(t.vModel,'Action"'):"",n=t.multiple?"multiple":"",o="text"!==t["list-type"]?'list-type="'.concat(t["list-type"],'"'):"",c=t.accept?'accept="'.concat(t.accept,'"'):"",i="file"!==t.name?'name="'.concat(t.name,'"'):"",l=!1===t["auto-upload"]?':auto-upload="false"':"",r=':before-upload="'.concat(t.vModel,'BeforeUpload"'),s=':file-list="'.concat(t.vModel,'fileList"'),d='ref="'.concat(t.vModel,'"'),p=S(t);return p&&(p="\n".concat(p,"\n")),"<".concat(t.tag," ").concat(d," ").concat(s," ").concat(a," ").concat(l," ").concat(n," ").concat(r," ").concat(o," ").concat(c," ").concat(i," ").concat(e,">").concat(p,"</").concat(t.tag,">")}};function N(t){return{vModel:'v-model="'.concat(n.formModel,".").concat(t.vModel,'"'),clearable:t.clearable?"clearable":"",placeholder:t.placeholder?'placeholder="'.concat(t.placeholder,'"'):"",width:t.style&&t.style.width?":style=\"{width: '100%'}\"":"",disabled:t.disabled?":disabled='true'":""}}function j(t){var e=[];return t.default&&e.push(t.default),e.join("\n")}function z(t){var e=[];return t.prepend&&e.push('<template slot="prepend">'.concat(t.prepend,"</template>")),t.append&&e.push('<template slot="append">'.concat(t.append,"</template>")),e.join("\n")}function F(t){var e=[];return t.options&&t.options.length&&e.push('<el-option v-for="(item, index) in '.concat(t.vModel,'Options" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>')),e.join("\n")}function _(t){var e=[];if(t.options&&t.options.length){var a="button"===t.optionType?"el-radio-button":"el-radio",n=t.border?"border":"";e.push("<".concat(a,' v-for="(item, index) in ').concat(t.vModel,'Options" :key="index" :label="item.value" :disabled="item.disabled" ').concat(n,">{{item.label}}</").concat(a,">"))}return e.join("\n")}function $(t){var e=[];if(t.options&&t.options.length){var a="button"===t.optionType?"el-checkbox-button":"el-checkbox",n=t.border?"border":"";e.push("<".concat(a,' v-for="(item, index) in ').concat(t.vModel,'Options" :key="index" :label="item.value" :disabled="item.disabled" ').concat(n,">{{item.label}}</").concat(a,">"))}return e.join("\n")}function S(t){var e=[];return"picture-card"===t["list-type"]?e.push('<i class="el-icon-plus"></i>'):e.push('<el-button size="small" type="primary" icon="el-icon-upload">'.concat(t.buttonText,"</el-button>")),t.showTip&&e.push('<div slot="tip" class="el-upload__tip">只能上传不超过 '.concat(t.fileSize).concat(t.sizeUnit," 的").concat(t.accept,"文件</div>")),e.join("\n")}function A(t,e){var a=[];n=t,o=t.fields.some((function(t){return 24!==t.span})),t.fields.forEach((function(t){a.push(D[t.layout](t))}));var c=a.join("\n"),i=I(t,c,e);return"dialog"===e&&(i=y(i)),n=null,i}var E,L=a("3022"),T={KB:"1024",MB:"1024 / 1024",GB:"1024 / 1024 / 1024"},J={file:"",dialog:"inheritAttrs: false,"};function R(t,e){E=t=JSON.parse(JSON.stringify(t));var a=[],n=[],o=[],c=[],i=V(e),l=[];t.fields.forEach((function(t){G(t,a,n,o,i,c,l)}));var r=Q(t,e,a.join("\n"),n.join("\n"),o.join("\n"),l.join("\n"),c.join("\n"),i.join("\n"));return E=null,r}function G(t,e,a,n,o,c,i){if(K(t,e),B(t,a),t.options&&t.options.length&&(q(t,n),"dynamic"===t.dataType)){var l="".concat(t.vModel,"Options"),r=Object(b["g"])(l);H("get".concat(r),l,o)}t.props&&t.props.props&&P(t,c),t.action&&"el-upload"===t.tag&&(i.push("".concat(t.vModel,"Action: '").concat(t.action,"',\n      ").concat(t.vModel,"fileList: [],")),o.push(W(t)),t["auto-upload"]||o.push(U(t))),t.children&&t.children.forEach((function(t){G(t,e,a,n,o,c,i)}))}function V(t){var e=[],a={file:E.formBtns?{submitForm:"submitForm() {\n        this.$refs['".concat(E.formRef,"'].validate(valid => {\n          if(!valid) return\n          // TODO 提交表单\n        })\n      },"),resetForm:"resetForm() {\n        this.$refs['".concat(E.formRef,"'].resetFields()\n      },")}:null,dialog:{onOpen:"onOpen() {},",onClose:"onClose() {\n        this.$refs['".concat(E.formRef,"'].resetFields()\n      },"),close:"close() {\n        this.$emit('update:visible', false)\n      },",handleConfirm:"handleConfirm() {\n        this.$refs['".concat(E.formRef,"'].validate(valid => {\n          if(!valid) return\n          this.close()\n        })\n      },")}},n=a[t];return n&&Object.keys(n).forEach((function(t){e.push(n[t])})),e}function K(t,e){var a;void 0!==t.vModel&&(a="string"!==typeof t.defaultValue||t.multiple?"".concat(JSON.stringify(t.defaultValue)):"'".concat(t.defaultValue,"'"),e.push("".concat(t.vModel,": ").concat(a,",")))}function B(t,e){if(void 0!==t.vModel){var a=[];if(g["e"][t.tag]){if(t.required){var n=Object(L["isArray"])(t.defaultValue)?"type: 'array',":"",o=Object(L["isArray"])(t.defaultValue)?"请至少选择一个".concat(t.vModel):t.placeholder;void 0===o&&(o="".concat(t.label,"不能为空")),a.push("{ required: true, ".concat(n," message: '").concat(o,"', trigger: '").concat(g["e"][t.tag],"' }"))}t.regList&&Object(L["isArray"])(t.regList)&&t.regList.forEach((function(e){e.pattern&&a.push("{ pattern: ".concat(JSON.parse(e.pattern),", message: '").concat(e.message,"', trigger: '").concat(g["e"][t.tag],"' }"))})),e.push("".concat(t.vModel,": [").concat(a.join(","),"],"))}}}function q(t,e){if(void 0!==t.vModel){"dynamic"===t.dataType&&(t.options=[]);var a="".concat(t.vModel,"Options: ").concat(JSON.stringify(t.options),",");e.push(a)}}function P(t,e){"dynamic"===t.dataType&&("value"!==t.valueKey&&(t.props.props.value=t.valueKey),"label"!==t.labelKey&&(t.props.props.label=t.labelKey),"children"!==t.childrenKey&&(t.props.props.children=t.childrenKey));var a="".concat(t.vModel,"Props: ").concat(JSON.stringify(t.props.props),",");e.push(a)}function W(t){var e=T[t.sizeUnit],a="",n="",o=[];t.fileSize&&(a="let isRightSize = file.size / ".concat(e," < ").concat(t.fileSize,"\n    if(!isRightSize){\n      this.$message.error('文件大小超过 ").concat(t.fileSize).concat(t.sizeUnit,"')\n    }"),o.push("isRightSize")),t.accept&&(n="let isAccept = new RegExp('".concat(t.accept,"').test(file.type)\n    if(!isAccept){\n      this.$message.error('应该选择").concat(t.accept,"类型的文件')\n    }"),o.push("isAccept"));var c="".concat(t.vModel,"BeforeUpload(file) {\n    ").concat(a,"\n    ").concat(n,"\n    return ").concat(o.join("&&"),"\n  },");return o.length?c:""}function U(t){var e="submitUpload() {\n    this.$refs['".concat(t.vModel,"'].submit()\n  },");return e}function H(t,e,a){var n="".concat(t,"() {\n    // TODO 发起请求获取数据\n    this.").concat(e,"\n  },");a.push(n)}function Q(t,e,a,n,o,c,i,l){var r="".concat(b["d"],"{\n  ").concat(J[e],"\n  components: {},\n  props: [],\n  data () {\n    return {\n      ").concat(t.formModel,": {\n        ").concat(a,"\n      },\n      ").concat(t.formRules,": {\n        ").concat(n,"\n      },\n      ").concat(c,"\n      ").concat(o,"\n      ").concat(i,"\n    }\n  },\n  computed: {},\n  watch: {},\n  created () {},\n  mounted () {},\n  methods: {\n    ").concat(l,"\n  }\n}");return r}var X={"el-rate":".el-rate{display: inline-block; vertical-align: text-top;}","el-upload":".el-upload__tip{line-height: 1.2;}"};function Y(t,e){var a=X[e.tag];a&&-1===t.indexOf(a)&&t.push(a),e.children&&e.children.forEach((function(e){return Y(t,e)}))}function Z(t){var e=[];return t.fields.forEach((function(t){return Y(e,t)})),e.join("\n")}var tt,et,at=[{layout:"colFormItem",tagIcon:"input",label:"手机号",vModel:"mobile",formId:6,tag:"el-input",placeholder:"请输入手机号",defaultValue:"",span:24,style:{width:"100%"},clearable:!0,prepend:"",append:"","prefix-icon":"el-icon-mobile","suffix-icon":"",maxlength:11,"show-word-limit":!0,readonly:!1,disabled:!1,required:!0,changeTag:!0,regList:[{pattern:"/^1(3|4|5|7|8|9)\\d{9}$/",message:"手机号格式错误"}]}],nt=a("81a5"),ot=a.n(nt),ct=a("a92a"),it=a("4923"),lt={components:{draggable:d.a,render:h["a"],RightPanel:v["default"],CodeTypeDialog:ct["default"],DraggableItem:it["default"]},data:function(){return{logo:ot.a,idGlobal:100,formConf:g["a"],inputComponents:g["b"],selectComponents:g["d"],layoutComponents:g["c"],labelWidth:100,drawingList:at,drawingData:{},activeId:at[0].formId,drawerVisible:!1,formData:{},dialogVisible:!1,generateConf:null,showFileName:!1,activeData:at[0]}},created:function(){document.body.ondrop=function(t){t.preventDefault(),t.stopPropagation()}},watch:{"activeData.label":function(t,e){void 0!==this.activeData.placeholder&&this.activeData.tag&&tt===this.activeId&&(this.activeData.placeholder=this.activeData.placeholder.replace(e,"")+t)},activeId:{handler:function(t){tt=t},immediate:!0}},mounted:function(){var t=this,e=new m.a("#copyNode",{text:function(e){var a=t.generateCode();return t.$notify({title:"成功",message:"代码已复制到剪切板，可粘贴。",type:"success"}),a}});e.on("error",(function(e){t.$message.error("代码复制失败")}))},methods:{activeFormItem:function(t){this.activeData=t,this.activeId=t.formId},onEnd:function(t,e){t.from!==t.to&&(this.activeData=et,this.activeId=this.idGlobal)},addComponent:function(t){var e=this.cloneComponent(t);this.drawingList.push(e),this.activeFormItem(e)},cloneComponent:function(t){var e=JSON.parse(JSON.stringify(t));return e.formId=++this.idGlobal,e.span=g["a"].span,e.renderKey=+new Date,e.layout||(e.layout="colFormItem"),"colFormItem"===e.layout?(e.vModel="field".concat(this.idGlobal),void 0!==e.placeholder&&(e.placeholder+=e.label),et=e):"rowFormItem"===e.layout&&(delete e.label,e.componentName="row".concat(this.idGlobal),e.gutter=this.formConf.gutter,et=e),et},AssembleFormData:function(){this.formData=Object(r["a"])({fields:JSON.parse(JSON.stringify(this.drawingList))},this.formConf)},generate:function(t){var e=this["exec".concat(Object(b["g"])(this.operationType))];this.generateConf=t,e&&e(t)},execRun:function(t){this.AssembleFormData(),this.drawerVisible=!0},execDownload:function(t){var e=this.generateCode(),a=new Blob([e],{type:"text/plain;charset=utf-8"});this.$download.saveAs(a,t.fileName)},execCopy:function(t){document.getElementById("copyNode").click()},empty:function(){var t=this;this.$confirm("确定要清空所有组件吗？","提示",{type:"warning"}).then((function(){t.drawingList=[]}))},drawingItemCopy:function(t,e){var a=JSON.parse(JSON.stringify(t));a=this.createIdAndKey(a),e.push(a),this.activeFormItem(a)},createIdAndKey:function(t){var e=this;return t.formId=++this.idGlobal,t.renderKey=+new Date,"colFormItem"===t.layout?t.vModel="field".concat(this.idGlobal):"rowFormItem"===t.layout&&(t.componentName="row".concat(this.idGlobal)),Array.isArray(t.children)&&(t.children=t.children.map((function(t){return e.createIdAndKey(t)}))),t},drawingItemDelete:function(t,e){var a=this;e.splice(t,1),this.$nextTick((function(){var t=a.drawingList.length;t&&a.activeFormItem(a.drawingList[t-1])}))},generateCode:function(){var t=this.generateConf.type;this.AssembleFormData();var e=x(R(this.formData,t)),a=w(A(this.formData,t)),n=C(Z(this.formData));return u.a.html(a+e+n,b["a"].html)},download:function(){this.dialogVisible=!0,this.showFileName=!0,this.operationType="download"},run:function(){this.dialogVisible=!0,this.showFileName=!1,this.operationType="run"},copy:function(){this.dialogVisible=!0,this.showFileName=!1,this.operationType="copy"},tagChange:function(t){var e=this;t=this.cloneComponent(t),t.vModel=this.activeData.vModel,t.formId=this.activeId,t.span=this.activeData.span,delete this.activeData.tag,delete this.activeData.tagIcon,delete this.activeData.document,Object.keys(t).forEach((function(a){void 0!==e.activeData[a]&&Object(l["a"])(e.activeData[a])===Object(l["a"])(t[a])&&(t[a]=e.activeData[a])})),this.activeData=t,this.updateDrawingList(t,this.drawingList)},updateDrawingList:function(t,e){var a=this,n=e.findIndex((function(t){return t.formId===a.activeId}));n>-1?e.splice(n,1,t):e.forEach((function(e){Array.isArray(e.children)&&a.updateDrawingList(t,e.children)}))}}},rt=lt,st=(a("0c74"),a("2877")),dt=Object(st["a"])(rt,c,i,!1,null,null,null);e["default"]=dt.exports},4923:function(t,e,a){"use strict";a.r(e);a("d81d"),a("d9e2");var n=a("b76a"),o=a.n(n),c=a("a85b"),i={itemBtns:function(t,e,a,n){var o=this.$listeners,c=o.copyItem,i=o.deleteItem;return[t("span",{class:"drawing-item-copy",attrs:{title:"复制"},on:{click:function(t){c(e,n),t.stopPropagation()}}},[t("i",{class:"el-icon-copy-document"})]),t("span",{class:"drawing-item-delete",attrs:{title:"删除"},on:{click:function(t){i(a,n),t.stopPropagation()}}},[t("i",{class:"el-icon-delete"})])]}},l={colFormItem:function(t,e,a,n){var o=this,l=this.$listeners.activeItem,r=this.activeId===e.formId?"drawing-item active-from-item":"drawing-item";return this.formConf.unFocusedComponentBorder&&(r+=" unfocus-bordered"),t("el-col",{attrs:{span:e.span},class:r,nativeOn:{click:function(t){l(e),t.stopPropagation()}}},[t("el-form-item",{attrs:{"label-width":e.labelWidth?"".concat(e.labelWidth,"px"):null,label:e.label,required:e.required}},[t(c["a"],{key:e.renderKey,attrs:{conf:e},on:{input:function(t){o.$set(e,"defaultValue",t)}}})]),i.itemBtns.apply(this,arguments)])},rowFormItem:function(t,e,a,n){var c=this.$listeners.activeItem,l=this.activeId===e.formId?"drawing-row-item active-from-item":"drawing-row-item",s=r.apply(this,arguments);return"flex"===e.type&&(s=t("el-row",{attrs:{type:e.type,justify:e.justify,align:e.align}},[s])),t("el-col",{attrs:{span:e.span}},[t("el-row",{attrs:{gutter:e.gutter},class:l,nativeOn:{click:function(t){c(e),t.stopPropagation()}}},[t("span",{class:"component-name"},[e.componentName]),t(o.a,{attrs:{list:e.children,animation:340,group:"componentsGroup"},class:"drag-wrapper"},[s]),i.itemBtns.apply(this,arguments)])])}};function r(t,e,a,n){var o=this;return Array.isArray(e.children)?e.children.map((function(a,n){var c=l[a.layout];return c?c.call(o,t,a,n,e.children):s()})):null}function s(){throw new Error("没有与".concat(this.element.layout,"匹配的layout"))}var d,p,u={components:{render:c["a"],draggable:o.a},props:["element","index","drawingList","activeId","formConf"],render:function(t){var e=l[this.element.layout];return e?e.call(this,t,this.element,this.index,this.drawingList):s()}},f=u,m=a("2877"),h=Object(m["a"])(f,d,p,!1,null,null,null);e["default"]=h.exports},a85b:function(t,e,a){"use strict";a("d3b7"),a("159b"),a("14d9"),a("b64b"),a("e9c4");var n=a("ed08"),o=Object(n["f"])("accept,accept-charset,accesskey,action,align,alt,async,autocomplete,autofocus,autoplay,autosave,bgcolor,border,buffered,challenge,charset,checked,cite,class,code,codebase,color,cols,colspan,content,http-equiv,name,contenteditable,contextmenu,controls,coords,data,datetime,default,defer,dir,dirname,disabled,download,draggable,dropzone,enctype,method,for,form,formaction,headers,height,hidden,high,href,hreflang,http-equiv,icon,id,ismap,itemprop,keytype,kind,label,lang,language,list,loop,low,manifest,max,maxlength,media,method,GET,POST,min,multiple,email,file,muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,preload,radiogroup,readonly,rel,required,reversed,rows,rowspan,sandbox,scope,scoped,seamless,selected,shape,size,type,text,password,sizes,span,spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,target,title,type,usemap,value,width,wrap");function c(t,e,a){e.props.value=a,e.on.input=function(e){t.$emit("input",e)}}var i={"el-button":{default:function(t,e,a){return e[a]}},"el-input":{prepend:function(t,e,a){return t("template",{slot:"prepend"},[e[a]])},append:function(t,e,a){return t("template",{slot:"append"},[e[a]])}},"el-select":{options:function(t,e,a){var n=[];return e.options.forEach((function(e){n.push(t("el-option",{attrs:{label:e.label,value:e.value,disabled:e.disabled}}))})),n}},"el-radio-group":{options:function(t,e,a){var n=[];return e.options.forEach((function(a){"button"===e.optionType?n.push(t("el-radio-button",{attrs:{label:a.value}},[a.label])):n.push(t("el-radio",{attrs:{label:a.value,border:e.border}},[a.label]))})),n}},"el-checkbox-group":{options:function(t,e,a){var n=[];return e.options.forEach((function(a){"button"===e.optionType?n.push(t("el-checkbox-button",{attrs:{label:a.value}},[a.label])):n.push(t("el-checkbox",{attrs:{label:a.value,border:e.border}},[a.label]))})),n}},"el-upload":{"list-type":function(t,e,a){var n=[];return"picture-card"===e["list-type"]?n.push(t("i",{class:"el-icon-plus"})):n.push(t("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-upload"}},[e.buttonText])),e.showTip&&n.push(t("div",{slot:"tip",class:"el-upload__tip"},["只能上传不超过 ",e.fileSize,e.sizeUnit," 的",e.accept,"文件"])),n}}};e["a"]={render:function(t){var e=this,a={attrs:{},props:{},on:{},style:{}},n=JSON.parse(JSON.stringify(this.conf)),l=[],r=i[n.tag];return r&&Object.keys(r).forEach((function(e){var a=r[e];n[e]&&l.push(a(t,n,e))})),Object.keys(n).forEach((function(t){var i=n[t];"vModel"===t?c(e,a,n.defaultValue):a[t]?a[t]=i:o(t)?a.attrs[t]=i:a.props[t]=i})),t(this.conf.tag,a,l)},props:["conf"]}},a92a:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",t._g(t._b({attrs:{width:"500px","close-on-click-modal":!1,"modal-append-to-body":!1},on:{open:t.onOpen,close:t.onClose}},"el-dialog",t.$attrs,!1),t.$listeners),[a("el-row",{attrs:{gutter:15}},[a("el-form",{ref:"elForm",attrs:{model:t.formData,rules:t.rules,size:"medium","label-width":"100px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"生成类型",prop:"type"}},[a("el-radio-group",{model:{value:t.formData.type,callback:function(e){t.$set(t.formData,"type",e)},expression:"formData.type"}},t._l(t.typeOptions,(function(e,n){return a("el-radio-button",{key:n,attrs:{label:e.value,disabled:e.disabled}},[t._v(" "+t._s(e.label)+" ")])})),1)],1),t.showFileName?a("el-form-item",{attrs:{label:"文件名",prop:"fileName"}},[a("el-input",{attrs:{placeholder:"请输入文件名",clearable:"",maxlength:"20"},model:{value:t.formData.fileName,callback:function(e){t.$set(t.formData,"fileName",e)},expression:"formData.fileName"}})],1):t._e()],1)],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:t.close}},[t._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleConfirm}},[t._v(" 确定 ")])],1)],1)],1)},o=[],c=a("5530"),i={inheritAttrs:!1,props:["showFileName"],data:function(){return{formData:{fileName:void 0,type:"file"},rules:{fileName:[{required:!0,message:"请输入文件名",trigger:"blur"}],type:[{required:!0,message:"生成类型不能为空",trigger:"change"}]},typeOptions:[{label:"页面",value:"file"},{label:"弹窗",value:"dialog"}]}},computed:{},watch:{},mounted:function(){},methods:{onOpen:function(){this.showFileName&&(this.formData.fileName="".concat(+new Date,".vue"))},onClose:function(){},close:function(t){this.$emit("update:visible",!1)},handleConfirm:function(){var t=this;this.$refs.elForm.validate((function(e){e&&(t.$emit("confirm",Object(c["a"])({},t.formData)),t.close())}))}}},l=i,r=a("2877"),s=Object(r["a"])(l,n,o,!1,null,null,null);e["default"]=s.exports}}]);