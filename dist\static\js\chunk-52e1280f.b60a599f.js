(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-52e1280f"],{"48bd":function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("span",[e._v("数据筛选")])]),i("div",{staticClass:"topBottom"},[i("div",{staticClass:"descriptions"},[i("el-descriptions",{attrs:{column:4}},[i("el-descriptions-item",[i("div",{staticClass:"labelStyle",attrs:{slot:"label"},slot:"label"},[e._v("单位名称")]),i("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入单位名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.protectionUnitName,callback:function(t){e.$set(e.queryParams,"protectionUnitName",t)},expression:"queryParams.protectionUnitName"}})],1)],1)],1),i("div",{staticClass:"tabButton"},[i("el-button",{staticClass:"queryBtn",attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v("重置")]),i("el-button",{staticClass:"queryBtn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v("搜索")])],1)])]),i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("span",[e._v("消防人员信息列表")]),i("el-button",{staticClass:"queryBtnT",attrs:{type:"primary",plain:"",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增消防人员")])],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{"cell-style":{padding:"0px"},"row-style":{height:"48px"},data:e.tableData}},[i("el-table-column",{attrs:{type:"index",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),i("el-table-column",{attrs:{label:"所属消防单位",align:"center",prop:"protectionUnitName"}}),i("el-table-column",{attrs:{label:"姓名",align:"center",prop:"name"}}),i("el-table-column",{attrs:{label:"性别",align:"center",prop:"sex"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",[e._v(" "+e._s(e.dict.type.sex.find((function(e){return e.value==t.row.sex})).label||"")+" ")])]}}])}),i("el-table-column",{attrs:{label:"联系电话",align:"center",prop:"phone"}}),i("el-table-column",{attrs:{label:"人员类型",align:"center",prop:"type"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",[e._v(" "+e._s(e.dict.type.firecontrol_firefighter_type.find((function(e){return e.value==t.row.type})).label||"")+" ")])]}}])}),i("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(i){return e.handleLook(t.row)}}},[e._v("详情")]),i("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(i){return e.handleUpdate(t.row)}}},[e._v("编辑")]),i("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v("删除")]),i("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-tickets"},on:{click:function(i){return e.handleRecord(t.row)}}},[e._v("信息变更记录")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}})],1),i("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"560px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"所属消防单位",prop:"protectionUnitId"}},[i("el-select",{staticStyle:{width:"245px"},attrs:{placeholder:"所属消防单位",disabled:e.disabled},model:{value:e.ruleForm.protectionUnitId,callback:function(t){e.$set(e.ruleForm,"protectionUnitId",t)},expression:"ruleForm.protectionUnitId"}},e._l(e.unitList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.protectionUnitName,value:e.id}})})),1)],1),i("el-form-item",{attrs:{label:"人员类型",prop:"type"}},[i("el-select",{staticStyle:{width:"245px"},attrs:{placeholder:"请选择人员类型",clearable:""},model:{value:e.ruleForm.type,callback:function(t){e.$set(e.ruleForm,"type",t)},expression:"ruleForm.type"}},e._l(e.dict.type.firecontrol_firefighter_type,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",{attrs:{label:"姓名",prop:"name"}},[i("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入姓名",maxlength:"32",disabled:e.disabled},model:{value:e.ruleForm.name,callback:function(t){e.$set(e.ruleForm,"name",t)},expression:"ruleForm.name"}})],1),i("el-form-item",{attrs:{label:"性别",prop:"sex"}},[i("el-radio-group",{attrs:{disabled:e.disabled},model:{value:e.ruleForm.sex,callback:function(t){e.$set(e.ruleForm,"sex",t)},expression:"ruleForm.sex"}},e._l(e.dict.type.sex,(function(t){return i("el-radio",{key:t.value,attrs:{label:t.value,value:t.value}},[e._v(" "+e._s(t.label)+" ")])})),1)],1),i("el-form-item",{attrs:{label:"民族",prop:"nation"}},[i("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入民族",maxlength:"32",disabled:e.disabled},model:{value:e.ruleForm.nation,callback:function(t){e.$set(e.ruleForm,"nation",t)},expression:"ruleForm.nation"}})],1),i("el-form-item",{attrs:{label:"政治面貌",prop:"politicCountenance"}},[i("el-radio-group",{model:{value:e.ruleForm.politicCountenance,callback:function(t){e.$set(e.ruleForm,"politicCountenance",t)},expression:"ruleForm.politicCountenance"}},e._l(e.dict.type.firecontrol_politic_countenance,(function(t){return i("el-radio",{key:t.value,attrs:{label:t.value,value:t.value}},[e._v(e._s(t.label))])})),1)],1),i("el-form-item",{attrs:{label:"身份证号",prop:"idCard"}},[i("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入身份证号",disabled:e.disabled,maxlength:"32"},model:{value:e.ruleForm.idCard,callback:function(t){e.$set(e.ruleForm,"idCard",t)},expression:"ruleForm.idCard"}})],1),i("el-form-item",{attrs:{label:"手机号码",prop:"phone"}},[i("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入手机号码",maxlength:"32"},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1),i("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[i("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入邮箱",maxlength:"32"},model:{value:e.ruleForm.email,callback:function(t){e.$set(e.ruleForm,"email",t)},expression:"ruleForm.email"}})],1),i("el-form-item",{attrs:{label:"紧急联系方式",prop:"emergencyContact"}},[i("el-input",{staticStyle:{width:"245px"},attrs:{placeholder:"请输入紧急联系方式",maxlength:"32"},model:{value:e.ruleForm.emergencyContact,callback:function(t){e.$set(e.ruleForm,"emergencyContact",t)},expression:"ruleForm.emergencyContact"}})],1)],1),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.unitSubmit}},[e._v("确 定")]),i("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")])],1)],1),i("el-dialog",{attrs:{title:"消防人员详情",visible:e.detailDialog,width:"560px"},on:{"update:visible":function(t){e.detailDialog=t}}},[i("el-descriptions",{attrs:{column:2}},[i("el-descriptions-item",{attrs:{label:"所属消防单位"}},[e._v(e._s(e.detail.protectionUnitName))]),i("el-descriptions-item",{attrs:{label:"人员类型"}},[e._v(e._s(e.detail.type))]),i("el-descriptions-item",{attrs:{label:"姓名"}},[e._v(e._s(e.detail.name))]),i("el-descriptions-item",{attrs:{label:"性别"}},[e._v(e._s(e.detail.sex))]),i("el-descriptions-item",{attrs:{label:"民族"}},[e._v(e._s(e.detail.nation))]),i("el-descriptions-item",{attrs:{label:"政治面貌"}},[e._v(e._s(e.detail.politicCountenance))]),i("el-descriptions-item",{attrs:{label:"身份证号"}},[e._v(e._s(e.detail.idCard))]),i("el-descriptions-item",{attrs:{label:"手机号码"}},[e._v(e._s(e.detail.phone))]),i("el-descriptions-item",{attrs:{label:"邮箱"}},[e._v(e._s(e.detail.email))]),i("el-descriptions-item",{attrs:{label:"紧急联系人"}},[e._v(e._s(e.detail.emergencyContact))])],1),i("el-button",{staticStyle:{"margin-top":"10px"},attrs:{type:"primary",icon:"el-icon-tickets"},on:{click:e.handleRecord}},[e._v("信息变更记录")])],1),i("el-dialog",{attrs:{title:"信息变更记录",visible:e.recordDialog,width:"560px"},on:{"update:visible":function(t){e.recordDialog=t}}},[i("el-timeline",{attrs:{reverse:!1}},e._l(e.records,(function(t,r){return i("el-timeline-item",{key:r,attrs:{timestamp:t.updateTime,placement:"top"}},e._l(t.firefighterChangeRecordDetailList,(function(t,r){return i("div",{key:r,staticStyle:{"margin-bottom":"20px"}},[e._v(" 变更内容："+e._s(t.field)+" "),i("br"),e._v(" "+e._s(t.beforeValue)+" "),i("i",{staticClass:"el-icon-right"}),e._v(" "+e._s(t.afterValue)+" ")])})),0)})),1)],1)],1)},a=[],l=(i("d3b7"),i("159b"),i("b64b"),i("e9c4"),i("25f0"),i("7db0"),i("b0c0"),i("b775"));function n(e){return Object(l["a"])({url:"/firecontrol-firefighter/page",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/firecontrol-firefighter/save",method:"post",data:e})}function s(e){return Object(l["a"])({url:"/firecontrol-firefighter/update",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/firecontrol-firefighter/delete",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/firecontrol-firefighter/detail",method:"get",params:e})}function d(e){return Object(l["a"])({url:"/firecontrol-protection-unit/list",method:"get",params:e})}function p(e){return Object(l["a"])({url:"/firecontrol-firefighter/changeRecord",method:"get",params:e})}var f={name:"policyConfiguration",dicts:["firecontrol_firefighter_type","sex","firecontrol_politic_countenance"],data:function(){return{loading:!1,showSearch:!0,total:0,tableData:null,open:!1,status:!0,queryParams:{current:1,size:10,protectionUnitName:void 0},optionSeen:[],title:"",disabled:!1,ruleForm:{protectionUnitId:"",type:"",name:"",sex:"",nation:"",politicCountenance:"",idCard:"",phone:"",email:"",emergencyContact:""},rules:{protectionUnitId:[{required:!0,message:"请选择所属消防单位",trigger:"change"}],type:[{required:!0,message:"请选择人员类型",trigger:"change"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],sex:[{required:!0,message:"请选择性别",trigger:"change"}],idCard:[{required:!0,message:"请输入身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"}]},dialogVisible:!1,unitList:[],detailDialog:!1,detail:{},recordDialog:!1,records:[]}},watch:{},created:function(){},mounted:function(){this.getList()},computed:{},methods:{getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){null!=t.data&&(e.tableData=t.data.records,e.total=t.data.total,d().then((function(i){e.unitList=i.data,t.data.records.forEach((function(e){i.data.forEach((function(t){e.protectionUnitId==t.id&&(e.protectionUnitName=t.protectionUnitName)}))}))}))),e.loading=!1}))},handleAdd:function(){this.$refs.ruleForm&&this.$refs.ruleForm.resetFields(),this.title="新增消防人员",this.dialogVisible=!0,this.disabled=!1},handleUpdate:function(e){var t=this;this.title="编辑消防人员",this.dialogVisible=!0,u({id:e.id}).then((function(e){t.ruleForm=JSON.parse(JSON.stringify(e.data)),t.ruleForm.protectionUnitId=t.ruleForm.protectionUnitId.toString()})),this.disabled=!0},handleLook:function(e){var t=this;this.detailDialog=!0,u({id:e.id}).then((function(e){t.unitList.forEach((function(t){e.data.protectionUnitId==t.id&&(e.data.protectionUnitName=t.protectionUnitName)})),e.data.type=t.dict.type.firecontrol_firefighter_type.find((function(t){return t.value==e.data.type})).label,e.data.sex=t.dict.type.sex.find((function(t){return t.value==e.data.sex})).label,e.data.politicCountenance=t.dict.type.firecontrol_politic_countenance.find((function(t){return t.value==e.data.politicCountenance})).label,t.detail=JSON.parse(JSON.stringify(e.data))}))},handleRecord:function(e){var t=this;this.recordDialog=!0,this.records=[],e.id?p({id:e.id}).then((function(e){e.data.forEach((function(e){e.firefighterChangeRecordDetailList.length>0&&e.firefighterChangeRecordDetailList.forEach((function(e){"人员类型"==e.field?(e.beforeValue=t.dict.type.firecontrol_firefighter_type.find((function(t){return t.value==e.beforeValue})).label||"",e.afterValue=t.dict.type.firecontrol_firefighter_type.find((function(t){return t.value==e.afterValue})).label||""):"政治面貌"==e.field&&(e.beforeValue=t.dict.type.firecontrol_politic_countenance.find((function(t){return t.value==e.beforeValue})).label||"",e.afterValue=t.dict.type.firecontrol_politic_countenance.find((function(t){return t.value==e.afterValue})).label||"")}))})),t.records=e.data})):p({id:this.detail.id}).then((function(e){e.data.forEach((function(e){e.firefighterChangeRecordDetailList.length>0&&e.firefighterChangeRecordDetailList.forEach((function(e){"人员类型"==e.field?(e.beforeValue=t.dict.type.firecontrol_firefighter_type.find((function(t){return t.value==e.beforeValue})).label||"",e.afterValue=t.dict.type.firecontrol_firefighter_type.find((function(t){return t.value==e.afterValue})).label||""):"政治面貌"==e.field&&(e.beforeValue=t.dict.type.firecontrol_politic_countenance.find((function(t){return t.value==e.beforeValue})).label||"",e.afterValue=t.dict.type.firecontrol_politic_countenance.find((function(t){return t.value==e.afterValue})).label||"")}))})),t.records=e.data}))},unitSubmit:function(){var e=this;this.$refs["ruleForm"].validate((function(t){if(!t)return console.log("error submit!!"),!1;console.log(e.ruleForm),e.ruleForm.id?s(e.ruleForm).then((function(t){200==t.code&&(e.$modal.msgSuccess("编辑人员成功！"),e.dialogVisible=!1,e.getList())})):o(e.ruleForm).then((function(t){200==t.code&&(e.$modal.msgSuccess("新增人员成功！"),e.dialogVisible=!1,e.getList())}))}))},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前人员").then((function(){return c({id:e.id})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleQuery:function(){this.queryParams.current=1,this.getList()},resetQuery:function(){this.queryParams.name="",this.handleQuery()}}},m=f,h=(i("a6ca"),i("2877")),b=Object(h["a"])(m,r,a,!1,null,"3221dfb2",null);t["default"]=b.exports},"6fd3":function(e,t,i){},a6ca:function(e,t,i){"use strict";i("6fd3")}}]);