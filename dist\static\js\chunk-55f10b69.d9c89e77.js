(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-55f10b69"],{"0330":function(e,t,i){"use strict";i("d4a8")},d4a8:function(e,t,i){},dc01:function(e,t,i){"use strict";i.d(t,"y",(function(){return a})),i.d(t,"a",(function(){return s})),i.d(t,"C",(function(){return r})),i.d(t,"B",(function(){return o})),i.d(t,"c",(function(){return l})),i.d(t,"m",(function(){return u})),i.d(t,"s",(function(){return c})),i.d(t,"z",(function(){return d})),i.d(t,"t",(function(){return p})),i.d(t,"A",(function(){return m})),i.d(t,"J",(function(){return h})),i.d(t,"K",(function(){return f})),i.d(t,"G",(function(){return b})),i.d(t,"M",(function(){return g})),i.d(t,"E",(function(){return v})),i.d(t,"w",(function(){return w})),i.d(t,"h",(function(){return k})),i.d(t,"f",(function(){return L})),i.d(t,"e",(function(){return y})),i.d(t,"q",(function(){return j})),i.d(t,"b",(function(){return D})),i.d(t,"r",(function(){return O})),i.d(t,"F",(function(){return I})),i.d(t,"k",(function(){return T})),i.d(t,"H",(function(){return C})),i.d(t,"l",(function(){return S})),i.d(t,"g",(function(){return A})),i.d(t,"D",(function(){return x})),i.d(t,"o",(function(){return $})),i.d(t,"I",(function(){return _})),i.d(t,"i",(function(){return N})),i.d(t,"j",(function(){return M})),i.d(t,"n",(function(){return G})),i.d(t,"x",(function(){return z})),i.d(t,"d",(function(){return P})),i.d(t,"u",(function(){return B})),i.d(t,"v",(function(){return W})),i.d(t,"p",(function(){return E})),i.d(t,"L",(function(){return q}));var n=i("b775");function a(e){return Object(n["a"])({url:"/schedule/arrangement/pageList",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/schedule/arrangement/save",method:"post",data:e})}function r(e){return Object(n["a"])({url:"/schedule/work-adjustment/pageList",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/schedule/schedule/mySchedule",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/schedule/work-adjustment/save",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/save",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/schedule/service-group/findList",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/schedule/service-group/getSelectList",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/schedule/member/findList",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/schedule/schedule/getShowData",method:"get",params:e})}function h(e){return Object(n["a"])({url:"/schedule/work-adjustment/detail",method:"get",params:e})}function f(e){return Object(n["a"])({url:"/schedule/work-adjustment/getActInfo",method:"get",params:e})}function b(e){return Object(n["a"])({url:"/schedule/work-adjustment/submitAct",method:"post",params:e})}function g(e){return Object(n["a"])({url:"/schedule/work-adjustment/withdrawAct",method:"get",params:e})}function v(e){return Object(n["a"])({url:"/schedule/schedule/pageList",method:"get",params:e})}function w(e){return Object(n["a"])({url:"/schedule/arrangement/getArrangementTypeList",method:"get",params:e})}function k(e){return Object(n["a"])({url:"/schedule/arrangement/findList",method:"get",params:e})}function L(e){return Object(n["a"])({url:"/schedule/arrangement/detail",method:"get",params:e})}function y(e){return Object(n["a"])({url:"/schedule/schedule/autoSetSchedule",method:"post",data:e})}function j(e){return Object(n["a"])({url:"/schedule/arrangement/deleteByIds",method:"post",params:e})}function D(e){return Object(n["a"])({url:"/schedule/schedule/save",method:"post",data:e})}function O(e){return Object(n["a"])({url:"/schedule/schedule/exportExcel",method:"get",params:e,responseType:"blob"})}function I(e){return Object(n["a"])({url:"/schedule/member-work/saveEntitys",method:"post",data:e})}function T(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/pageList",method:"get",params:e})}function C(e){return Object(n["a"])({url:"/schedule/work-adjustment/update",method:"post",data:e})}function S(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/update",method:"post",data:e})}function A(e){return Object(n["a"])({url:"/schedule/arrangement/update",method:"post",data:e})}function x(e){return Object(n["a"])({url:"/schedule/schedule/deleteByIds",method:"post",params:e})}function $(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/submitAct",method:"post",params:e})}function _(e){return Object(n["a"])({url:"/schedule/work-adjustment/deleteByIds",method:"post",params:e})}function N(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/deleteByIds",method:"post",params:e})}function M(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/detail",method:"get",params:e})}function G(e){return Object(n["a"])({url:"/schedule/repair-attend-apply/getActInfo",method:"get",params:e})}function z(e){return Object(n["a"])({url:"/schedule/sysQuery/getLoginMemberInfo",method:"get",params:e})}function P(e){return Object(n["a"])({url:"/schedule/work-adjustment/approvalOperation",method:"post",params:e})}function B(e){return Object(n["a"])({url:"/schedule/schedule/getAllShowData",method:"get",params:e})}function W(e){return Object(n["a"])({url:"/schedule/service-group/findList",method:"get"})}function E(e){return Object(n["a"])({url:"/schedule/arrangement/updateStatus",method:"post",params:e})}function q(e){return Object(n["a"])({url:"/schedule/service-group/updateRemind",method:"post",data:e})}},f882:function(e,t,i){"use strict";i.r(t);var n,a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"body"},[i("div",[i("div",{staticClass:"center"},[i("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formInline}},[i("el-form-item",{staticClass:"marb",attrs:{label:"补卡申请人："}},[i("el-input",{attrs:{placeholder:"请输入姓名",clearable:"",maxlength:20},model:{value:e.formInline.workName,callback:function(t){e.$set(e.formInline,"workName",t)},expression:"formInline.workName"}})],1),i("el-form-item",{staticClass:"marb",attrs:{label:"所属服务组："}},[i("el-select",{staticClass:"selectW",attrs:{placeholder:"请选择服务组"},model:{value:e.formInline.workServer,callback:function(t){e.$set(e.formInline,"workServer",t)},expression:"formInline.workServer"}},e._l(e.serviceArr,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),i("el-form-item",{staticClass:"marb",attrs:{label:"补卡类型："}},[i("el-select",{staticClass:"selectW",attrs:{placeholder:"请选择补卡类型"},model:{value:e.formInline.workType,callback:function(t){e.$set(e.formInline,"workType",t)},expression:"formInline.workType"}},e._l(e.dictList.dict.KQDK_type,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",{attrs:{label:"审批状态："}},[i("el-select",{staticClass:"selectW marl",attrs:{placeholder:"请选择审批状态"},model:{value:e.formInline.workState,callback:function(t){e.$set(e.formInline,"workState",t)},expression:"formInline.workState"}},e._l(e.dictList.dict.ACT_status,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",{attrs:{label:"补卡时间："}},[i("el-date-picker",{staticClass:"selectW marl",attrs:{type:"datetimerange","picker-options":e.pickerOptions,"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"right","value-format":"yyyy-MM-dd HH:mm:ss"},on:{change:e.chooseDateM},model:{value:e.workTime,callback:function(t){e.workTime=t},expression:"workTime"}})],1)],1)],1),i("div",{staticClass:"center-btn"},[i("div",[i("el-button",{on:{click:e.openDrawerBtn}},[e._v("新增审批")]),i("el-button",{on:{click:e.openDrawerBtn}},[e._v("批量导出")])],1),i("div",[i("el-button",{attrs:{type:"primary"},on:{click:e.findList}},[e._v("查询")]),i("el-button",{on:{click:e.resetList}},[e._v("重置")])],1)])]),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"highlight-current-row":!0}},[i("el-table-column",{attrs:{type:"selection",width:"55",align:"center","header-align":"center","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{prop:"userName",label:"员工",width:"200",align:"center","header-align":"center"}}),i("el-table-column",{attrs:{prop:"deptName",label:"所属部门",align:"center","header-align":"center","show-overflow-tooltip":""}}),i("el-table-column",{attrs:{prop:"serviceGroupName",label:"所属服务组",align:"center","header-align":"center"}}),i("el-table-column",{attrs:{prop:"repairAttendTypeName",label:"补卡类型",align:"center","header-align":"center"}}),i("el-table-column",{attrs:{prop:"repairAttendDatetime",label:"补卡时间",width:"200",align:"center","header-align":"center"}}),i("el-table-column",{attrs:{prop:"applyDatetime",label:"申请时间",width:"200",align:"center","header-align":"center"}}),i("el-table-column",{attrs:{prop:"approveStatusName",label:"审批状态",width:"200",align:"center","header-align":"center"}}),i("el-table-column",{attrs:{width:"240",label:"操作",align:"center","header-align":"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",[i("span",{staticClass:"caozuo",on:{click:function(i){return e.goDetail(t.$index,t.row)}}},[e._v("查看详情")]),"101"==t.row.approveStatus||"105"==t.row.approveStatus?i("span",{staticClass:"caozuo",on:{click:function(i){return e.editData(t.$index,t.row)}}},[e._v("编辑")]):e._e(),"101"==t.row.approveStatus||"105"==t.row.approveStatus?i("span",{staticClass:"caozuo",on:{click:function(i){return e.submit(t.$index,t.row)}}},[e._v("提交")]):e._e(),"102"==t.row.approveStatus||"103"==t.row.approveStatus?i("span",{staticClass:"caozuo",on:{click:function(i){return e.withdraw(t.$index,t.row)}}},[e._v("撤回")]):e._e(),"101"==t.row.approveStatus||"105"==t.row.approveStatus?i("span",{staticClass:"caozuo",on:{click:function(i){return e.deleteList(t.$index,t.row)}}},[e._v("删除")]):e._e()])]}}])})],1)],1),i("el-dialog",{attrs:{title:"新建审批",visible:e.openDrawer,"before-close":e.handleClose,"append-to-body":""},on:{"update:visible":function(t){e.openDrawer=t}}},[i("el-form",{ref:"siteList",attrs:{"label-position":e.labelPosition,"label-width":"100px",model:e.siteList,rules:e.rules}},[i("el-form-item",{attrs:{label:"员工",prop:"shiftMan"}},[i("el-select",{staticClass:"selectW1",attrs:{placeholder:"请选择员工"},model:{value:e.siteList.shiftMan,callback:function(t){e.$set(e.siteList,"shiftMan",t)},expression:"siteList.shiftMan"}},e._l(e.manArr,(function(e){return i("el-option",{key:e.id,attrs:{label:e.userName,value:e.id}})})),1)],1),i("el-form-item",{attrs:{label:"服务组",prop:"shiftGroup"}},[i("el-select",{staticClass:"selectW1",attrs:{placeholder:"请选择服务组"},on:{change:e.chooseFWZ},model:{value:e.siteList.shiftGroup,callback:function(t){e.$set(e.siteList,"shiftGroup",t)},expression:"siteList.shiftGroup"}},e._l(e.serviceArr,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),i("el-form-item",{attrs:{label:"补卡类型",prop:"shiftType"}},[i("el-select",{staticClass:"selectW1",attrs:{placeholder:"请选择补卡类型"},model:{value:e.siteList.shiftType,callback:function(t){e.$set(e.siteList,"shiftType",t)},expression:"siteList.shiftType"}},e._l(e.dictList.dict.KQDK_type,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",{attrs:{label:"补卡时间",prop:"restDatetime"}},[i("el-date-picker",{staticClass:"selectW1",attrs:{type:"datetime",align:"right","value-format":"yyyy-MM-dd hh:mm:ss",placeholder:"请选择补卡时间"},model:{value:e.siteList.restDatetime,callback:function(t){e.$set(e.siteList,"restDatetime",t)},expression:"siteList.restDatetime"}})],1),i("el-form-item",{attrs:{label:"附件上传"}},[i("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{action:e.uploadUrl,headers:e.headers,accept:"","on-remove":e.handleRemove,"on-success":e.handleSuccess,limit:10,"file-list":e.fileList}},[i("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")])],1)],1),i("el-form-item",{attrs:{label:"调班理由",prop:"shiftCause"}},[i("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请填写调班理由",maxlength:20},model:{value:e.siteList.shiftCause,callback:function(t){e.$set(e.siteList,"shiftCause",t)},expression:"siteList.shiftCause"}})],1),i("el-form-item",[i("div",{staticClass:"up-btn"},[i("el-button",{on:{click:function(t){e.openDrawer=!1}}},[e._v("取消")]),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitList("siteList")}}},[e._v("确定")])],1)])],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{limit:e.pageSize,page:e.pageNum,total:e.total},on:{"update:limit":function(t){e.pageSize=t},"update:page":function(t){e.pageNum=t},pagination:e.bkPageLists}})],1)},s=[],r=i("ade3"),o=(i("d3b7"),i("159b"),i("14d9"),i("b0c0"),i("a15b"),i("dc01")),l=i("5f87"),u={name:"",props:{},data:function(){return{rules:{shiftMan:[{required:!0,message:"请选择员工",trigger:"change"}],shiftGroup:[{required:!0,message:"请选择服务组",trigger:"change"}],shiftType:[{required:!0,message:"请选择补卡类型",trigger:"change"}],shiftCause:[{required:!0,message:"请输入调班理由",trigger:"blur"}],restDatetime:[{required:!0,message:"请选择补卡时间",trigger:"change"}]},uploadUrl:"attendance/file/uploadFile",headers:{Authorization:Object(l["a"])()},fileList:[],name:"环境数据",tabPosition:"1",formInline:{workName:"",workServer:"",workType:"",workState:"",workbeginDate:"",workendDate:""},workTime:"",loading:!1,tableData:[],pageSize:10,pageNum:1,total:0,alarm:"",targetList:[{name:"轻度",degree:"102001",startNum:"",startType:"1",endNum:"",endType:"1"},{name:"中度",degree:"102002",startNum:"",startType:"1",endNum:"",endType:"1"},{name:"重度",degree:"102003",startNum:"",startType:"1",endNum:"",endType:"1"}],symbolList:[{value:"1",label:"<"},{value:"2",label:"≤"}],openDrawer:!1,labelPosition:"right",manArr:[],serviceArr:[],siteList:{shiftMan:"",shiftGroup:"",shiftType:"",shiftCause:"",restDatetime:""},pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,i=new Date;i.setTime(i.getTime()-6048e5),e.$emit("pick",[i,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,i=new Date;i.setTime(i.getTime()-2592e6),e.$emit("pick",[i,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,i=new Date;i.setTime(i.getTime()-7776e6),e.$emit("pick",[i,t])}}]},vegeid:"",isAdd:1,detailList:{}}},created:function(){this.bkPageLists(),this.getDict(),this.findLists()},mounted:function(){},watch:{},filters:{},components:{},computed:{dictList:function(){return this.$store.state.dict}},methods:(n={bkDeleteByIdss:function(e){var t=this;this.loading=!0;var i={ids:e};console.log(i,"params"),Object(o["i"])(i).then((function(e){console.log(e),t.$message({message:"删除成功",type:"success"}),t.bkPageLists(),t.loading=!1}))},deleteList:function(e,t){console.log(e,t),this.bkDeleteByIdss(t.id)},handleRemove:function(e,t){var i=this;console.log(e,t),this.fileList=[],t.forEach((function(e){i.fileList.push({name:e.name,url:e.response?e.response.viewPath:e.url})})),console.log(this.fileList)},handleSuccess:function(e,t){console.log(t),this.fileList.push({name:t.name,url:t.response.viewPath,id:t.response.fileId}),console.log(this.fileList)},submit:function(e,t){this.bksubmitActs(t.id)},bksubmitActs:function(e){var t=this;this.loading=!0;var i={id:e};console.log(i,"params"),Object(o["o"])(i).then((function(e){t.$message({message:"提交成功",type:"success"}),setTimeout((function(){t.loading=!1,t.openDrawer=!1,t.bkPageLists()}),1e3)}))},submitList:function(e){var t=this;console.log(e),this.$refs[e].validate((function(e){if(console.log(e),!e)return console.log("error submit!!"),!1;1==t.isAdd?t.bkaddsaves():t.bkUpdates()}))},bkUpdates:function(){var e=this;this.loading=!0;var t=[];this.fileList.forEach((function(e,i){t.push(e.id)})),console.log(t);var i={memberId:this.siteList.shiftMan,serviceGroupId:this.siteList.shiftGroup,repairAttendType:this.siteList.shiftType,repairAttendDatetime:this.siteList.restDatetime,reason:this.siteList.shiftCause,fileIds:t.join(","),id:this.vegeid};console.log(i,"params"),Object(o["l"])(i).then((function(t){e.$message({message:"修改成功",type:"success"}),setTimeout((function(){e.loading=!1,e.openDrawer=!1,e.bkPageLists()}),1e3)}))},bkaddsaves:function(){var e=this;this.loading=!0;var t=[];this.fileList.forEach((function(e,i){t.push(e.id)})),console.log(t);var i={memberId:this.siteList.shiftMan,serviceGroupId:this.siteList.shiftGroup,repairAttendType:this.siteList.shiftType,repairAttendDatetime:this.siteList.restDatetime,reason:this.siteList.shiftCause,fileIds:t.join(",")};console.log(i,"params"),Object(o["m"])(i).then((function(t){e.$message({message:"新增成功",type:"success"}),setTimeout((function(){e.loading=!1,e.openDrawer=!1,e.bkPageLists()}),1e3)}))},chooseDateM:function(e){console.log(e,"val"),this.formInline.workbeginDate=e[0],this.formInline.workendDate=e[1],console.log(this.formInline.workbeginDate)},handleClose:function(e){this.$confirm("确认关闭？").then((function(t){e()})).catch((function(e){}))},bkDetails:function(e,t){var i=this,n={id:e};Object(o["j"])(n).then((function(e){i.detailList=e.data,i.fileList=i.detailList.files,i.isAdd=2,console.log(t," rowrow"),i.siteList.shiftMan=t.memberId,i.siteList.shiftGroup=t.serviceGroupId,i.siteList.shiftType=t.repairAttendType,i.siteList.shiftCause=t.reason,i.siteList.restDatetime=t.repairAttendDatetime,i.vegeid=t.id,i.openDrawer=!0}))},editData:function(e,t){this.bkDetails(t.id,t)},goDetail:function(e,t){this.$router.push({name:"replacementCardDetail",query:t})},onSubmit:function(){console.log("submit!")},findListMans:function(e){var t=this,i={serviceGroupId:e};Object(o["t"])(i).then((function(e){t.manArr=e.data}))},chooseFWZ:function(e){console.log(e),this.findListMans(e)}},Object(r["a"])(n,"handleClose",(function(e){this.$confirm("确认关闭？").then((function(t){e()})).catch((function(e){}))})),Object(r["a"])(n,"findLists",(function(){var e=this,t={};Object(o["s"])(t).then((function(t){e.serviceArr=t.data}))})),Object(r["a"])(n,"bkPageLists",(function(){var e=this;this.loading=!0;var t={repairAttendType:this.formInline.workType,approveStatus:this.formInline.workState,userName:this.formInline.workName,serviceGroupId:this.formInline.workServer,repairAttendDatetimeBg:this.formInline.workbeginDate,repairAttendDatetimeEd:this.formInline.workendDate,pageNum:this.pageNum,pageSize:this.pageSize};Object(o["k"])(t).then((function(t){e.tableData=t.data.list,e.total=t.data.total,console.log(e.tableData,"this.res"),e.loading=!1}))})),Object(r["a"])(n,"getDict",(function(){this.$store.dispatch("dict/setDict",{})})),Object(r["a"])(n,"findList",(function(){this.bkPageLists()})),Object(r["a"])(n,"resetList",(function(){this.tableData=[],this.pageNum=1,this.pageSize=10,this.formInline={},this.bkPageLists()})),Object(r["a"])(n,"openDrawerBtn",(function(){this.openDrawer=!0,this.isAdd=1,this.siteList.shiftMan="",this.siteList.shiftGroup="",this.siteList.shiftType="",this.siteList.shiftCause="",this.siteList.restDatetime="",this.fileList=[]})),n)},c=u,d=(i("0330"),i("2877")),p=Object(d["a"])(c,a,s,!1,null,"6dc296f2",null);t["default"]=p.exports}}]);