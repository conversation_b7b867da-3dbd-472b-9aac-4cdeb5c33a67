(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-572e4912"],{"5e10":function(e,t,i){"use strict";i("66c3")},"66c3":function(e,t,i){},"69b7":function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("el-row",{staticClass:"mb8",attrs:{gutter:10}},[i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增消防预演")])],1),i("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roleList}},[i("el-table-column",{attrs:{type:"index",width:"50"}}),i("el-table-column",{attrs:{label:"预演名称","show-overflow-tooltip":!0,prop:"name",align:"center"}}),i("el-table-column",{attrs:{label:"预演描述",prop:"description",align:"center"}}),i("el-table-column",{attrs:{label:"制定时间",prop:"ruleTime",align:"center"}}),i("el-table-column",{attrs:{label:"创建人",prop:"createUser",align:"center"}}),i("el-table-column",{attrs:{label:"预演次数",prop:"amount",align:"center"}}),i("el-table-column",{attrs:{label:"创建日期",prop:"createTime",align:"center"}}),i("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleUpdate(t.row)}}},[e._v("修改")]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),i("el-dialog",{attrs:{title:e.title,visible:e.open,width:"650px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[i("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"消防预演名称",prop:"name"}},[i("el-input",{attrs:{placeholder:"请输入消防预演名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),i("el-form-item",{attrs:{label:"制定日期",prop:"ruleTime"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择日期时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.ruleTime,callback:function(t){e.$set(e.form,"ruleTime",t)},expression:"form.ruleTime"}})],1),i("el-form-item",{attrs:{label:"备注",prop:"description"}},[i("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),i("el-form-item",{attrs:{label:"附件",prop:"attachment"}},[i("el-upload",{ref:"my-upload",attrs:{headers:e.headers,action:e.uploadImgUrl,"on-remove":e.handleRemove,"on-success":e.handlePreview,"file-list":e.fileList}},[i("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")])],1)],1),i("el-form-item",{directives:[{name:"show",rawName:"v-show",value:void 0!=e.form.id,expression:"form.id != undefined"}],attrs:{label:"演练记录"}},[i("el-button",{attrs:{type:"primary"},on:{click:e.addTabel}},[e._v("新增")])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingtable,expression:"loadingtable"},{name:"show",rawName:"v-show",value:void 0!=e.form.id,expression:"form.id != undefined"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"250",border:""}},[i("el-table-column",{attrs:{prop:"participant",label:"参与人员","show-overflow-tooltip":!0,align:"center"}}),i("el-table-column",{attrs:{prop:"rehearsalTime",label:"演练日期","show-overflow-tooltip":!0,align:"center"}}),i("el-table-column",{attrs:{prop:"result",label:"演练结果","show-overflow-tooltip":!0,align:"center"}}),i("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(i){return e.updateTabel(t.row)}}},[e._v("修改")]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(i){return e.delTabel(t.row)}}},[e._v("删除")])]}}])})],1),i("el-dialog",{attrs:{width:"600px",title:e.titleVisible,visible:e.innerVisible,"append-to-body":""},on:{"update:visible":function(t){e.innerVisible=t}}},[i("el-form",{ref:"formVisible",attrs:{model:e.formVisible,rules:e.rulesVisible,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"参与人员",prop:"participant"}},[i("el-input",{attrs:{placeholder:"请输入参与人员"},model:{value:e.formVisible.participant,callback:function(t){e.$set(e.formVisible,"participant",t)},expression:"formVisible.participant"}})],1),i("el-form-item",{attrs:{label:"演练时间",prop:"rehearsalTime"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择演练时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.formVisible.rehearsalTime,callback:function(t){e.$set(e.formVisible,"rehearsalTime",t)},expression:"formVisible.rehearsalTime"}})],1),i("el-form-item",{attrs:{label:"演练结果",prop:"result"}},[i("el-input",{attrs:{placeholder:"请输入演练结果"},model:{value:e.formVisible.result,callback:function(t){e.$set(e.formVisible,"result",t)},expression:"formVisible.result"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitFormVisible}},[e._v("确 定")]),i("el-button",{on:{click:e.cancelVisible}},[e._v("取 消")])],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),i("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},a=[],l=(i("d81d"),i("99af"),i("14d9"),i("a15b"),i("b64b"),i("e9c4"),i("b775"));function o(e){return Object(l["a"])({url:"/firecontrol-rehearsal/page",method:"get",params:e})}function s(e){return Object(l["a"])({url:"/firecontrol-rehearsal/save",method:"post",data:e})}function n(e){return Object(l["a"])({url:"/firecontrol-rehearsal/update",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/firecontrol-rehearsal-record/list",method:"get",params:e})}function m(e){return Object(l["a"])({url:"/firecontrol-rehearsal-record/save",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/firecontrol-rehearsal-record/update",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/firecontrol-rehearsal-record/delete",method:"post",data:e})}var p=i("90c5"),f={name:"FireFehearsal",data:function(){return{loading:!0,showSearch:!0,total:0,roleList:[{}],title:"",open:!1,queryParams:{current:1,size:10},form:{},dateRange:[],rules:{name:[{required:!0,message:"请输入消防预演名称",trigger:"blur"}],ruleTime:[{required:!0,message:"选择日期时间",trigger:"change"}]},tableData:[],loadingtable:!1,innerVisible:!1,formVisible:{},titleVisible:"",fileList:[],headers:{Authorization:localStorage.getItem("token")},uploadImgUrl:"/emergency-v2/file/uploadFile",uploadFile:"api/file/uploadFile",rulesVisible:{participant:[{required:!0,message:"请输入参与人员",trigger:"blur"}],rehearsalTime:[{required:!0,message:"选择演练时间",trigger:"change"}],result:[{required:!0,message:"请输入演练结果",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.roleList=t.data.records,e.total=t.data.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset(),this.getList()},reset:function(){this.form={id:void 0,name:void 0,ruleTime:void 0,description:void 0,attachment:void 0,amount:null},this.resetForm("form")},handleQuery:function(){this.queryParams.current=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handledownloadFile:function(e){var t=e.attachment.split(",");console.log(t),t.map((function(e){var t=e.split("/");console.log(t),window.open("/api/file/downloadFile?bucket=".concat(t[3],"&path=").concat(t[4],"/").concat(t[5],"&fileName=").concat(t[6]))}))},handleRemove:function(e,t){console.log(e,t);var i=[];t.map((function(e){i.push(e.url)})),this.form.attachment=i.join(",")},handlePreview:function(e,t,i){if(console.log(e,t,i),0==t.size)return this.$modal.msgWarning("当前文件大小不符合规范"),!0;var r=[];i.map((function(e){e.response?r.push(JSON.parse(p["a"].decryptAES(e.response,p["a"].aesKey))):r.push(e.url)})),this.form.attachment=r.join(",")},handleAdd:function(){this.reset(),this.open=!0,this.title="新增消防预演",this.form.amount=0,this.fileList=[]},handleUpdate:function(e){var t=this;if(this.reset(),this.open=!0,this.title="修改消防预演",this.form=JSON.parse(JSON.stringify(e)),this.fileList=[],this.form.amount=null,null!=e.attachment){var i=[];i=JSON.parse(JSON.stringify(this.form.attachment.split(","))),i.map((function(e,i){var r=e.split("/");t.fileList.push({name:Date.now()+"_"+r[r.length-1],url:e})}))}this.getPageRecord()},getPageRecord:function(){var e=this;this.loadingtable=!0,c({rehearsalId:this.form.id}).then((function(t){console.log(t),e.tableData=t.data,e.loadingtable=!1}))},resetVisible:function(){this.formVisible={id:void 0,participant:void 0,rehearsalTime:void 0,result:void 0,rehearsalId:this.form.id},this.resetForm("formVisible")},addTabel:function(){this.resetVisible(),this.innerVisible=!0,this.titleVisible="新增演练记录"},updateTabel:function(e){this.resetVisible(),this.innerVisible=!0,this.formVisible=JSON.parse(JSON.stringify(e)),this.titleVisible="编辑演练记录"},delTabel:function(e){var t=this;t.$modal.confirm("是否确认删除当前数据").then((function(){return d({id:e.id,rehearsalId:t.form.id})})).then((function(){t.getPageRecord(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},submitFormVisible:function(){var e=this;this.$refs["formVisible"].validate((function(t){t&&(void 0!=e.formVisible.id?u(e.formVisible).then((function(t){e.$modal.msgSuccess("修改成功"),e.innerVisible=!1,e.getPageRecord(),e.getList()})):m(e.formVisible).then((function(t){e.$modal.msgSuccess("新增成功"),e.innerVisible=!1,e.getPageRecord(),e.getList()})))}))},cancelVisible:function(){this.innerVisible=!1,this.resetVisible()},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(console.log(e.form),void 0!=e.form.id?n(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):s(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return n({id:e.id,isDeleted:1})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},h=f,b=(i("5e10"),i("2877")),g=Object(b["a"])(h,r,a,!1,null,"59356328",null);t["default"]=g.exports}}]);