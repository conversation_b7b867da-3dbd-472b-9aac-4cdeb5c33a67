(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-5899c0bc","chunk-44475347"],{"16f7":function(e,t,o){"use strict";o("4196")},"18a5":function(e,t,o){"use strict";o("5f3d")},4196:function(e,t,o){},"5f3d":function(e,t,o){},a85a:function(e,t,o){"use strict";o.r(t);var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("div",{staticClass:"titleDiv"},[e._v("当前值班人员")]),o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:10}},[o("el-carousel",{attrs:{interval:4e3,height:"200px"}},e._l(e.getDutyData,(function(t,a){return o("el-carousel-item",{key:a},[o("el-card",[o("div",{staticClass:"card_top"},[o("div",[o("p",[e._v("姓名："+e._s(t.name))]),o("p",[e._v("工号："+e._s(t.workNumber))]),o("p",[e._v("电话："+e._s(t.phone))])]),o("div",{staticStyle:{"text-align":"center"}},[o("p",{staticClass:"card_num"},[e._v(e._s(t.lncumbency))]),o("p",{staticStyle:{margin:"0"}},[e._v("在岗率")])])])])],1)})),1)],1),o("el-col",{attrs:{span:14}},[o("div",{staticStyle:{"margin-bottom":"20px"}},[o("span",[e._v("请选择消控室")]),o("el-select",{attrs:{placeholder:"请选择"},on:{change:e.fireControlChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.fireControlList,(function(e){return o("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),o("el-carousel",{attrs:{interval:4e3,height:"200px"}},e._l(e.FlvData,(function(t,a){return o("el-carousel-item",{key:a},[o("jessibuca-player",{ref:"videoPlayer",refInFor:!0,attrs:{id:"videoElement1",videoUrl:e.videoUrl,error:e.videoError,message:e.videoError,height:!1,hasAudio:e.hasAudio,fluent:"",autoplay:"",live:""}})],1)})),1)],1)],1),o("div",{staticClass:"titleDiv"},[e._v("查岗记录")]),o("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0}},[o("el-form-item",{attrs:{label:"姓名"}},[o("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),o("el-form-item",{attrs:{label:"应岗"}},[o("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"应岗",clearable:""},model:{value:e.queryParams.checkStatus,callback:function(t){e.$set(e.queryParams,"checkStatus",t)},expression:"queryParams.checkStatus"}},e._l(e.dict.type.check_post_status,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),o("el-form-item",{attrs:{label:"查岗时间",prop:"lxr"}},[o("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),o("el-form-item",[o("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("一键查岗")])],1),o("right-toolbar",{attrs:{"show-search":e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.postList}},[o("el-table-column",{attrs:{label:"姓名","show-overflow-tooltip":!0,prop:"inspectedPostUserName",align:"center"}}),o("el-table-column",{attrs:{label:"工号","show-overflow-tooltip":!0,prop:"inspectedPostWorkNumber",align:"center"}}),o("el-table-column",{attrs:{label:"应岗","show-overflow-tooltip":!0,prop:"checkStatus",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("dict-tag",{attrs:{options:e.dict.type.check_post_status,value:t.row.checkStatus,type:1}})]}}])}),o("el-table-column",{attrs:{label:"查岗时间","show-overflow-tooltip":!0,prop:"checkTime",align:"center"}}),o("el-table-column",{attrs:{label:"创建人","show-overflow-tooltip":!0,prop:"createUser",align:"center"}})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),o("el-row",{staticStyle:{"margin-top":"30px"},attrs:{gutter:20}},[o("el-col",{attrs:{span:6}},[o("div",{attrs:{id:"ecahrts_pie"}})]),o("el-col",{attrs:{span:18}},[o("el-form",{ref:"queryForm",staticStyle:{"margin-left":"3%"},attrs:{model:e.cahrtsFoem,size:"small",inline:!0}},[o("el-form-item",{attrs:{label:"值班人员",prop:"qy"}},[o("el-select",{attrs:{multiple:"","multiple-limit":2,placeholder:"请选择"},on:{change:e.staffChange},model:{value:e.cahrtsFoem.inspectedPostUserId,callback:function(t){e.$set(e.cahrtsFoem,"inspectedPostUserId",t)},expression:"cahrtsFoem.inspectedPostUserId"}},e._l(e.staffOptions,(function(e){return o("el-option",{key:e.workNumber,attrs:{label:e.staffName,value:e.workNumber}})})),1)],1),o("el-form-item",{attrs:{label:"日期",prop:"qy"}},[o("el-radio-group",{on:{change:e.typeChange},model:{value:e.cahrtsFoem.dateType,callback:function(t){e.$set(e.cahrtsFoem,"dateType",t)},expression:"cahrtsFoem.dateType"}},[o("el-radio",{attrs:{label:"currentWeek"}},[e._v("本周")]),o("el-radio",{attrs:{label:"currentMonth"}},[e._v("本月")])],1)],1)],1),o("div",{attrs:{id:"ecahrts_line"}})],1)],1),o("el-dialog",{attrs:{title:e.title,visible:e.open,width:"650px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[o("el-row",{staticStyle:{"margin-bottom":"10px"}},[o("span",[e._v("选择消控室：")]),o("el-select",{attrs:{placeholder:"请选择"},model:{value:e.roomId,callback:function(t){e.roomId=t},expression:"roomId"}},e._l(e.options,(function(e){return o("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")]),o("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},s=[],i=(o("d3b7"),o("159b"),o("14d9"),o("e9c4"),o("a434"),o("a9e3"),o("a15b"),o("b0c0"),o("313e")),n=o("b775");function r(e){return Object(n["a"])({url:"/firecontrol-check_post/page",method:"get",params:e})}function l(){return Object(n["a"])({url:"/firecontrol-room/roomList",method:"get"})}function c(e){return Object(n["a"])({url:"/firecontrol-check_post/save",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/firecontrol-check_post/getDutyPerson",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/firecontrol-check_post/getDutyTime",method:"post",data:e})}function h(e){return Object(n["a"])({url:"/firecontrol-check_post/getOnDutyRate",method:"post",data:e})}function m(e){return Object(n["a"])({url:"firecontrol-check-record/duty",megetthod:"get",params:e})}function p(e){return Object(n["a"])({url:"/monitor/getVideoStreaming",method:"post",data:e})}function f(e){return Object(n["a"])({url:"/firecontrol-room/relatedMonitor",method:"get",params:e})}function y(e){return Object(n["a"])({url:"/firecontrol-room/list",method:"get",params:e})}var g=o("eae2"),v={name:"FireControl",dicts:["check_post_status"],components:{jessibucaPlayer:g["default"]},data:function(){return{loading:!0,getDutyData:[],showSearch:!0,total:0,value:"",fireControlList:[],postList:[],title:"",open:!1,queryParams:{current:1,size:10,name:void 0,isChecked:void 0,startTime:void 0,endTime:void 0},startTime:"",endTime:"",form:{},dateRange:[],rules:{},hasAudio:!1,roomId:"",options:[],FlvData:[],videoUrl:"",staffOptions:[],diaArr:[{checkDate:"",checkTime:""}],cahrtsFoem:{inspectedPostUserId:[],dateType:"currentWeek",dutyMonth:[],staffName:[]},pickerOptions:{disabledDate:function(e){return e.getTime()<Date.now()-864e5}},pickerOptions1:{}}},created:function(){var e=this;l().then((function(t){e.options=t.data})),this.getDuty(),this.fireControl(),this.getList()},mounted:function(){this.startTime=this.$moment().weekday(1).format("YYYY-MM-DD"),this.endTime=this.$moment().weekday(7).format("YYYY-MM-DD"),console.log(this.startTime,this.endTime),this.getDutyPerson()},methods:{getList:function(){var e=this;this.loading=!0,r(this.queryParams).then((function(t){console.log(t),e.postList=t.data.records,e.total=t.data.total,e.loading=!1}))},getDutyPerson:function(){var e=this;this.staffOptions=[],u({startTime:this.startTime,endTime:this.endTime,isDelete:0}).then((function(t){e.staffOptions=t.data,console.log(e.staffOptions,"staffOptions"),e.cahrtsFoem.inspectedPostUserId=[],e.cahrtsFoem.staffName=[],t.data.forEach((function(t){e.cahrtsFoem.inspectedPostUserId.push(t.workNumber),e.cahrtsFoem.staffName.push(t.staffName)}));var o=[],a=[];e.cahrtsFoem.inspectedPostUserId.forEach((function(t){"currentWeek"==e.cahrtsFoem.dateType?(o.push({inspectedPostUserId:t,currentWeek:"1"}),a.push({inspectedPostUserId:t,currentWeek:"1"})):(o.push({inspectedPostUserId:t,currentMonth:"1"}),a.push({inspectedPostUserId:t,currentMonth:"1"}))})),d(o).then((function(t){e.cahrtsFoem.dutyTime=t.data,console.log(t,"图表数据");var o=[];if(t.data[0]){for(var a=0;a<t.data[0].length;a++)o.push(parseInt(a+1)+"号");e.cahrtsFoem.dutyMonth=o}e.rightEcharts()})),h(a).then((function(t){e.leftEcharts(t.data)}))}))},fireControlChange:function(e){var t=this;f({id:e}).then((function(e){console.log(e,"sss"),t.FlvData=e.data,e.data.forEach((function(e){console.log(e,"w-w-"),t.getvideoFlv(e.deviceId)}))}))},getvideoFlv:function(e){var t=this;p({equipmentIdList:[e]}).then((function(e){console.log(e.data[0].flvAddress,"ssssss"),t.$nextTick((function(){t.videoUrl=e.data[0].flvAddress,t.$refs.videoPlayer.play(t.videoUrl)}))}))},fireControl:function(){var e=this;y(null).then((function(t){200==t.code&&(e.fireControlList=t.data,console.log(e.$route.query,t),e.$route.query.id?e.value=e.$route.query.id:e.value=t.data[0].id),e.fireControlChange(e.value)}))},videoError:function(e){console.log("播放器错误："+JSON.stringify(e))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.diaArr=[{checkDate:"",checkTime:""}]},handleQuery:function(){this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.queryParams.current=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(){this.reset(),this.open=!0,this.title="一键查岗"},addRound:function(){this.diaArr.push({checkDate:"",checkTime:""})},removeRound:function(e){this.diaArr.splice(e,1)},staffChange:function(e){var t=this;this.cahrtsFoem.staffName=[],e.forEach((function(e){t.staffOptions.forEach((function(o){o.workNumber==e&&t.cahrtsFoem.staffName.push(o.staffName)}))}));var o=[],a=[];e.forEach((function(e){"currentWeek"==t.cahrtsFoem.dateType?(o.push({inspectedPostUserId:e,currentWeek:"1"}),a.push({inspectedPostUserId:e})):(o.push({inspectedPostUserId:e,currentMonth:"1"}),a.push({inspectedPostUserId:e}))})),d(o).then((function(e){t.cahrtsFoem.dutyTime=e.data,t.rightEcharts()})),h(a).then((function(e){t.leftEcharts(e.data)}))},typeChange:function(e){this.cahrtsFoem.dateType=e,"currentWeek"==e?(this.startTime=this.$moment().weekday(1).format("YYYY-MM-DD"),this.endTime=this.$moment().weekday(7).format("YYYY-MM-DD")):(this.startTime=this.$moment().startOf("month").format("YYYY-MM-DD"),this.endTime=this.$moment().endOf("month").format("YYYY-MM-DD")),this.getDutyPerson()},leftEcharts:function(e){var t=i["init"](document.getElementById("ecahrts_pie")),o=e,a={tooltip:{formatter:"{a} <br/>{b} : {c}%"},title:{show:!0,x:"center",y:"68%",text:"在岗率",textStyle:{fontSize:14,fontWeight:"bolder",fontStyle:"normal",color:"#31F3FF"}},series:[{name:"外部线",type:"gauge",radius:"95%",startAngle:225,endAngle:-45,axisLine:{lineStyle:{color:[[1,"#31F3FF"]],width:1}},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},detail:{show:!1},title:{show:!1}},{name:"外部刻度",type:"gauge",radius:"99%",min:0,max:100,splitNumber:10,startAngle:225,endAngle:-45,axisLine:{show:!1,lineStyle:{color:[[1,"rgba(0,0,0,0)"]]}},axisLabel:{show:!0,color:"#31F3FF",fontSize:10,distance:-20},axisTick:{show:!1},splitLine:{show:!1}},{name:"内部宽线条",type:"gauge",radius:"73%",startAngle:225,endAngle:-45,axisLine:{lineStyle:{color:[[1,"#122B3C"]],width:20}},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},detail:{show:!1},title:{show:!1}},{name:"内部细线条",type:"gauge",radius:"70%",startAngle:225,endAngle:-45,axisLine:{lineStyle:{color:[[1,"#122B3C"]],width:3}},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},detail:{show:!1},title:{show:!1}},{name:"间隔条形",type:"gauge",radius:"73%",z:4,splitNumber:35,startAngle:225,endAngle:-45,axisLine:{lineStyle:{opacity:0}},axisLabel:{show:!1},axisTick:{show:!0,length:20,splitNumber:1,lineStyle:{color:"#122B3C",width:1}},splitLine:{show:!1},detail:{show:!1},title:{show:!1}},{name:"数据",type:"gauge",radius:"72.5%",z:3,startAngle:225,max:100,endAngle:-45,axisLine:{lineStyle:{color:[[o/100,"#31F3FF"],[1,"#185363"]],width:20}},tooltip:{show:!1},axisLabel:{show:!1},axisTick:{show:!1},splitLine:{show:!1},detail:{formatter:function(e){return o+"%"},show:!0,fontWeight:"bold",fontSize:20},pointer:{show:!0,width:3},data:[{name:"",value:o}]},{name:"内圆环",type:"pie",radius:["4%","2%"],hoverAnimation:!1,tooltip:{show:!1},cursor:"default",labelLine:{normal:{show:!1}},itemStyle:{color:"#122B3C"},animation:!1,data:[1]},{name:"内圆环2",type:"pie",radius:"2%",hoverAnimation:!1,cursor:"default",tooltip:{show:!1},labelLine:{normal:{show:!1}},itemStyle:{color:"#31F3FF"},animation:!1,data:[1]}]};t.setOption(a)},rightEcharts:function(){var e=i["init"](document.getElementById("ecahrts_line")),t={grid:{top:"5%",left:"3%",right:"4%",bottom:"13%",containLabel:!0},backgroundColor:"#fff",legend:{icon:"rect",bottom:10,textStyle:{color:"#000"},data:this.cahrtsFoem.staffName,itemWidth:15,itemHeight:15},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:"currentWeek"==this.cahrtsFoem.dateType?["星期一","星期二","星期三","星期四","星期五","星期六","星期日"]:this.cahrtsFoem.dutyMonth,axisLabel:{textStyle:{fontSize:12,color:"#000"}},axisLine:{lineStyle:{color:"#000"}}},yAxis:{min:0,type:"value",minInterval:1,splitLine:{show:!0,lineStyle:{type:"dashed"}},axisLabel:{textStyle:{fontSize:12,color:"#000"}},axisLine:{show:!1,lineStyle:{color:"#fff"}}},series:[{name:this.cahrtsFoem.staffName[0],data:this.cahrtsFoem.dutyTime[0],type:"line",smooth:!1,areaStyle:{opacity:0},itemStyle:{normal:{color:"#197CD8",lineStyle:{color:"#197CD8"}}},lineStyle:{normal:{width:3}}},{name:this.cahrtsFoem.staffName[1],data:this.cahrtsFoem.dutyTime[1],type:"line",smooth:!1,areaStyle:{opacity:0},itemStyle:{normal:{color:"#2B9F50",lineStyle:{color:"#2B9F50"}}},lineStyle:{normal:{width:3}}}]};e.setOption(t)},getDuty:function(){var e=this;m().then((function(t){200==t.code&&(e.getDutyData=t.data),console.log(t,"值班人员")}))},submit:function(){var e=this;if(""!=this.roomId){var t=this.$moment().format("YYYY-MM-DD HH:mm:ss").split(":"),o=[t[0],Number(t[1])+1+"",t[2]];if(this.getDutyData){var a=[];this.getDutyData.forEach((function(t){console.log(t,"ssssssssssss"),a.push({checkStatus:"4010801",firecontrolRoomId:e.roomId,checkTime:o.join(":"),inspectedPostUserId:t.id,inspectedPostWorkNumber:t.workNumber,inspectedPostUserName:t.name})})),console.log(a),c(a).then((function(t){e.$modal.msgSuccess("新增成功"),e.getList(),e.open=!1}))}}else this.$modal.msgError("请选择安排查岗的消控室！")}}},b=v,w=(o("16f7"),o("2877")),k=Object(w["a"])(b,a,s,!1,null,"00360bf8",null);t["default"]=k.exports},eae2:function(e,t,o){"use strict";o.r(t);var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticStyle:{width:"auto",height:"300px"},attrs:{id:"jessibuca"}},[o("div",{ref:e.id,staticStyle:{width:"100%",height:"300px","background-color":"#000"},attrs:{id:e.id},on:{dblclick:e.fullscreenSwich}})])},s=[],i={name:"jessibuca",data:function(){return{jessibuca:null,playing:!1,isNotMute:!1,quieting:!1,fullscreen:!1,loaded:!1,speed:0,performance:"",kBps:0,btnDom:null,videoInfo:null,volume:1,rotate:0,vod:!0,forceNoOffscreen:!0}},props:["videoUrl","error","hasAudio","height","id"],mounted:function(){var e=this;window.onerror=function(e){};var t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){var o=document.getElementById(e.id);o.style.height=9/16*o.clientWidth+"px","undefined"==typeof e.videoUrl&&(e.videoUrl=t),console.log("初始化时的地址为: "+e.videoUrl),e.play(e.videoUrl)}))},created:function(){console.log(this.videoUrl)},methods:{create:function(){this.jessibuca=new JessibucaPro({container:"#"+this.id,decoder:"./decoder-pro.js",videoBuffer:.2,isResize:!1,text:"",loadingText:"加载中",debug:!0,isMulti:!0,useMSE:!0,useSIMD:!0,useWCS:!0,hasAudio:!1,useVideoRender:!0,controlAutoHide:!0,showBandwidth:!0,showPerformance:!1,operateBtns:{fullscreen:!0,screenshot:!0,play:!0,audio:!0},watermarkConfig:{text:{content:"摄像头"},right:10,top:10}}),this.jessibuca.on("fullscreen",(function(e){console.log("is fullscreen",index,e)}))},playBtnClick:function(e){this.play(this.videoUrl)},play:function(e){var t=this;console.log(e),this.jessibuca&&this.destroy(),this.create(),this.jessibuca.on("play",(function(){t.playing=!0,t.loaded=!0,t.quieting=t.jessibuca.quieting})),this.jessibuca.hasLoaded()?this.jessibuca.play(e):this.jessibuca.on("load",(function(){console.log("load 播放"),t.jessibuca.play(e)}))},pause:function(){this.jessibuca&&this.jessibuca.pause(),this.playing=!1,this.err="",this.performance=""},destroy:function(){this.jessibuca&&this.jessibuca.destroy(),this.jessibuca=null,this.playing=!1,this.err="",this.performance=""},eventcallbacK:function(e,t){console.log("player 事件回调"),console.log(e),console.log(t)},fullscreenSwich:function(){var e=this.isFullscreen();this.jessibuca.setFullscreen(!e),this.fullscreen=!e},isFullscreen:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}},destroyed:function(){this.jessibuca&&this.jessibuca.destroy(),this.playing=!1,this.loaded=!1,this.performance=""}},n=i,r=(o("18a5"),o("2877")),l=Object(r["a"])(n,a,s,!1,null,null,null);t["default"]=l.exports}}]);