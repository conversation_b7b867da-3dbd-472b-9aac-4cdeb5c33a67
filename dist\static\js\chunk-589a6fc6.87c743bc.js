(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-589a6fc6"],{7643:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"top_device"},[a("div",{staticClass:"top_card"},[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"top_title"},[e._v("全部设备")]),a("div",{staticClass:"top_num"},[e._v(e._s(e.deviceObj.total))])])],1),a("div",[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"top_title"},[e._v("正常")]),a("div",{staticClass:"top_num"},[e._v(e._s(e.deviceObj.normal))])])],1),a("div",[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"top_title"},[e._v("报警")]),a("div",{staticClass:"top_num"},[e._v(e._s(e.deviceObj.emergency))])])],1),a("div",[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"top_title"},[e._v("故障")]),a("div",{staticClass:"top_num"},[e._v(e._s(e.deviceObj.breakdown))])])],1),a("div",[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"top_title"},[e._v("离线")]),a("div",{staticClass:"top_num"},[e._v(e._s(e.deviceObj.offline))])])],1)]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:4,xs:24}},[a("div",{staticClass:"head-container"},[a("el-input",{staticStyle:{"margin-bottom":"20px"},attrs:{placeholder:"请输入设备名称",clearable:"",size:"small","prefix-icon":"el-icon-search"},model:{value:e.deptName,callback:function(t){e.deptName=t},expression:"deptName"}})],1),a("div",{staticClass:"head-container"},[a("el-tree",{ref:"tree",attrs:{data:e.dict.type.fire_device_type,props:e.defaultProps,"expand-on-click-node":!1,"filter-node-method":e.filterNode,"node-key":"id","default-expand-all":"","highlight-current":""},on:{"node-click":e.handleNodeClick}})],1)]),a("el-col",{attrs:{span:20,xs:24}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"设备编号",prop:"deviceId"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入设备编号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceId,callback:function(t){e.$set(e.queryParams,"deviceId",t)},expression:"queryParams.deviceId"}})],1),a("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入设备名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceName,callback:function(t){e.$set(e.queryParams,"deviceName",t)},expression:"queryParams.deviceName"}})],1),a("el-form-item",{attrs:{label:"设备状态",prop:"status"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"设备状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.fire_device_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"deviceId"}}),a("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),a("el-table-column",{attrs:{label:"所属部门",align:"center",prop:"organizationId","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e._f("getfullName")(t.row.organizationId,e.deptArr))+" ")]}}])}),a("el-table-column",{attrs:{label:"安装位置",align:"center",prop:"location","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"责任人",align:"center",prop:"principal","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.fire_device_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDetail(t.row)}}},[e._v("详情")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.hanldClock(t.row)}}},[e._v("禁用")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}})],1)],1),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px","label-position":"left"}},[a("el-collapse",{model:{value:e.activeNames,callback:function(t){e.activeNames=t},expression:"activeNames"}},[a("el-collapse-item",{attrs:{name:"1"}},[a("template",{slot:"title"},[a("div",{staticClass:"diaTil"},[a("p",[e._v("基础信息")])])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"设备编号",prop:"deviceId"}},[a("el-input",{attrs:{placeholder:"请输入设备编号",disabled:e.disabled},model:{value:e.form.deviceId,callback:function(t){e.$set(e.form,"deviceId",t)},expression:"form.deviceId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[a("el-input",{attrs:{placeholder:"请输入设备名称",disabled:e.disabled},model:{value:e.form.deviceName,callback:function(t){e.$set(e.form,"deviceName",t)},expression:"form.deviceName"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"设备型号",prop:"deviceModel"}},[a("el-input",{attrs:{placeholder:"请输入设备型号",disabled:e.disabled},model:{value:e.form.deviceModel,callback:function(t){e.$set(e.form,"deviceModel",t)},expression:"form.deviceModel"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"设备类型",prop:"deviceType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择设备类型",disabled:e.disabled},model:{value:e.form.deviceType,callback:function(t){e.$set(e.form,"deviceType",t)},expression:"form.deviceType"}},e._l(e.dict.type.fire_device_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"所属部门",prop:"organizationId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属部门",clearable:"",disabled:e.disabled},on:{click:function(t){return e.deptChange()}},model:{value:e.form.organizationId,callback:function(t){e.$set(e.form,"organizationId",t)},expression:"form.organizationId"}},e._l(e.deptArr,(function(e){return a("el-option",{key:e.organizationId,attrs:{label:e.name,value:e.organizationId}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"关联摄像头",prop:"monitor"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择关联摄像头"},model:{value:e.form.monitor,callback:function(t){e.$set(e.form,"monitor",t)},expression:"form.monitor"}})],1)],1)],1)],2),a("el-collapse-item",{attrs:{name:"2"}},[a("template",{slot:"title"},[a("div",{staticClass:"diaTil"},[a("p",[e._v("责任人")])])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"责任人",prop:"principal"}},[a("el-input",{attrs:{placeholder:"请输入责任人",disabled:e.disabled},model:{value:e.form.principal,callback:function(t){e.$set(e.form,"principal",t)},expression:"form.principal"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{attrs:{disabled:e.disabled,placeholder:"请输入联系电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1)],1)],2),a("el-collapse-item",{attrs:{name:"3"}},[a("template",{slot:"title"},[a("div",{staticClass:"diaTil"},[a("p",[e._v("安装位置")])])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"安装区域",prop:"griddingId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.disabled,placeholder:"安装区域"},model:{value:e.form.griddingId,callback:function(t){e.$set(e.form,"griddingId",t)},expression:"form.griddingId"}},e._l(e.areaArr,(function(e){return a("el-option",{key:e.griddingId,attrs:{label:e.griddingName,value:e.griddingId}})})),1)],1)],1)],1)],2),a("el-collapse-item",{attrs:{name:"5"}},[a("template",{slot:"title"},[a("div",{staticClass:"diaTil"},[a("p",[e._v("设备照片")])])]),a("el-upload",{attrs:{disabled:e.disabled,action:e.uploadPic,"list-type":"picture-card","on-remove":e.handleRemove,"on-preview":e.handlePreview}},[a("i",{staticClass:"el-icon-camera-solid"})])],2)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:e.upload.title,visible:e.upload.open,width:"400px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.upload,"open",t)}}},[a("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:e.upload.headers,action:e.upload.url+"?updateSupport="+e.upload.updateSupport,disabled:e.upload.isUploading,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,"auto-upload":!1,drag:""}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip text-center",attrs:{slot:"tip"},slot:"tip"},[a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("el-checkbox",{model:{value:e.upload.updateSupport,callback:function(t){e.$set(e.upload,"updateSupport",t)},expression:"upload.updateSupport"}}),e._v(" 是否更新已经存在的用户数据 ")],1),a("span",[e._v("仅允许导入xls、xlsx格式文件。")]),a("el-link",{staticStyle:{"font-size":"12px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:e.importTemplate}},[e._v("下载模板")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.upload.open=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"设备详情",visible:e.detailOpen,width:"1000px","append-to-body":""},on:{"update:visible":function(t){e.detailOpen=t}}},[a("el-card",{attrs:{shadow:"hover"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:14}},[a("div",{staticClass:"diadevice_top",staticStyle:{"margin-bottom":"40px"}},[a("div",[e._v("设备编号："+e._s(e.diaDeviceObj.deviceId))]),a("div",[e._v("设备名称："+e._s(e.diaDeviceObj.deviceName))]),a("div",[e._v(" 所属部门："+e._s(e._f("getfullName")(e.diaDeviceObj.organizationId,e.deptArr))+" ")])]),a("div",{staticClass:"diadevice_top"},[a("div",{staticStyle:{display:"flex"}},[e._v(" 当前状态： "),a("dict-tag",{attrs:{options:e.dict.type.fire_device_status,value:e.diaDeviceObj.status,type:1}})],1)])])],1)],1),a("el-button-group",{staticStyle:{"margin-top":"10px"}},[a("el-button",{attrs:{type:"1"==e.btnDia?"primary":"",size:"mini"},on:{click:function(t){return e.diaDeatilBtn("1")}}},[e._v("设备状态")]),a("el-button",{attrs:{type:"2"==e.btnDia?"primary":"",size:"mini"},on:{click:function(t){return e.diaDeatilBtn("2")}}},[e._v("运行数据")])],1),a("el-form",{ref:"diaFrom",staticStyle:{"margin-top":"10px"},attrs:{model:e.diaFrom,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:""}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.diaFrom.dateRange,callback:function(t){e.$set(e.diaFrom,"dateRange",t)},expression:"diaFrom.dateRange"}})],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"1"==e.btnDia,expression:"btnDia == '1'"}],attrs:{label:""}},[a("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"设备状态",clearable:""},model:{value:e.diaFrom.status,callback:function(t){e.$set(e.diaFrom,"status",t)},expression:"diaFrom.status"}},e._l(e.dict.type.fire_device_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"3"==e.btnDia,expression:"btnDia == '3'"}],attrs:{label:"报警类型"}},[a("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"报警类型",clearable:""},model:{value:e.diaFrom.emergencyType,callback:function(t){e.$set(e.diaFrom,"emergencyType",t)},expression:"diaFrom.emergencyType"}},e._l(e.dict.type.emergency_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"3"==e.btnDia,expression:"btnDia == '3'"}],attrs:{label:"处理状态"}},[a("el-select",{staticStyle:{width:"200px"},attrs:{placeholder:"处理状态",clearable:""},model:{value:e.diaFrom.emergencyStatus,callback:function(t){e.$set(e.diaFrom,"emergencyStatus",t)},expression:"diaFrom.emergencyStatus"}},e._l(e.dict.type.emergency_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:function(t){return e.diaSerch()}}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:function(t){return e.resetTbableDia()}}},[e._v("重置")])],1)],1),a("div",{staticStyle:{width:"100%",height:"400px"}},[0!=e.tableData.length?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingDia,expression:"loadingDia"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"350",border:""}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),"3"==e.btnDia?a("el-table-column",{attrs:{prop:"emergencyType",label:"告警类型",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.emergency_type,value:t.row.emergencyType,type:1}})]}}],null,!1,3505228643)}):e._e(),"1"==e.btnDia?a("el-table-column",{attrs:{prop:"createTime",label:"时间",align:"center"}}):e._e(),"2"==e.btnDia?a("el-table-column",{attrs:{prop:"collectTime",label:"时间",align:"center"}}):e._e(),"2"==e.btnDia?a("el-table-column",{attrs:{prop:"dataType",label:"参数",align:"center"}}):e._e(),"2"==e.btnDia?a("el-table-column",{attrs:{prop:"dataValue",label:"参数值",align:"center"}}):e._e(),"1"==e.btnDia?a("el-table-column",{attrs:{prop:"status",label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.fire_device_status,value:t.row.status,type:1}})]}}],null,!1,1542345692)}):e._e(),"3"==e.btnDia?a("el-table-column",{attrs:{prop:"emergencyStatus",label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.emergency_status,value:t.row.emergencyStatus,type:1}})]}}],null,!1,52819043)}):e._e(),"3"==e.btnDia?a("el-table-column",{attrs:{label:"操作",align:"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.getdiaDetail(t.row)}}},[e._v("详情")]),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.getchukli(t.row)}}},[e._v("处理")])]}}],null,!1,3805453048)}):e._e()],1):e._e(),0!=e.tableData.length?a("pagination",{attrs:{total:e.totalDia,page:e.diaFrom.current,limit:e.diaFrom.size},on:{"update:page":function(t){return e.$set(e.diaFrom,"current",t)},"update:limit":function(t){return e.$set(e.diaFrom,"size",t)},pagination:e.diaGitList}}):e._e()],1),a("el-dialog",{attrs:{title:"告警详情",visible:e.openDetail,width:"1000px","append-to-body":""},on:{"update:visible":function(t){e.openDetail=t}}},[a("div",{staticClass:"diaTil"},[a("p",[e._v("处理记录")])]),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableDataDetail,height:"200",border:""}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"createTime",label:"时间",align:"center"}}),a("el-table-column",{attrs:{prop:"date",label:"状态",align:"center"}}),a("el-table-column",{attrs:{prop:"description",label:"内容",align:"center"}})],1),a("div",{staticClass:"diaTil",staticStyle:{"padding-top":"10px"}},[a("p",[e._v("设备信息")])]),a("el-card",{attrs:{shadow:"hover"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:14,xs:14}},[a("div",{staticClass:"diadevice_top",staticStyle:{"margin-bottom":"30px"}},[a("div",[e._v("设备编号："+e._s(e.detailDiadeviceObj.deviceId))]),a("div",[e._v("设备名称："+e._s(e.detailDiadeviceObj.deviceName))]),a("div",[e._v("所属部门："+e._s(e.detailDiadeviceObj.organizationName))])]),a("div",{staticClass:"diadevice_top",staticStyle:{"margin-bottom":"30px"}},[a("div",{staticStyle:{display:"flex"}},[e._v(" 当前状态： "),a("dict-tag",{attrs:{options:e.dict.type.fire_device_status,value:e.detailDiadeviceObj.status,type:1}})],1),a("div",[e._v("安装位置："+e._s(e.detailDiadeviceObj.location))]),a("div",[e._v("关联摄像头："+e._s(e.detailDiadeviceObj.monitor))])]),a("div",{staticClass:"diadevice_top"},[a("div",[e._v("责任人："+e._s(e.detailDiadeviceObj.principal))]),a("div",[e._v("联系电话："+e._s(e.detailDiadeviceObj.phone))])])]),a("el-col",{attrs:{span:10,xs:10}},[a("div",{staticStyle:{width:"100%",height:"120px",background:"#333",color:"#fff","text-align":"center","line-height":"120px"}},[e._v(" 暂无视频 ")])])],1)],1),a("div",{staticClass:"diaTil",staticStyle:{"padding-top":"10px"}},[a("p",[e._v("告警信息")])]),a("el-card",{attrs:{shadow:"hover"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8,xs:8}},[a("div",{staticStyle:{display:"flex"}},[e._v(" 告警类型："),a("dict-tag",{attrs:{options:e.dict.type.emergency_type,value:e.detailDiaemergencyObj.emergencyType,type:1}})],1)]),a("el-col",{attrs:{span:8,xs:8}},[a("div",[e._v("告警值："+e._s(e.detailDiaemergencyObj.threshold)+"%")])]),a("el-col",{attrs:{span:8,xs:8}},[a("div",[e._v("告警时间："+e._s(e.detailDiaemergencyObj.createTime))])])],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancelDetail}},[e._v("关 闭")])],1)],1),a("el-dialog",{attrs:{title:"告警处理",visible:e.opengaojin,width:"900px","append-to-body":""},on:{"update:visible":function(t){e.opengaojin=t}}},[a("el-form",{ref:"formgaojin",attrs:{model:e.formgaojin,rules:e.rulesgaojin,"label-width":"100px","label-position":"left"}},[a("el-card",{attrs:{shadow:"hover"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:14,xs:14}},[a("div",{staticClass:"diadevice_top",staticStyle:{"margin-bottom":"40px"}},[a("div",[e._v("ID："+e._s(e.detailDiadeviceObj.deviceId))]),a("div",[e._v("设备名称："+e._s(e.detailDiadeviceObj.deviceName))]),a("div",[e._v("所属部门："+e._s(e.detailDiadeviceObj.organizationName))])]),a("div",{staticClass:"diadevice_top"},[a("div",{staticStyle:{display:"flex"}},[e._v(" 当前状态： "),a("dict-tag",{attrs:{options:e.dict.type.fire_device_status,value:e.detailDiadeviceObj.status,type:1}})],1),a("div",[e._v("安装位置："+e._s(e.detailDiadeviceObj.location))]),a("div",[e._v("关联摄像头："+e._s(e.detailDiadeviceObj.monitor))])])]),a("el-col",{attrs:{span:10,xs:10}},[a("div",{staticStyle:{width:"100%",height:"120px",background:"#333",color:"#fff","text-align":"center","line-height":"120px"}},[e._v(" 暂无视频 ")])])],1)],1),a("el-row",{staticStyle:{"padding-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:12,xs:12}},[a("el-form-item",{attrs:{label:"告警类型",prop:"emergencyType"}},[a("el-select",{attrs:{placeholder:"告警类型",clearable:""},model:{value:e.formgaojin.emergencyType,callback:function(t){e.$set(e.formgaojin,"emergencyType",t)},expression:"formgaojin.emergencyType"}},e._l(e.dict.type.emergency_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12,xs:12}},[a("el-form-item",{attrs:{label:"处理方式",prop:"handleType"}},[a("el-radio-group",{model:{value:e.formgaojin.handleType,callback:function(t){e.$set(e.formgaojin,"handleType",t)},expression:"formgaojin.handleType"}},[a("el-radio",{attrs:{label:4010701}},[e._v("线下处理")]),a("el-radio",{attrs:{label:4010602}},[e._v("转工单")])],1)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"处理描述",prop:"description"}},[a("el-input",{attrs:{type:"textarea",placeholder:"处理描述"},model:{value:e.formgaojin.description,callback:function(t){e.$set(e.formgaojin,"description",t)},expression:"formgaojin.description"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"上传照片",prop:"photo"}},[a("el-upload",{attrs:{action:e.uploadPic,"list-type":"picture-card","on-remove":e.handleRemovegaojin,"on-preview":e.handlePreviewgaojin}},[a("i",{staticClass:"el-icon-camera-solid"})])],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFormgaojin}},[e._v("完成 处理")]),a("el-button",{on:{click:e.cancelgaojin}},[e._v("取 消")])],1)],1)],1)],1)},l=[],o=(a("d9e2"),a("ac1f"),a("00b4"),a("4de4"),a("d3b7"),a("d81d"),a("b0c0"),a("159b"),a("14d9"),a("5f87")),r=a("b775");function s(e){return Object(r["a"])({url:"/firecontrol-device/page",method:"get",params:e})}function n(e){return Object(r["a"])({url:"/firecontrol-area/page",method:"get",params:e})}function d(e){return Object(r["a"])({url:"/organization/tree",method:"get",params:e})}function c(e){return Object(r["a"])({url:"/firecontrol-device/save",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/firecontrol-device/update",method:"post",data:e})}function p(e){return Object(r["a"])({url:"/firecontrol-device/data",method:"post",data:e})}function m(e){return Object(r["a"])({url:"/firecontrol-history/pageForDeviceId",method:"get",params:e})}function v(e){return Object(r["a"])({url:"/firecontrol-device-emergency/page",method:"get",params:e})}function h(e){return Object(r["a"])({url:"/firecontrol-device-status/page",method:"get",params:e})}function g(e){return Object(r["a"])({url:"/firecontrol-device-emergency/detail",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/firecontrol-device-emergency/deal",method:"post",data:e})}var b={name:"DeviceManagement",dicts:["fire_device_type","fire_device_status","emergency_type","symbol","emergency_status","param_type"],data:function(){var e=function(e,t,a){""===t?a(new Error("请输入绑定的手机号码")):/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(t)?a():a(new Error("请输入正确的手机号码"))};return{loading:!1,loadingDia:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,totalDia:0,userList:null,title:"",deptOptions:void 0,open:!1,deptName:void 0,dateRange:[],areaArr:[],tableData:[],form:{},activeNames:["1"],defaultProps:{children:"children",label:"label"},uploadPic:"api/file/uploadFilePic",deptArr:[],upload:{open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+Object(o["a"])()},url:"api/file/uploadFilePic"},queryParams:{deviceId:void 0,deviceName:void 0,status:void 0,deviceType:void 0,current:1,size:10},rules:{deviceId:[{required:!0,message:"请输入设备编号",trigger:"blur"}],deviceName:[{required:!0,message:"请输入设备名称",trigger:"blur"}],deviceModel:[{required:!0,message:"请输入设备型号",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"blur"}],organizationId:[{required:!0,message:"请选择所属部门",trigger:"blur"}],monitor:[{required:!0,message:"请选择关联摄像头",trigger:"blur"}],principal:[{required:!0,message:"请输入责任人",trigger:"blur"}],location:[{required:!0,message:"请输入详细地址",trigger:"blur"}],threshold:[{required:!0,message:"请输入报警阈值",trigger:"blur"}],griddingId:[{required:!0,message:"请选择安装区域",trigger:"blur"}],emergencyType:[{required:!0,message:"请选择报警类型",trigger:"blur"}],phone:[{validator:e,required:!0,trigger:"blur"}]},detailOpen:!1,btnDia:"2",diaFrom:{},diaId:void 0,deviceObj:{},diaDeviceObj:{},openDetail:!1,tableDataDetail:[],detailDiadeviceObj:{},detailDiaemergencyObj:{},opengaojin:!1,formgaojin:{},rulesgaojin:{handleType:[{required:!0,message:"处理方式",trigger:"blur"}],emergencyType:[{required:!0,message:"告警类型",trigger:"blur"}],description:[{required:!0,message:"处理描述",trigger:"blur"}]},disabled:!1}},watch:{deptName:function(e){this.$refs.tree.filter(e)}},filters:{getfullName:function(e,t){var a="";return t.map((function(t){e==t.id&&(a=t.name)})),a}},created:function(){this.getTreeList(),this.getList(),this.getAreaPage(),this.getdeviceData()},methods:{getAreaPage:function(){var e=this;n({current:1,size:1e3}).then((function(t){e.areaArr=t.data.records}))},getList:function(){var e=this;this.loading=!0,s(this.queryParams).then((function(t){e.userList=t.data.records,e.total=t.data.total,e.loading=!1}))},getTreeList:function(){var e=this;d().then((function(t){console.log(t),e.deptArr=t.data[0].children}))},getdeviceData:function(){var e=this;p({}).then((function(t){e.deviceObj=t.data}))},filterNode:function(e,t){return!e||-1!==t.label.indexOf(e)},handleNodeClick:function(e){this.queryParams.deviceType=e.value,this.handleQuery()},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:void 0,deviceId:void 0,deviceName:void 0,deviceType:void 0,organizationId:void 0,deviceModel:void 0,monitor:void 0,principal:void 0,phone:void 0,griddingId:void 0,location:void 0,emergencyType:void 0,threshold:void 0,photo:void 0,status:"4010501"},this.resetForm("form")},handleQuery:function(){this.queryParams.current=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.queryParams.deviceType=void 0,this.$refs.tree.setCurrentKey(null),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="新增设备",this.disabled=!1},handleUpdate:function(e){this.reset(),this.open=!0,this.title="关联摄像头",this.form=e,this.disabled=!0},diaSerch:function(){this.diaFrom.current=1,this.diaFrom.dateRange.length>0&&(this.diaFrom.startTime=this.diaFrom.dateRange[0],this.diaFrom.endTime=this.diaFrom.dateRange[1]),"1"==this.btnDia?this.pageStatus():"2"==this.btnDia?this.pageDevice():this.pageEmergency()},diaGitList:function(){"1"==this.btnDia?this.pageStatus():"2"==this.btnDia?this.pageDevice():this.pageEmergency()},resetTbableDia:function(){this.diaReset(),this.diaGitList()},diaReset:function(){this.diaFrom={startTime:void 0,endTime:void 0,param:void 0,value:void 0,id:this.diaId,current:1,size:10,dateRange:[],emergencyType:void 0,emergencyStatus:void 0,status:void 0}},diaDeatilBtn:function(e){this.btnDia=e,this.diaReset(),this.diaGitList()},handleDetail:function(e){this.detailOpen=!0,this.diaId=e.id,this.diaReset(),this.diaDeviceObj=e,this.pageDevice()},pageDevice:function(){var e=this;this.loadingDia=!0,this.tableData=[],this.diaFrom.deviceId=this.diaDeviceObj.deviceId,console.log(this.diaFrom,this.diaDeviceObj),m(this.diaFrom).then((function(t){console.log(t),e.tableData=t.data.records,e.totalDia=t.data.total,e.loadingDia=!1}))},pageEmergency:function(){var e=this;this.loadingDia=!0,this.tableData=[],v(this.diaFrom).then((function(t){e.tableData=t.data.records,e.totalDia=t.data.total,e.loadingDia=!1}))},pageStatus:function(){var e=this;this.loadingDia=!0,this.tableData=[],h(this.diaFrom).then((function(t){e.tableData=t.data.records,e.totalDia=t.data.total,e.loadingDia=!1}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.id?u({firecontrolDevices:[e.form]}).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},deleMory:function(){var e=this,t=[];this.ids.forEach((function(e){t.push({id:e,isDeleted:1})})),this.$modal.confirm("是否确认删除当前数据").then((function(){return u({firecontrolDevices:t})})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},statMory:function(){var e=this,t=[];this.ids.forEach((function(e){t.push({id:e,status:"4010501"})})),this.$modal.confirm("是否确认修改当前状态").then((function(){return u({firecontrolDevices:t})})).then((function(){e.getList(),e.$modal.msgSuccess("修改成功")})).catch((function(){}))},clockMory:function(){var e=this,t=[];this.ids.forEach((function(e){t.push({id:e,status:"4010505"})})),console.log(t),this.$modal.confirm("是否确认修改当前状态").then((function(){return u({firecontrolDevices:t})})).then((function(){e.getList(),e.$modal.msgSuccess("修改成功")})).catch((function(){}))},hanldClock:function(e){var t=this;this.$modal.confirm("是否确认修改当前状态").then((function(){return u({firecontrolDevices:[{id:e.id,status:"4010505"}]})})).then((function(){t.getList(),t.$modal.msgSuccess("修改成功")})).catch((function(){}))},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认修改当前状态").then((function(){return u({firecontrolDevices:[{id:e.id,isDeleted:1}]})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},getdiaDetail:function(e){var t=this;this.openDetail=!0,this.tableDataDetail=[],this.detailDiadeviceObj={},this.detailDiaemergencyObj={},g({id:e.id}).then((function(e){t.tableDataDetail=e.data.deals,t.detailDiadeviceObj=null==e.data.device?{}:e.data.device,t.detailDiaemergencyObj=e.data.emergency}))},cancelDetail:function(){this.openDetail=!1,this.tableDataDetail=[],this.detailDiadeviceObj={},this.detailDiaemergencyObj={}},cancelgaojin:function(){this.openDetail=!1,this.resetgaojin()},getchukli:function(e){var t=this;this.opengaojin=!0,this.resetgaojin(),g({id:e.id}).then((function(e){t.detailDiadeviceObj=null==e.data.device?{}:e.data.device})),this.formgaojin.emergencyId=e.id,console.log(this.formgaojin)},resetgaojin:function(){this.formgaojin={emergencyId:void 0,emergencyType:void 0,handleType:void 0,description:void 0,photo:void 0},this.resetForm("formgaojin")},submitFormgaojin:function(){var e=this;console.log(this.formgaojin),this.$refs["formgaojin"].validate((function(t){t&&f(e.formgaojin).then((function(t){e.$modal.msgSuccess("处理成功"),e.opengaojin=!1,e.diaSerch()}))}))},handleRemovegaojin:function(e,t){console.log(e,t)},handlePreviewgaojin:function(e){console.log(e)},handleRemove:function(e,t){console.log(e,t)},handlePreview:function(e){console.log(e)},handleImport:function(){this.upload.title="设备导入",this.upload.open=!0},importTemplate:function(){this.download("system/user/importTemplate",{},"user_template_".concat((new Date).getTime(),".xlsx"))},handleFileUploadProgress:function(e,t,a){this.upload.isUploading=!0},handleFileSuccess:function(e,t,a){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),this.getList()},submitFileForm:function(){this.$refs.upload.submit()}}},y=b,_=(a("c8bf"),a("2877")),D=Object(_["a"])(y,i,l,!1,null,"2921cb7f",null);t["default"]=D.exports},"7a01":function(e,t,a){},c8bf:function(e,t,a){"use strict";a("7a01")}}]);