(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-5cacb3a4"],{1253:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"body"},[a("el-card",{staticClass:"out"},[a("div",{staticClass:"content"},[a("div",{staticClass:"content_left"},[a("el-card",[a("MyCalendar",{attrs:{timeStemp:t.timeStemp1,isYear:!0,nowDataList:t.dateListInfo},on:{getId:t.getDayId}})],1)],1),a("div",{staticClass:"content_right"},[a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[t._v(" "+t._s(t.clickDayTime)+" ")]),a("div",{staticClass:"work-time"},[1==t.dayList.arrangementTypeId?a("div",{staticClass:"sign"},[t._v("固定班次")]):2==t.dayList.arrangementTypeId?a("div",{staticClass:"sign sign-two"},[t._v("弹性班次")]):3==t.dayList.arrangementTypeId?a("div",{staticClass:"sign sign-three"},[t._v("签到班次")]):0==t.dayList.arrangementTypeId?a("div",{staticClass:"sign sign-three"},[t._v("今日休息")]):-1==t.dayList.arrangementTypeId?a("div",{staticClass:"sign sign-three"},[t._v("暂未排班")]):t._e(),-1==t.dayList.arrangementTypeId?a("el-empty",{attrs:{description:"暂未排班"}}):t._e(),0==t.dayList.arrangementTypeId?a("el-empty",{attrs:{description:"今日休息"}}):t._e(),t._l(t.dayList.arrangementTimeInfoList,(function(e,i){return 1==t.dayList.arrangementTypeId?a("div",{key:e.id,staticClass:"work-con"},[a("div",{staticClass:"work-tit"},[t._v("班段"+t._s(i+1))]),a("div",{staticClass:"work-out-line"},[a("div",{staticClass:"line-tit"},[t._v("工作时间")]),a("div",{staticClass:"go_work_time"},[a("p",[t._v("上班时间：")]),a("p",[t._v(" "+t._s("2"==e.startWorkDayType?"次日":"")+" "+t._s(e.startWorkTime)+" ")])]),a("div",{staticClass:"go_work_time"},[a("p",[t._v("下班时间：")]),a("p",[t._v(" "+t._s("2"==e.endWorkDayType?"次日":"")+" "+t._s(e.endWorkTime)+" ")])])]),a("div",{staticClass:"work-out-line"},[a("div",{staticClass:"line-tit"},[t._v("打卡时间")]),a("div",{staticClass:"go_work_time"},[a("p",[t._v("最早打卡时间：")]),a("p",[t._v(" "+t._s("2"==e.startEarliestDayType?"次日":"")+" "+t._s(e.startEarliestWorkTime)+" ")])]),a("div",{staticClass:"go_work_time"},[a("p",[t._v("最晚打卡时间：")]),a("p",[t._v(" "+t._s("2"==e.endLatestDayType?"次日":"")+" "+t._s(e.endLatestWorkTime)+" ")])])])]):2==t.dayList.arrangementTypeId?t._l(t.dayList.arrangementTimeInfoList,(function(e,i){return a("div",{key:e.id,staticClass:"work-con"},[a("div",{staticClass:"work-tit"},[t._v("班段"+t._s(i+1))]),a("div",{staticClass:"work-out-line"},[a("div",{staticClass:"line-tit"},[t._v("工作时长")]),a("div",{staticClass:"go_work_time"},[a("p",[t._v("必须工作时长为："+t._s(t.dayList.workTimeAmount)+" 小时，计为 "+t._s(t.dayList.ycqts)+" 天出勤")])])]),a("div",{staticClass:"work-out-line"},[a("div",{staticClass:"line-tit"},[t._v("打卡时间")]),a("div",{staticClass:"go_work_time"},[a("p",[t._v("最早打卡时间：")]),a("p",[t._v(t._s(e.startEarliestWorkTime))])]),a("div",{staticClass:"go_work_time"},[a("p",[t._v("最晚打卡时间：")]),a("p",[t._v(t._s(e.endLatestWorkTime))])])])])})):3==t.dayList.arrangementTypeId?t._l(t.dayList.arrangementTimeInfoList,(function(e,i){return a("div",{key:e.id,staticClass:"work-con"},[a("div",{staticClass:"work-tit"},[t._v("班段"+t._s(i+1))]),a("div",{staticClass:"work-out-line"},[a("div",{staticClass:"line-tit"},[t._v("打卡时间")]),a("div",{staticClass:"go_work_time"},[a("p",[t._v("最早打卡时间：")]),a("p",[t._v(t._s(e.startEarliestWorkTime))])]),a("div",{staticClass:"go_work_time"},[a("p",[t._v("最晚打卡时间：")]),a("p",[t._v(t._s(e.endLatestWorkTime))])])])])})):t._e()}))],2),a("div",{staticClass:"work-btm-time"},[a("div",{staticClass:"work-con"},[0!=t.dayList.arrangementTypeId&&-1!=t.dayList.arrangementTypeId?a("div",{staticClass:"work-out-line"},[a("div",{staticClass:"line-tit"},[t._v("加班设置")]),a("div",{staticClass:"go_work_time"},[t.dayList.overtime?a("p",[t._v(t._s(t.dayList.overtime)+"之后算加班")]):a("p",[t._v("无")])])]):t._e(),1==t.dayList.arrangementTypeId?a("div",{staticClass:"work-out-line"},[a("div",{staticClass:"line-tit"},[t._v("弹性设置")]),a("div",{staticClass:"go_work_time"},[a("div",[a("el-checkbox",{attrs:{"true-label":"1","false-label":"0",disabled:""},model:{value:t.addShift.checkbox1,callback:function(e){t.$set(t.addShift,"checkbox1",e)},expression:"addShift.checkbox1"}}),t._v(" "),a("span",{staticStyle:{"margin-left":"8px"}},[t._v("晚到、早走几分钟不记为异常")]),1==t.addShift.checkbox1?a("div",{staticClass:"checkbox"},[a("div",[t._v(" 上班最多可晚到： "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择",disabled:""},model:{value:t.addShift.checkbox1Up,callback:function(e){t.$set(t.addShift,"checkbox1Up",e)},expression:"addShift.checkbox1Up"}},t._l(t.Dateoptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(" 内不算迟到 ")],1),a("div",[t._v(" 下班最多可早走： "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择",disabled:""},model:{value:t.addShift.checkbox1Down,callback:function(e){t.$set(t.addShift,"checkbox1Down",e)},expression:"addShift.checkbox1Down"}},t._l(t.Dateoptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(" 内不算早退 ")],1)]):t._e()],1),a("div",[a("el-checkbox",{attrs:{"true-label":"1","false-label":"0",disabled:""},model:{value:t.addShift.checkbox2,callback:function(e){t.$set(t.addShift,"checkbox2",e)},expression:"addShift.checkbox2"}}),a("span",{staticStyle:{"margin-left":"10px"}},[t._v("下班晚走，第二天可晚到")]),1==t.addShift.checkbox2?a("div",{staticClass:"checkbox"},[a("div",[a("el-radio",{attrs:{label:"1",disabled:""},model:{value:t.addShift.radio1,callback:function(e){t.$set(t.addShift,"radio1",e)},expression:"addShift.radio1"}},[t._v("仅下班的内勤打卡计算为晚走")]),a("el-radio",{attrs:{label:"2",disabled:""},model:{value:t.addShift.radio1,callback:function(e){t.$set(t.addShift,"radio1",e)},expression:"addShift.radio1"}},[t._v("下班的内勤，外勤打卡均计算为晚走")])],1),a("div",[t._v(" 第一天下班后晚走 "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择",disabled:""},model:{value:t.addShift.checkbox2Up,callback:function(e){t.$set(t.addShift,"checkbox2Up",e)},expression:"addShift.checkbox2Up"}},t._l(t.DateoptionsHour,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(" ，第二天上班可以晚到 "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择",disabled:""},model:{value:t.addShift.checkbox2Down,callback:function(e){t.$set(t.addShift,"checkbox2Down",e)},expression:"addShift.checkbox2Down"}},t._l(t.DateoptionsHour,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)]):t._e()],1)])]):t._e()])])])],1)])])],1)},s=[],n=(a("d3b7"),a("159b"),a("dc01")),r=a("9284"),o=a("5a0c"),c={name:"",props:{},components:{MyCalendar:r["default"]},data:function(){return{name:"环境数据",loading:!1,monthDate:o(new Date).format("YYYY-MM"),monthDateApi:o(new Date).format("YYYY-MM"),dateListInfo:[],dateListHead:[],dayList:[],timeStemp1:o(new Date).format("YYYY-MM"),clickDayTime:"",isDataNull:!1,Dateoptions:[{value:15,label:"15分钟"},{value:30,label:"30分钟"},{value:45,label:"45分钟"},{value:60,label:"60分钟"}],DateoptionsHour:[{value:1,label:"1小时"},{value:2,label:"2小时"},{value:3,label:"3小时"},{value:4,label:"4小时"}],addShift:{name:"",type:1,typeName:"",days:"",overtime:"",tableData:[{name:"",startWorkTime:"",endWorkTime:"",endWorkDayType:"",startEarliestWorkTime:"",endEarliestWorkTime:"",endEarliestDayType:"",startLatestWorkTime:"",endLatestDayType:"",endLatestWorkTime:"",restStartTime:"",restEndTime:"",restLength:"",overtime:""}],checkbox1:"0",checkbox2:"0",checkbox1Up:"",checkbox1Down:"",radio1:"0",checkbox2Up:"",checkbox2Down:""}}},created:function(){this.getShowDatas()},mounted:function(){},watch:{},filters:{},computed:{},methods:{getShowDatas:function(){var t=this;this.loading=!0;var e={monthStr:this.monthDateApi};console.log(e,"时间"),Object(n["B"])(e).then((function(e){t.dateListInfo=e.data.infoList[0].memberWorkVos,t.dateListHead=e.data.header,t.dateListHead.forEach((function(t){t.isAll=0})),t.loading=!1}))},getDayId:function(t){var e=this;if(console.log(t),"0"==t.arrangementId)this.dayList.arrangementTypeId="0",this.clickDayTime=t.thisDayTime,this.isDataNull=!0;else if("-1"==t.arrangementId)this.dayList.arrangementTypeId="-1",this.clickDayTime=t.thisDayTime,this.isDataNull=!0;else if(this.isDataNull=!1,this.clickDayTime=t.thisDayTime,0!==t.arrangementId&&-1!==t.arrangementId){var a={id:t.arrangementId};Object(n["f"])(a).then((function(t){e.dayList=t.data,e.addShift.checkbox1=e.dayList.optionOne,e.addShift.checkbox2=e.dayList.optionTwo,e.addShift.checkbox1Up=e.dayList.oneBelateLength,e.addShift.checkbox1Down=e.dayList.oneLeaveEarlyLength,e.addShift.radio1=e.dayList.twoType,e.addShift.checkbox2Up=e.dayList.twoStayLateLength,e.addShift.checkbox2Down=e.dayList.twoArriveLateLength}))}}}},d=c,l=(a("397d"),a("2877")),u=Object(l["a"])(d,i,s,!1,null,"83b6924a",null);e["default"]=u.exports},1860:function(t,e,a){},"397d":function(t,e,a){"use strict";a("1860")},"5a0c":function(t,e,a){!function(e,a){t.exports=a()}(0,(function(){"use strict";var t=1e3,e=6e4,a=36e5,i="millisecond",s="second",n="minute",r="hour",o="day",c="week",d="month",l="quarter",u="year",h="date",f="Invalid Date",v=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,m=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,p={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],a=t%100;return"["+t+(e[(a-20)%10]||e[a]||e[0])+"]"}},y=function(t,e,a){var i=String(t);return!i||i.length>=e?t:""+Array(e+1-i.length).join(a)+t},_={s:y,z:function(t){var e=-t.utcOffset(),a=Math.abs(e),i=Math.floor(a/60),s=a%60;return(e<=0?"+":"-")+y(i,2,"0")+":"+y(s,2,"0")},m:function t(e,a){if(e.date()<a.date())return-t(a,e);var i=12*(a.year()-e.year())+(a.month()-e.month()),s=e.clone().add(i,d),n=a-s<0,r=e.clone().add(i+(n?-1:1),d);return+(-(i+(a-s)/(n?s-r:r-s))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:d,y:u,w:c,d:o,D:h,h:r,m:n,s:s,ms:i,Q:l}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},k="en",b={};b[k]=p;var g="$isDayjsObject",$=function(t){return t instanceof L||!(!t||!t[g])},w=function t(e,a,i){var s;if(!e)return k;if("string"==typeof e){var n=e.toLowerCase();b[n]&&(s=n),a&&(b[n]=a,s=n);var r=e.split("-");if(!s&&r.length>1)return t(r[0])}else{var o=e.name;b[o]=e,s=o}return!i&&s&&(k=s),s||!i&&k},D=function(t,e){if($(t))return t.clone();var a="object"==typeof e?e:{};return a.date=t,a.args=arguments,new L(a)},S=_;S.l=w,S.i=$,S.w=function(t,e){return D(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var L=function(){function p(t){this.$L=w(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[g]=!0}var y=p.prototype;return y.parse=function(t){this.$d=function(t){var e=t.date,a=t.utc;if(null===e)return new Date(NaN);if(S.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var i=e.match(v);if(i){var s=i[2]-1||0,n=(i[7]||"0").substring(0,3);return a?new Date(Date.UTC(i[1],s,i[3]||1,i[4]||0,i[5]||0,i[6]||0,n)):new Date(i[1],s,i[3]||1,i[4]||0,i[5]||0,i[6]||0,n)}}return new Date(e)}(t),this.init()},y.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},y.$utils=function(){return S},y.isValid=function(){return!(this.$d.toString()===f)},y.isSame=function(t,e){var a=D(t);return this.startOf(e)<=a&&a<=this.endOf(e)},y.isAfter=function(t,e){return D(t)<this.startOf(e)},y.isBefore=function(t,e){return this.endOf(e)<D(t)},y.$g=function(t,e,a){return S.u(t)?this[e]:this.set(a,t)},y.unix=function(){return Math.floor(this.valueOf()/1e3)},y.valueOf=function(){return this.$d.getTime()},y.startOf=function(t,e){var a=this,i=!!S.u(e)||e,l=S.p(t),f=function(t,e){var s=S.w(a.$u?Date.UTC(a.$y,e,t):new Date(a.$y,e,t),a);return i?s:s.endOf(o)},v=function(t,e){return S.w(a.toDate()[t].apply(a.toDate("s"),(i?[0,0,0,0]:[23,59,59,999]).slice(e)),a)},m=this.$W,p=this.$M,y=this.$D,_="set"+(this.$u?"UTC":"");switch(l){case u:return i?f(1,0):f(31,11);case d:return i?f(1,p):f(0,p+1);case c:var k=this.$locale().weekStart||0,b=(m<k?m+7:m)-k;return f(i?y-b:y+(6-b),p);case o:case h:return v(_+"Hours",0);case r:return v(_+"Minutes",1);case n:return v(_+"Seconds",2);case s:return v(_+"Milliseconds",3);default:return this.clone()}},y.endOf=function(t){return this.startOf(t,!1)},y.$set=function(t,e){var a,c=S.p(t),l="set"+(this.$u?"UTC":""),f=(a={},a[o]=l+"Date",a[h]=l+"Date",a[d]=l+"Month",a[u]=l+"FullYear",a[r]=l+"Hours",a[n]=l+"Minutes",a[s]=l+"Seconds",a[i]=l+"Milliseconds",a)[c],v=c===o?this.$D+(e-this.$W):e;if(c===d||c===u){var m=this.clone().set(h,1);m.$d[f](v),m.init(),this.$d=m.set(h,Math.min(this.$D,m.daysInMonth())).$d}else f&&this.$d[f](v);return this.init(),this},y.set=function(t,e){return this.clone().$set(t,e)},y.get=function(t){return this[S.p(t)]()},y.add=function(i,l){var h,f=this;i=Number(i);var v=S.p(l),m=function(t){var e=D(f);return S.w(e.date(e.date()+Math.round(t*i)),f)};if(v===d)return this.set(d,this.$M+i);if(v===u)return this.set(u,this.$y+i);if(v===o)return m(1);if(v===c)return m(7);var p=(h={},h[n]=e,h[r]=a,h[s]=t,h)[v]||1,y=this.$d.getTime()+i*p;return S.w(y,this)},y.subtract=function(t,e){return this.add(-1*t,e)},y.format=function(t){var e=this,a=this.$locale();if(!this.isValid())return a.invalidDate||f;var i=t||"YYYY-MM-DDTHH:mm:ssZ",s=S.z(this),n=this.$H,r=this.$m,o=this.$M,c=a.weekdays,d=a.months,l=a.meridiem,u=function(t,a,s,n){return t&&(t[a]||t(e,i))||s[a].slice(0,n)},h=function(t){return S.s(n%12||12,t,"0")},v=l||function(t,e,a){var i=t<12?"AM":"PM";return a?i.toLowerCase():i};return i.replace(m,(function(t,i){return i||function(t){switch(t){case"YY":return String(e.$y).slice(-2);case"YYYY":return S.s(e.$y,4,"0");case"M":return o+1;case"MM":return S.s(o+1,2,"0");case"MMM":return u(a.monthsShort,o,d,3);case"MMMM":return u(d,o);case"D":return e.$D;case"DD":return S.s(e.$D,2,"0");case"d":return String(e.$W);case"dd":return u(a.weekdaysMin,e.$W,c,2);case"ddd":return u(a.weekdaysShort,e.$W,c,3);case"dddd":return c[e.$W];case"H":return String(n);case"HH":return S.s(n,2,"0");case"h":return h(1);case"hh":return h(2);case"a":return v(n,r,!0);case"A":return v(n,r,!1);case"m":return String(r);case"mm":return S.s(r,2,"0");case"s":return String(e.$s);case"ss":return S.s(e.$s,2,"0");case"SSS":return S.s(e.$ms,3,"0");case"Z":return s}return null}(t)||s.replace(":","")}))},y.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},y.diff=function(i,h,f){var v,m=this,p=S.p(h),y=D(i),_=(y.utcOffset()-this.utcOffset())*e,k=this-y,b=function(){return S.m(m,y)};switch(p){case u:v=b()/12;break;case d:v=b();break;case l:v=b()/3;break;case c:v=(k-_)/6048e5;break;case o:v=(k-_)/864e5;break;case r:v=k/a;break;case n:v=k/e;break;case s:v=k/t;break;default:v=k}return f?v:S.a(v)},y.daysInMonth=function(){return this.endOf(d).$D},y.$locale=function(){return b[this.$L]},y.locale=function(t,e){if(!t)return this.$L;var a=this.clone(),i=w(t,e,!0);return i&&(a.$L=i),a},y.clone=function(){return S.w(this.$d,this)},y.toDate=function(){return new Date(this.valueOf())},y.toJSON=function(){return this.isValid()?this.toISOString():null},y.toISOString=function(){return this.$d.toISOString()},y.toString=function(){return this.$d.toUTCString()},p}(),T=L.prototype;return D.prototype=T,[["$ms",i],["$s",s],["$m",n],["$H",r],["$W",o],["$M",d],["$y",u],["$D",h]].forEach((function(t){T[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),D.extend=function(t,e){return t.$i||(t(e,L,D),t.$i=!0),D},D.locale=w,D.isDayjs=$,D.unix=function(t){return D(1e3*t)},D.en=b[k],D.Ls=b,D.p={},D}))}}]);