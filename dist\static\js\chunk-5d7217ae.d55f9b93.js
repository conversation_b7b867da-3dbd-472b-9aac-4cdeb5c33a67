(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-5d7217ae"],{"4c8f":function(e,t,n){"use strict";n.d(t,"b",(function(){return l})),n.d(t,"a",(function(){return o})),n.d(t,"e",(function(){return i})),n.d(t,"g",(function(){return r})),n.d(t,"f",(function(){return c})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return p}));n("99af");var a=n("b775");function l(e){return Object(a["a"])({url:"/emergency_plan/page",method:"get",params:e})}function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(a["a"])({url:"/emergency_plan/getPlans",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/emergency_plan/save",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/emergency_plan/update",method:"post",data:e})}function c(e){return Object(a["a"])({url:"/emergency_plan/detail",method:"get",params:e})}function s(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(a["a"])({url:"/staff/list",method:"get",params:e})}function p(e){return Object(a["a"])({url:"/file/downloadFile?bucket=".concat(e[1],"&path=").concat(e[2],"&fileName=").concat(e[3]),method:"get",responseType:"blob"})}},"6fce":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-row",{attrs:{gutter:20,type:"flex"}},[n("el-col",{attrs:{span:8}},[n("el-card",{staticClass:"box-content"},[n("div",{attrs:{slot:"header"},slot:"header"},[e._v("选择预案")]),n("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"搜索","prefix-icon":"el-icon-search",maxlength:"20"},nativeOn:{change:function(t){return e.getLeftData(t)}},model:{value:e.planName,callback:function(t){e.planName=t},expression:"planName"}}),e._l(e.leftDataList,(function(t){return[n("el-card",{key:t.id,class:["card-item",e.currentPlanId===t.id?"actived":""],attrs:{shadow:"never"},nativeOn:{click:function(n){return e.handleClickPlan(t)}}},[n("div",[e._v("预案名称："+e._s(t.planName))]),n("div",[e._v(" 预案类型："+e._s((e.dict.type.plan_deduction.find((function(e){return e.value==t.planType}))||{}).label)+" ")])])]}))],2)],1),n("el-col",{staticClass:"box-content",attrs:{span:16}},[n("el-card",[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("专家队伍")]),n("el-button",{staticClass:"addBtn",attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.handleAdd("5010801")}}},[e._v("新增")])],1),n("el-table",{attrs:{data:e.contingentList}},[e._l(e.contingentTableColumn,(function(t){return n("el-table-column",{key:t.prop,attrs:{label:t.label,prop:t.prop,align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){var l=a.row;return["contingentType"===t.prop?n("span",[e._v(e._s((e.dict.type.team_type.find((function(e){return e.value==l.contingentType}))||{}).label))]):n("span",[e._v(e._s(l[t.prop]))])]}}],null,!0)})})),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"text"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],2)],1),n("el-card",{staticStyle:{"margin-top":"20px"}},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("应急资源")]),n("el-button",{staticClass:"addBtn",attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.handleAdd("5010802")}}},[e._v("新增")])],1),n("el-table",{attrs:{data:e.materialList}},[e._l(e.materialTableColumn,(function(t){return n("el-table-column",{key:t.prop,attrs:{label:t.label,prop:t.prop,align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){var l=a.row;return["materialType"===t.prop?n("span",[e._v(e._s((e.dict.type.materiel_type.find((function(e){return e.value==l.materialType}))||{}).label))]):n("span",[e._v(e._s(l[t.prop]))])]}}],null,!0)})})),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"text"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],2)],1),n("el-card",{staticStyle:{"margin-top":"20px"}},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("应急专家")]),n("el-button",{staticClass:"addBtn",attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.handleAdd("5010803")}}},[e._v("新增")])],1),n("el-table",{attrs:{data:e.expertList}},[e._l(e.expertTableColumn,(function(t){return n("el-table-column",{key:t.prop,attrs:{label:t.label,prop:t.prop,align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){var l=a.row;return["expertLevel"===t.prop?n("span",[e._v(e._s((e.dict.type.emergency_expert_level.find((function(e){return e.value==l.expertLevel}))||{}).label))]):"eventType"===t.prop?n("span",[e._v(" "+e._s((e.eventTypeList.find((function(e){return e.id==l.eventType}))||{}).name)+" ")]):n("span",[e._v(e._s(l[t.prop]))])]}}],null,!0)})})),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"text"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],2)],1)],1)],1),n("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogInfo.loading,expression:"dialogInfo.loading"}],attrs:{title:e.dialogInfo.title,visible:e.dialogInfo.show,width:"960px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.dialogInfo,"show",t)}}},["5010801"===e.dialogInfo.type?[n("el-form",{ref:"form",attrs:{"label-width":"80px",inline:""}},[n("el-form-item",{attrs:{label:"队伍名称"}},[n("el-input",{attrs:{placeholder:"请输入队伍名称",maxlength:"20"},on:{change:e.getContingentList},model:{value:e.dialogInfo.query.contingentName,callback:function(t){e.$set(e.dialogInfo.query,"contingentName",t)},expression:"dialogInfo.query.contingentName"}})],1),n("el-form-item",{attrs:{label:"队伍类型"}},[n("el-select",{attrs:{placeholder:"请选择队伍类型",clearable:""},on:{change:e.getContingentList},model:{value:e.dialogInfo.query.contingentType,callback:function(t){e.$set(e.dialogInfo.query,"contingentType",t)},expression:"dialogInfo.query.contingentType"}},e._l(e.dict.type.team_type,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),n("el-table",{attrs:{data:e.dialogInfo.tableList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),e._l(e.contingentTableColumn,(function(t){return n("el-table-column",{key:t.prop,attrs:{label:t.label,prop:t.prop,align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){var l=a.row;return["contingentType"===t.prop?n("span",[e._v(e._s((e.dict.type.team_type.find((function(e){return e.value==l.contingentType}))||{}).label))]):n("span",[e._v(e._s(l[t.prop]))])]}}],null,!0)})}))],2)]:e._e(),"5010802"===e.dialogInfo.type?[n("el-form",{ref:"form",attrs:{"label-width":"80px",inline:""}},[n("el-form-item",{attrs:{label:"物资名称"}},[n("el-input",{attrs:{placeholder:"请输入物资库名称",maxlength:"20"},on:{change:e.getMaterialList},model:{value:e.dialogInfo.query.materialName,callback:function(t){e.$set(e.dialogInfo.query,"materialName",t)},expression:"dialogInfo.query.materialName"}})],1),n("el-form-item",{attrs:{label:"资源类型"}},[n("el-select",{attrs:{placeholder:"请选择资源类型",clearable:""},on:{change:e.getMaterialList},model:{value:e.dialogInfo.query.materialType,callback:function(t){e.$set(e.dialogInfo.query,"materialType",t)},expression:"dialogInfo.query.materialType"}},e._l(e.dict.type.materiel_type,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),n("el-table",{attrs:{data:e.dialogInfo.tableList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),e._l(e.materialTableColumn,(function(t){return n("el-table-column",{key:t.prop,attrs:{label:t.label,prop:t.prop,align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){var l=a.row;return["materialType"===t.prop?n("span",[e._v(e._s((e.dict.type.materiel_type.find((function(e){return e.value==l.materialType}))||{}).label))]):n("span",[e._v(e._s(l[t.prop]))])]}}],null,!0)})}))],2)]:e._e(),"5010803"===e.dialogInfo.type?[n("el-form",{ref:"form",attrs:{"label-width":"80px",inline:""}},[n("el-form-item",{attrs:{label:"专家姓名"}},[n("el-input",{attrs:{placeholder:"请输入专家姓名",maxlength:"20"},on:{change:e.getExpertList},model:{value:e.dialogInfo.query.name,callback:function(t){e.$set(e.dialogInfo.query,"name",t)},expression:"dialogInfo.query.name"}})],1),n("el-form-item",{attrs:{label:"擅长事件"}},[n("el-select",{attrs:{placeholder:"请选择事件类型",clearable:""},on:{change:e.getExpertList},model:{value:e.dialogInfo.query.eventType,callback:function(t){e.$set(e.dialogInfo.query,"eventType",t)},expression:"dialogInfo.query.eventType"}},e._l(e.eventTypeList,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),n("el-table",{attrs:{data:e.dialogInfo.tableList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),e._l(e.expertTableColumn,(function(t){return n("el-table-column",{key:t.prop,attrs:{label:t.label,prop:t.prop,align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){var l=a.row;return["expertLevel"===t.prop?n("span",[e._v(e._s((e.dict.type.emergency_expert_level.find((function(e){return e.value==l.expertLevel}))||{}).label))]):"eventType"===t.prop?n("span",[e._v(" "+e._s((e.eventTypeList.find((function(e){return e.id==l.eventType}))||{}).name)+" ")]):n("span",[e._v(e._s(l[t.prop]))])]}}],null,!0)})}))],2)]:e._e(),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v("确定")]),n("el-button",{on:{click:function(t){e.dialogInfo.show=!1}}},[e._v("取消")])],1)],2)],1)},l=[],o=n("b85c"),i=(n("b0c0"),n("14d9"),n("4c8f")),r=n("9f24"),c={dicts:["plan_deduction","team_type","materiel_type","emergency_expert_level"],data:function(){return{planName:"",currentPlanId:0,leftDataList:[],contingentTableColumn:[{label:"队伍名称",prop:"contingentName"},{label:"队伍类型",prop:"contingentType"},{label:"队伍人数",prop:"headcount"},{label:"队伍负责人",prop:"contact"},{label:"联系电话",prop:"phone"}],materialTableColumn:[{label:"物资名称",prop:"materialName"},{label:"物资类型",prop:"materialType"},{label:"存放物资库",prop:"supplyDepot"},{label:"库存",prop:"inventory"},{label:"责任人",prop:"liabilityUser"},{label:"联系电话",prop:"phone"}],expertTableColumn:[{label:"姓名",prop:"name"},{label:"性别",prop:"gender"},{label:"擅长事件类型",prop:"eventType"},{label:"专家级别",prop:"expertLevel"}],contingentList:[],materialList:[],expertList:[],eventTypeList:[],multipleSelection:[],dialogInfo:{title:"",type:"",show:!1,loading:!1,query:{},tableList:[]}}},created:function(){var e=this;this.getLeftData(),Object(r["d"])().then((function(t){console.log(t.data),e.eventTypeList=t.data}))},methods:{getLeftData:function(){var e=this;Object(i["a"])({planName:this.planName}).then((function(t){e.leftDataList=t.data||[],console.log("左侧",e.leftDataList),e.handleClickPlan(e.leftDataList[0])}))},handleClickPlan:function(e){this.currentPlanId=e.id,this.getRightTables()},getRightTables:function(){var e=this;Object(r["b"])({planId:this.currentPlanId}).then((function(t){var n=t.data||{};e.contingentList=n["5010801"]||[],e.materialList=n["5010802"]||[],e.expertList=n["5010803"]||[],console.log(n)}))},getContingentList:function(){var e=this,t=this.dialogInfo.query,n=t.contingentName,a=t.contingentType;Object(r["c"])({planId:this.currentPlanId,contingentName:n,contingentType:a}).then((function(t){e.dialogInfo.tableList=t.data.records||[],console.log(t.data.records)}))},getMaterialList:function(){var e=this,t=this.dialogInfo.query,n=t.materialName,a=t.materialType;Object(r["f"])({planId:this.currentPlanId,materialName:n,materialType:a}).then((function(t){e.dialogInfo.tableList=t.data.records||[],console.log(t.data.records)}))},getExpertList:function(){var e=this,t=this.dialogInfo.query,n=t.name,a=t.eventType;Object(r["e"])({planId:this.currentPlanId,name:n,eventType:a}).then((function(t){e.dialogInfo.tableList=t.data.records||[],console.log(t.data.records)}))},handleAdd:function(e){this.resetData(),this.dialogInfo.show=!0,this.dialogInfo.type=e,"5010801"===e&&(this.dialogInfo.title="关联队伍",this.getContingentList()),"5010802"===e&&(this.dialogInfo.title="关联物资",this.getMaterialList()),"5010803"===e&&(this.dialogInfo.title="关联专家",this.getExpertList())},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return console.log({id:e.id}),Object(r["a"])({id:e.id})})).then((function(e){200===e.code&&(t.getRightTables(),t.$modal.msgSuccess("删除成功"))})).catch((function(e){}))},handleSelectionChange:function(e){this.multipleSelection=e},confirm:function(){var e=this;if(0!==this.multipleSelection.length){var t,n=[],a=Object(o["a"])(this.multipleSelection);try{for(a.s();!(t=a.n()).done;){var l=t.value;n.push({contentId:l.id,contentType:this.dialogInfo.type,planId:this.currentPlanId})}}catch(i){a.e(i)}finally{a.f()}console.log(n),this.dialogInfo.loading=!1,Object(r["g"])(n).then((function(t){e.resetData(),200===t.code&&(e.getRightTables(),e.$modal.msgSuccess("新增成功"))}))}else this.$modal.msgWarning("请至少选择一条数据")},resetData:function(){this.dialogInfo.show=!1,this.dialogInfo.type="",this.dialogInfo.loading=!1,this.dialogInfo.query={},this.dialogInfo.tableList=[],this.multipleSelection=[]}}},s=c,p=(n("9eaa"),n("2877")),u=Object(p["a"])(s,a,l,!1,null,"80eb7488",null);t["default"]=u.exports},"9eaa":function(e,t,n){"use strict";n("cb1c")},"9f24":function(e,t,n){"use strict";n.d(t,"b",(function(){return l})),n.d(t,"f",(function(){return o})),n.d(t,"c",(function(){return i})),n.d(t,"e",(function(){return r})),n.d(t,"d",(function(){return c})),n.d(t,"g",(function(){return s})),n.d(t,"a",(function(){return p}));var a=n("b775");function l(e){return Object(a["a"])({url:"/emergency-plan-content-digitization/list",method:"get",params:e})}function o(e){return Object(a["a"])({url:"/emergency-material/pageForPlan",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/emergency_expert_contingent/pageForPlan",method:"get",params:e})}function r(e){return Object(a["a"])({url:"/emergency-expert/list",method:"get",params:e})}function c(e){return Object(a["a"])({url:"/emergency-event-type/list",method:"get",params:e})}function s(e){return Object(a["a"])({url:"/emergency-plan-content-digitization/save",method:"post",data:e})}function p(e){return Object(a["a"])({url:"/emergency-plan-content-digitization/delete",method:"post",data:e})}},cb1c:function(e,t,n){}}]);