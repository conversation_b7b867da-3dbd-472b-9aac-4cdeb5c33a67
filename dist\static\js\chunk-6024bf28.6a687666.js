(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-6024bf28"],{"03fe":function(e,t,a){},"31dc":function(e,t,a){"use strict";a("03fe")},"4a00":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:24}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("历史记录")])]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.dataList}},[a("el-table-column",{attrs:{label:"序号",align:"center",prop:"id","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"类型",align:"center",prop:"type","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"time","show-overflow-tooltip":""}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}})],1)],1)],1),a("el-dialog",{attrs:{title:e.title,visible:e.abilityOpen,width:"960px","append-to-body":"",fullscreen:""},on:{"update:visible":function(t){e.abilityOpen=t}}},[a("div",[a("el-row",[a("el-col",{attrs:{span:3}},[a("el-button",{staticClass:"navigation",staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.navigation("1")}}},[e._v("危险品名称")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("2")}}},[e._v("成分/组成信息")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("3")}}},[e._v("危险性概述")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("4")}}},[e._v("急救措施")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("5")}}},[e._v("消防措施")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("6")}}},[e._v("泄露应急处理")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("7")}}},[e._v("操作处理与储存")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("8")}}},[e._v("接触控制/个体防护")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("9")}}},[e._v("理化特性")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("10")}}},[e._v("稳定性和反应活性")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("11")}}},[e._v("毒理学资料")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("12")}}},[e._v("生态学资料")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("13")}}},[e._v("废弃处置")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("14")}}},[e._v("运输信息")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("15")}}},[e._v("法规信息")]),a("el-button",{staticClass:"navigation",on:{click:function(t){return e.navigation("16")}}},[e._v("安全标签信息")])],1),a("el-col",{attrs:{span:21}},[a("el-collapse",{on:{change:e.handleChange},model:{value:e.activeNames,callback:function(t){e.activeNames=t},expression:"activeNames"}},[a("el-collapse-item",{attrs:{title:"化学品名称",name:"1"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"危化品中文名称",prop:"name"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.name,callback:function(t){e.$set(e.detailInfo,"name",t)},expression:"detailInfo.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"危化品英文名称",prop:"engName"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.engName,callback:function(t){e.$set(e.detailInfo,"engName",t)},expression:"detailInfo.engName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"危化品俗名",prop:"trivialName"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.trivialName,callback:function(t){e.$set(e.detailInfo,"trivialName",t)},expression:"detailInfo.trivialName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"英文名称",prop:"trivialEngName"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.trivialEngName,callback:function(t){e.$set(e.detailInfo,"trivialEngName",t)},expression:"detailInfo.trivialEngName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"技术说明书编号",prop:"msdsId"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.msdsId,callback:function(t){e.$set(e.detailInfo,"msdsId",t)},expression:"detailInfo.msdsId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"CAS号",prop:"casId"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.casId,callback:function(t){e.$set(e.detailInfo,"casId",t)},expression:"detailInfo.casId"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"分子式",prop:"molecularFormula"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.molecularFormula,callback:function(t){e.$set(e.detailInfo,"molecularFormula",t)},expression:"detailInfo.molecularFormula"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"分子量",prop:"molecularWeight"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.molecularWeight,callback:function(t){e.$set(e.detailInfo,"molecularWeight",t)},expression:"detailInfo.molecularWeight"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"成分/组成信息",name:"2"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"纯品或混合物",prop:"pureOrMix"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.pureOrMix,callback:function(t){e.$set(e.detailInfo,"pureOrMix",t)},expression:"detailInfo.pureOrMix"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"有害物成分1",prop:"ingredientI"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientI,callback:function(t){e.$set(e.detailInfo,"ingredientI",t)},expression:"detailInfo.ingredientI"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"含量(%)",prop:"ingredientContentI"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientContentIi,callback:function(t){e.$set(e.detailInfo,"ingredientContentIi",t)},expression:"detailInfo.ingredientContentIi"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"有害物cas号",prop:"ingredientCasI"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientCasI,callback:function(t){e.$set(e.detailInfo,"ingredientCasI",t)},expression:"detailInfo.ingredientCasI"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"有害物成分2",prop:"ingredientIi"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientIi,callback:function(t){e.$set(e.detailInfo,"ingredientIi",t)},expression:"detailInfo.ingredientIi"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"含量(%)",prop:"ingredientContentIi"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientContentIi,callback:function(t){e.$set(e.detailInfo,"ingredientContentIi",t)},expression:"detailInfo.ingredientContentIi"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"有害物cas号",prop:"ingredientCasIi"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientCasIi,callback:function(t){e.$set(e.detailInfo,"ingredientCasIi",t)},expression:"detailInfo.ingredientCasIi"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"有害物成分3",prop:"ingredientIii"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientIii,callback:function(t){e.$set(e.detailInfo,"ingredientIii",t)},expression:"detailInfo.ingredientIii"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"含量(%)",prop:"ingredientContentIii"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientContentIii,callback:function(t){e.$set(e.detailInfo,"ingredientContentIii",t)},expression:"detailInfo.ingredientContentIii"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"有害物cas号",prop:"ingredientCasIii"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientCasIii,callback:function(t){e.$set(e.detailInfo,"ingredientCasIii",t)},expression:"detailInfo.ingredientCasIii"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"有害物成分4",prop:"ingredientIv"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientIv,callback:function(t){e.$set(e.detailInfo,"ingredientIv",t)},expression:"detailInfo.ingredientIv"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"含量(%)",prop:"ingredientContentIv"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientContentIv,callback:function(t){e.$set(e.detailInfo,"ingredientContentIv",t)},expression:"detailInfo.ingredientContentIv"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"有害物cas号",prop:"ingredientCasIv"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingredientCasIv,callback:function(t){e.$set(e.detailInfo,"ingredientCasIv",t)},expression:"detailInfo.ingredientCasIv"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"危险性概述",name:"3"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"危险性类别",prop:"healthHazard"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.healthHazard,callback:function(t){e.$set(e.detailInfo,"healthHazard",t)},expression:"detailInfo.healthHazard"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"侵入途径",prop:"invasionPath"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.invasionPath,callback:function(t){e.$set(e.detailInfo,"invasionPath",t)},expression:"detailInfo.invasionPath"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"健康危害",prop:"healthHazard"}},[a("el-input",{attrs:{maxlength:"200",disabled:!0,type:"textarea"},model:{value:e.detailInfo.healthHazard,callback:function(t){e.$set(e.detailInfo,"healthHazard",t)},expression:"detailInfo.healthHazard"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"爆燃危害",prop:"deflagrationHazard"}},[a("el-input",{attrs:{maxlength:"200",disabled:!0,type:"textarea"},model:{value:e.detailInfo.deflagrationHazard,callback:function(t){e.$set(e.detailInfo,"deflagrationHazard",t)},expression:"detailInfo.deflagrationHazard"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"环境危害",prop:"environmentHazard"}},[a("el-input",{attrs:{maxlength:"200",disabled:!0,type:"textarea"},model:{value:e.detailInfo.environmentHazard,callback:function(t){e.$set(e.detailInfo,"environmentHazard",t)},expression:"detailInfo.environmentHazard"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"急救措施",name:"4"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"皮肤接触",prop:"skinContact"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.skinContact,callback:function(t){e.$set(e.detailInfo,"skinContact",t)},expression:"detailInfo.skinContact"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"眼睛接触",prop:"eyesContact"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.eyesContact,callback:function(t){e.$set(e.detailInfo,"eyesContact",t)},expression:"detailInfo.eyesContact"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"吸入",prop:"suction"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.suction,callback:function(t){e.$set(e.detailInfo,"suction",t)},expression:"detailInfo.suction"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"食入",prop:"ingestion"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.ingestion,callback:function(t){e.$set(e.detailInfo,"ingestion",t)},expression:"detailInfo.ingestion"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"消防措施",name:"5"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"危险特性",prop:"forbiddenCompound"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.forbiddenCompound,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"forbiddenCompound",t)},expression:"detailInfo.emergencyMsdsSafeinfos.forbiddenCompound"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"有害燃烧产物",prop:"harmfulCombustionProduct"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.harmfulCombustionProduct,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"harmfulCombustionProduct",t)},expression:"detailInfo.emergencyMsdsSafeinfos.harmfulCombustionProduct"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"灭火方法",prop:"outfireMethod"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.outfireMethod,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"outfireMethod",t)},expression:"detailInfo.emergencyMsdsSafeinfos.outfireMethod"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"泄露应急处理",name:"6"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"应急处理",prop:"leakEmergency"}},[a("el-input",{attrs:{maxlength:"200",disabled:!0,type:"textarea"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.leakEmergency,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"leakEmergency",t)},expression:"detailInfo.emergencyMsdsSafeinfos.leakEmergency"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"操作处理与储存",name:"7"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"操作注意事项",prop:"operateMatter"}},[a("el-input",{attrs:{maxlength:"200",disabled:!0,type:"textarea"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.operateMatter,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"operateMatter",t)},expression:"detailInfo.emergencyMsdsSafeinfos.operateMatter"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"存储注意事项",prop:"storeMatter"}},[a("el-input",{attrs:{maxlength:"200",disabled:!0,type:"textarea"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.storeMatter,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"storeMatter",t)},expression:"detailInfo.emergencyMsdsSafeinfos.storeMatter"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"接触控制/个体防护",name:"8"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"中国MAC(mg/m)",prop:"chinaMac"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.chinaMac,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"chinaMac",t)},expression:"detailInfo.emergencyMsdsSafeinfos.chinaMac"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"前苏联mac(mg/m)",prop:"fsuMac"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.fsuMac,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"fsuMac",t)},expression:"detailInfo.emergencyMsdsSafeinfos.fsuMac"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"TLVIN(ppm,mg/m³)",prop:"tlvtn"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.tlvtn,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"tlvtn",t)},expression:"detailInfo.emergencyMsdsSafeinfos.tlvtn"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"TLVWN(ppm,mg/m³)",prop:"tlvwn"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.tlvwn,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"tlvwn",t)},expression:"detailInfo.emergencyMsdsSafeinfos.tlvwn"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"监测方法",prop:"avoidCondition"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.avoidCondition,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"avoidCondition",t)},expression:"detailInfo.emergencyMsdsSafeinfos.avoidCondition"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"工程控制",prop:"engineeringControl"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.engineeringControl,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"engineeringControl",t)},expression:"detailInfo.emergencyMsdsSafeinfos.engineeringControl"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"呼吸系统防护",prop:"respiratoryProtection"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.respiratoryProtection,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"respiratoryProtection",t)},expression:"detailInfo.emergencyMsdsSafeinfos.respiratoryProtection"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"眼睛防护",prop:"eyesProtection"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.eyesProtection,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"eyesProtection",t)},expression:"detailInfo.emergencyMsdsSafeinfos.eyesProtection"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"应急处理",prop:"leakEmergency"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.leakEmergency,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"leakEmergency",t)},expression:"detailInfo.emergencyMsdsSafeinfos.leakEmergency"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"身体防护",prop:"bodyProtection"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.bodyProtection,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"bodyProtection",t)},expression:"detailInfo.emergencyMsdsSafeinfos.bodyProtection"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"手防护",prop:"handProtection"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.handProtection,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"handProtection",t)},expression:"detailInfo.emergencyMsdsSafeinfos.handProtection"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"其他防护",prop:"otherProtection"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.otherProtection,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"otherProtection",t)},expression:"detailInfo.emergencyMsdsSafeinfos.otherProtection"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"理化特性",name:"9"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"外观与形状",prop:"appearanceShape"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.appearanceShape,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"appearanceShape",t)},expression:"detailInfo.emergencyMsdsSafeinfos.appearanceShape"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"溶解性",prop:"solubility"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.solubility,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"solubility",t)},expression:"detailInfo.emergencyMsdsSafeinfos.solubility"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"主要用途",prop:"used"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.used,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"used",t)},expression:"detailInfo.emergencyMsdsSafeinfos.used"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"pH值",prop:"ph"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.ph,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"ph",t)},expression:"detailInfo.emergencyMsdsSafeinfos.ph"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"熔点(℃)",prop:"fusingPoint"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.fusingPoint,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"fusingPoint",t)},expression:"detailInfo.emergencyMsdsSafeinfos.fusingPoint"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"相对密度(水=1)",prop:"relativeDensity"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.relativeDensity,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"relativeDensity",t)},expression:"detailInfo.emergencyMsdsSafeinfos.relativeDensity"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"沸点(℃)",prop:"boilingPoint"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.boilingPoint,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"boilingPoint",t)},expression:"detailInfo.emergencyMsdsSafeinfos.boilingPoint"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"相对密度(空气=1)",prop:"relativeDensity"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.relativeDensity,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"relativeDensity",t)},expression:"detailInfo.emergencyMsdsSafeinfos.relativeDensity"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"闪点(℃)",prop:"flashPoint"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.flashPoint,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"flashPoint",t)},expression:"detailInfo.emergencyMsdsSafeinfos.flashPoint"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"辛醇/水分配系数",prop:"distributionCoefficient"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.distributionCoefficient,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"distributionCoefficient",t)},expression:"detailInfo.emergencyMsdsSafeinfos.distributionCoefficient"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"引燃温度(℃)",prop:"ignitionTemperature"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.ignitionTemperature,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"ignitionTemperature",t)},expression:"detailInfo.emergencyMsdsSafeinfos.ignitionTemperature"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"爆炸下限[%(V/V)]",prop:"explosiveLowLimit"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.explosiveLowLimit,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"explosiveLowLimit",t)},expression:"detailInfo.emergencyMsdsSafeinfos.explosiveLowLimit"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"临界温度(℃)",prop:"explosiveUpLimitcriticalTemperature"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.explosiveUpLimitcriticalTemperature,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"explosiveUpLimitcriticalTemperature",t)},expression:"detailInfo.emergencyMsdsSafeinfos.explosiveUpLimitcriticalTemperature"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"爆炸上限[%(V/V)]",prop:"explosiveUpLimit"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.explosiveUpLimit,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"explosiveUpLimit",t)},expression:"detailInfo.emergencyMsdsSafeinfos.explosiveUpLimit"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"临界压力(MPa)",prop:"criticalPressure"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.criticalPressure,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"criticalPressure",t)},expression:"detailInfo.emergencyMsdsSafeinfos.criticalPressure"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"饱和蒸气压(kPa)",prop:"saturatedVaporPressure"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.saturatedVaporPressure,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"saturatedVaporPressure",t)},expression:"detailInfo.emergencyMsdsSafeinfos.saturatedVaporPressure"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"其他理化性质",prop:"otherProperty"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.otherProperty,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"otherProperty",t)},expression:"detailInfo.emergencyMsdsSafeinfos.otherProperty"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"稳定性和反应活性",name:"10"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"稳定性",prop:"stability"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.stability,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"stability",t)},expression:"detailInfo.emergencyMsdsSafeinfos.stability"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"禁配物",prop:"forbiddenCompound"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.forbiddenCompound,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"forbiddenCompound",t)},expression:"detailInfo.emergencyMsdsSafeinfos.forbiddenCompound"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"禁配物",prop:"forbiddenCompound"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.forbiddenCompound,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"forbiddenCompound",t)},expression:"detailInfo.emergencyMsdsSafeinfos.forbiddenCompound"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"避免接触条件",prop:"avoidCondition"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.avoidCondition,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"avoidCondition",t)},expression:"detailInfo.emergencyMsdsSafeinfos.avoidCondition"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"聚合危害",prop:"polHazard"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.polHazard,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"polHazard",t)},expression:"detailInfo.emergencyMsdsSafeinfos.polHazard"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"燃烧分解产物",prop:"combustionProduct"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.combustionProduct,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"combustionProduct",t)},expression:"detailInfo.emergencyMsdsSafeinfos.combustionProduct"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"毒理学资料",name:"11"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"LD50",prop:"ld50"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.ld50,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"ld50",t)},expression:"detailInfo.emergencyMsdsTransportinfos.ld50"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"lc50",prop:"lc50"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.lc50,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"lc50",t)},expression:"detailInfo.emergencyMsdsTransportinfos.lc50"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"LD50",prop:"ld50"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.materialName,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"materialName",t)},expression:"detailInfo.emergencyMsdsTransportinfos.materialName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"亚急性和慢性毒性",prop:"subacute"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.subacute,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"subacute",t)},expression:"detailInfo.emergencyMsdsTransportinfos.subacute"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"刺激性",prop:"thrill"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.thrill,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"thrill",t)},expression:"detailInfo.emergencyMsdsTransportinfos.thrill"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"致敏性",prop:"anaphylaxis"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.anaphylaxis,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"anaphylaxis",t)},expression:"detailInfo.emergencyMsdsTransportinfos.anaphylaxis"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"致突变性",prop:"mutability"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.mutability,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"mutability",t)},expression:"detailInfo.emergencyMsdsTransportinfos.mutability"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"致畸性",prop:"teratogenicity"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.teratogenicity,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"teratogenicity",t)},expression:"detailInfo.emergencyMsdsTransportinfos.teratogenicity"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"致癌性",prop:"carcinogenicity"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.carcinogenicity,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"carcinogenicity",t)},expression:"detailInfo.emergencyMsdsTransportinfos.carcinogenicity"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"其他有害作用",prop:"toxiOther"}},[a("el-input",{attrs:{maxlength:"200",disabled:!0,type:"textarea"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.toxiOther,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"toxiOther",t)},expression:"detailInfo.emergencyMsdsTransportinfos.toxiOther"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"生态学资料",name:"12"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"生态毒性",prop:"ecotoxicity"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.ecotoxicity,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"ecotoxicity",t)},expression:"detailInfo.emergencyMsdsTransportinfos.ecotoxicity"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"生物降解性",prop:"degradability"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.degradability,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"degradability",t)},expression:"detailInfo.emergencyMsdsTransportinfos.degradability"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"非生物降解性",prop:"nondegradability"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.nondegradability,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"nondegradability",t)},expression:"detailInfo.emergencyMsdsTransportinfos.nondegradability"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"生物富集或生物积累性",prop:"fswjjx"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.fswjjx,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"fswjjx",t)},expression:"detailInfo.emergencyMsdsTransportinfos.fswjjx"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"其它有害作用",prop:"otherHarm"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.otherHarm,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"otherHarm",t)},expression:"detailInfo.emergencyMsdsTransportinfos.otherHarm"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"废弃处置",name:"13"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"废弃物性质",prop:"wasteProperty"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.materialName,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"materialName",t)},expression:"detailInfo.emergencyMsdsTransportinfos.materialName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"废弃处置方法",prop:"wasteDisposal"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.wasteDisposal,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"wasteDisposal",t)},expression:"detailInfo.emergencyMsdsTransportinfos.wasteDisposal"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"废弃注意事项",prop:"wasteMatter"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.wasteMatter,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"wasteMatter",t)},expression:"detailInfo.emergencyMsdsTransportinfos.wasteMatter"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"运输信息",name:"14"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"危险物质编号",prop:"dangerousGoodsId"}},[a("el-input",{attrs:{disabled:!0,type:"textarea",maxlength:"200"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.dangerousGoodsId,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"dangerousGoodsId",t)},expression:"detailInfo.emergencyMsdsTransportinfos.dangerousGoodsId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"UN编号",prop:"unId"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.unId,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"unId",t)},expression:"detailInfo.emergencyMsdsTransportinfos.unId"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包装标志",prop:"packingSign"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.packingSign,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"packingSign",t)},expression:"detailInfo.emergencyMsdsTransportinfos.packingSign"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包装类别",prop:"packingType"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.packingType,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"packingType",t)},expression:"detailInfo.emergencyMsdsTransportinfos.packingType"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包装方法",prop:"packingMethod"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.packingMethod,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"packingMethod",t)},expression:"detailInfo.emergencyMsdsTransportinfos.packingMethod"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"运输注意事项",prop:"tranMatter"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.tranMatter,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"tranMatter",t)},expression:"detailInfo.emergencyMsdsTransportinfos.tranMatter"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"法规信息",name:"15"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"法规信息",prop:"lawInfo"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsTransportinfos.lawInfo,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"lawInfo",t)},expression:"detailInfo.emergencyMsdsTransportinfos.lawInfo"}})],1)],1)],1)],1)],1)],1)],1)]),a("el-collapse-item",{attrs:{title:"安全标签信息",name:"16"}},[a("div",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"abilityForm",attrs:{model:e.detailInfo,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"分子式",prop:"molecularFormula"}},[a("el-input",{attrs:{disabled:!0,maxlength:"20"},model:{value:e.detailInfo.emergencyMsdsTransportinfos.molecularFormula,callback:function(t){e.$set(e.detailInfo.emergencyMsdsTransportinfos,"molecularFormula",t)},expression:"detailInfo.emergencyMsdsTransportinfos.molecularFormula"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"危害程度",prop:"hazardousProperty"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.hazardousProperty,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"hazardousProperty",t)},expression:"detailInfo.emergencyMsdsSafeinfos.hazardousProperty"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"危险性概述",prop:"materialType"}},[a("el-input",{attrs:{maxlength:"20",disabled:!0},model:{value:e.detailInfo.emergencyMsdsSafeinfos.materialName,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"materialName",t)},expression:"detailInfo.emergencyMsdsSafeinfos.materialName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"安全措施",prop:"materialName"}},[a("el-input",{attrs:{disabled:!0,maxlength:"200",type:"textarea"},model:{value:e.abilityForm.materialName,callback:function(t){e.$set(e.abilityForm,"materialName",t)},expression:"abilityForm.materialName"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"灭火方法",prop:"outfireMethod"}},[a("el-input",{attrs:{maxlength:"200",disabled:!0,type:"textarea"},model:{value:e.detailInfo.emergencyMsdsSafeinfos.outfireMethod,callback:function(t){e.$set(e.detailInfo.emergencyMsdsSafeinfos,"outfireMethod",t)},expression:"detailInfo.emergencyMsdsSafeinfos.outfireMethod"}})],1)],1)],1)],1)],1)],1)],1)])],1)],1)],1)],1)])],1)},n=[],s=a("efcb"),o={data:function(){return{loading:!1,dataList:[{id:1,type:"物理爆炸",time:"2024-1-22 11:25:00"},{id:2,type:"火灾",time:"2024-1-23 09:21:00"},{id:3,type:"压缩气体爆炸",time:"2024-1-24 16:05:50"}],detailInfo:{emergencyMsdsSafeinfos:{},emergencyMsdsTransportinfos:{}},activeNames:["1"],popoverVisible:!1,total:0,abilityOpen:!1,title:"危险品详情",text:void 0,queryParams:{current:1,size:10,name:void 0},frequency:0,abilityForm:{},disabled:!1,nodeObj:void 0}},watch:{},created:function(){},methods:{handleChange:function(e){console.log(e)},showPopover:function(){this.popoverVisible=!this.popoverVisible},getList:function(){var e=this;this.loading=!0,Object(s["a"])(this.queryParams).then((function(t){console.log(t),null!=t.data&&(e.dataList=t.data.records,e.total=t.data.total),e.loading=!1}))},handleLook:function(e){var t=this;Object(s["b"])({id:e.id}).then((function(e){console.log(e),null!=e.data&&(t.detailInfo=e.data,t.abilityOpen=!0)}))},handleQuery:function(){this.queryParams.current=1,this.getList()},reset:function(){this.abilityForm={id:void 0,refugeName:void 0,refugeArea:void 0,holdsNumber:void 0,liabilityUser:void 0,phone:void 0,remark:void 0},this.lngAndLat="",this.resetForm("abilityForm")},resetQuery:function(){this.queryParams={current:1,size:10,name:void 0},this.resetForm("queryForm"),this.handleQuery()},navigation:function(e){this.activeNames=[e]}}},i=o,r=(a("31dc"),a("2877")),d=Object(r["a"])(i,l,n,!1,null,"2f5080ac",null);t["default"]=d.exports},efcb:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return s}));var l=a("b775");function n(e){return Object(l["a"])({url:"/emergency-msds/page",method:"get",params:e})}function s(e){return Object(l["a"])({url:"/emergency-msds/detail",method:"get",params:e})}}}]);