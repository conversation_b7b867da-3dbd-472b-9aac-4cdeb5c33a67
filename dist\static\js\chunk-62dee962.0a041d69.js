(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-62dee962"],{"45c8":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"d",(function(){return o}));var s=a("b775");a("c38a");function i(){return Object(s["a"])({url:"/emergency-event-type/tree",method:"get"})}function n(e){return Object(s["a"])({url:"/emergency-event-type-label/selectById",method:"get",params:{eventTypeId:e}})}function r(e,t){return Object(s["a"])({url:"/emergency-event-type-label/save",method:"post",data:{eventTypeId:e,label:t}})}function o(e){return Object(s["a"])({url:"/emergency-event-type-label/deleteById",method:"post",data:{id:e}})}},"57b7":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-card",{staticClass:"left-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e._v("事件类型")]),a("el-tree",{ref:"tree",attrs:{"highlight-current":!0,data:e.treeData,props:e.defaultProps,"filter-node-method":e.filterNode},on:{"node-click":e.handleNodeClick}})],1)],1),a("el-col",{directives:[{name:"show",rawName:"v-show",value:e.treeItemId,expression:"treeItemId"}],attrs:{span:16}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e._v("事件参数")]),a("div",{staticClass:"tag-add-box"},[a("span",[e._v("持续时间（天）")]),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"下限"},model:{value:e.editParams.durationTime1,callback:function(t){e.$set(e.editParams,"durationTime1",t)},expression:"editParams.durationTime1"}}),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"上限"},model:{value:e.editParams.durationTime2,callback:function(t){e.$set(e.editParams,"durationTime2",t)},expression:"editParams.durationTime2"}}),a("el-button",{attrs:{round:"",type:"primary"},on:{click:function(t){return e.addTag("durationTime")}}},[e._v("添加")])],1),a("div",{staticClass:"tag-box"},e._l(e.durationTimeList,(function(t,s){return a("el-tag",{key:s+"",staticClass:"tag-item",attrs:{closable:"","disable-transitions":!1},on:{close:function(t){return e.clearTag("durationTime",s)}}},[e._v("持续时间"+e._s(t)+"（天）")])})),1),a("div",{staticClass:"tag-add-box"},[a("span",[e._v("死亡人数（人）")]),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"下限"},model:{value:e.editParams.deathNumber1,callback:function(t){e.$set(e.editParams,"deathNumber1",t)},expression:"editParams.deathNumber1"}}),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"上限"},model:{value:e.editParams.deathNumber2,callback:function(t){e.$set(e.editParams,"deathNumber2",t)},expression:"editParams.deathNumber2"}}),a("el-button",{attrs:{round:"",type:"primary"},on:{click:function(t){return e.addTag("deathNumber")}}},[e._v("添加")])],1),a("div",{staticClass:"tag-box"},e._l(e.deathNumberList,(function(t,s){return a("el-tag",{key:s+"",staticClass:"tag-item",attrs:{closable:"","disable-transitions":!1},on:{close:function(t){return e.clearTag("deathNumber",s)}}},[e._v("死亡人数"+e._s(t)+"（人）")])})),1),a("div",{staticClass:"tag-add-box"},[a("span",[e._v("感染人数（人）")]),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"下限"},model:{value:e.editParams.infectNumber1,callback:function(t){e.$set(e.editParams,"infectNumber1",t)},expression:"editParams.infectNumber1"}}),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"上限"},model:{value:e.editParams.infectNumber2,callback:function(t){e.$set(e.editParams,"infectNumber2",t)},expression:"editParams.infectNumber2"}}),a("el-button",{attrs:{round:"",type:"primary"},on:{click:function(t){return e.addTag("infectNumber")}}},[e._v("添加")])],1),a("div",{staticClass:"tag-box"},e._l(e.infectNumberList,(function(t,s){return a("el-tag",{key:s+"",staticClass:"tag-item",attrs:{closable:"","disable-transitions":!1},on:{close:function(t){return e.clearTag("infectNumber",s)}}},[e._v("感染人数"+e._s(t)+"（人）")])})),1),a("div",{staticClass:"tag-add-box"},[a("span",[e._v("失踪人数（人）")]),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"下限"},model:{value:e.editParams.missingNumber1,callback:function(t){e.$set(e.editParams,"missingNumber1",t)},expression:"editParams.missingNumber1"}}),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"上限"},model:{value:e.editParams.missingNumber2,callback:function(t){e.$set(e.editParams,"missingNumber2",t)},expression:"editParams.missingNumber2"}}),a("el-button",{attrs:{round:"",type:"primary"},on:{click:function(t){return e.addTag("missingNumber")}}},[e._v("添加")])],1),a("div",{staticClass:"tag-box"},e._l(e.missingNumberList,(function(t,s){return a("el-tag",{key:s+"",staticClass:"tag-item",attrs:{closable:"","disable-transitions":!1},on:{close:function(t){return e.clearTag("missingNumber",s)}}},[e._v("失踪人数"+e._s(t)+"（人）")])})),1),a("div",{staticClass:"tag-add-box"},[a("span",[e._v("受伤人数（人）")]),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"下限"},model:{value:e.editParams.woundedNumber1,callback:function(t){e.$set(e.editParams,"woundedNumber1",t)},expression:"editParams.woundedNumber1"}}),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"上限"},model:{value:e.editParams.woundedNumber2,callback:function(t){e.$set(e.editParams,"woundedNumber2",t)},expression:"editParams.woundedNumber2"}}),a("el-button",{attrs:{round:"",type:"primary"},on:{click:function(t){return e.addTag("woundedNumber")}}},[e._v("添加")])],1),a("div",{staticClass:"tag-box"},e._l(e.woundedNumberList,(function(t,s){return a("el-tag",{key:s+"",staticClass:"tag-item",attrs:{closable:"","disable-transitions":!1},on:{close:function(t){return e.clearTag("woundedNumber",s)}}},[e._v("受伤人数"+e._s(t)+"（人）")])})),1),a("div",{staticClass:"tag-add-box"},[a("span",[e._v("经济损失（万元）")]),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"下限"},model:{value:e.editParams.economicLosses1,callback:function(t){e.$set(e.editParams,"economicLosses1",t)},expression:"editParams.economicLosses1"}}),a("el-input",{staticClass:"tag-add-ipt",attrs:{maxlength:"20",type:"number",min:0,placeholder:"上限"},model:{value:e.editParams.economicLosses2,callback:function(t){e.$set(e.editParams,"economicLosses2",t)},expression:"editParams.economicLosses2"}}),a("el-button",{attrs:{round:"",type:"primary"},on:{click:function(t){return e.addTag("economicLosses")}}},[e._v("添加")])],1),a("div",{staticClass:"tag-box"},e._l(e.economicLossesList,(function(t,s){return a("el-tag",{key:s+"",staticClass:"tag-item",attrs:{closable:"","disable-transitions":!1},on:{close:function(t){return e.clearTag("economicLosses",s)}}},[e._v("经济损失"+e._s(t)+"（万元）")])})),1)])],1)],1)],1)},i=[],n=(a("4de4"),a("d3b7"),a("a434"),a("a15b"),a("159b"),a("14d9"),a("45c8")),r=a("7545"),o={data:function(){return{treeData:[],defaultProps:{children:"children",label:"nodeName"},durationTimeList:[],deathNumberList:[],infectNumberList:[],missingNumberList:[],woundedNumberList:[],economicLossesList:[],editParams:{durationTime1:"",durationTime2:"",deathNumber1:"",deathNumber2:"",infectNumber1:"",infectNumber2:"",missingNumber1:"",missingNumber2:"",woundedNumber1:"",woundedNumber2:"",economicLosses1:"",economicLosses2:""},searchIpt:"",eventTypeId:"",treeItemId:"",timer:"",tagId:""}},watch:{filterText:function(e){this.$refs.tree.filter(e)}},created:function(){this.getTreeData()},methods:{filterNode:function(e,t){return!e||-1!==t.nodeName.indexOf(e)},clearTag:function(e,t){console.log(this[e+"List"]);var a=this;this.$modal.confirm("是否确认删除当前标签").then((function(){a[e+"List"].splice(t,1);var s={id:a.tagId,eventTypeId:a.treeItemId};s[e]=a[e+"List"].join(),Object(r["b"])(s).then((function(e){a.getDetail(),a.$modal.msgSuccess("删除成功")}))})).catch((function(){}))},searchInput:function(e){var t=this;this.timer&&clearTimeout(this.timer),this.timer=setTimeout((function(){t.timer=null,t.recursion(e,t.treeData)}),500),console.log()},recursion:function(e,t){var a=this;t.forEach((function(t){if(-1!=t.nodeName.indexOf(e)&&(console.log(t,"sss"),a.treeData=[t]),t.children)return-1!=t.nodeName.indexOf(e)&&console.log(t,"www"),a.recursion(e,t.children);-1!=t.nodeName.indexOf(e)&&console.log(t,"www")}))},addTag:function(e){var t=this,a=parseInt(this.editParams[e+"1"]),s=parseInt(this.editParams[e+"2"]);if(a&&s)if(a>=s||a<0)this.$message({type:"error",message:"请确认是否输入正确"});else{var i={id:this.tagId||"",eventTypeId:this.treeItemId};this[e+"List"].push(a+"-"+s),i[e]=this[e+"List"].join(),Object(r["b"])(i).then((function(e){t.getDetail()}))}else this.$message({type:"error",message:"请输入"})},handleNodeClick:function(e){this.deathNumberList=[],this.durationTimeList=[],this.economicLossesList=[],this.infectNumberList=[],this.missingNumberList=[],this.woundedNumberList=[],e.children&&e.children.length>0?this.treeItemId="":(this.treeItemId=e.id,this.getDetail())},getDetail:function(){var e=this;Object(r["a"])(this.treeItemId).then((function(t){200==(null===t||void 0===t?void 0:t.code)&&(e.tagId=t.data.id||"",e.deathNumberList=t.data.deathNumber||[],e.durationTimeList=t.data.durationTime||[],e.economicLossesList=t.data.economicLosses||[],e.infectNumberList=t.data.infectNumber||[],e.missingNumberList=t.data.missingNumber||[],e.woundedNumberList=t.data.woundedNumber||[],e.editParams={durationTime1:"",durationTime2:"",deathNumber1:"",deathNumber2:"",infectNumber1:"",infectNumber2:"",missingNumber1:"",missingNumber2:"",woundedNumber1:"",woundedNumber2:"",economicLosses1:"",economicLosses2:""})}))},getTreeData:function(){var e=this;Object(n["b"])().then((function(t){200==(null===t||void 0===t?void 0:t.code)&&(e.treeData=t.data)}))}}},d=o,c=(a("7b8e"),a("2877")),u=Object(c["a"])(d,s,i,!1,null,"7aa474ce",null);t["default"]=u.exports},7545:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var s=a("b775");a("c38a");function i(e){return Object(s["a"])({url:"/emergency-event-type-parameter/selectById",method:"get",params:{eventTypeId:e}})}function n(e){return Object(s["a"])({url:"/emergency-event-type-parameter/saveOrUpdate",method:"post",data:e})}},"7b8e":function(e,t,a){"use strict";a("9c07")},"9c07":function(e,t,a){}}]);