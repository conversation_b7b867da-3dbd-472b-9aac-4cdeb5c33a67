(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-6f71757d"],{"18d6":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"body flexs"},[n("div",{staticClass:"body-left"},[n("div",{staticClass:"left-line"},[n("div",{staticClass:"left-lineTitle"},[t._v("员工信息")]),n("div",{staticClass:"left-lineBody"},[n("p",{staticClass:"left-txt"},[t._v("姓名："+t._s(t.detailList.userName))]),n("p",{staticClass:"left-txt"},[t._v("所属部门："+t._s(t.detailList.deptName))]),n("p",{staticClass:"left-txt"},[t._v(" 服务组："+t._s(t.detailList.serviceGroupName)+" ")])])]),n("div",{staticClass:"left-line"},[n("div",{staticClass:"left-lineTitle"},[t._v("调班信息")]),n("div",{staticClass:"left-lineBody"},[n("p",{staticClass:"left-txt maxW"},[t._v(" 补卡时间："+t._s(t.detailList.repairAttendDatetime)+" ")]),n("p",{staticClass:"left-txt maxW"},[t._v(" 补卡类型："+t._s(t.detailList.repairAttendTypeName)+" ")]),n("p",{staticClass:"left-txt maxW"},[t._v(" 补卡原因："+t._s(t.detailList.reason)+" ")])])]),n("div",{staticClass:"left-down"},[n("div",{staticClass:"left-down-up"},[t._v(" 附件： "),n("div",t._l(t.detailList.files,(function(e,r){return n("div",{key:r,staticClass:"attachment"},[n("div",{staticClass:"attachmentDetail",on:{click:function(n){return t.upLoad(e)}}},[t._v(" "+t._s(e.name)+" ")])])})),0)])])]),n("div",{staticClass:"body-right"},[n("div",{staticClass:"block"},[n("el-timeline",t._l(t.processList,(function(e,r){return n("el-timeline-item",{key:r,attrs:{color:e.duration?"#0bbd87":"#FF0000"}},[n("el-card",[n("div",{staticClass:"cards cardsm"},[n("span",{staticClass:"cards-left"},[t._v("时间：")]),n("span",[t._v(t._s(e.createTime))])]),n("div",{staticClass:"cards cardsm"},[n("span",{staticClass:"cards-left"},[t._v("人员：")]),n("span",[t._v(t._s(e.assigneeName))])]),n("div",{staticClass:"cards"},[n("span",{staticClass:"cards-left"},[t._v("流程状态：")]),t._l(e.commentList,(function(r,a){return n("div",{key:a},[n("span",[t._v(t._s(r.message))]),n("span",{staticClass:"cards-dian",style:"background-color: "+(e.duration?"#0bbd87":"#FF0000")}),n("span",[t._v(t._s(1==r.type?"通过":"驳回"))])])}))],2)])],1)})),1)],1)])])},a=[],s=(n("b0c0"),n("dc01")),u={name:"",props:{},data:function(){return{processList:[],detailList:{},opinion:"",activities:[{name:"马振兵",state:"提交",stateRight:"通过",timestamp:"2018-04-12 20:46",color:"#1890ff"},{name:"马振兵",state:"提交",stateRight:"通过",timestamp:"2018-04-03 20:46",color:"#0bbd87"},{name:"马振兵",state:"提交",stateRight:"驳回",timestamp:"2018-04-03 20:46",color:"#FF0000"},{name:"马振兵",state:"提交",stateRight:"通过",timestamp:"2018-04-03 20:46"}]}},created:function(){this.bkDetails(),this.bksqlcDetails()},mounted:function(){},watch:{},filters:{},components:{},computed:{},methods:{bkDetails:function(){var t=this,e=this.$route.query.id,n={id:e};Object(s["j"])(n).then((function(e){t.detailList=e.data}))},bksqlcDetails:function(){var t=this;this.loading=!0,console.log(this.$route.query);var e=this.$route.query.id,n={workAdjustmentId:e};console.log(n,"params"),Object(s["n"])(n).then((function(e){console.log(e),t.processList=e.data.historyProcNodeList,t.loading=!1}))},upLoad:function(t){var e=document.createElement("a");e.style.display="none",e.download=t.name,e.href=t.url,document.body.appendChild(e),e.click()}}},c=u,i=(n("1bc7"),n("2877")),d=Object(i["a"])(c,r,a,!1,null,"546243fe",null);e["default"]=d.exports},"1bc7":function(t,e,n){"use strict";n("d953")},d953:function(t,e,n){},dc01:function(t,e,n){"use strict";n.d(e,"y",(function(){return a})),n.d(e,"a",(function(){return s})),n.d(e,"C",(function(){return u})),n.d(e,"B",(function(){return c})),n.d(e,"c",(function(){return i})),n.d(e,"m",(function(){return d})),n.d(e,"s",(function(){return o})),n.d(e,"z",(function(){return l})),n.d(e,"t",(function(){return m})),n.d(e,"A",(function(){return p})),n.d(e,"J",(function(){return f})),n.d(e,"K",(function(){return h})),n.d(e,"G",(function(){return b})),n.d(e,"M",(function(){return g})),n.d(e,"E",(function(){return v})),n.d(e,"w",(function(){return j})),n.d(e,"h",(function(){return O})),n.d(e,"f",(function(){return y})),n.d(e,"e",(function(){return _})),n.d(e,"q",(function(){return C})),n.d(e,"b",(function(){return L})),n.d(e,"r",(function(){return k})),n.d(e,"F",(function(){return w})),n.d(e,"k",(function(){return x})),n.d(e,"H",(function(){return A})),n.d(e,"l",(function(){return D})),n.d(e,"g",(function(){return I})),n.d(e,"D",(function(){return B})),n.d(e,"o",(function(){return F})),n.d(e,"I",(function(){return S})),n.d(e,"i",(function(){return q})),n.d(e,"j",(function(){return N})),n.d(e,"n",(function(){return T})),n.d(e,"x",(function(){return E})),n.d(e,"d",(function(){return R})),n.d(e,"u",(function(){return $})),n.d(e,"v",(function(){return J})),n.d(e,"p",(function(){return W})),n.d(e,"L",(function(){return G}));var r=n("b775");function a(t){return Object(r["a"])({url:"/schedule/arrangement/pageList",method:"get",params:t})}function s(t){return Object(r["a"])({url:"/schedule/arrangement/save",method:"post",data:t})}function u(t){return Object(r["a"])({url:"/schedule/work-adjustment/pageList",method:"get",params:t})}function c(t){return Object(r["a"])({url:"/schedule/schedule/mySchedule",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/schedule/work-adjustment/save",method:"post",data:t})}function d(t){return Object(r["a"])({url:"/schedule/repair-attend-apply/save",method:"post",data:t})}function o(t){return Object(r["a"])({url:"/schedule/service-group/findList",method:"get",params:t})}function l(t){return Object(r["a"])({url:"/schedule/service-group/getSelectList",method:"get",params:t})}function m(t){return Object(r["a"])({url:"/schedule/member/findList",method:"get",params:t})}function p(t){return Object(r["a"])({url:"/schedule/schedule/getShowData",method:"get",params:t})}function f(t){return Object(r["a"])({url:"/schedule/work-adjustment/detail",method:"get",params:t})}function h(t){return Object(r["a"])({url:"/schedule/work-adjustment/getActInfo",method:"get",params:t})}function b(t){return Object(r["a"])({url:"/schedule/work-adjustment/submitAct",method:"post",params:t})}function g(t){return Object(r["a"])({url:"/schedule/work-adjustment/withdrawAct",method:"get",params:t})}function v(t){return Object(r["a"])({url:"/schedule/schedule/pageList",method:"get",params:t})}function j(t){return Object(r["a"])({url:"/schedule/arrangement/getArrangementTypeList",method:"get",params:t})}function O(t){return Object(r["a"])({url:"/schedule/arrangement/findList",method:"get",params:t})}function y(t){return Object(r["a"])({url:"/schedule/arrangement/detail",method:"get",params:t})}function _(t){return Object(r["a"])({url:"/schedule/schedule/autoSetSchedule",method:"post",data:t})}function C(t){return Object(r["a"])({url:"/schedule/arrangement/deleteByIds",method:"post",params:t})}function L(t){return Object(r["a"])({url:"/schedule/schedule/save",method:"post",data:t})}function k(t){return Object(r["a"])({url:"/schedule/schedule/exportExcel",method:"get",params:t,responseType:"blob"})}function w(t){return Object(r["a"])({url:"/schedule/member-work/saveEntitys",method:"post",data:t})}function x(t){return Object(r["a"])({url:"/schedule/repair-attend-apply/pageList",method:"get",params:t})}function A(t){return Object(r["a"])({url:"/schedule/work-adjustment/update",method:"post",data:t})}function D(t){return Object(r["a"])({url:"/schedule/repair-attend-apply/update",method:"post",data:t})}function I(t){return Object(r["a"])({url:"/schedule/arrangement/update",method:"post",data:t})}function B(t){return Object(r["a"])({url:"/schedule/schedule/deleteByIds",method:"post",params:t})}function F(t){return Object(r["a"])({url:"/schedule/repair-attend-apply/submitAct",method:"post",params:t})}function S(t){return Object(r["a"])({url:"/schedule/work-adjustment/deleteByIds",method:"post",params:t})}function q(t){return Object(r["a"])({url:"/schedule/repair-attend-apply/deleteByIds",method:"post",params:t})}function N(t){return Object(r["a"])({url:"/schedule/repair-attend-apply/detail",method:"get",params:t})}function T(t){return Object(r["a"])({url:"/schedule/repair-attend-apply/getActInfo",method:"get",params:t})}function E(t){return Object(r["a"])({url:"/schedule/sysQuery/getLoginMemberInfo",method:"get",params:t})}function R(t){return Object(r["a"])({url:"/schedule/work-adjustment/approvalOperation",method:"post",params:t})}function $(t){return Object(r["a"])({url:"/schedule/schedule/getAllShowData",method:"get",params:t})}function J(t){return Object(r["a"])({url:"/schedule/service-group/findList",method:"get"})}function W(t){return Object(r["a"])({url:"/schedule/arrangement/updateStatus",method:"post",params:t})}function G(t){return Object(r["a"])({url:"/schedule/service-group/updateRemind",method:"post",data:t})}}}]);