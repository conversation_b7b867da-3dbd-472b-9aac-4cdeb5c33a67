(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-813e36ce"],{"1d9e":function(e,t,a){},2298:function(e,t,a){"use strict";a("1d9e")},f589:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0}},[a("el-form-item",{attrs:{label:"区域名称",prop:"areaName"}},[a("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入区域名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.areaName,callback:function(t){e.$set(e.queryParams,"areaName",t)},expression:"queryParams.areaName"}})],1),a("el-form-item",{attrs:{label:"创建时间"}},[a("el-date-picker",{staticStyle:{width:"230px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",{attrs:{label:"联系人",prop:"principal"}},[a("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入联系人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.principal,callback:function(t){e.$set(e.queryParams,"principal",t)},expression:"queryParams.principal"}})],1),a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入联系电话",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.phone,callback:function(t){e.$set(e.queryParams,"phone",t)},expression:"queryParams.phone"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增区域")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roleList}},[a("el-table-column",{attrs:{label:"ID","show-overflow-tooltip":!0,prop:"id",align:"center"}}),a("el-table-column",{attrs:{label:"消防区域网格",prop:"griddingName",align:"center"}}),a("el-table-column",{attrs:{label:"区域范围",prop:"areaName",align:"center"}}),a("el-table-column",{attrs:{label:"责任人",prop:"principal",align:"center"}}),a("el-table-column",{attrs:{label:"联系电话",prop:"phone",align:"center"}}),a("el-table-column",{attrs:{label:"创建人",prop:"createUser",align:"center"}}),a("el-table-column",{attrs:{label:"创建日期",prop:"createTime",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("div",{staticClass:"diaTil"},[a("p",[e._v("基础信息")]),a("el-dialog",{attrs:{width:"600px",title:"关联区域",visible:e.innerVisible,"append-to-body":""},on:{"update:visible":function(t){e.innerVisible=t}}},[a("el-tree",{ref:"tree",attrs:{data:e.treeData,"show-checkbox":"","default-expand-all":"","node-key":"areaId","highlight-current":"",props:e.defaultProps,"check-strictly":""}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.determine()}}},[e._v("确 定")])],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"区域名称",prop:"griddingName"}},[a("el-input",{attrs:{placeholder:"请输入区域名称"},model:{value:e.form.griddingName,callback:function(t){e.$set(e.form,"griddingName",t)},expression:"form.griddingName"}})],1)],1)],1),a("el-button",{attrs:{type:"primary",size:"mini",disabled:e.disabled},on:{click:function(t){return e.innerVisibleOpen()}}},[e._v("关联区域")]),a("div",[e._v(e._s(e.areaName))]),a("div",{staticClass:"diaTil"},[a("p",[e._v("责任人")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"责任人",prop:"principal"}},[a("el-input",{attrs:{placeholder:"请输入责任人"},model:{value:e.form.principal,callback:function(t){e.$set(e.form,"principal",t)},expression:"form.principal"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],n=(a("d9e2"),a("ac1f"),a("00b4"),a("d3b7"),a("159b"),a("b0c0"),a("14d9"),a("a15b"),a("b775"));function o(e){return Object(n["a"])({url:"/firecontrol-area/page",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/firecontrol-area/save",method:"post",data:e})}function s(){return Object(n["a"])({url:"/area/tree",method:"get"})}function d(e){return Object(n["a"])({url:"/firecontrol-area/update",method:"post",data:e})}var c={name:"Region",data:function(){var e=function(e,t,a){""===t?a(new Error("请输入绑定的手机号码")):/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(t)?a():a(new Error("请输入正确的手机号码"))};return{loading:!0,showSearch:!0,total:0,roleList:[{}],title:"",open:!1,disabled:!1,queryParams:{current:1,size:10,griddingId:void 0,areaName:void 0,principal:void 0,phone:void 0,startTime:void 0,endTime:void 0},form:{},dateRange:[],innerVisible:!1,treeData:[],defaultProps:{children:"children",label:"name"},areaName:"",rules:{griddingId:[{required:!0,message:"请输入区域ID",trigger:"blur"}],griddingName:[{required:!0,message:"请输入区域名称",trigger:"blur"}],principal:[{required:!0,message:"请输入责任人",trigger:"blur"}],phone:[{validator:e,required:!0,trigger:"blur"}]}}},created:function(){this.getList(),this.getAreaList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.roleList=t.data.records,e.total=t.data.total,e.loading=!1}))},getAreaList:function(){var e=this;s().then((function(t){t.data.forEach((function(e){e.areaName=e.name})),e.addName(t.data),e.treeData=t.data}))},determine:function(){var e=this;this.areaName="",this.form.areaNameArr=[],this.form.areaIdArr=[];var t=this.$refs.tree.getCheckedNodes();t.forEach((function(t){e.form.areaNameArr.push(t.name),e.form.areaIdArr.push(t.areaId),e.areaName+=t.areaName+" , "})),this.innerVisible=!1},addName:function(e){var t=this;e.forEach((function(e){e.children&&(e.children.forEach((function(t){t.areaName=e.areaName+"-"+t.name})),t.addName(e.children))}))},cancel:function(){this.open=!1,this.reset()},innerVisibleOpen:function(){this.innerVisible=!0},reset:function(){this.form={id:void 0,areaIdArr:[],areaNameArr:[],areaId:void 0,areaName:void 0,griddingName:void 0,griddingId:void 0,principal:void 0,phone:void 0},this.resetForm("form")},handleQuery:function(){this.queryParams.current=1,this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(){this.reset(),this.open=!0,this.title="新增区域"},handleUpdate:function(e){this.reset(),this.open=!0,this.title="修改区域",this.form=e,this.disabled=!0},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.id?d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):(e.form.areaName=e.form.areaNameArr.join(),e.form.areaId=e.form.areaIdArr.join(),e.$refs.tree.setCheckedKeys([]),l(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()}))))}))},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return d({id:e.id,isDeleted:1})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(e){console.log(e)}))}}},u=c,m=(a("2298"),a("2877")),p=Object(m["a"])(u,r,i,!1,null,"7eab45ae",null);t["default"]=p.exports}}]);