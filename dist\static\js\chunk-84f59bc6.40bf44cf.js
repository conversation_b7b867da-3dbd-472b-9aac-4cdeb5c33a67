(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-84f59bc6"],{2995:function(t,e,o){},b74f:function(t,e,o){"use strict";o("2995")},d051:function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("el-card",{staticClass:"box-card"},[o("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[o("span",[t._v("数据筛选")])]),o("div",{staticClass:"topBottom"},[o("div",{staticClass:"descriptions"},[o("el-descriptions",{attrs:{column:4}},[o("el-descriptions-item",[o("div",{staticClass:"labelStyle",attrs:{slot:"label"},slot:"label"},[t._v("单位名称")]),o("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入单位名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.protectionUnitName,callback:function(e){t.$set(t.queryParams,"protectionUnitName",e)},expression:"queryParams.protectionUnitName"}})],1)],1)],1),o("div",{staticClass:"tabButton"},[o("el-button",{staticClass:"queryBtn",attrs:{icon:"el-icon-refresh"},on:{click:t.resetQuery}},[t._v("重置")]),o("el-button",{staticClass:"queryBtn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleQuery}},[t._v("搜索")])],1)])]),o("el-card",{staticClass:"box-card"},[o("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[o("span",[t._v("消防单位展示列表")]),o("el-button",{staticClass:"queryBtnT",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.handleAdd}},[t._v("新增消防单位")])],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{"cell-style":{padding:"0px"},"row-style":{height:"48px"},data:t.tableData}},[o("el-table-column",{attrs:{type:"index",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("span",[t._v(t._s((t.queryParams.current-1)*t.queryParams.size+e.$index+1))])]}}])}),o("el-table-column",{attrs:{label:"单位名称",align:"center",prop:"protectionUnitName"}}),o("el-table-column",{attrs:{label:"单位属性",align:"center",prop:"attribute"}}),o("el-table-column",{attrs:{label:"联系人",align:"center",prop:"contacts"}}),o("el-table-column",{attrs:{label:"联系电话",align:"center",prop:"phone"}}),o("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(o){return t.handleLook(e.row)}}},[t._v("详情")]),o("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(o){return t.handleUpdate(e.row)}}},[t._v("编辑")]),o("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(o){return t.handleDelete(e.row)}}},[t._v("删除")])]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.current,limit:t.queryParams.size},on:{"update:page":function(e){return t.$set(t.queryParams,"current",e)},"update:limit":function(e){return t.$set(t.queryParams,"size",e)},pagination:t.getList}})],1),o("el-dialog",{attrs:{title:t.title,visible:t.dialogVisible,width:"720px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[o("el-form",{ref:"ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"100px"}},[o("el-form-item",{attrs:{label:"单位名称",prop:"protectionUnitName"}},[o("el-input",{staticStyle:{width:"245px"},attrs:{maxlength:"32",disabled:t.disabled},model:{value:t.ruleForm.protectionUnitName,callback:function(e){t.$set(t.ruleForm,"protectionUnitName",e)},expression:"ruleForm.protectionUnitName"}}),o("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.connectRoom}},[t._v("关联消控室监控点")])],1),o("el-form-item",{attrs:{label:"单位属性",prop:"attribute"}},[o("el-input",{staticStyle:{width:"245px"},attrs:{type:"textarea",maxlength:"512",disabled:t.disabled},model:{value:t.ruleForm.attribute,callback:function(e){t.$set(t.ruleForm,"attribute",e)},expression:"ruleForm.attribute"}})],1),o("el-form-item",{attrs:{label:"联系人",prop:"contacts"}},[o("el-input",{staticStyle:{width:"245px"},attrs:{maxlength:"32"},model:{value:t.ruleForm.contacts,callback:function(e){t.$set(t.ruleForm,"contacts",e)},expression:"ruleForm.contacts"}})],1),o("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[o("el-input",{staticStyle:{width:"245px"},attrs:{maxlength:"32"},model:{value:t.ruleForm.phone,callback:function(e){t.$set(t.ruleForm,"phone",e)},expression:"ruleForm.phone"}})],1)],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:t.unitSubmit}},[t._v("确 定")]),o("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")])],1)],1),o("el-dialog",{attrs:{title:"关联消控室监控点",visible:t.monitorDialog,width:"560px","show-close":!1,"close-on-press-escape":!1,"close-on-click-modal":!1,"append-to-body":""},on:{"update:visible":function(e){t.monitorDialog=e}}},[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.roomLoading,expression:"roomLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.fireControlData,"tooltip-effect":"dark",height:"360","row-key":function(t){return t.id}},on:{"selection-change":t.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection","reserve-selection":!0,width:"100"}}),o("el-table-column",{attrs:{prop:"principal",align:"center",label:"负责人"}}),o("el-table-column",{attrs:{prop:"name",align:"center",label:"消控室名称","show-overflow-tooltip":""}})],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:function(e){t.monitorDialog=!1}}},[t._v("确 定")])],1)],1),o("el-dialog",{attrs:{title:"消防单位详情",visible:t.detailDialog,width:"720px","close-on-press-escape":!1,"close-on-click-modal":!1,"append-to-body":""},on:{"update:visible":function(e){t.detailDialog=e}}},[o("el-descriptions",{attrs:{column:2}},[o("el-descriptions-item",{attrs:{label:"单位名称"}},[t._v(t._s(t.detail.protectionUnitName))]),o("el-descriptions-item",{attrs:{label:"单位属性"}},[t._v(t._s(t.detail.attribute))]),o("el-descriptions-item",{attrs:{label:"联系人"}},[t._v(t._s(t.detail.contacts))]),o("el-descriptions-item",{attrs:{label:"联系电话"}},[t._v(t._s(t.detail.phone))])],1),o("p",[t._v("关联消控室监控点")]),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.roomLoading,expression:"roomLoading"}],staticStyle:{width:"100%"},attrs:{data:t.detailRoomList,"tooltip-effect":"dark",height:"360","row-key":function(t){return t.id}},on:{"selection-change":t.handleSelectionChange}},[o("el-table-column",{attrs:{prop:"principal",align:"center",label:"负责人"}}),o("el-table-column",{attrs:{prop:"name",align:"center",label:"消控室名称","show-overflow-tooltip":""}}),o("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(o){return t.handleViewRoom(e.row)}}},[t._v("查看消控室")])]}}])})],1)],1)],1)},n=[],a=(o("ac1f"),o("00b4"),o("d9e2"),o("b64b"),o("e9c4"),o("d3b7"),o("159b"),o("14d9"),o("d81d"),o("b0c0"),o("b775"));function l(t){return Object(a["a"])({url:"/firecontrol-protection-unit/page",method:"get",params:t})}function r(t){return Object(a["a"])({url:"/firecontrol-protection-unit/save",method:"post",data:t})}function s(t){return Object(a["a"])({url:"/firecontrol-protection-unit/update",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/firecontrol-protection-unit/delete",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/firecontrol-protection-unit/detail",method:"get",params:t})}function d(t){return Object(a["a"])({url:"/firecontrol-room/list",method:"get",params:t})}var p={name:"policyConfiguration",dicts:["event_handle_type","effective_status","event_priority"],data:function(){var t=function(t,e,o){var i=/^1[345789]\d{9}$/;i.test(e)?o():o(new Error("请输入11位手机号"))};return{loading:!1,showSearch:!0,total:0,tableData:null,open:!1,status:!0,queryParams:{current:1,size:10,protectionUnitName:void 0},optionSeen:[],protectionUnitId:"",title:"",disabled:!1,ruleForm:{protectionUnitName:"",attribute:"",contacts:"",phone:""},rules:{protectionUnitName:[{required:!0,message:"请输入单位名称",trigger:"blur"}],attribute:[{required:!0,message:"请输入单位属性",trigger:"blur"}],contacts:[{required:!0,message:"请输入联系人",trigger:"blur"}],phone:[{type:"number",validator:t,message:"请输入正确的手机号",trigger:"change",required:!0}]},dialogVisible:!1,monitorDialog:!1,roomLoading:!1,fireControlName:"",fireControlData:[],multipleSelection:[],detailDialog:!1,detail:{},detailRoomList:[]}},watch:{},created:function(){this.getList()},computed:{},methods:{getList:function(){var t=this;this.loading=!0,l(this.queryParams).then((function(e){null!=e.data&&(t.tableData=e.data.records,t.total=e.data.total),t.loading=!1}))},handleAdd:function(){this.protectionUnitId="",this.$refs.ruleForm&&this.$refs.ruleForm.resetFields(),this.title="新增消防单位",this.dialogVisible=!0,this.disabled=!1},handleUpdate:function(t){var e=this;this.title="编辑消防单位",this.dialogVisible=!0,u({id:t.id}).then((function(t){if(e.protectionUnitId=t.data.id,e.ruleForm=JSON.parse(JSON.stringify(t.data)),t.data.firecontrolProtectionUnitAssociationRoomList.length>0){var o=[];t.data.firecontrolProtectionUnitAssociationRoomList.forEach((function(t){o.push({id:t.roomId})})),e.multipleSelection=o}})),this.disabled=!0},handleLook:function(t){var e=this;this.detailDialog=!0,u({id:t.id}).then((function(t){console.log(t.data),e.detail=JSON.parse(JSON.stringify(t.data)),e.detailRoomList=t.data.firecontrolRoomList}))},handleViewRoom:function(t){console.log(t),this.dialogVisible=!1,this.$router.push({path:"fireManagement/fireControlRoom/fireControl",name:"FireControl",query:{id:t.id}})},connectRoom:function(){var t=this;this.monitorDialog=!0,this.roomLoading=!0,d(null).then((function(e){t.fireControlData=e.data,t.roomLoading=!1,t.multipleSelection.length>0&&t.multipleSelection.map((function(e){t.$refs.multipleTable.toggleRowSelection(e,!0)}))}))},handleSelectionChange:function(t){var e=this;this.multipleSelection=t,this.protectionUnitId?(this.ruleForm.firecontrolProtectionUnitAssociationRoomList=[],t.forEach((function(t){e.ruleForm.firecontrolProtectionUnitAssociationRoomList.push({roomId:t.id,protectionUnitId:e.protectionUnitId})}))):(this.ruleForm.firecontrolProtectionUnitAssociationRoomList=[],t.forEach((function(t){e.ruleForm.firecontrolProtectionUnitAssociationRoomList.push({roomId:t.id})})))},unitSubmit:function(){var t=this;this.$refs["ruleForm"].validate((function(e){if(!e)return console.log("error submit!!"),!1;console.log(t.ruleForm),t.protectionUnitId?s(t.ruleForm).then((function(e){200==e.code&&(t.$modal.msgSuccess("单位编辑成功"),t.dialogVisible=!1,t.getList())})):r(t.ruleForm).then((function(e){200==e.code&&(t.$modal.msgSuccess("单位新增成功"),t.dialogVisible=!1,t.getList())}))}))},handleDelete:function(t){var e=this;this.$modal.confirm("是否确认删除当前单位").then((function(){return c({id:t.id})})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleQuery:function(){this.queryParams.current=1,this.getList()},resetQuery:function(){this.queryParams.name="",this.handleQuery()},formScene:function(t){var e="";return this.optionSeen.map((function(o){t.parentSceneType==o.id&&(e=o.sceneName)})),e},formtype:function(t){var e="";return this.dict.type.effective_status.map((function(o){t.status==o.value&&(e=o.label)})),e}}},m=p,f=(o("b74f"),o("2877")),h=Object(f["a"])(m,i,n,!1,null,"283cbfeb",null);e["default"]=h.exports}}]);