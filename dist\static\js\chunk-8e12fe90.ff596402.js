(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-8e12fe90"],{"553e":function(t,e,a){},aa29:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"body"},[a("div",{staticClass:"add"},[a("p",{staticClass:"addtitle"},[t._v("基本设置")]),a("div",{staticClass:"addbody"},[a("el-form",{ref:"form",attrs:{model:t.addShift,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"班次名称："}},[a("el-input",{staticClass:"width455",attrs:{maxlength:20},model:{value:t.addShift.name,callback:function(e){t.$set(t.addShift,"name",e)},expression:"addShift.name"}})],1),a("el-form-item",{attrs:{label:"班次类型："}},[a("el-radio-group",{on:{input:t.changeRadio},model:{value:t.addShift.type,callback:function(e){t.$set(t.addShift,"type",e)},expression:"addShift.type"}},[a("el-radio",{attrs:{label:1}},[t._v("固定班次")]),a("el-radio",{attrs:{label:2}},[t._v("弹性班次")]),a("el-radio",{attrs:{label:3}},[t._v("签到班次")]),a("el-radio",{attrs:{label:0}},[t._v("自定义 "),a("el-input",{staticStyle:{width:"167px",height:"32px","margin-left":"5px"},attrs:{placeholder:"请输入班次类型名称",maxlength:20},model:{value:t.addShift.typeName,callback:function(e){t.$set(t.addShift,"typeName",e)},expression:"addShift.typeName"}})],1)],1)],1),1==t.addShift.type||0==t.addShift.type?a("div",[a("el-form-item",{attrs:{label:"上下班时间："}},[a("div",{staticClass:"flexs"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addList()}}},[t._v("新增班段")])],1),a("div",{staticClass:"flexs"},[t._v(" 工作时间总计： "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择"},model:{value:t.addShift.hour,callback:function(e){t.$set(t.addShift,"hour",e)},expression:"addShift.hour"}},t._l(t.houroptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",[a("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:t.addShift.tableData,border:""}},[a("el-table-column",{attrs:{label:"班段",width:"140",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("el-input",{staticClass:"width120",attrs:{maxlength:20},model:{value:e.row.name,callback:function(a){t.$set(e.row,"name",a)},expression:"scope.row.name"}})],1)]}}],null,!1,3404598186)}),a("el-table-column",{attrs:{label:"工作时间",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"oneceil"},[a("div",{staticStyle:{"border-bottom":"1px solid #f2f2f2"}},[t._v(" 上班： "),t.addShift.tableData[e.$index].goToFollowingDay?a("span",[t._v("次日")]):a("span",[t._v("当天")]),a("el-time-picker",{staticClass:"width120",attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"上班时间"},on:{change:function(a){t.upClassTime(a,e.$index,e.row,"sb")}},model:{value:t.addShift.tableData[e.$index].startWorkTime,callback:function(a){t.$set(t.addShift.tableData[e.$index],"startWorkTime",a)},expression:"addShift.tableData[scope.$index].startWorkTime\n                                                        "}})],1),a("div",[t._v(" 下班： "),t.addShift.tableData[e.$index].afterFollowingDay?a("span",[t._v(" 次日 ")]):a("span",[t._v("当天")]),a("el-time-picker",{staticClass:"width120",attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"下班时间"},on:{change:function(a){t.upClassTime(a,e.$index,e.row,"xb")}},model:{value:t.addShift.tableData[e.$index].endWorkTime,callback:function(a){t.$set(t.addShift.tableData[e.$index],"endWorkTime",a)},expression:"addShift.tableData[scope.$index].endWorkTime\n                                                        "}})],1)])]}}],null,!1,2847827604)}),a("el-table-column",{attrs:{label:"最早打卡时间",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"oneceil padLeftNone"},[a("div",[t._v(" 提前 "),a("el-input",{staticStyle:{width:"50px","margin-right":"3px","margin-left":"5px"},attrs:{type:"number",min:"0",placeholder:"时",maxlength:20},on:{input:function(a){return t.computeTime(e.row,e.$index,"goToEarlyMinutes")}},model:{value:t.addShift.tableData[e.$index].goToEarlyHour,callback:function(a){t.$set(t.addShift.tableData[e.$index],"goToEarlyHour",a)},expression:"addShift.tableData[scope.$index].goToEarlyHour"}}),t._v(" 小时 "),a("el-input",{staticStyle:{width:"50px","margin-right":"3px","margin-left":"3px"},attrs:{type:"number",min:"0",placeholder:"分",maxlength:20},on:{input:function(a){return t.computeTime(e.row,e.$index,"goToEarlyMinutes")}},model:{value:t.addShift.tableData[e.$index].goToEarlyMinutes,callback:function(a){t.$set(t.addShift.tableData[e.$index],"goToEarlyMinutes",a)},expression:"addShift.tableData[scope.$index].goToEarlyMinutes"}}),t._v(" 分钟打卡 ")],1),a("div",[t._v(" 提前 "),a("el-input",{staticStyle:{width:"50px","margin-right":"3px","margin-left":"5px"},attrs:{type:"number",min:"0",placeholder:"时",maxlength:20},on:{input:function(a){return t.computeTime(e.row,e.$index,"afterEarlyMinutes")}},model:{value:t.addShift.tableData[e.$index].afterEarlyHours,callback:function(a){t.$set(t.addShift.tableData[e.$index],"afterEarlyHours",a)},expression:"addShift.tableData[scope.$index].afterEarlyHours"}}),t._v(" 小时 "),a("el-input",{staticStyle:{width:"50px","margin-right":"3px","margin-left":"3px"},attrs:{type:"number",min:"0",placeholder:"分",maxlength:20},on:{input:function(a){return t.computeTime(e.row,e.$index,"afterEarlyMinutes")}},model:{value:t.addShift.tableData[e.$index].afterEarlyMinutes,callback:function(a){t.$set(t.addShift.tableData[e.$index],"afterEarlyMinutes",a)},expression:"addShift.tableData[scope.$index].afterEarlyMinutes"}}),t._v(" 分钟打卡 ")],1)])]}}],null,!1,216063566)}),a("el-table-column",{attrs:{label:"最晚打卡时间",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"oneceil padLeftNone"},[a("div",[t._v(" 延迟 "),a("el-input",{staticStyle:{width:"50px","margin-right":"3px","margin-left":"5px"},attrs:{type:"number",min:"0",placeholder:"时",maxlength:20},on:{input:function(a){return t.computeTime(e.row,e.$index,"goToLateMinutes")}},model:{value:t.addShift.tableData[e.$index].goToLateHour,callback:function(a){t.$set(t.addShift.tableData[e.$index],"goToLateHour",a)},expression:"addShift.tableData[scope.$index].goToLateHour"}}),t._v(" 小时 "),a("el-input",{staticStyle:{width:"50px","margin-right":"3px","margin-left":"3px"},attrs:{type:"number",min:"0",placeholder:"分",maxlength:20},on:{input:function(a){return t.computeTime(e.row,e.$index,"goToLateMinutes")}},model:{value:t.addShift.tableData[e.$index].goToLateMinutes,callback:function(a){t.$set(t.addShift.tableData[e.$index],"goToLateMinutes",a)},expression:"addShift.tableData[scope.$index].goToLateMinutes"}}),t._v(" 分钟打卡 ")],1),a("div",[t._v(" 延迟 "),a("el-input",{staticStyle:{width:"50px","margin-right":"3px","margin-left":"5px"},attrs:{type:"number",min:"0",placeholder:"时",maxlength:20},on:{input:function(a){return t.computeTime(e.row,e.$index,"afterLateMinutes")}},model:{value:t.addShift.tableData[e.$index].afterLateHours,callback:function(a){t.$set(t.addShift.tableData[e.$index],"afterLateHours",a)},expression:"addShift.tableData[scope.$index].afterLateHours"}}),t._v(" 小时 "),a("el-input",{staticStyle:{width:"50px","margin-right":"3px","margin-left":"3px"},attrs:{type:"number",min:"0",placeholder:"分",maxlength:20},on:{input:function(a){return t.computeTime(e.row,e.$index,"afterLateMinutes")}},model:{value:t.addShift.tableData[e.$index].afterLateMinutes,callback:function(a){t.$set(t.addShift.tableData[e.$index],"afterLateMinutes",a)},expression:"addShift.tableData[scope.$index].afterLateMinutes"}}),t._v(" 分钟打卡 ")],1)])]}}],null,!1,365396622)}),a("el-table-column",{attrs:{label:"休息时间",width:"210",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"oneceil"},[a("div",[t._v(" 休息开始： "),a("el-time-picker",{staticClass:"width120",attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"休息开始"},on:{change:function(a){t.upClassTime(a,e.$index,e.row)}},model:{value:t.addShift.tableData[e.$index].restStartTime,callback:function(a){t.$set(t.addShift.tableData[e.$index],"restStartTime",a)},expression:"addShift.tableData[scope.$index].restStartTime\n                                                        "}})],1),a("div",[t._v(" 休息结束： "),a("el-time-picker",{staticClass:"width120",attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"休息结束"},on:{change:function(a){t.upClassTime(a,e.$index,e.row)}},model:{value:t.addShift.tableData[e.$index].restEndTime,callback:function(a){t.$set(t.addShift.tableData[e.$index],"restEndTime",a)},expression:"addShift.tableData[scope.$index].restEndTime\n                                                        "}})],1)])]}}],null,!1,4049827382)}),a("el-table-column",{attrs:{label:"操作",width:"140",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{on:{click:function(a){return t.deleteList(e.$index,e.row)}}},[a("span",[t._v("删除")])])]}}],null,!1,4232237793)})],1)],1)]),a("el-form-item",{attrs:{label:"弹性打卡："}},[a("div",[a("el-checkbox",{attrs:{"true-label":"1","false-label":"0"},model:{value:t.addShift.checkbox1,callback:function(e){t.$set(t.addShift,"checkbox1",e)},expression:"addShift.checkbox1"}},[t._v("晚到、早走几分钟不记为异常 ")]),1==t.addShift.checkbox1?a("div",{staticClass:"checkbox"},[a("div",[t._v(" 上班最多可晚到： "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择"},model:{value:t.addShift.checkbox1Up,callback:function(e){t.$set(t.addShift,"checkbox1Up",e)},expression:"addShift.checkbox1Up"}},t._l(t.Dateoptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(" 内不算迟到 ")],1),a("div",[t._v(" 下班最多可早走： "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择"},model:{value:t.addShift.checkbox1Down,callback:function(e){t.$set(t.addShift,"checkbox1Down",e)},expression:"addShift.checkbox1Down"}},t._l(t.Dateoptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(" 内不算早退 ")],1)]):t._e()],1),a("div",[a("el-checkbox",{attrs:{"true-label":"1","false-label":"0"},model:{value:t.addShift.checkbox2,callback:function(e){t.$set(t.addShift,"checkbox2",e)},expression:"addShift.checkbox2"}},[t._v("下班晚走，第二天可晚到 ")]),1==t.addShift.checkbox2?a("div",{staticClass:"checkbox"},[a("div",[a("el-radio",{attrs:{label:"1"},model:{value:t.addShift.radio1,callback:function(e){t.$set(t.addShift,"radio1",e)},expression:"addShift.radio1"}},[t._v("仅下班的内勤打卡计算为晚走")]),a("el-radio",{attrs:{label:"2"},model:{value:t.addShift.radio1,callback:function(e){t.$set(t.addShift,"radio1",e)},expression:"addShift.radio1"}},[t._v("下班的内勤，外勤打卡均计算为晚走")])],1),a("div",[t._v(" 第一天下班后晚走 "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择"},model:{value:t.addShift.checkbox2Up,callback:function(e){t.$set(t.addShift,"checkbox2Up",e)},expression:"addShift.checkbox2Up"}},t._l(t.DateoptionsHour,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),t._v(" ，第二天上班可以晚到 "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择"},model:{value:t.addShift.checkbox2Down,callback:function(e){t.$set(t.addShift,"checkbox2Down",e)},expression:"addShift.checkbox2Down"}},t._l(t.DateoptionsHour,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticStyle:{"font-size":"12px",color:"#999999"}})]):t._e()],1)])],1):t._e(),2==t.addShift.type?a("div",[a("el-form-item",{attrs:{label:"工作时长"}},[a("div",{staticClass:"flexs"},[t._v(" 必须工作时长为： "),a("el-input",{staticClass:"width100",attrs:{maxlength:20},model:{value:t.addShift.hour,callback:function(e){t.$set(t.addShift,"hour",e)},expression:"addShift.hour"}}),t._v(" 小时，计为 "),a("el-input",{staticClass:"width100",attrs:{maxlength:20},model:{value:t.addShift.days,callback:function(e){t.$set(t.addShift,"days",e)},expression:"addShift.days"}}),t._v(" 天出勤 ")],1)]),a("el-form-item",{attrs:{label:"打卡时间"}},[a("div",[a("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:t.addShift.tableData,border:""}},[a("el-table-column",{attrs:{label:"班段",width:"300",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("el-input",{staticClass:"width120",attrs:{maxlength:20},model:{value:e.row.name,callback:function(a){t.$set(e.row,"name",a)},expression:"scope.row.name"}})],1)]}}],null,!1,3404598186)}),a("el-table-column",{attrs:{label:"最早打卡时间",width:"300",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("div",[t._v(" 最早打卡：当天 "),a("el-time-picker",{staticClass:"width120",attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"上班时间"},on:{change:function(a){t.upClassTime(a,e.$index,e.row,"txSb")}},model:{value:t.addShift.tableData[e.$index].startEarliestWorkTime,callback:function(a){t.$set(t.addShift.tableData[e.$index],"startEarliestWorkTime",a)},expression:"addShift.tableData[scope.$index].startEarliestWorkTime\n                                                        "}})],1)])]}}],null,!1,1685303098)}),a("el-table-column",{attrs:{label:"最晚打卡时间",width:"300",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("div",[t._v(" 最晚打卡： "),"2"==t.addShift.tableData[e.$index].endLatestDayType?a("span",[t._v(" 次日 ")]):a("span",[t._v("当天")]),a("el-time-picker",{staticClass:"width120",attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"下班时间"},on:{change:function(a){t.upClassTime(a,e.$index,e.row,"txXb")}},model:{value:t.addShift.tableData[e.$index].endLatestWorkTime,callback:function(a){t.$set(t.addShift.tableData[e.$index],"endLatestWorkTime",a)},expression:"addShift.tableData[scope.$index].endLatestWorkTime\n                                                        "}})],1)])]}}],null,!1,1079639295)}),a("el-table-column",{attrs:{label:"休息时间",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("div",[t._v(" 休息时间： "),a("el-select",{staticClass:"width90",attrs:{placeholder:"请选择"},model:{value:e.row.restLength,callback:function(a){t.$set(e.row,"restLength",a)},expression:"scope.row.restLength"}},t._l(t.DateoptionsHour,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)])]}}],null,!1,1582909396)})],1)],1)])],1):t._e(),3==t.addShift.type?a("div",[a("el-form-item",{attrs:{label:"上下班时间"}},[a("div",{staticClass:"flexs"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addList()}}},[a("i",{staticClass:"el-icon-plus",staticStyle:{"margin-right":"8px"}}),t._v("添加班段 ")])],1),a("div",[a("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:t.addShift.tableData,border:""}},[a("el-table-column",{attrs:{label:"班段",width:"300",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("el-input",{staticClass:"width120",attrs:{maxlength:20},model:{value:e.row.name,callback:function(a){t.$set(e.row,"name",a)},expression:"scope.row.name"}})],1)]}}],null,!1,3404598186)}),a("el-table-column",{attrs:{label:"最早打卡时间",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("div",[t._v(" 最早打卡： "),"2"==t.addShift.tableData[e.$index].startEarliestDayType?a("span",[t._v("次日")]):a("span",[t._v("当天")]),a("el-time-picker",{staticClass:"width120",attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"上班时间"},on:{change:function(a){t.upClassTime(a,e.$index,e.row,"qdStart")}},model:{value:t.addShift.tableData[e.$index].startEarliestWorkTime,callback:function(a){t.$set(t.addShift.tableData[e.$index],"startEarliestWorkTime",a)},expression:"addShift.tableData[scope.$index].startEarliestWorkTime\n                                                        "}})],1)])]}}],null,!1,2633669614)}),a("el-table-column",{attrs:{label:"最晚打卡时间",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("div",[t._v(" 最晚打卡： "),"2"==t.addShift.tableData[e.$index].endLatestDayType?a("span",[t._v("次日")]):a("span",[t._v("当天")]),a("el-time-picker",{staticClass:"width120",attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"下班时间"},on:{change:function(a){t.upClassTime(a,e.$index,e.row,"qdEnd")}},model:{value:t.addShift.tableData[e.$index].endLatestWorkTime,callback:function(a){t.$set(t.addShift.tableData[e.$index],"endLatestWorkTime",a)},expression:"addShift.tableData[scope.$index].endLatestWorkTime"}})],1)])]}}],null,!1,2737838539)}),a("el-table-column",{attrs:{label:"操作",width:"150",align:"center","header-align":"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{on:{click:function(a){return t.deleteList(e.$index,e.row)}}},[a("span",[t._v("删除")])])]}}],null,!1,4232237793)})],1)],1)])],1):t._e(),a("el-form-item",[a("div",{staticClass:"btns"},[a("el-button",{on:{click:t.goList}},[t._v("返回")]),a("el-button",{staticStyle:{"margin-left":"25px"},attrs:{type:"primary"},on:{click:t.onSubmit}},[t._v("保存")])],1)])],1)],1)])])},s=[],r=a("2909"),o=(a("a434"),a("d3b7"),a("159b"),a("b64b"),a("e9c4"),a("14d9"),a("b0c0"),a("a9e3"),a("99af"),a("dc01")),n={name:"",props:{},data:function(){return{typeId:"",defaultClasses:"",addShift:{name:"",type:1,typeName:"",hour:"",days:"",overtime:"",tableData:[{name:"",startWorkTime:"",endWorkTime:"",endWorkDayType:"",startEarliestWorkTime:"",endEarliestWorkTime:"",endEarliestDayType:"",startLatestWorkTime:"",endLatestDayType:"",endLatestWorkTime:"",restStartTime:"",restEndTime:"",restLength:"",overtime:"",afterFollowingDay:!1,goToFollowingDay:!1,goToEarlyHour:0,goToEarlyMinutes:0,goToLateHour:0,goToLateMinutes:0,afterEarlyHours:0,afterEarlyMinutes:0,afterLateHours:0,afterLateMinutes:0}],checkbox1:"0",checkbox2:"0",checkbox1Up:"",checkbox1Down:"",radio1:"0",checkbox2Up:"",checkbox2Down:""},copyList:{name:"",startWorkTime:"",endWorkTime:"",endWorkDayType:"",startEarliestWorkTime:"",endEarliestWorkTime:"",startLatestWorkTime:"",startEarliestDayType:"1",endEarliestDayType:"",endLatestDayType:"",endLatestWorkTime:"",restStartTime:"",restEndTime:"",restLength:"",goToEarlyHour:0,goToEarlyMinutes:0,goToLateHour:0,goToLateMinutes:0,afterEarlyHours:0,afterEarlyMinutes:0,afterLateHours:0,afterLateMinutes:0},options:[{value:"1",label:"当天"},{value:"2",label:"次日"}],Dateoptions:[{value:15,label:"15分钟"},{value:30,label:"30分钟"},{value:45,label:"45分钟"},{value:60,label:"60分钟"}],DateoptionsHour:[{value:0,label:"不休息"},{value:1,label:"1小时"},{value:2,label:"2小时"},{value:3,label:"3小时"},{value:4,label:"4小时"}],houroptions:[{value:8,label:"8小时"},{value:12,label:"12小时"}],typeoptions:[{value:"1",label:"系统消息"},{value:"2",label:"短信"}],submmitSwitch:{hourAdd:0,nameJY:1,startWorkTimeJY:1,endWorkTimeJY:1,endWorkDayTypeJY:1,endEarliestWorkTimeJY:1,startLatestWorkTimeJY:1,endEarliestDayTypeJY:1,endLatestDayTypeJY:1,endLatestWorkTimeJY:1,restStartTimeJY:1,restEndTimeJY:1,restLengthJY:1,startEarliestMinuteJY:1,goToLateHourJY:1}}},created:function(){},mounted:function(){this.$route.query.id&&this.bcDetails();this.getNowDate()},watch:{},filters:{},components:{},computed:{dictList:function(){return this.$store.state.dict}},methods:{deleteList:function(t,e){var a=this;console.log(111),console.log(t,e),this.addShift.tableData.splice(t,1),this.addShift.tableData.forEach((function(t,e){a.addShift.hour+=a.getDateDiff(new Date(a.getNowDate()+" "+t.startWorkTime),new Date(a.getNowDate()+" "+t.endWorkTime),"hour")-a.getDateDiff(new Date(a.getNowDate()+" "+t.restStartTime),new Date(a.getNowDate()+" "+t.restEndTime),"hour")}))},changeRadio:function(t){console.log(t,"点击切换班次类型");var e=JSON.parse(JSON.stringify(this.copyList));this.defaultClasses&&t==this.defaultClasses?this.bcDetails():(this.addShift.hour="",this.addShift.checkbox1="",this.addShift.checkbox1Up="",this.addShift.checkbox1Down="",this.addShift.overtime="",this.addShift.tableData=[e])},addList:function(){var t=JSON.parse(JSON.stringify(this.copyList));this.addShift.tableData.length>0&&(t.goToFollowingDay=this.addShift.tableData[this.addShift.tableData.length-1].afterFollowingDay,t.afterFollowingDay=t.goToFollowingDay,t.startWorkDayType=this.addShift.tableData[this.addShift.tableData.length-1].endWorkDayType,t.endWorkDayType=t.startWorkDayType),this.addShift.tableData.push(t)},bcDetails:function(){var t=this;this.loading=!0;var e=this.$route.query.id,a={id:e};Object(o["f"])(a).then((function(e){var a=e.data;t.defaultClasses=e.data.arrangementTypeName,t.addShift.name=a.name,a.arrangementTypeId>3?(t.addShift.type=0,t.typeId=a.arrangementTypeId,t.defaultClasses=0):(t.addShift.type=a.arrangementTypeId,t.typeId=a.arrangementTypeId,t.defaultClasses=a.arrangementTypeId),t.addShift.typeName=a.arrangementTypeId>3?a.arrangementTypeName:"",t.addShift.hour=a.workTimeAmount,t.addShift.days=a.ycqts,t.addShift.overtime=a.overtime,t.addShift.tableData=[],a.arrangementTimeInfoList.forEach((function(e,a){var i=Math.floor(e.startEarliestMinute/60),s=e.startEarliestMinute%60,r=Math.floor(e.endEarliestMinute/60),o=e.endEarliestMinute%60,n=Math.floor(e.startLatestMinute/60),l=e.startLatestMinute%60,d=Math.floor(e.endLatestMinute/60),h=e.endLatestMinute%60,u={};u.id=e.id,u.name=e.name,u.startWorkTime=e.startWorkTime,u.endWorkTime=e.endWorkTime,u.startEarliestWorkTime=e.startEarliestWorkTime,u.endEarliestWorkTime=e.endEarliestWorkTime,u.startLatestWorkTime=e.startLatestWorkTime,u.startWorkDayType=e.startWorkDayType,u.endWorkDayType=e.endWorkDayType,u.endEarliestDayType=e.endEarliestDayType,u.endLatestDayType=e.endLatestDayType,u.endLatestWorkTime=e.endLatestWorkTime,u.restStartTime=e.restStartTime,u.restEndTime=e.restEndTime,u.restLength=String(e.restLength),"1"==e.startWorkDayType?u.goToFollowingDay=!1:u.goToFollowingDay=!0,"1"==e.endWorkDayType?u.afterFollowingDay=!1:u.afterFollowingDay=!0,u.goToEarlyHour=i,u.goToEarlyMinutes=s,u.goToLateHour=n,u.goToLateMinutes=l,u.afterEarlyHours=r,u.afterEarlyMinutes=o,u.afterLateHours=d,u.afterLateMinutes=h,2!=t.addShift.type&&3!=t.addShift.type||e.startEarliestDayType?u.startEarliestDayType=e.startEarliestDayType:u.startEarliestDayType="1",t.addShift.tableData.push(u),console.log(t.addShift.tableData,"最终反显数据")})),t.addShift.checkbox1=a.optionOne,t.addShift.checkbox2=a.optionTwo,t.addShift.checkbox1Up=a.oneBelateLength,t.addShift.checkbox1Down=a.oneLeaveEarlyLength,t.addShift.radio1=a.twoType,t.addShift.checkbox2Up=a.twoStayLateLength,t.addShift.checkbox2Down=a.twoArriveLateLength,console.log(t.addShift.tableData,"this.tableData"),console.log(t.addShift.type,"this.addShift.type22")}))},computeTime:function(t,e,a){if(this.initNumber(e),"goToEarlyMinutes"==a)if(t.startWorkTime)if(0==e){var i=480,s=60*Number(t.goToEarlyHour)+Number(t.goToEarlyMinutes),r=this.timeToMinutes(t.startWorkTime);if(i<Number(r)){if(Number(s)>i){var o=i/60,n=i%60;this.$message({showClose:!0,message:"最早不可超过".concat(o,"时").concat(n,"分"),type:"error"}),this.addShift.tableData[e].goToEarlyHour="0",this.addShift.tableData[e].goToEarlyMinutes="0"}}else if(Number(s)>Number(r)){Number(r),Number(r);this.$message({showClose:!0,message:"最早不可超过".concat(t.startWorkTime.split(":")[0],"时").concat(t.startWorkTime.split(":")[1],"分"),type:"error"}),this.addShift.tableData[e].goToEarlyHour="0",this.addShift.tableData[e].goToEarlyMinutes="0"}}else if(this.addShift.tableData[e-1].afterFollowingDay){var l=this.timeToMinutes(this.addShift.tableData[e-1].endWorkTime),d=this.timeToMinutes(t.startWorkTime),h=Number(60*t.goToEarlyHour)+Number(t.goToEarlyMinutes),u=d-l;if(u<h){this.addShift.tableData[e].goToEarlyHour="0",this.addShift.tableData[e].goToEarlyMinutes="0";var f=Math.floor(u/60),c=u%60;this.$message({showClose:!0,message:"最早不可超过".concat(f,"时").concat(c,"分"),type:"error"})}}else if(!this.addShift.tableData[e-1].afterFollowingDay&&this.addShift.tableData[e].goToFollowingDay){var m=Number(60*t.goToEarlyHour)+Number(t.goToEarlyMinutes),b=1439,p=this.timeToMinutes(this.addShift.tableData[e-1].endWorkTime),g=b-p,S=this.timeToMinutes(t.startWorkTime),T=g+S,y=Math.floor(T/60),D=T%60;m>T&&(this.$message({showClose:!0,message:"最早不可超过".concat(y,"时").concat(D,"分"),type:"error"}),this.addShift.tableData[e].goToEarlyHour="0",this.addShift.tableData[e].goToEarlyMinutes="0")}else{var k=this.timeToMinutes(this.addShift.tableData[e-1].endWorkTime),v=this.timeToMinutes(t.startWorkTime),w=Number(60*t.goToEarlyHour)+Number(t.goToEarlyMinutes),x=v-k;if(x<w){this.addShift.tableData[e].goToEarlyHour="0",this.addShift.tableData[e].goToEarlyMinutes="0";var L=Math.floor(x/60),M=x%60;this.$message({showClose:!0,message:"最早不可超过".concat(L,"时").concat(M,"分"),type:"error"})}}else this.$message({showClose:!0,message:"请输入上班时间",type:"error"}),this.addShift.tableData[e].goToEarlyHour="0",this.addShift.tableData[e].goToEarlyMinutes="0";else if("goToLateMinutes"==a)if(t.startWorkTime&&t.endWorkTime)if(this.addShift.tableData[e].goToFollowingDay||!this.addShift.tableData[e].goToFollowingDay&&!this.addShift.tableData[e].afterFollowingDay){var E=this.timeToMinutes(t.startWorkTime),W=this.timeToMinutes(t.endWorkTime),$=60*Number(t.goToLateHour)+Number(t.goToLateMinutes),H=W-E;if($<0||$>H){var _=Math.floor(H/60),N=H%60;this.$message({showClose:!0,message:"延迟不可超过".concat(_,"时").concat(N,"分"),type:"error"}),this.addShift.tableData[e].goToLateHour="0",this.addShift.tableData[e].goToLateMinutes="0"}}else{var C=this.timeToMinutes(t.startWorkTime),J=this.timeToMinutes(t.endWorkTime),j=60*Number(t.goToLateHour)+Number(t.goToLateMinutes),O=1440-C+J;if(j<0||j>O){var Y=Math.floor(O/60),F=O%60;this.$message({showClose:!0,message:"延迟不可超过".concat(Y,"时").concat(F,"分"),type:"error"}),this.addShift.tableData[e].goToLateHour="0",this.addShift.tableData[e].goToLateMinutes="0"}}else this.$message({showClose:!0,message:"请先输入上班与下班时间",type:"error"}),this.addShift.tableData[e].goToLateHour="0",this.addShift.tableData[e].goToLateMinutes="0";else if("afterEarlyMinutes"==a)if(t.startWorkTime&&t.endWorkTime){var I=this.timeToMinutes(t.startWorkTime);this.addShift.tableData[e].goToFollowingDay&&(I=1440+I);var A=this.timeToMinutes(t.endWorkTime);this.addShift.tableData[e].afterFollowingDay&&(A=1440+A);var U=60*Number(t.afterEarlyHours)+Number(t.afterEarlyMinutes),q=A-I;if(U<0||U>q){var B=Math.floor(q/60),R=q%60;this.$message({showClose:!0,message:"延迟不可超过".concat(B,"时").concat(R,"分"),type:"error"}),this.addShift.tableData[e].afterEarlyHours="0",this.addShift.tableData[e].afterEarlyMinutes="0"}}else this.$message({showClose:!0,message:"请先输入上班与下班时间",type:"error"}),this.addShift.tableData[e].afterEarlyHours="0",this.addShift.tableData[e].afterEarlyMinutes="0";else if("afterLateMinutes"==a)if(t.endWorkTime)if(0!=e||1!=this.addShift.tableData.length||this.addShift.tableData[e].afterFollowingDay){if(0==e&&this.addShift.tableData.length>0||0!=e&&this.addShift.tableData.length>0&&e!=this.addShift.tableData.length-1)if(this.addShift.tableData[e].afterFollowingDay){var z=this.timeToMinutes(this.addShift.tableData[e+1].startWorkTime),X=this.timeToMinutes(this.addShift.tableData[e].endWorkTime),G=z-X,K=Number(60*t.afterLateHours)+Number(t.afterLateMinutes);if(K>G){var Q=Math.floor(G/60),P=G%60;this.$message({showClose:!0,message:"延迟不可超过".concat(Q,"时").concat(P,"分"),type:"error"}),this.addShift.tableData[e].afterLateHours="0",this.addShift.tableData[e].afterLateMinutes="0"}}else if(!this.addShift.tableData[e].afterFollowingDay&&this.addShift.tableData[e+1].goToFollowingDay){var V=Number(60*t.afterLateHours)+Number(t.afterLateMinutes),Z=this.timeToMinutes(this.addShift.tableData[e].endWorkTime),tt=this.timeToMinutes(this.addShift.tableData[e+1].startWorkTime),et=1440-Z+tt;if(V>et){var at=Math.floor(et/60),it=et%60;this.$message({showClose:!0,message:"延迟不可超过".concat(at,"时").concat(it,"分"),type:"error"}),this.addShift.tableData[e].afterLateHours="0",this.addShift.tableData[e].afterLateMinutes="0"}}else{var st=this.timeToMinutes(this.addShift.tableData[e+1].startWorkTime),rt=this.timeToMinutes(this.addShift.tableData[e].endWorkTime),ot=st-rt,nt=Number(60*t.afterLateHours)+Number(t.afterLateMinutes);if(nt>ot){var lt=Math.floor(ot/60),dt=ot%60;this.$message({showClose:!0,message:"延迟不可超过".concat(lt,"时").concat(dt,"分"),type:"error"}),this.addShift.tableData[e].afterLateHours="0",this.addShift.tableData[e].afterLateMinutes="0"}}else if(0!=e&&e==this.addShift.tableData.length-1)if(this.addShift.tableData[e].afterFollowingDay){var ht=this.timeToMinutes(this.addShift.tableData[0].startWorkTime),ut=this.timeToMinutes(this.addShift.tableData[e].endWorkTime),ft=ht-ut,ct=Number(60*t.afterLateHours)+Number(t.afterLateMinutes);if(ct>ft){var mt=Math.floor(ft/60),bt=ft%60;this.$message({showClose:!0,message:"延迟不可超过".concat(mt,"时").concat(bt,"分"),type:"error"}),this.addShift.tableData[e].afterLateHours="0",this.addShift.tableData[e].afterLateMinutes="0"}}else if(!this.addShift.tableData[e].afterFollowingDay){var pt=Number(60*t.afterLateHours)+Number(t.afterLateMinutes),gt=this.timeToMinutes(t.endWorkTime),St=1439-gt;if(pt>St){var Tt=Math.floor(St/60),yt=St%60;this.$message({showClose:!0,message:"延迟不可超过".concat(Tt,"时").concat(yt,"分"),type:"error"}),this.addShift.tableData[e].afterLateHours="0",this.addShift.tableData[e].afterLateMinutes="0"}}}else{var Dt=this.timeToMinutes(t.endWorkTime),kt=1439-Dt,vt=Number(60*t.afterLateHours)+Number(t.afterLateMinutes);if(vt>kt){var wt=1439-Dt,xt=Math.floor(wt/60),Lt=wt%60;this.$message({showClose:!0,message:"延迟不可超过".concat(xt,"时").concat(Lt,"分"),type:"error"}),this.addShift.tableData[e].afterLateHours="0",this.addShift.tableData[e].afterLateMinutes="0"}}else this.$message({showClose:!0,message:"请先输入下班时间",type:"error"}),this.addShift.tableData[e].afterLateHours="0",this.addShift.tableData[e].afterLateMinutes="0"},addRoles:function(t){var e=this;this.loading=!0,console.log(t,"params"),Object(o["a"])(t).then((function(t){e.$message({message:"新增成功",type:"success"}),setTimeout((function(){e.loading=!1}),1e3),e.goList()}))},bcUpdates:function(t){var e=this;this.loading=!0,console.log(t,"params"),Object(o["g"])(t).then((function(t){e.$message({message:"修改成功",type:"success"}),setTimeout((function(){e.loading=!1}),1e3),e.goList()}))},getDict:function(){this.$store.dispatch("dict/setDict",{})},timeToMinutes:function(t){return 60*Number(t.split(":")[0])+Number(t.split(":")[1])},upClassTime:function(t,e,a,i){var s=this;if("xb"==i){t.split(":");var r=this.timeToMinutes(this.addShift.tableData[e].startWorkTime),o=this.timeToMinutes(t);Number(o)<Number(r)||this.addShift.tableData[e].goToFollowingDay?(this.addShift.tableData[e].afterFollowingDay=!0,Number(o)<Number(r)&&this.addShift.tableData[e].goToFollowingDay&&(this.addShift.tableData[e].endWorkTime="",this.$message.error("不可以比上一个次日更早"))):this.addShift.tableData[e].afterFollowingDay=!1;var n,l=this.timeToMinutes(this.addShift.tableData[e].endWorkTime);this.addShift.tableData[e].afterFollowingDay&&(l+=1440),e!==this.addShift.tableData.length-1?(n=this.timeToMinutes(this.addShift.tableData[e+1].startWorkTime),this.addShift.tableData[e+1].goToFollowingDay&&(n+=1440),l>n&&(this.addShift.tableData[e].endWorkTime="",this.$message.error("不可以比下个班段的上班时间晚"))):(n=this.timeToMinutes(this.addShift.tableData[0].startWorkTime),this.addShift.tableData[e].afterFollowingDay&&(l-=1440,l>n&&(this.addShift.tableData[e].endWorkTime="",this.$message.error("不可以迟于第一个班段的上班时间"))))}else if("sb"==i){if(0==e){if(this.addShift.tableData[e].goToFollowingDay){if(this.addShift.tableData[e].afterFollowingDay){var d=this.timeToMinutes(this.addShift.tableData[e].startWorkTime),h=this.timeToMinutes(this.addShift.tableData[e].endWorkTime);h<d&&this.$message({showClose:!0,message:"不可以比上个班段的下班时间早",type:"error"})}}else if(!this.addShift.tableData[e].afterFollowingDay){var u=this.timeToMinutes(this.addShift.tableData[e].startWorkTime),f=this.timeToMinutes(this.addShift.tableData[e].endWorkTime);f<u&&(this.addShift.tableData[e].startWorkTime="",this.$message({showClose:!0,message:"不可以比下班时间早",type:"error"}))}}else{var c=this.timeToMinutes(this.addShift.tableData[e-1].endWorkTime),m=this.timeToMinutes(this.addShift.tableData[e].endWorkTime);this.addShift.tableData[e-1].goToFollowingDay?(c<m||this.$message.error("不可以比上个班段下班时间早"),this.addShift.tableData[e].goToFollowingDay=!1):this.addShift.tableData[e].goToFollowingDay=!(c<m)}if(0!==e){t.split(":"),this.addShift.tableData[e-1].endWorkTime.split(":");var b=this.timeToMinutes(t),p=this.timeToMinutes(this.addShift.tableData[e-1].endWorkTime);this.addShift.tableData[e-1].afterFollowingDay||Number(b)<Number(p)?(this.addShift.tableData[e].goToFollowingDay=!0,this.addShift.tableData[e].afterFollowingDay=!0,this.addShift.tableData[e-1].afterFollowingDay&&Number(b)<Number(p)&&(this.addShift.tableData[e].startWorkTime="",this.$message.error("不可以比上一个次日更早"))):(this.addShift.tableData[e].goToFollowingDay=!1,this.addShift.tableData[e].afterFollowingDay=!1)}var g=this.timeToMinutes(this.addShift.tableData[e].endWorkTime);this.addShift.tableData[e].afterFollowingDay&&(g+=1440);this.addShift.tableData[e].startWorkTime.split(":");var S=this.timeToMinutes(this.addShift.tableData[e].startWorkTime);this.addShift.tableData[e].goToFollowingDay&&(S+=1440),S>g&&(this.addShift.tableData[e].startWorkTime="",this.$message.error("不可以比上个班段的下班时间早"))}else if("txSb"==i)this.addShift.tableData[e].endLatestWorkTime="";else if("txXb"==i){var T=this.timeToMinutes(this.addShift.tableData[e].startEarliestWorkTime),y=this.timeToMinutes(this.addShift.tableData[e].endLatestWorkTime);this.addShift.tableData[e].endLatestDayType=y<T?"2":"1"}else if("qdStart"==i){if(0!==e){var D=this.timeToMinutes(t),k=this.timeToMinutes(this.addShift.tableData[e-1].endLatestWorkTime);console.log(this.addShift.tableData[e-1].endLatestDayType),"2"==this.addShift.tableData[e-1].endLatestDayType&&(this.addShift.tableData[e].startEarliestDayType="2",this.addShift.tableData[e].endLatestDayType="2",Number(D)<Number(k)&&(this.addShift.tableData[e].startEarliestDayType="2",this.addShift.tableData[e].startEarliestWorkTime="",this.$message.error("不可以比上一个次日更早"))),"1"==this.addShift.tableData[e-1].endLatestDayType&&(Number(D)>Number(k)?this.addShift.tableData[e].startEarliestDayType="1":this.addShift.tableData[e].startEarliestDayType="2"),console.log(this.addShift.tableData[e].startEarliestDayType,"次日")}else this.addShift.tableData[e].startEarliestDayType="1";var v=this.timeToMinutes(this.addShift.tableData[e].startEarliestWorkTime);if("2"==this.addShift.tableData[e].startEarliestDayType&&(v+=1440),this.addShift.tableData[e].endLatestWorkTime){var w=this.timeToMinutes(this.addShift.tableData[e].endLatestWorkTime);"2"==this.addShift.tableData[e].endWorkDayType&&(w+=1440),v>w&&(this.addShift.tableData[e].startEarliestWorkTime="",this.$message.error("上班时间不可晚于下班时间"))}}else if("qdEnd"==i){var x=this.timeToMinutes(this.addShift.tableData[e].endLatestWorkTime),L=this.timeToMinutes(this.addShift.tableData[e].startEarliestWorkTime);"1"==this.addShift.tableData[e].startEarliestDayType&&x<L?this.addShift.tableData[e].endLatestDayType="2":"2"==this.addShift.tableData[e].startEarliestDayType&&x<L?(this.$message.error("不可以比上一个次日更早"),this.addShift.tableData[e].endLatestDayType="2",this.addShift.tableData[e].endLatestWorkTime=""):"1"==this.addShift.tableData[e].startEarliestDayType&&x>L?this.addShift.tableData[e].endLatestDayType="1":this.addShift.tableData[e].endLatestDayType="2";var M,E=this.timeToMinutes(this.addShift.tableData[e].endLatestWorkTime);"2"==this.addShift.tableData[e].endLatestDayType&&(E+=1440),e==this.addShift.tableData.length-1?(M=this.timeToMinutes(this.addShift.tableData[0].startEarliestWorkTime),"2"==this.addShift.tableData[e].endLatestDayType&&(E-=1440),"2"==this.addShift.tableData[e].endLatestDayType&&E>M&&(this.addShift.tableData[e].endLatestWorkTime="",this.$message.error("不可以比此班次最早打卡时间晚"))):(M=this.timeToMinutes(this.addShift.tableData[e+1].startEarliestWorkTime),"2"==this.addShift.tableData[e+1].startEarliestDayType&&(M+=1440),E>M&&(this.addShift.tableData[e].endLatestWorkTime="",this.$message.error("不可以比下个班段上班时间晚")))}this.hourAdd=0,this.addShift.tableData.forEach((function(t,e){s.hourAdd+=s.getDateDiff(new Date(s.getNowDate()+" "+t.startWorkTime),new Date(s.getNowDate()+" "+t.endWorkTime),"hour")-s.getDateDiff(new Date(s.getNowDate()+" "+t.restStartTime),new Date(s.getNowDate()+" "+t.restEndTime),"hour")}))},initNumber:function(t){this.addShift.tableData[t].goToEarlyMinutes||(this.addShift.tableData[t].goToEarlyMinutes=0),this.addShift.tableData[t].goToLateMinutes||(this.addShift.tableData[t].goToLateMinutes=0),this.addShift.tableData[t].afterEarlyMinutes||(this.addShift.tableData[t].afterEarlyMinutes=0),this.addShift.tableData[t].afterLateMinutes||(this.addShift.tableData[t].afterLateMinutes=0),this.addShift.tableData[t].goToEarlyHour||(this.addShift.tableData[t].goToEarlyHour=0),this.addShift.tableData[t].goToLateHour||(this.addShift.tableData[t].goToLateHour=0),this.addShift.tableData[t].afterEarlyHours||(this.addShift.tableData[t].afterEarlyHours=0),this.addShift.tableData[t].afterLateHours||(this.addShift.tableData[t].afterLateHours=0)},onSubmit:function(){var t=this;if(console.log(this.addShift.type,"type"),this.submmitSwitch={hourAdd:0,nameJY:1,startWorkTimeJY:1,endWorkTimeJY:1,endWorkDayTypeJY:1,endEarliestWorkTimeJY:1,startLatestWorkTimeJY:1,endEarliestDayTypeJY:1,endLatestDayTypeJY:1,endLatestWorkTimeJY:1,restStartTimeJY:1,restEndTimeJY:1,restLengthJY:1,startEarliestMinuteJY:1,goToLateHourJY:1,startEarliestWorkTimeJY:1},this.addShift.name)if(this.addShift.tableData.forEach((function(e){e.name||(t.submmitSwitch.nameJY=2),e.startWorkTime||(t.submmitSwitch.startWorkTimeJY=2),e.endWorkTime||(t.submmitSwitch.endWorkTimeJY=2),t.submmitSwitch.endWorkDayTypeJY=1,e.startEarliestWorkTime||2!=t.addShift.type&&3!=t.addShift.type||(t.submmitSwitch.startEarliestWorkTimeJY=2),e.endEarliestWorkTime||(t.submmitSwitch.endEarliestWorkTimeJY=2),e.startLatestWorkTime||(t.submmitSwitch.startLatestWorkTimeJY=2),t.submmitSwitch.endEarliestDayTypeJY=1,e.endLatestDayType||2!=t.addShift.type&&3!=t.addShift.type||(t.submmitSwitch.endLatestDayTypeJY=2),e.endLatestWorkTime||2!=t.addShift.type&&3!=t.addShift.type||(t.submmitSwitch.endLatestWorkTimeJY=2),e.restStartTime||(t.submmitSwitch.restStartTimeJY=2),e.restEndTime||(t.submmitSwitch.restEndTimeJY=2),t.submmitSwitch.restLengthJY=2,(0==e.goToEarlyHour&&0==e.goToEarlyMinutes||0==e.afterEarlyHours&&0==e.afterEarlyMinutes)&&(t.submmitSwitch.startEarliestMinuteJY=2),(0==e.goToLateHour&&0==e.goToLateMinutes||0==e.afterLateHours&&0==e.afterLateMinutes)&&(t.submmitSwitch.goToLateHourJY=2)})),console.log(this.submmitSwitch),2!=this.submmitSwitch.nameJY){if(2==this.submmitSwitch.startEarliestWorkTimeJY)return console.log("弹性、签到 最早打卡时间为空"),void this.$message({message:"请选择最早打卡时间"});if(2!=this.submmitSwitch.endLatestWorkTimeJY){if(0==this.addShift.type||1==this.addShift.type){if(2==this.submmitSwitch.startWorkTimeJY)return void this.$message({message:"请选择上班时间"});if(2==this.submmitSwitch.endWorkTimeJY)return void this.$message({message:"请选择下班时间"});if(2==this.submmitSwitch.startEarliestMinuteJY)return void this.$message({message:"请选择最早打卡时间"});if(2==this.submmitSwitch.goToLateHourJY)return void this.$message({message:"请选择最晚打卡时间"})}this.addShift.tableData.forEach((function(e,a){e.startEarliestMinute=60*Number(e.goToEarlyHour)+Number(e.goToEarlyMinutes),e.startLatestMinute=60*Number(e.goToLateHour)+Number(e.goToLateMinutes),1!=t.addShift.type&&0!=t.addShift.type||(e.endWorkDayType=e.afterFollowingDay?"2":"1",e.startWorkDayType=e.goToFollowingDay?"2":"1"),e.endEarliestMinute=60*Number(e.afterEarlyHours)+Number(e.afterEarlyMinutes),e.endLatestMinute=60*Number(e.afterLateHours)+Number(e.afterLateMinutes)}));var e={name:this.addShift.name,arrangementTypeId:this.addShift.type,customTypeName:this.addShift.typeName,workTimeAmount:this.addShift.hour,ycqts:this.addShift.days,arrangementTimeInfoList:Object(r["a"])(this.addShift.tableData),optionOne:this.addShift.checkbox1,oneBelateLength:this.addShift.checkbox1Up,oneLeaveEarlyLength:this.addShift.checkbox1Down,optionTwo:this.addShift.checkbox2,twoType:this.addShift.radio1,twoStayLateLength:this.addShift.checkbox2Up,twoArriveLateLength:this.addShift.checkbox2Down,overtime:this.addShift.overtime};console.log(this.$route.query);var a=this.$route.query.id;a?(e.id=a,console.log("修改"),this.bcUpdates(e)):(console.log("新增"),this.addRoles(e))}else this.$message({message:"请选择最晚打卡时间"})}else this.$message({message:"请输入班段名称"});else this.$message({message:"请输入班次名称"})},goList:function(){this.$router.push({name:"shiftmana",query:{}})}}},l=n,d=(a("d9ac"),a("2877")),h=Object(d["a"])(l,i,s,!1,null,"0c6d9d48",null);e["default"]=h.exports},d9ac:function(t,e,a){"use strict";a("553e")},dc01:function(t,e,a){"use strict";a.d(e,"y",(function(){return s})),a.d(e,"a",(function(){return r})),a.d(e,"C",(function(){return o})),a.d(e,"B",(function(){return n})),a.d(e,"c",(function(){return l})),a.d(e,"m",(function(){return d})),a.d(e,"s",(function(){return h})),a.d(e,"z",(function(){return u})),a.d(e,"t",(function(){return f})),a.d(e,"A",(function(){return c})),a.d(e,"J",(function(){return m})),a.d(e,"K",(function(){return b})),a.d(e,"G",(function(){return p})),a.d(e,"M",(function(){return g})),a.d(e,"E",(function(){return S})),a.d(e,"w",(function(){return T})),a.d(e,"h",(function(){return y})),a.d(e,"f",(function(){return D})),a.d(e,"e",(function(){return k})),a.d(e,"q",(function(){return v})),a.d(e,"b",(function(){return w})),a.d(e,"r",(function(){return x})),a.d(e,"F",(function(){return L})),a.d(e,"k",(function(){return M})),a.d(e,"H",(function(){return E})),a.d(e,"l",(function(){return W})),a.d(e,"g",(function(){return $})),a.d(e,"D",(function(){return H})),a.d(e,"o",(function(){return _})),a.d(e,"I",(function(){return N})),a.d(e,"i",(function(){return C})),a.d(e,"j",(function(){return J})),a.d(e,"n",(function(){return j})),a.d(e,"x",(function(){return O})),a.d(e,"d",(function(){return Y})),a.d(e,"u",(function(){return F})),a.d(e,"v",(function(){return I})),a.d(e,"p",(function(){return A})),a.d(e,"L",(function(){return U}));var i=a("b775");function s(t){return Object(i["a"])({url:"/schedule/arrangement/pageList",method:"get",params:t})}function r(t){return Object(i["a"])({url:"/schedule/arrangement/save",method:"post",data:t})}function o(t){return Object(i["a"])({url:"/schedule/work-adjustment/pageList",method:"get",params:t})}function n(t){return Object(i["a"])({url:"/schedule/schedule/mySchedule",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/schedule/work-adjustment/save",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/schedule/repair-attend-apply/save",method:"post",data:t})}function h(t){return Object(i["a"])({url:"/schedule/service-group/findList",method:"get",params:t})}function u(t){return Object(i["a"])({url:"/schedule/service-group/getSelectList",method:"get",params:t})}function f(t){return Object(i["a"])({url:"/schedule/member/findList",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/schedule/schedule/getShowData",method:"get",params:t})}function m(t){return Object(i["a"])({url:"/schedule/work-adjustment/detail",method:"get",params:t})}function b(t){return Object(i["a"])({url:"/schedule/work-adjustment/getActInfo",method:"get",params:t})}function p(t){return Object(i["a"])({url:"/schedule/work-adjustment/submitAct",method:"post",params:t})}function g(t){return Object(i["a"])({url:"/schedule/work-adjustment/withdrawAct",method:"get",params:t})}function S(t){return Object(i["a"])({url:"/schedule/schedule/pageList",method:"get",params:t})}function T(t){return Object(i["a"])({url:"/schedule/arrangement/getArrangementTypeList",method:"get",params:t})}function y(t){return Object(i["a"])({url:"/schedule/arrangement/findList",method:"get",params:t})}function D(t){return Object(i["a"])({url:"/schedule/arrangement/detail",method:"get",params:t})}function k(t){return Object(i["a"])({url:"/schedule/schedule/autoSetSchedule",method:"post",data:t})}function v(t){return Object(i["a"])({url:"/schedule/arrangement/deleteByIds",method:"post",params:t})}function w(t){return Object(i["a"])({url:"/schedule/schedule/save",method:"post",data:t})}function x(t){return Object(i["a"])({url:"/schedule/schedule/exportExcel",method:"get",params:t,responseType:"blob"})}function L(t){return Object(i["a"])({url:"/schedule/member-work/saveEntitys",method:"post",data:t})}function M(t){return Object(i["a"])({url:"/schedule/repair-attend-apply/pageList",method:"get",params:t})}function E(t){return Object(i["a"])({url:"/schedule/work-adjustment/update",method:"post",data:t})}function W(t){return Object(i["a"])({url:"/schedule/repair-attend-apply/update",method:"post",data:t})}function $(t){return Object(i["a"])({url:"/schedule/arrangement/update",method:"post",data:t})}function H(t){return Object(i["a"])({url:"/schedule/schedule/deleteByIds",method:"post",params:t})}function _(t){return Object(i["a"])({url:"/schedule/repair-attend-apply/submitAct",method:"post",params:t})}function N(t){return Object(i["a"])({url:"/schedule/work-adjustment/deleteByIds",method:"post",params:t})}function C(t){return Object(i["a"])({url:"/schedule/repair-attend-apply/deleteByIds",method:"post",params:t})}function J(t){return Object(i["a"])({url:"/schedule/repair-attend-apply/detail",method:"get",params:t})}function j(t){return Object(i["a"])({url:"/schedule/repair-attend-apply/getActInfo",method:"get",params:t})}function O(t){return Object(i["a"])({url:"/schedule/sysQuery/getLoginMemberInfo",method:"get",params:t})}function Y(t){return Object(i["a"])({url:"/schedule/work-adjustment/approvalOperation",method:"post",params:t})}function F(t){return Object(i["a"])({url:"/schedule/schedule/getAllShowData",method:"get",params:t})}function I(t){return Object(i["a"])({url:"/schedule/service-group/findList",method:"get"})}function A(t){return Object(i["a"])({url:"/schedule/arrangement/updateStatus",method:"post",params:t})}function U(t){return Object(i["a"])({url:"/schedule/service-group/updateRemind",method:"post",data:t})}}}]);