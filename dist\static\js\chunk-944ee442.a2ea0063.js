(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-944ee442"],{"57b6":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("消防指挥")])]),a("div",{staticClass:"topBottom"},e._l(e.btnArr,(function(t,r){return a("el-card",{key:r,staticClass:"box-card titleCard",attrs:{shadow:"hover"},nativeOn:{click:function(a){return e.cardClick(t.code)}}},[a("p",{staticStyle:{"font-weight":"600"}},[e._v(e._s(t.title))]),a("div",[e._v(e._s(t.content))])])})),1)]),a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("消防指挥操作记录")])]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{"cell-style":{padding:"0px"},"row-style":{height:"48px"},data:e.tableData}},[a("el-table-column",{attrs:{type:"index",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),a("el-table-column",{attrs:{label:"执行操作",align:"center",prop:"executeOperation"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(" "+e._s(e.dict.type.firecontrol_execute_operation.find((function(e){return e.value==t.row.executeOperation})).label||"")+" ")])]}}])}),a("el-table-column",{attrs:{label:"执行人",align:"center",prop:"executePerson"}}),a("el-table-column",{attrs:{label:"执行时间",align:"center",prop:"executeTime"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}})],1),a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"720px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"活动名称",prop:"name"}},[a("el-input",{model:{value:e.ruleForm.name,callback:function(t){e.$set(e.ruleForm,"name",t)},expression:"ruleForm.name"}})],1),a("el-form-item",{attrs:{label:"活动区域",prop:"region"}},[a("el-select",{attrs:{placeholder:"请选择活动区域"},model:{value:e.ruleForm.region,callback:function(t){e.$set(e.ruleForm,"region",t)},expression:"ruleForm.region"}},[a("el-option",{attrs:{label:"区域一",value:"shanghai"}}),a("el-option",{attrs:{label:"区域二",value:"beijing"}})],1)],1),a("el-form-item",{attrs:{label:"活动时间",required:""}},[a("el-col",{attrs:{span:11}},[a("el-form-item",{attrs:{prop:"date1"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期"},model:{value:e.ruleForm.date1,callback:function(t){e.$set(e.ruleForm,"date1",t)},expression:"ruleForm.date1"}})],1)],1),a("el-col",{staticClass:"line",attrs:{span:2}},[e._v("-")]),a("el-col",{attrs:{span:11}},[a("el-form-item",{attrs:{prop:"date2"}},[a("el-time-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"选择时间"},model:{value:e.ruleForm.date2,callback:function(t){e.$set(e.ruleForm,"date2",t)},expression:"ruleForm.date2"}})],1)],1)],1),a("el-form-item",{attrs:{label:"活动形式",prop:"desc"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.ruleForm.desc,callback:function(t){e.$set(e.ruleForm,"desc",t)},expression:"ruleForm.desc"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)],1)],1)},l=[],o=(a("7db0"),a("d3b7"),a("b775"));function n(e){return Object(o["a"])({url:"/firecontrol-fire-command/page",method:"get",params:e})}function i(e){return Object(o["a"])({url:"/firecontrol-fire-command/save",method:"post",data:e})}var s={name:"policyConfiguration",dicts:["firecontrol_execute_operation"],data:function(){return{loading:!1,showSearch:!0,total:0,tableData:null,open:!1,status:!0,queryParams:{current:1,size:10},btnArr:[{title:"门禁一键控制",content:"一键打开全部逃生门禁,并关闭电梯进口门禁",code:"4013701"},{title:"消防发布告警",content:"一键进行园区广播告警",code:"4013702"},{title:"拨打消防119",content:"一键进行119消防报警",code:"4013703"},{title:"着火视频推送",content:"一键将着火区域的视频推送到安保人员的移动端",code:"4013704"},{title:"关闭区域电源",content:"手动关闭区域电源",code:"4013705"}],title:"",dialogVisible:!1,ruleForm:{},rules:{}}},watch:{},created:function(){this.getList()},computed:{},methods:{getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){console.log(t,"response"),null!=t.data&&(e.tableData=t.data.records,e.total=t.data.total),e.loading=!1}))},cardClick:function(e){var t=this,a=this.dict.type.firecontrol_execute_operation.find((function(t){return t.value==e})).label;this.$modal.confirm('是否确认执行"'+a+'"的操作？').then((function(){return i({executeOperation:e})})).then((function(){t.getList(),t.$modal.msgSuccess("操作成功！")})).catch((function(){}))}}},c=s,u=(a("e8e2"),a("2877")),d=Object(u["a"])(c,r,l,!1,null,"1cc7582d",null);t["default"]=d.exports},c4fa:function(e,t,a){},e8e2:function(e,t,a){"use strict";a("c4fa")}}]);