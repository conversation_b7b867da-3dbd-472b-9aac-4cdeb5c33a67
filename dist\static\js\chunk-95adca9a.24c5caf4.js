(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-95adca9a"],{"0b49":function(t,e,r){"use strict";r("2174")},2174:function(t,e,r){},"2ca0":function(t,e,r){"use strict";var a=r("23e7"),n=r("e330"),i=r("06cf").f,c=r("50c4"),s=r("577e"),u=r("5a34"),l=r("1d80"),o=r("ab13"),h=r("c430"),d=n("".startsWith),b=n("".slice),f=Math.min,p=o("startsWith"),m=!h&&!p&&!!function(){var t=i(String.prototype,"startsWith");return t&&!t.writable}();a({target:"String",proto:!0,forced:!m&&!p},{startsWith:function(t){var e=s(l(this));u(t);var r=c(f(arguments.length>1?arguments[1]:void 0,e.length)),a=s(t);return d?d(e,a,r):b(e,r,r+a.length)===a}})},6350:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-breadcrumb",{staticClass:"app-breadcrumb",attrs:{separator:"/"}},[r("transition-group",{attrs:{name:"breadcrumb"}},t._l(t.levelList,(function(e,a){return r("el-breadcrumb-item",{key:e.path},["noRedirect"===e.redirect||a==t.levelList.length-1?r("span",{staticClass:"no-redirect"},[t._v(t._s(e.meta.title))]):r("a",{on:{click:function(r){return r.preventDefault(),t.handleLink(e)}}},[t._v(t._s(e.meta.title))])])})),1)],1)},n=[],i=(r("2ca0"),r("4de4"),r("d3b7"),r("99af"),r("b0c0"),r("498a"),r("14d9"),{data:function(){return{levelList:null}},watch:{$route:function(t){t.path.startsWith("/redirect/")||this.getBreadcrumb()}},created:function(){this.getBreadcrumb()},methods:{getBreadcrumb:function(){var t=this.$route.matched.filter((function(t){return t.meta&&t.meta.title})),e=t[0];this.isDashboard(e)||(t=[{path:"/"+this.$global.frontUrl+"/index",meta:{title:this.$global.breadTitle}}].concat(t)),this.levelList=t.filter((function(t){return t.meta&&t.meta.title&&!1!==t.meta.breadcrumb}))},isDashboard:function(t){var e=t&&t.name;return!!e&&"Index"===e.trim()},handleLink:function(t){var e=t.redirect,r=t.path;e?this.$router.push(e):this.$router.push(r)}}}),c=i,s=(r("0b49"),r("2877")),u=Object(s["a"])(c,a,n,!1,null,"ee063936",null);e["default"]=u.exports}}]);