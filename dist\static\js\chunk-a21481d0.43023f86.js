(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-a21481d0"],{8054:function(e,t,a){},f0ae:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增规章制度")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roleList}},[a("el-table-column",{attrs:{type:"index",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),a("el-table-column",{attrs:{label:"制度名称","show-overflow-tooltip":!0,prop:"ruleName",align:"center"}}),a("el-table-column",{attrs:{label:"制度类型",prop:"ruleType",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.regulations_type,value:t.row.ruleType,type:1}})]}}])}),a("el-table-column",{attrs:{label:"制定时间",prop:"ruleTime",align:"center"}}),a("el-table-column",{attrs:{label:"创建人",prop:"createUser",align:"center"}}),a("el-table-column",{attrs:{label:"创建日期",prop:"createTime",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handledownloadFile(t.row)}}},[e._v("下载")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"650px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"制度名称",prop:"ruleName"}},[a("el-input",{attrs:{placeholder:"请输入制度名称"},model:{value:e.form.ruleName,callback:function(t){e.$set(e.form,"ruleName",t)},expression:"form.ruleName"}})],1),a("el-form-item",{attrs:{label:"制度类型",prop:"ruleType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择制度类型"},model:{value:e.form.ruleType,callback:function(t){e.$set(e.form,"ruleType",t)},expression:"form.ruleType"}},e._l(e.dict.type.regulations_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"制定日期",prop:"ruleTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择日期时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.ruleTime,callback:function(t){e.$set(e.form,"ruleTime",t)},expression:"form.ruleTime"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),a("el-form-item",{attrs:{label:"附件",prop:"attachment"}},[a("el-upload",{ref:"my-upload",attrs:{headers:e.headers,action:e.uploadImgUrl,"on-remove":e.handleRemove,"on-success":e.handlePreview,"file-list":e.fileList}},[a("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],i=(a("b64b"),a("e9c4"),a("d81d"),a("14d9"),a("a15b"),a("99af"),a("b775"));function o(e){return Object(i["a"])({url:"/firecontrol-rule/page",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/firecontrol-rule/save",method:"post",data:e})}function s(e){return Object(i["a"])({url:"/firecontrol-rule/update",method:"post",data:e})}var u=a("90c5"),c={name:"RulesRegulations",dicts:["rule_type","regulations_type"],data:function(){return{loading:!0,showSearch:!0,total:0,roleList:[],title:"",open:!1,queryParams:{current:1,size:10},form:{},dateRange:[],fileList:[],rules:{ruleType:[{required:!0,message:"请选择制度类型",trigger:"change"}],ruleName:[{required:!0,message:"请输入制度名称",trigger:"blur"}],ruleTime:[{required:!0,message:"选择日期时间",trigger:"change"}]},uploadFile:"api/file/uploadFile",headers:{Authorization:localStorage.getItem("token")},uploadImgUrl:"/emergency-v2/file/uploadFile"}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.roleList=t.data.records,e.total=t.data.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:void 0,ruleName:void 0,ruleType:void 0,ruleTime:void 0,remark:void 0,attachment:void 0},this.resetForm("form")},handleQuery:function(){this.queryParams.current=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(){this.reset(),this.open=!0,this.fileList=[],this.title="新增规章制度"},handleUpdate:function(e){var t=this;if(this.reset(),this.open=!0,this.fileList=[],this.title="修改规章制度",this.form=JSON.parse(JSON.stringify(e)),this.fileList=[],null!=e.attachment){var a=[];a=JSON.parse(JSON.stringify(this.form.attachment.split(","))),a.map((function(e,a){var r=e.split("/");console.log(r),t.fileList.push({name:Date.now()+"_"+r[r.length-1],url:e})}))}},handleRemove:function(e,t){console.log(e,t);var a=[];t.map((function(e){a.push(e.url)})),this.form.attachment=a.join(",")},handledownloadFile:function(e){var t=e.attachment.split(",");console.log(t),t.map((function(e){var t=e.split("/");console.log(t),window.open("/api/file/downloadFile?bucket=".concat(t[3],"&path=").concat(t[4],"/").concat(t[5],"&fileName=").concat(t[6]))}))},handlePreview:function(e,t,a){if(console.log(e,t,a),0==t.size)return this.$modal.msgWarning("当前文件大小不符合规范"),!0;var r=[];a.map((function(e){e.response?r.push(JSON.parse(u["a"].decryptAES(e.response,u["a"].aesKey))):r.push(e.url)})),this.form.attachment=r.join(",")},submitForm:function(){var e=this;console.log(this.form),this.$refs["form"].validate((function(t){t&&(void 0!=e.form.id?s(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.$refs["my-upload"].clearFiles(),e.open=!1,e.getList()})):n(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.$refs["my-upload"].clearFiles(),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return s({id:e.id,isDeleted:1})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},m=c,d=(a("f907"),a("2877")),p=Object(d["a"])(m,r,l,!1,null,"7606c03a",null);t["default"]=p.exports},f907:function(e,t,a){"use strict";a("8054")}}]);