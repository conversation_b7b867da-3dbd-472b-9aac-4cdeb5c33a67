(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-a3c25c5a"],{1702:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:t.handleAdd}},[t._v("新增消防器材")])],1),n("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.roleList}},[n("el-table-column",{attrs:{type:"index",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s((t.queryParams.current-1)*t.queryParams.size+e.$index+1))])]}}])}),n("el-table-column",{attrs:{label:"器材类型","show-overflow-tooltip":!0,prop:"equipmentType",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("dict-tag",{attrs:{options:t.dict.type.equipment_type,value:e.row.equipmentType,type:1}})]}}])}),n("el-table-column",{attrs:{label:"规格",prop:"equipmentUnit",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("dict-tag",{attrs:{options:t.dict.type.equipment_unit,value:e.row.equipmentUnit,type:1}})]}}])}),n("el-table-column",{attrs:{label:"数量",prop:"amount",align:"center"}}),n("el-table-column",{attrs:{label:"创建人",prop:"createUser",align:"center"}}),n("el-table-column",{attrs:{label:"创建日期",prop:"createTime",align:"center"}}),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(n){return t.handleUpdate(e.row)}}},[t._v("修改")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(n){return t.handleDelete(e.row)}}},[t._v("删除")])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.current,limit:t.queryParams.size},on:{"update:page":function(e){return t.$set(t.queryParams,"current",e)},"update:limit":function(e){return t.$set(t.queryParams,"size",e)},pagination:t.getList}}),n("el-dialog",{attrs:{title:t.title,visible:t.open,width:"650px","append-to-body":""},on:{"update:visible":function(e){t.open=e}}},[n("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"器材类型",prop:"equipmentType"}},[n("el-select",{attrs:{placeholder:"器材类型",clearable:""},model:{value:t.form.equipmentType,callback:function(e){t.$set(t.form,"equipmentType",e)},expression:"form.equipmentType"}},t._l(t.dict.type.equipment_type,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),n("el-form-item",{attrs:{label:"规格",prop:"equipmentUnit"}},[n("el-select",{attrs:{placeholder:"规格",clearable:""},model:{value:t.form.equipmentUnit,callback:function(e){t.$set(t.form,"equipmentUnit",e)},expression:"form.equipmentUnit"}},t._l(t.dict.type.equipment_unit,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),n("el-form-item",{attrs:{label:"数量",prop:"amount"}},[n("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入数量"},model:{value:t.form.amount,callback:function(e){t.$set(t.form,"amount",t._n(e))},expression:"form.amount"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")]),n("el-button",{on:{click:t.cancel}},[t._v("取 消")])],1)],1)],1)},a=[],i=n("fc49"),o={name:"Equipment",dicts:["equipment_type","equipment_unit"],data:function(){return{loading:!0,title:"",open:!1,showSearch:!0,total:0,roleList:[],queryParams:{current:1,size:10,stationId:void 0},form:{},rules:{equipmentType:[{required:!0,message:"请选择器材类型",trigger:"change"}],equipmentUnit:[{required:!0,message:"请选择规格",trigger:"change"}],amount:[{required:!0,message:"请输入数量",trigger:"blur"}]},stationId:void 0}},created:function(){this.getList(),this.queryParams.stationId=this.$route.query.deviceId,this.stationId=this.$route.query.deviceId},methods:{getList:function(){var t=this;this.loading=!0,Object(i["c"])(this.queryParams).then((function(e){t.roleList=e.data.records,t.total=e.data.total,t.loading=!1}))},reset:function(){this.form={id:void 0,name:void 0,sex:null,phone:void 0,stationId:this.stationId},this.resetForm("form")},handleAdd:function(){this.reset(),this.open=!0,this.title="新增消防器材"},handleUpdate:function(t){this.reset(),this.open=!0,this.title="修改消防器材",this.form=t,console.log(this.form)},handleQuery:function(){this.queryParams.current=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},cancel:function(){this.open=!1,this.reset()},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(void 0!=t.form.id?Object(i["i"])(t.form).then((function(e){console.log(e),t.$modal.msgSuccess("修改成功"),t.open=!1,t.getList()})):Object(i["f"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.open=!1,t.getList()})))}))},handleDelete:function(t){var e=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return Object(i["i"])({id:t.id,isDeleted:1})})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},s=o,l=(n("840a"),n("2877")),u=Object(l["a"])(s,r,a,!1,null,"66d5f230",null);e["default"]=u.exports},"840a":function(t,e,n){"use strict";n("d6e7")},d6e7:function(t,e,n){},fc49:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"d",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return s})),n.d(e,"e",(function(){return l})),n.d(e,"h",(function(){return u})),n.d(e,"g",(function(){return c})),n.d(e,"j",(function(){return d})),n.d(e,"f",(function(){return m})),n.d(e,"i",(function(){return p}));var r=n("b775");function a(t){return Object(r["a"])({url:"/firecontrol-station/page",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/firecontrol-station/human/page",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/firecontrol-station/equipment/page",method:"get",params:t})}function s(t){return Object(r["a"])({url:"/firecontrol-area/page",method:"get",params:t})}function l(t){return Object(r["a"])({url:"/firecontrol-station/save",method:"post",data:t})}function u(t){return Object(r["a"])({url:"/firecontrol-station/update",method:"post",data:t})}function c(t){return Object(r["a"])({url:"/firecontrol-station/human/save",method:"post",data:t})}function d(t){return Object(r["a"])({url:"/firecontrol-station/human/update",method:"post",data:t})}function m(t){return Object(r["a"])({url:"/firecontrol-station/equipment/save",method:"post",data:t})}function p(t){return Object(r["a"])({url:"/firecontrol-station/equipment/update",method:"post",data:t})}}}]);