(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-a726e650"],{"4a41":function(e,t,a){"use strict";a("ed45a")},"8d42":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("数据筛选")])]),a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticStyle:{display:"flex","justify-content":"space-between"},attrs:{model:e.queryParams,size:"small",inline:!0}},[a("div",[a("el-form-item",{attrs:{label:"隐患ID",prop:"id"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入隐患ID",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.id,callback:function(t){e.$set(e.queryParams,"id",t)},expression:"queryParams.id"}})],1),a("el-form-item",{attrs:{label:"修复时间"}},[a("el-date-picker",{staticStyle:{width:"10vw"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",{attrs:{label:"上报人",prop:"createUser"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入上报人",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.createUser,callback:function(t){e.$set(e.queryParams,"createUser",t)},expression:"queryParams.createUser"}})],1),a("el-form-item",{attrs:{label:"隐患类型",prop:"dangerType"}},[a("el-select",{staticStyle:{width:"10vw"},attrs:{placeholder:"隐患类型",clearable:""},model:{value:e.queryParams.dangerType,callback:function(t){e.$set(e.queryParams,"dangerType",t)},expression:"queryParams.dangerType"}},e._l(e.dict.type.fault_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("div",{staticStyle:{"min-width":"200px"}},[a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)])],1),a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("隐患配置列表")]),a("el-button",{staticClass:"queryBtnT",attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("上报隐患")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roleList}},[a("el-table-column",{attrs:{label:"隐患ID","show-overflow-tooltip":!0,prop:"id",align:"center"}}),a("el-table-column",{attrs:{label:"隐患简述",prop:"description",align:"center"}}),a("el-table-column",{attrs:{label:"隐患类型",prop:"dangerType",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.fault_type,value:t.row.dangerType,type:1}})]}}])}),a("el-table-column",{attrs:{label:"所属区域",prop:"griddingId",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e._f("getfullName")(t.row.griddingId,e.originArr))+" ")]}}])}),a("el-table-column",{attrs:{label:"上报人员",prop:"createUser",align:"center"}}),a("el-table-column",{attrs:{label:"具体位置",prop:"location",align:"center"}}),a("el-table-column",{attrs:{label:"上报时间",prop:"createTime",align:"center"}}),a("el-table-column",{attrs:{label:"修复时间",prop:"restoreTime",align:"center"}}),a("el-table-column",{attrs:{label:"状态",prop:"handleType",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getNameById(t.row))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row,"look")}}},[e._v("详情")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handledownloadFile(t.row)}}},[e._v("下载附件")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}})],1),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"750px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"隐患类型",prop:"dangerType"}},[a("el-select",{attrs:{placeholder:"隐患类型",clearable:"",disabled:e.disabled},model:{value:e.form.dangerType,callback:function(t){e.$set(e.form,"dangerType",t)},expression:"form.dangerType"}},e._l(e.dict.type.fault_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"区域名称",prop:"griddingId"}},[a("el-select",{attrs:{placeholder:"隐患类型",clearable:"",disabled:e.disabled},model:{value:e.form.griddingId,callback:function(t){e.$set(e.form,"griddingId",t)},expression:"form.griddingId"}},e._l(e.originArr,(function(e){return a("el-option",{key:e.griddingId,attrs:{label:e.griddingName,value:e.griddingId}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"具体位置",prop:"location"}},[a("el-input",{attrs:{disabled:e.disabled,placeholder:"请输入具体位置"},model:{value:e.form.location,callback:function(t){e.$set(e.form,"location",t)},expression:"form.location"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"隐患描述",prop:"description"}},[a("el-input",{attrs:{disabled:e.disabled,type:"textarea",placeholder:"请输入"},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"上传附件",prop:"attachment"}},[a("el-upload",{ref:"my-upload",attrs:{disabled:e.disabled,headers:e.headers,action:e.uploadFile,"on-remove":e.handleRemove,"on-success":e.handlePreview,"file-list":e.fileList}},[a("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB")])])],1)],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",disabled:e.disabled},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],l=(a("d81d"),a("99af"),a("14d9"),a("a15b"),a("b64b"),a("e9c4"),a("4de4"),a("d3b7"),a("b775"));function n(e){return Object(l["a"])({url:"/firecontrol-hidden-danger/page",method:"get",params:e})}function o(){return Object(l["a"])({url:"/firecontrol-area/list",method:"get"})}function s(e){return Object(l["a"])({url:"/firecontrol-hidden-danger/save",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/firecontrol-hidden-danger/update",method:"post",data:e})}function c(e){return Object(l["a"])({url:"/firecontrol-hidden-danger/delete",method:"post",data:e})}var u=a("90c5"),m={name:"HiddenDangerRecord",dicts:["fault_type","firecontrol_alarm_status","emergency_status"],data:function(){return{loading:!0,showSearch:!0,total:0,roleList:[],title:"",open:!1,queryParams:{current:1,size:10,startTime:void 0,endTime:void 0,id:void 0,description:void 0,createUser:void 0,dangerType:void 0},headers:{Authorization:localStorage.getItem("token")},uploadFile:"api/file/uploadFile",fileList:[],form:{},dateRange:[],originArr:[],rules:{dangerType:[{required:!0,message:"隐患类型",trigger:"blur"}],griddingId:[{required:!0,message:"区域名称",trigger:"blur"}],location:[{required:!0,message:"请输入具体位置",trigger:"blur"}],handleType:[{required:!0,message:"处理方式",trigger:"blur"}]},disabled:!1}},created:function(){this.getList(),this.getListpage()},filters:{getfullName:function(e,t){var a="";return t.map((function(t){e==t.griddingId&&(a=t.griddingName)})),a}},methods:{getListpage:function(){var e=this;o().then((function(t){e.originArr=t.data}))},getList:function(){var e=this;this.loading=!0,console.log(this.queryParams),n(this.queryParams).then((function(t){console.log(t),e.roleList=t.data.records,e.total=t.data.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,dangerType:void 0,griddingId:void 0,location:void 0,description:void 0,attachment:void 0},this.resetForm("form")},handleQuery:function(){this.dateRange.length>0&&(this.queryParams.startTime=this.dateRange[0],this.queryParams.endTime=this.dateRange[1]),this.queryParams.current=1,this.getList()},resetQuery:function(){this.dateRange=[],this.queryParams.startTime=void 0,this.queryParams.endTime=void 0,this.resetForm("queryForm"),this.handleQuery()},handledownloadFile:function(e){var t=this;this.$modal.confirm("是否确认下载附件").then((function(){var t=e.attachment.split(",");console.log(t),t.map((function(e){var t=e.split("/"),a=document.createElement("a");a.href="/api/file/downloadFile?bucket=".concat(t[3],"&path=").concat(t[4],"&fileName=").concat(t[5]),document.body.appendChild(a),a.click(),document.body.removeChild(a)}))})).then((function(){t.getList(),t.$modal.msgSuccess("下载成功")})).catch((function(e){}))},handleRemove:function(e,t){console.log(e,t);var a=[];t.map((function(e){a.push(e.url)})),this.form.attachment=a.join(",")},handlePreview:function(e,t,a){if(console.log(e,t,a),0==t.size)return this.$modal.msgWarning("当前文件大小不符合规范"),!0;var r=[];a.map((function(e){e.response?r.push(JSON.parse(u["a"].decryptAES(e.response,u["a"].aesKey))):r.push(e.url)})),this.form.attachment=r.join(",")},handleAdd:function(){this.disabled=!1,this.reset(),this.open=!0,this.title="上报隐患",this.fileList=[]},handleUpdate:function(e,t){var a=this;if(this.disabled="look"==t,this.reset(),this.open=!0,this.title="修改隐患",this.form=JSON.parse(JSON.stringify(e)),console.log(this.form),this.fileList=[],this.form.amount=null,null!=e.attachment){var r=[];r=JSON.parse(JSON.stringify(this.form.attachment.split(","))),r.map((function(e,t){var r=e.split("/");a.fileList.push({name:r[r.length-1],url:e})}))}},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(console.log(e.form),void 0!=e.form.id?d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):s(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},getNameById:function(e){if(console.log(e,this.dict.type.firecontrol_alarm_status,"Name"),void 0!=e.dangerStatus&&""!=e.dangerStatus&&null!=e.dangerStatus)return this.dict.type.firecontrol_alarm_status.filter((function(t){return t.value==e.dangerStatus}))[0].label},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return c({id:e.id})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},p=m,f=(a("4a41"),a("2877")),h=Object(f["a"])(p,r,i,!1,null,"14191753",null);t["default"]=h.exports},ed45a:function(e,t,a){}}]);