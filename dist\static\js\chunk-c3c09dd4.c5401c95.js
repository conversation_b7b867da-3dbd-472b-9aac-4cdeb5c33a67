(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-c3c09dd4"],{"5ba1":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container"},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:t.handleAdd}},[t._v("新增人员")])],1),r("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.roleList}},[r("el-table-column",{attrs:{type:"index",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s((t.queryParams.current-1)*t.queryParams.size+e.$index+1))])]}}])}),r("el-table-column",{attrs:{label:"姓名","show-overflow-tooltip":!0,prop:"name",align:"center"}}),r("el-table-column",{attrs:{label:"性别",prop:"sex",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("dict-tag",{attrs:{options:t.dict.type.sex,value:e.row.sex,type:1}})]}}])}),r("el-table-column",{attrs:{label:"责任人",prop:"principal",align:"center"}}),r("el-table-column",{attrs:{label:"联系电话",prop:"phone",align:"center"}}),r("el-table-column",{attrs:{label:"创建人",prop:"createUser",align:"center"}}),r("el-table-column",{attrs:{label:"创建日期",prop:"createTime",align:"center"}}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return t.handleUpdate(e.row)}}},[t._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return t.handleDelete(e.row)}}},[t._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.current,limit:t.queryParams.size},on:{"update:page":function(e){return t.$set(t.queryParams,"current",e)},"update:limit":function(e){return t.$set(t.queryParams,"size",e)},pagination:t.getList}}),r("el-dialog",{attrs:{title:t.title,visible:t.open,width:"650px","append-to-body":""},on:{"update:visible":function(e){t.open=e}}},[r("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"姓名",prop:"name"}},[r("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入姓名"},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),r("el-form-item",{attrs:{label:"性别",prop:"sex"}},[r("el-radio-group",{model:{value:t.form.sex,callback:function(e){t.$set(t.form,"sex",e)},expression:"form.sex"}},[r("el-radio",{attrs:{label:"4010001"}},[t._v("男")]),r("el-radio",{attrs:{label:"4010002"}},[t._v("女")])],1)],1),r("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[r("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入手机号"},model:{value:t.form.phone,callback:function(e){t.$set(t.form,"phone",e)},expression:"form.phone"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")]),r("el-button",{on:{click:t.cancel}},[t._v("取 消")])],1)],1)],1)},a=[],o=(r("d9e2"),r("ac1f"),r("00b4"),r("fc49")),i={name:"Personnel",dicts:["sex"],data:function(){var t=function(t,e,r){""===e?r(new Error("请输入绑定的手机号码")):/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(e)?r():r(new Error("请输入正确的手机号码"))};return{loading:!0,title:"",open:!1,showSearch:!0,total:0,roleList:[],queryParams:{current:1,size:10,stationId:void 0},form:{},rules:{name:[{required:!0,message:"请输入姓名",trigger:"blur"}],sex:[{required:!0,message:"请选择性别",trigger:"change"}],phone:[{validator:t,required:!0,trigger:"blur"}]},stationId:void 0}},created:function(){this.getList(),this.queryParams.stationId=this.$route.query.deviceId,this.stationId=this.$route.query.deviceId},methods:{getList:function(){var t=this;this.loading=!0,Object(o["d"])(this.queryParams).then((function(e){t.roleList=e.data.records,t.total=e.data.total,t.loading=!1}))},reset:function(){this.form={id:void 0,name:void 0,sex:null,phone:void 0,stationId:this.stationId},this.resetForm("form")},handleAdd:function(){this.reset(),this.open=!0,this.title="新增人员"},handleUpdate:function(t){this.reset(),this.open=!0,this.title="修改人员",this.form=t,console.log(this.form)},handleQuery:function(){this.queryParams.current=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},cancel:function(){this.open=!1,this.reset()},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(void 0!=t.form.id?Object(o["j"])(t.form).then((function(e){console.log(e),t.$modal.msgSuccess("修改成功"),t.open=!1,t.getList()})):Object(o["g"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.open=!1,t.getList()})))}))},handleDelete:function(t){var e=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return Object(o["j"])({id:t.id,isDeleted:1})})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},s=i,l=(r("b68a"),r("2877")),u=Object(l["a"])(s,n,a,!1,null,"394c8f02",null);e["default"]=u.exports},8945:function(t,e,r){},b68a:function(t,e,r){"use strict";r("8945")},fc49:function(t,e,r){"use strict";r.d(e,"b",(function(){return a})),r.d(e,"d",(function(){return o})),r.d(e,"c",(function(){return i})),r.d(e,"a",(function(){return s})),r.d(e,"e",(function(){return l})),r.d(e,"h",(function(){return u})),r.d(e,"g",(function(){return c})),r.d(e,"j",(function(){return d})),r.d(e,"f",(function(){return m})),r.d(e,"i",(function(){return p}));var n=r("b775");function a(t){return Object(n["a"])({url:"/firecontrol-station/page",method:"get",params:t})}function o(t){return Object(n["a"])({url:"/firecontrol-station/human/page",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/firecontrol-station/equipment/page",method:"get",params:t})}function s(t){return Object(n["a"])({url:"/firecontrol-area/page",method:"get",params:t})}function l(t){return Object(n["a"])({url:"/firecontrol-station/save",method:"post",data:t})}function u(t){return Object(n["a"])({url:"/firecontrol-station/update",method:"post",data:t})}function c(t){return Object(n["a"])({url:"/firecontrol-station/human/save",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/firecontrol-station/human/update",method:"post",data:t})}function m(t){return Object(n["a"])({url:"/firecontrol-station/equipment/save",method:"post",data:t})}function p(t){return Object(n["a"])({url:"/firecontrol-station/equipment/update",method:"post",data:t})}}}]);