(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-dd15effc"],{"4d57":function(e,t,l){"use strict";l("d2f3")},"7d47":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("el-row",{staticStyle:{"margin-bottom":"20px"},attrs:{gutter:10}},[l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:add"],expression:"['system:user:add']"}],attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增")])],1)],1),l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData}},[l("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s((e.queryParams.current-1)*e.queryParams.size+t.$index+1))])]}}])}),l("el-table-column",{attrs:{align:"center",prop:"fileName",label:"文件名称"}}),l("el-table-column",{attrs:{align:"center",prop:"fileType",label:"文件类型"}}),l("el-table-column",{attrs:{align:"center",prop:"uploadTime",label:"上传时间"}}),l("el-table-column",{attrs:{align:"center",prop:"createUser",label:"创建人"}}),l("el-table-column",{attrs:{align:"center",prop:"updateTime",label:"更新日期"}}),l("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{type:"text",size:"mini"},on:{click:function(l){return e.handleUpdate(t.row)}}},[e._v("编辑")]),l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],staticStyle:{"margin-left":"20px"},attrs:{type:"text",size:"mini"},on:{click:function(l){return e.handleDelete(t.row)}}},[e._v("删除")]),l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],staticStyle:{"margin-left":"20px"},attrs:{type:"text",size:"mini"},on:{click:function(l){return e.download(t.row)}}},[e._v("下载")])]}}])})],1),l("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,layout:"prev, pager, next, jumper",page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),l("el-dialog",{attrs:{title:e.title,visible:e.abilityOpen,width:"680px","append-to-body":""},on:{"update:visible":function(t){e.abilityOpen=t}}},[l("el-form",{ref:"knowledgeForm",attrs:{model:e.knowledgeForm,rules:e.abilityRules,"label-width":"110px"}},[l("el-form-item",{attrs:{label:"文件名称",prop:"fileName"}},[l("el-input",{attrs:{placeholder:"请输入文件名称",maxlength:"30"},model:{value:e.knowledgeForm.fileName,callback:function(t){e.$set(e.knowledgeForm,"fileName",t)},expression:"knowledgeForm.fileName"}})],1),l("el-form-item",{attrs:{label:"文件类型",prop:"fileType"}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择文件类型",disabled:e.disabled},model:{value:e.knowledgeForm.fileType,callback:function(t){e.$set(e.knowledgeForm,"fileType",t)},expression:"knowledgeForm.fileType"}},e._l(e.dict.type.file_type,(function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1),l("el-form-item",{attrs:{label:"附件"}},[l("el-upload",{staticClass:"upload-demo",attrs:{limit:1,"on-exceed":e.handleExceed,"on-remove":e.handleRemove,"before-remove":e.beforeRemove,action:e.uploadFileUrl,"on-success":e.handlePreview,headers:e.headers,"file-list":e.fileList}},[l("el-button",{attrs:{type:"primary",disabled:e.disabled}},[e._v("点击上传")]),l("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[l("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB")])])],1)],1),l("el-form-item",{attrs:{label:"备注",prop:"remark"}},[l("el-input",{attrs:{type:"textarea",autosize:"",placeholder:"请输入备注"},model:{value:e.knowledgeForm.remark,callback:function(t){e.$set(e.knowledgeForm,"remark",t)},expression:"knowledgeForm.remark"}})],1)],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:e.cancel}},[e._v("取 消")]),l("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.confirm("knowledgeForm")}}},[e._v("确 定")])],1)],1)],1)},i=[],o=(l("b0c0"),l("5f87")),r=l("b775");function n(e){return Object(r["a"])({url:"/emergency_knowledge/page",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/emergency_knowledge/save",method:"post",data:e})}function d(e){return Object(r["a"])({url:"/emergency_knowledge/update",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/emergency_knowledge/deleteById",method:"post",data:e})}var m={name:"knowledgeBase",dicts:["file_type"],data:function(){return{loading:!1,fileList:[],headers:{Authorization:"Bearer "+Object(o["a"])()},uploadFileUrl:"api/file/uploadFile",total:0,tableData:[],abilityOpen:!1,title:"新增应急知识库",queryParams:{current:1,size:10},knowledgeForm:{fileName:void 0,fileType:void 0,remark:void 0,fileUrl:void 0},disabled:!1,abilityRules:{}}},watch:{},created:function(){this.getList()},methods:{handlePreview:function(e,t){0===e.code&&(this.$modal.msgSuccess(e.msg),this.knowledgeForm.fileUrl=e.fileUrl)},beforeRemove:function(e,t){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){this.knowledgeForm.fileUrl=""},handleExceed:function(){this.$modal.msgError("上传文件数量不能超过1个!")},handleUploadError:function(e){this.$modal.msgError("上传失败，请重试")},getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){null!=t.data&&(console.log(t.data),e.tableData=t.data.records,e.total=t.data.total),e.loading=!1}))},handleUpdate:function(e){this.abilityOpen=!0,this.reset(),this.knowledgeForm.fileId=e.fileId,this.knowledgeForm.fileName=e.fileName,this.knowledgeForm.fileType=e.fileType,this.knowledgeForm.remark=e.remark,this.title="编辑应急知识库",this.disabled=!0},handleAdd:function(){this.reset(),this.abilityOpen=!0,this.title="新增应急知识库",this.disabled=!1},handleDelete:function(e){var t=this,l=e.fileId;this.$modal.confirm("是否确认删除当前数据").then((function(){return c({fileId:l})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},cancel:function(){this.abilityOpen=!1,this.reset()},download:function(e){window.open("/api/file/downloadFile?fileId=".concat(e.fileId))},confirm:function(e){var t=this;console.log(this.knowledgeForm),this.$refs[e].validate((function(e){e&&(void 0!=t.knowledgeForm.fileId?d(t.knowledgeForm).then((function(e){console.log(e,"编辑"),200==e.code&&(t.$modal.msgSuccess("编辑成功"),t.abilityOpen=!1,t.getList())})):s(t.knowledgeForm).then((function(e){console.log(e,"新增"),200==e.code&&(t.$modal.msgSuccess("新增成功"),t.abilityOpen=!1,t.getList())})))}))},handleQuery:function(){this.queryParams.current=1,this.getList()},reset:function(){this.knowledgeForm={fileName:"",fileType:"",remark:"",fileUrl:""},this.fileList=[],this.resetForm("knowledgeForm")}}},u=m,p=(l("4d57"),l("2877")),f=Object(p["a"])(u,a,i,!1,null,"d96c570e",null);t["default"]=f.exports},d2f3:function(e,t,l){}}]);