(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-ea964368"],{"4c99e":function(e,t,a){},"79cf":function(e,t,a){"use strict";a("4c99e")},d1b9:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0}},[a("el-form-item",{attrs:{label:"消控室名称",prop:"qy"}},[a("el-input",{staticStyle:{width:"230px"},attrs:{placeholder:"请输入消控室名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"关联区域"}},[a("el-cascader",{attrs:{options:e.options,props:{checkStrictly:!0,label:"name",value:"id",emitPath:!1},clearable:""},model:{value:e.queryParams.areaId,callback:function(t){e.$set(e.queryParams,"areaId",t)},expression:"queryParams.areaId"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增消控室")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roomList}},[a("el-table-column",{attrs:{label:"消控室名称",prop:"name",align:"center"}}),a("el-table-column",{attrs:{label:"关联区域",prop:"areaName",align:"center"}}),a("el-table-column",{attrs:{label:"责任人",prop:"principal",align:"center"}}),a("el-table-column",{attrs:{label:"联系电话",prop:"phone",align:"center"}}),a("el-table-column",{attrs:{label:"创建人",prop:"principal",align:"center"}}),a("el-table-column",{attrs:{label:"创建日期",prop:"createTime",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("编辑")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.current,limit:e.queryParams.size},on:{"update:page":function(t){return e.$set(e.queryParams,"current",t)},"update:limit":function(t){return e.$set(e.queryParams,"size",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"消控室名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入消控室名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"关联区域",prop:"areaId"}},[a("el-cascader",{attrs:{options:e.options,props:{checkStrictly:!0,label:"name",value:"id",emitPath:!1},clearable:""},on:{change:e.handleChange},model:{value:e.form.areaId,callback:function(t){e.$set(e.form,"areaId",t)},expression:"form.areaId"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"责任人",prop:"principal"}},[a("el-input",{attrs:{placeholder:"请输入责任人"},model:{value:e.form.principal,callback:function(t){e.$set(e.form,"principal",t)},expression:"form.principal"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1)],1),a("el-row",[a("el-col",[a("el-button",{attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:e.device}},[e._v("关联监控设备")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"关联设备",visible:e.dialogTableVisible,"append-to-body":"",width:"1400px"},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[a("el-row",[a("el-col",{attrs:{span:11}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.deviceQueryParams,size:"small",inline:!0}},[a("div",[a("el-form-item",{attrs:{label:"设备名称 :",prop:"deviceName","label-width":"90px"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入消控室名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleDeviceQuery(t)}},model:{value:e.deviceQueryParams.deviceName,callback:function(t){e.$set(e.deviceQueryParams,"deviceName",t)},expression:"deviceQueryParams.deviceName"}})],1),a("el-form-item",{attrs:{label:"安装位置 :","label-width":"90px"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入消控室名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleDeviceQuery(t)}},model:{value:e.deviceQueryParams.placementLocation,callback:function(t){e.$set(e.deviceQueryParams,"placementLocation",t)},expression:"deviceQueryParams.placementLocation"}})],1)],1),a("div",{staticStyle:{"min-width":"166px"}},[a("el-form-item",[a("el-button",{staticClass:"resetQueryStyle",attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleDeviceQuery}},[e._v("搜索")]),a("el-button",{staticClass:"resetQueryStyle",attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery1}},[e._v("重置")])],1)],1)]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.deviceLoading,expression:"deviceLoading"}],attrs:{data:e.deviceData},on:{"row-click":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"ID",prop:"deviceId",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"摄像头名称",prop:"deviceName",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"所属部门",prop:"organizationName",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"安装位置",prop:"placementLocation",align:"center","show-overflow-tooltip":""}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.deviceTotal>0,expression:"deviceTotal > 0"}],attrs:{total:e.deviceTotal,page:e.deviceQueryParams.current,limit:e.deviceQueryParams.size},on:{"update:page":function(t){return e.$set(e.deviceQueryParams,"current",t)},"update:limit":function(t){return e.$set(e.deviceQueryParams,"size",t)},pagination:e.devicePage}})],1),a("el-col",{staticStyle:{"min-height":"100px"},attrs:{span:2}}),a("el-col",{attrs:{span:11}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.deviceLoading,expression:"deviceLoading"}],attrs:{data:e.deviceData1}},[a("el-table-column",{attrs:{label:"ID",prop:"deviceId",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"摄像头名称",prop:"deviceName",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"所属部门",prop:"organizationName",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"安装位置",prop:"placementLocation",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-plus"},on:{click:function(a){return e.handleDelete1(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.devicesubmitForm}},[e._v("确认添加")]),a("el-button",{on:{click:e.devicecancel}},[e._v("取 消")])],1)],1)],1)},r=[],o=(a("d9e2"),a("ac1f"),a("00b4"),a("d3b7"),a("159b"),a("14d9"),a("b0c0"),a("4de4"),a("25f0"),a("b775"));function n(e){return Object(o["a"])({url:"/firecontrol-room/page",method:"get",params:e})}function l(e){return Object(o["a"])({url:"/monitor/page",method:"get",params:e})}function s(e){return Object(o["a"])({url:"/firecontrol-room/save",method:"post",data:e})}function c(e){return Object(o["a"])({url:"organization/tree",method:"get",params:e})}function d(e){return Object(o["a"])({url:"/firecontrol-room/update",method:"post",data:e})}function u(e){return Object(o["a"])({url:"/firecontrol-room/delete",method:"post",data:e})}function m(){return Object(o["a"])({url:"/area/tree",method:"get"})}var p={name:"FireStation",data:function(){var e=function(e,t,a){""===t?a(new Error("请输入绑定的手机号码")):/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(t)?a():a(new Error("请输入正确的手机号码"))};return{dialogTableVisible:!1,deviceLoading:!1,deviceData:[],deviceData1:[],deviceQueryParams:{current:1,size:10,organizationId:void 0,organizationName:void 0},loading:!0,showSearch:!0,total:0,deviceTotal:0,options:[],roomList:[{}],title:"",open:!1,queryParams:{current:1,size:10,name:void 0,areaId:void 0},form:{name:void 0,areaId:void 0,principal:void 0,phone:void 0,monitorIdList:[]},treeData:[],defaultProps:{children:"children",label:"name"},flag:!1,rules:{name:[{required:!0,message:"请输入消控室名称",trigger:"blur"}],areaId:[{required:!0,message:"请选择关联区域",trigger:"blur"}],principal:[{required:!0,message:"请输入责任人",trigger:"blur"}],phone:[{type:"number",validator:e,message:"请输入正确的手机号",trigger:"change",required:!0}]}}},created:function(){this.getList()},methods:{devicePage:function(){var e=this;l(this.deviceQueryParams).then((function(t){200==t.code&&(e.deviceData=t.data.records,e.deviceTotal=t.data.total),console.log(t)}))},devicesubmitForm:function(){var e=this;this.dialogTableVisible=!1,this.deviceData1.forEach((function(t){e.form.monitorIdList.push(t.id)}))},handleSelectionChange:function(e){var t=this;this.flag=!1,console.log(e,this.deviceData1),0!=this.deviceData1.length?(this.deviceData1.forEach((function(a){a.id==e.id&&(t.flag=!0,t.$modal.msgSuccess("请勿重复添加！"))})),this.flag||this.deviceData1.push(e)):this.deviceData1.push(e)},handleChange:function(e){this.findName(this.options,e)},getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){m().then((function(a){a.data.forEach((function(e){e.areaName=e.name})),e.addName(a.data),e.options=a.data,e.roomList=t.data.records,e.total=t.data.total,e.loading=!1}))}))},handleNodeClick:function(e,t,a){console.log(e,t,a,"树结构"),t?(console.log(111),this.$refs.tree.setCheckedNodes([e]),this.deviceQueryParams.organizationId=e.id,this.deviceQueryParams.organizationName=e.name):(this.deviceQueryParams.organizationId=void 0,this.deviceQueryParams.organizationName=void 0)},addName:function(e){var t=this;e.forEach((function(e){e.children&&(e.children.forEach((function(t){t.areaName=e.areaName+"-"+t.name})),t.addName(e.children))}))},device:function(){this.getEventType(),this.devicePage(),this.dialogTableVisible=!0,this.resetQuery1()},findName:function(e,t){var a=this;e.forEach((function(e){e.areaId==t?(console.log(1111),a.form.areaName=e.areaName):e.children&&a.findName(e.children,t)}))},cancel:function(){this.open=!1,this.reset()},devicecancel:function(){this.resetQuery1(),this.dialogTableVisible=!1},reset:function(){this.form={name:void 0,areaId:void 0,principal:void 0,phone:void 0,monitorIdList:[]},this.resetForm("form")},handleQuery:function(){this.queryParams.current=1,this.getList()},handleDeviceQuery:function(){this.deviceQueryParams.current=1,this.devicePage()},resetQuery:function(){this.queryParams={current:1,size:10,name:void 0,areaId:void 0},this.resetForm("queryForm"),this.handleQuery()},resetQuery1:function(){this.deviceQueryParams={current:1,size:10,organizationId:void 0,organizationName:void 0},this.deviceData1=[],this.handleDeviceQuery()},handleDelete1:function(e){this.deviceData1=this.deviceData1.filter((function(t){return t.id!=e.id})),console.log(e)},handleAdd:function(){this.reset(),this.open=!0,this.title="新增消防站"},handleUpdate:function(e){this.reset(),e.areaId=e.areaId.toString(),this.open=!0,this.title="修改消防站",console.log(e),this.form=e},submitForm:function(){var e=this;console.log(this.form,"this.form"),this.$refs["form"].validate((function(t){t&&(void 0!=e.form.id?d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):s(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$modal.confirm("是否确认删除当前数据").then((function(){return u({id:e.id})})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},getEventType:function(){var e=this;c().then((function(t){console.log(t,"事件类型"),200==t.code&&(e.treeData=t.data)}))}}},h=p,v=(a("79cf"),a("2877")),f=Object(v["a"])(h,i,r,!1,null,"d8c6ffac",null);t["default"]=f.exports}}]);