(window["webpackJsonpvueApp"]=window["webpackJsonpvueApp"]||[]).push([["chunk-f3e54666"],{1918:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return l})),a.d(e,"c",(function(){return r})),a.d(e,"f",(function(){return o})),a.d(e,"d",(function(){return s})),a.d(e,"e",(function(){return d}));var n=a("b775");function i(t){return Object(n["a"])({url:"/emergency_structured_template/page",method:"get",params:t})}function l(){return Object(n["a"])({url:"/emergency_structured_template/List",method:"get"})}function r(t){return Object(n["a"])({url:"/emergency_structured_template/save",method:"post",data:t})}function o(t){return Object(n["a"])({url:"/emergency_structured_template/update",method:"post",data:t})}function s(t){return Object(n["a"])({url:"/emergency_structured_template/delete",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/emergency_structured_template/detail",method:"get",params:t})}},7809:function(t,e,a){},"953d":function(t,e,a){!function(e,n){t.exports=n(a("9339"))}(0,(function(t){return function(t){function e(n){if(a[n])return a[n].exports;var i=a[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,e),i.l=!0,i.exports}var a={};return e.m=t,e.c=a,e.i=function(t){return t},e.d=function(t,a,n){e.o(t,a)||Object.defineProperty(t,a,{configurable:!1,enumerable:!0,get:n})},e.n=function(t){var a=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(a,"a",a),a},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="/",e(e.s=2)}([function(e,a){e.exports=t},function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a(4),i=a.n(n),l=a(6),r=a(5),o=r(i.a,l.a,!1,null,null,null);e.default=o.exports},function(t,e,a){"use strict";function n(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0}),e.install=e.quillEditor=e.Quill=void 0;var i=a(0),l=n(i),r=a(1),o=n(r),s=window.Quill||l.default,d=function(t,e){e&&(o.default.props.globalOptions.default=function(){return e}),t.component(o.default.name,o.default)},c={Quill:s,quillEditor:o.default,install:d};e.default=c,e.Quill=s,e.quillEditor=o.default,e.install=d},function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={theme:"snow",boundary:document.body,modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["clean"],["link","image","video"]]},placeholder:"Insert text here ...",readOnly:!1}},function(t,e,a){"use strict";function n(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});var i=a(0),l=n(i),r=a(3),o=n(r),s=window.Quill||l.default;"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var a=Object(t),n=1;n<arguments.length;n++){var i=arguments[n];if(null!=i)for(var l in i)Object.prototype.hasOwnProperty.call(i,l)&&(a[l]=i[l])}return a},writable:!0,configurable:!0}),e.default={name:"quill-editor",data:function(){return{_options:{},_content:"",defaultOptions:o.default}},props:{content:String,value:String,disabled:{type:Boolean,default:!1},options:{type:Object,required:!1,default:function(){return{}}},globalOptions:{type:Object,required:!1,default:function(){return{}}}},mounted:function(){this.initialize()},beforeDestroy:function(){this.quill=null,delete this.quill},methods:{initialize:function(){var t=this;this.$el&&(this._options=Object.assign({},this.defaultOptions,this.globalOptions,this.options),this.quill=new s(this.$refs.editor,this._options),this.quill.enable(!1),(this.value||this.content)&&this.quill.pasteHTML(this.value||this.content),this.disabled||this.quill.enable(!0),this.quill.on("selection-change",(function(e){e?t.$emit("focus",t.quill):t.$emit("blur",t.quill)})),this.quill.on("text-change",(function(e,a,n){var i=t.$refs.editor.children[0].innerHTML,l=t.quill,r=t.quill.getText();"<p><br></p>"===i&&(i=""),t._content=i,t.$emit("input",t._content),t.$emit("change",{html:i,text:r,quill:l})})),this.$emit("ready",this.quill))}},watch:{content:function(t,e){this.quill&&(t&&t!==this._content?(this._content=t,this.quill.pasteHTML(t)):t||this.quill.setText(""))},value:function(t,e){this.quill&&(t&&t!==this._content?(this._content=t,this.quill.pasteHTML(t)):t||this.quill.setText(""))},disabled:function(t,e){this.quill&&this.quill.enable(!t)}}}},function(t,e){t.exports=function(t,e,a,n,i,l){var r,o=t=t||{},s=typeof t.default;"object"!==s&&"function"!==s||(r=t,o=t.default);var d,c="function"==typeof o?o.options:o;if(e&&(c.render=e.render,c.staticRenderFns=e.staticRenderFns,c._compiled=!0),a&&(c.functional=!0),i&&(c._scopeId=i),l?(d=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),n&&n.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(l)},c._ssrRegister=d):n&&(d=n),d){var u=c.functional,f=u?c.render:c.beforeCreate;u?(c._injectStyles=d,c.render=function(t,e){return d.call(e),f(t,e)}):c.beforeCreate=f?[].concat(f,d):[d]}return{esModule:r,exports:o,options:c}}},function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"quill-editor"},[t._t("toolbar"),t._v(" "),a("div",{ref:"editor"})],2)},i=[],l={render:n,staticRenderFns:i};e.a=l}])}))},bf74:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,xs:24}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("筛选条件")])]),a("el-form",{ref:"queryForm",staticClass:"queryBox",attrs:{model:t.queryParams,"label-width":"100px",size:"small",inline:""}},[a("div",[a("el-form-item",{attrs:{label:"模板名称"}},[a("el-input",{staticStyle:{width:"10vw"},attrs:{placeholder:"请输入模板名称",clearable:"",maxlength:"20"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.templateName,callback:function(e){t.$set(t.queryParams,"templateName",e)},expression:"queryParams.templateName"}})],1),a("el-form-item",{attrs:{label:"创建时间"}},[a("el-date-picker",{staticStyle:{width:"300px"},attrs:{"value-format":"yyyy-MM-dd hh:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.queryParams.dateRange,callback:function(e){t.$set(t.queryParams,"dateRange",e)},expression:"queryParams.dateRange"}})],1)],1),a("el-form-item",{attrs:{label:" "}},[a("el-button",{staticClass:"resetQueryStyle",staticStyle:{"font-size":"13px"},attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")]),a("el-button",{staticClass:"resetQueryStyle",staticStyle:{"font-size":"13px"},attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("结构化模板展示列表")]),a("el-button",{staticClass:"queryBtn",attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:function(e){return t.handleOperation("add")}}},[t._v("新增预案模板")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableList,"cell-style":{padding:"0px"},"row-style":{height:"48px"}}},[a("el-table-column",{attrs:{label:"序号",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"模板名称",align:"center",prop:"templateName"}}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"320","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",icon:"el-icon-view"},on:{click:function(a){return t.handleOperation("look",e.row)}}},[t._v("查看")]),a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return t.handleOperation("edit",e.row)}}},[t._v("编辑")]),a("el-button",{attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(a){return t.handleOperation("delete",e.row)}}},[t._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.pageInfo.total>0,expression:"pageInfo.total > 0"}],attrs:{total:t.pageInfo.total,page:t.pageInfo.current,limit:t.pageInfo.size},on:{"update:page":function(e){return t.$set(t.pageInfo,"current",e)},"update:limit":function(e){return t.$set(t.pageInfo,"size",e)},pagination:t.getList}})],1)],1)],1),a("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:t.dialogInfo.loading,expression:"dialogInfo.loading"}],attrs:{title:t.dialogInfo.title,visible:t.dialogInfo.show,width:"960px","append-to-body":""},on:{"update:visible":function(e){return t.$set(t.dialogInfo,"show",e)}}},[a("el-form",{ref:"ruleForm",attrs:{model:t.formData,disabled:t.dialogInfo.disabled}},[a("el-form-item",{attrs:{"label-width":"110px",label:"模板名称",prop:"templateName",rules:{required:!0,message:"模板名称不能为空",trigger:"blur"}}},[a("el-input",{staticStyle:{width:"40%"},attrs:{placeholder:"请输入模板名称",clearable:"",maxlength:"20"},model:{value:t.formData.templateName,callback:function(e){t.$set(t.formData,"templateName",e)},expression:"formData.templateName"}})],1),a("el-row",{staticClass:"scroll-box",attrs:{type:"flex",gutter:20}},[a("el-col",{attrs:{offset:1,span:9}},[a("el-card",{staticStyle:{height:"100%"},attrs:{shadow:"never"}},[a("el-form-item",{attrs:{label:"",prop:"title",rules:{required:!0,message:"标题名称不能为空",trigger:"blur"}}},[a("el-input",{staticStyle:{width:"60%"},attrs:{placeholder:"请输入标题名称",clearable:"",maxlength:"20"},model:{value:t.formData.title,callback:function(e){t.$set(t.formData,"title",e)},expression:"formData.title"}}),t.dialogInfo.disabled?t._e():a("i",{staticClass:"btn el-icon-circle-plus",on:{click:t.addChild}})],1),a("el-tree",{ref:"tree",attrs:{"highlight-current":!0,data:t.treeData,"node-key":"id","default-expand-all":"",props:{label:"title",children:"children"},"expand-on-click-node":!1},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.node,i=e.data;return a("div",{},[t.dialogInfo.disabled?a("span",[t._v(t._s(i.title)+" ")]):[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入名称",maxlength:"20"},model:{value:i.title,callback:function(e){t.$set(i,"title",e)},expression:"data.title"}}),a("span",[a("i",{staticClass:"btn el-icon-circle-plus",on:{click:function(e){return e.stopPropagation(),t.handleChildrenOperation("add",n,i)}}}),a("i",{staticClass:"btn el-icon-remove",on:{click:function(e){return e.stopPropagation(),t.handleChildrenOperation("delete",n,i)}}})])]],2)}}])})],1)],1),a("el-col",{attrs:{span:14}},[a("el-form-item",{attrs:{label:"","label-width":"0"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:"",clearable:"",maxlength:"20"},model:{value:t.formData.title,callback:function(e){t.$set(t.formData,"title",e)},expression:"formData.title"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"","label-width":"0"}},[a("quill-editor",{staticClass:"ql-editor",attrs:{disabled:t.dialogInfo.disabled},model:{value:t.formData.content,callback:function(e){t.$set(t.formData,"content",e)},expression:"formData.content"}})],1),a("el-tree",{attrs:{data:t.treeData,"default-expand-all":"","node-key":"id","expand-on-click-node":!1,props:{label:"title"},"icon-class":" ",indent:0},scopedSlots:t._u([{key:"default",fn:function(e){e.node;var n=e.data;return a("div",{staticClass:"custom-tree-node"},[a("el-input",{staticClass:"title",staticStyle:{width:"100%"},attrs:{placeholder:"请输入标题",disabled:"",maxlength:"20"},model:{value:n.title,callback:function(e){t.$set(n,"title",e)},expression:"data.title"}}),a("quill-editor",{staticClass:"ql-editor",attrs:{disabled:t.dialogInfo.disabled},model:{value:n.content,callback:function(e){t.$set(n,"content",e)},expression:"data.content"}})],1)}}])})],1)],1)],1),t.dialogInfo.disabled?t._e():a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确定")]),a("el-button",{on:{click:function(e){t.dialogInfo.show=!1}}},[t._v("取消")])],1)],1)],1)},i=[],l=a("5530"),r=(a("caad"),a("14d9"),a("d81d"),a("1918")),o=a("953d"),s={components:{quillEditor:o["quillEditor"]},name:"StructuredTemplate",dicts:["material_type"],data:function(){return{queryParams:{dateRange:[]},tableLoading:!1,tableList:[],treeData:[],pageInfo:{total:0,current:1,size:10},dialogInfo:{show:!1,loading:!1,disabled:!1,title:""},formData:{templateName:"",title:""}}},created:function(){this.getList()},methods:{handleQuery:function(){this.pageInfo.current=1,this.getList()},resetQuery:function(){this.queryParams={dateRange:[]},this.handleQuery()},getList:function(){var t=this;this.tableLoading=!0;var e=this.queryParams,a=e.templateName,n=e.dateRange,i={};n&&n.length>0&&(i.startTime=n[0],i.endTime=n[1]),Object(r["b"])(Object(l["a"])({current:this.pageInfo.current,size:this.pageInfo.size,templateName:a},i)).then((function(e){t.tableLoading=!1;var a=e.code,n=e.data;200===a&&(t.tableList=n.records||[],t.pageInfo.total=n.total)}))},handleOperation:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};switch(t){case"delete":this.$modal.confirm("是否确认删除当前数据").then((function(){return Object(r["d"])({id:a.id})})).then((function(t){200===t.code&&(e.getList(),e.$modal.msgSuccess("删除成功"))})).catch((function(t){}));break;case"edit":case"look":case"add":this.resetData(),this.dialogInfo.show=!0,this.dialogInfo.title={add:"新增",edit:"编辑",look:"查看"}[t]+"预案模板",this.dialogInfo.disabled="look"===t,["edit","look"].includes(t)&&(this.dialogInfo.loading=!0,Object(r["e"])({structuredTemplateId:a.id}).then((function(t){var a=t.code,n=t.data;if(console.log(n),200===a){var i=n.id,l=n.templateName,r=n.emergencyStructuredTemplateDetailVO,o=void 0===r?{}:r,s=o.children,d=void 0===s?[]:s,c=o.title,u=void 0===c?"":c,f=o.content,p=void 0===f?"":f;e.treeData=d||[],e.formData.id=i,e.formData.title=u,e.formData.content=p,e.formData.templateName=l}e.dialogInfo.loading=!1})));break}},addChild:function(){this.treeData.push({title:"",id:Date.now(),sort:this.treeData.length+1,children:[]}),this.$forceUpdate()},handleChildrenOperation:function(t,e,a){"add"===t?(Array.isArray(a.children)||(a.children=[]),a.children.push({title:"",id:Date.now(),sort:a.children.length+1,children:[]}),this.$forceUpdate()):this.$refs.tree.remove(e)},resetData:function(){var t=this;this.formData={templateName:"",title:""},this.treeData=[],this.dialogInfo.show=!1,this.dialogInfo.loading=!1,this.$refs.ruleForm&&this.$nextTick((function(){t.$refs.ruleForm.resetFields()}))},formatterData:function(t){var e=this;t.map((function(t){delete t.id,delete t.parentId,t.children&&t.children.length&&e.formatterData(t.children)}))},submitForm:function(){var t=this;this.$refs.ruleForm.validate((function(e){if(e){t.formatterData(t.treeData);var a={id:t.formData.id,templateName:t.formData.templateName,emergencyStructuredTemplateDetailVO:{title:t.formData.title,content:t.formData.content,parentId:0,children:t.treeData}};console.log(a),t.dialogInfo.loading=!0,t.formData.id?Object(r["f"])(a).then((function(e){t.dialogInfo.loading=!1,200===e.code&&(t.$modal.msgSuccess("编辑成功"),t.resetData(),t.getList())})):Object(r["c"])(a).then((function(e){t.dialogInfo.loading=!1,200===e.code&&(t.$modal.msgSuccess("新增成功"),t.resetData(),t.getList())}))}}))}}},d=s,c=(a("dd5b"),a("2877")),u=Object(c["a"])(d,n,i,!1,null,"23465fd4",null);e["default"]=u.exports},dd5b:function(t,e,a){"use strict";a("7809")}}]);