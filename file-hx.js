const fs = require('fs')
const path = require('path');


function replaceFile(fromUrl,toUrl){
    const rfs = fs.createReadStream(fromUrl)
    // const wfs = fs.createWriteStream('./permission2.js')
    const wfs = fs.createWriteStream(toUrl)  
    rfs.on('data',(chunk)=>{
        wfs.write(chunk)
      })
      rfs.on('end',()=>{
          console.log("over.");
      })
      rfs.on('error',(err)=>{
          console.log(err.message);
      })
}
replaceFile(path.join(__dirname,'/nodejs-hx/permission.js'),path.join(__dirname,"/src/permission.js"))
replaceFile(path.join(__dirname,'/nodejs-hx/login.vue'),path.join(__dirname,"/src/views/login.vue"))
replaceFile(path.join(__dirname,'/nodejs-hx/api/login.js'),path.join(__dirname,"/src/api/login.js"))
replaceFile(path.join(__dirname,'/nodejs-hx/api/login1.js'),path.join(__dirname,"/src/api/login/login.js"))
replaceFile(path.join(__dirname,'/nodejs-hx/other/auth.js'),path.join(__dirname,"/src/utils/auth.js"))
replaceFile(path.join(__dirname,'/nodejs-hx/other/permission.js'),path.join(__dirname,"/src/store/modules/permission.js"))
replaceFile(path.join(__dirname,'/nodejs-hx/other/user.js'),path.join(__dirname,"/src/store/modules/user.js"))
replaceFile(path.join(__dirname,'/nodejs-hx/Navbar.vue'),path.join(__dirname,"/src/layout/components/Navbar.vue"))
replaceFile(path.join(__dirname,'/nodejs-hx/App.vue'),path.join(__dirname,"/src/App.vue"))
replaceFile(path.join(__dirname,'/nodejs-hx/bread.vue'),path.join(__dirname,"/src/components/Breadcrumb/index.vue"))
replaceFile(path.join(__dirname,'/nodejs-hx/router.js'),path.join(__dirname,"/src/router/index.js"))
replaceFile(path.join(__dirname,'/nodejs-hx/variables.scss'),path.join(__dirname,"/src/assets/styles/variables.scss"))
replaceFile(path.join(__dirname,'/nodejs-hx/Logo.vue'),path.join(__dirname,"/src/layout/components/Sidebar/Logo.vue"))
replaceFile(path.join(__dirname,'/nodejs-hx/request.js'),path.join(__dirname,"/src/utils/request.js"))
replaceFile(path.join(__dirname,'/nodejs-hx/settings.js'),path.join(__dirname,"/src/store/modules/settings.js"))
replaceFile(path.join(__dirname,'/nodejs-hx/sidebar.scss'),path.join(__dirname,"/src/assets/styles/sidebar.scss"))
replaceFile(path.join(__dirname,'/nodejs-hx/index.vue'),path.join(__dirname,"/src/layout/index.vue"))
replaceFile(path.join(__dirname,'/nodejs-hx/AppMain.vue'),path.join(__dirname,"/src/layout/components/AppMain.vue"))




