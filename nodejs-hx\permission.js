import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'
import Vue from 'vue'


// 11111111111111111111
NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/auth-redirect', '/bind', '/register']

router.beforeEach((to, from, next) => {
  console.log(to, 'to')
  if (window.__POWERED_BY_QIANKUN__) {
    if (to.path === `/${Vue.prototype.$global.frontUrl}/404`) {
 // if (to.path === '/vue-equipment/404') {
      next()
    }
    console.log(to.path);
    if (!to.path.includes(`${Vue.prototype.$global.frontUrl}`)) {
      next({
        path:`${Vue.prototype.$global.frontUrl}` + to.path
      })
    }
    NProgress.start()
    if (localStorage.getItem('token')) {
      console.log("00000000000000000000");
      to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
      /* has token*/
      if (to.path === '/login') {
        next()
        // NProgress.done()
      } else {
        console.log("11111111111111111111");

        if (store.getters.roles&&store.getters.roles.length === 0) {
          // isRelogin.show = true
          // 判断当前用户是否已拉取完user_info信息
          store.dispatch('GetInfo').then(() => {
            console.log("++++++++++++");
            // isRelogin.show = false
            store.dispatch('GenerateRoutes').then(accessRoutes => {
              // 根据roles权限生成可访问的路由表
              console.log("++++++++++==========");
              router.addRoutes(accessRoutes) // 动态添加可访问路由表
              next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
            })

          }).catch(err => {
            // store.dispatch('LogOut').then(() => {
            //   Message.error(err)
            //   next({ path: '/' })
            // })
            NProgress.done()
            next()
          })
        } else {
          next()
        }
      }
    } else {
      // 没有token
      if (whiteList.indexOf(to.path) != -1) {
        // 在免登录白名单，直接进入
        next()
      } else {
        next()
        // NProgress.done()
        // next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
        // NProgress.done()
      }
    }
  } else {
    console.log("=====================");
    console.log(`/${Vue.prototype.$global.frontUrl}/404`);

    if (to.path === `/${Vue.prototype.$global.frontUrl}/404`) {
      next()
    }
    console.log(to.path, from);

    if (!to.path.includes(`${Vue.prototype.$global.frontUrl}`) && to.path != '/login') {
      console.log("098776");
      return next({
        path: `${Vue.prototype.$global.frontUrl}` + to.path
      })
    }
    NProgress.start()
    if (getToken()) {
      // if (!localStorage.getItem('token')) {
      to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
      /* has token*/
      if (to.path === '/login') {
        next({ path: '/' })
        NProgress.done()
      } else {
        if (store.getters.roles&&store.getters.roles.length === 0) {
          // isRelogin.show = true
          // 判断当前用户是否已拉取完user_info信息
          store.dispatch('GetInfo').then(() => {
            // isRelogin.show = false
            store.dispatch('GenerateRoutes').then(accessRoutes => {
              // 根据roles权限生成可访问的路由表
              router.addRoutes(accessRoutes) // 动态添加可访问路由表
              next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
            })
          }).catch(err => {
            // store.dispatch('LogOut').then(() => {
            //   Message.error(err)
            //   next()
            // })
            localStorage.clear()
            console.log(err)
              next()

            // next('/login')
          })
        } else {
          next()
        }
      }
    } else {
      // 没有token
      if (whiteList.indexOf(to.path) !== -1) {
        // 在免登录白名单，直接进入
        next()
      } else {
        // next()
        next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
        NProgress.done()
      }
    }
  }

})

router.afterEach(() => {
  console.log(1111)
  NProgress.done()
})
