<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import actions from "@/action.js"
export default  {
  name:  'App',
  data(){
    return{
    
    }
  },
    metaInfo() {
        return {
            title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
            titleTemplate: title => {
                return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
            }
        }
    },
 
    methods:{
      
    },
    // qiankun start
     mounted () { 
    actions.onGlobalStateChange(state=>{
      if(JSON.stringify(state)!="{}"){
          var token=""
      console.log(state,'子应用检测数据');
      for(var item in state) {
        token+=state[item]
      }
      localStorage.setItem("token",token)
      }else{
      }
    
    },true) // onGlobalStateChange 第二个参数设置为true，会立即触发一次观察者函数
  // qiankun end


  },
}
</script>
