import request from '@/utils/request'

// 获取查询条件信息
export function showClassifyInfo() {
  return request({
    url: '/equipment/maintain-standing-book/show-classifyInfo',
    method: 'get'
  })
}

//   按条件分页查询设备台账
export function selectPage(pages, size, equipmentId, equipmentName, typeName, groupId, areaId, vendorName, modelName, equipmentStatus, isIntelligent) {
  return request({
    url: '/equipment/maintain-standing-book/select-page',
    method: 'post',
    data: {
      pages: pages,
      size: size,
      equipmentId: equipmentId,
      equipmentName: equipmentName,
      typeName: typeName,
      groupId: groupId,
      areaId: areaId,
      vendorName: vendorName,
      modelName: modelName,
      equipmentStatus: equipmentStatus,
      isIntelligent: isIntelligent,


    }
  })
}
// 查看设备详情
export function detile(equipmentId) {
  return request({
    url: '/equipment/maintain-standing-book/select-detail',
    method: 'get',
    params: {
      equipmentId: equipmentId
    }
  })
}
// 删除设备
export function deleteEquipment(equipmentId) {
  return request({
    url: '/equipment/maintain-standing-book/delete-equipment',
    method: 'get',
    params: {
      equipmentId: equipmentId
    }
  })
}
// 查看所有设备类型
export function selectAllType() {
  return request({
    url: '/equipment/type/select',
    method: 'get',

  })
}
// 根据类型查看自定义属性
export function selectAllAttribute(equipmentTypeId) {
  return request({
    url: '/equipment/maintain-standing-book/select-attribute',
    method: 'get',
    params: {
      equipmentTypeId: equipmentTypeId
    }

  })
}
// 添加设备信息
export function addEquipment(equipmentId, equipmentName, typeId, groupId, vendorId, modelId, isIntelligent, equipmentTag, buyDate, installDate, maintenanceStartDate, maintenanceFinishDate, attributeList, areaId,areaName, longitude, latitude,areaNumber) {
  return request({
    url: '/equipment/maintain-standing-book/add-equipment',
    method: 'post',
    data: {
      equipmentId: equipmentId,
      equipmentName: equipmentName,
      typeId: typeId,
      groupId: groupId,
      vendorId: vendorId,
      modelId: modelId,
      isIntelligent: isIntelligent,
      equipmentTag: equipmentTag,
      buyDate: buyDate,
      installDate: installDate,
      maintenanceStartDate: maintenanceStartDate,
      maintenanceFinishDate: maintenanceFinishDate,
      attributeList:attributeList,
      areaId: areaId,
      areaName:areaName,
      longitude: longitude,
      latitude: latitude,
      areaNumber:areaNumber
    }
  })
}
// 编辑设备信息
export function updateEquipment(equipmentId, equipmentName, typeId, groupId, vendorId, modelId, isIntelligent, equipmentTag, buyDate, installDate, maintenanceStartDate, maintenanceFinishDate, attributeList, areaId,areaName, longitude, latitude,areaNumber) {
  return request({
    url: '/equipment/maintain-standing-book/update-equipment',
    method: 'post',
    data: {
      equipmentId: equipmentId,
      equipmentName: equipmentName,
      typeId: typeId,
      groupId: groupId,
      vendorId: vendorId,
      modelId: modelId,
      isIntelligent: isIntelligent,
      equipmentTag: equipmentTag,
      buyDate: buyDate,
      installDate: installDate,
      maintenanceStartDate: maintenanceStartDate,
      maintenanceFinishDate: maintenanceFinishDate,
      attributeList:attributeList,
      areaId: areaId,
      areaName:areaName,
      longitude: longitude,
      latitude: latitude,
      areaNumber:areaNumber
    }
  })
}
// 根据厂商信息查询型号信息
export function showClassifyModel(vendorId) {
  return request({
    url: '/equipment/maintain-standing-book/show-classifyModel',
    method: 'get',
    params: {
      vendorId:vendorId
    }
  })
}
// 查询子分组
export function selectSonGroup(parentId) {
  return request({
    url: '/equipment/group/select-son',
    method: 'get',
    params: {
      parentId:parentId
    }
  })
}
// 查看区域信息
export function getArea(data) {
  return request({
    url: '/equipment/maintain-standing-book/getArea',
    method: 'get',
    params: data
  })
}
// 查看全部区域信息
export function getAllArea(data) {
  return request({
    url: '/equipment/maintain-standing-book/getArea',
    method: 'get',
    params: data
  })
}
