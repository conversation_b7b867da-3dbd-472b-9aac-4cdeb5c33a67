import request from '@/utils/request'

// 获取告警列表信息
export function getAlarmList(data) {
  return request({
    url: '/safety-v2/alarm-record/select-page',
    method: 'post',
    data:data
  })
}
// 获取告警详情
export function getAlarmDetail(id) {
    return request({
      url: '/equipment/alarm/detail',
      method: 'get',
      params:{
        id:id
      }
    })
  }
  // 获取告警记录
export function getAlarmRecord(data) {
    return request({
      url: '/equipment/alarm/record',
      method: 'get',
      params:data
    })
  }
  // 获取设备地图
  export function getEquipmentMap(data) {
    return request({
      url: '/equipment/overview/equipmentMap',
      method: 'get',
      timeout: 20000,
      params:data
    })
  }
  // 获取部门
  export function getDeptList() {
    return request({
      url: '/equipment/dept/list',
      method: 'get',
    })
  }
    // 获取用户分页
    export function listByDept(data) {
      return request({
        url: '/equipment/user/listByDept',
        method: 'get',
        params:data
      })
    }
    //   关闭告警
  export function closeEquipmentAlarm(id) {
    return request({
      url: '/equipment/alarm/close',
      method: 'post',
      data: {
        id: id
      }
    })
  }
  // 派单
  export function sendDispatch(data) {
    return request({
      url: '/equipment/alarm/dispatch',
      method: 'post',
      data:data
    })
  }

  // 获取设备数量总览
  export function quantity() {
    return request({
      url: '/equipment/overview/quantity',
      method: 'get',
    })
  }
    // 获取类型
    export function countByType() {
      return request({
        url: '/equipment/overview/countByType',
        method: 'get',
      })
    }
    // 获取告警
    export function alarmByTime(data) {
      return request({
        url: '/equipment/overview/alarmByTime',
        method: 'get',
        params:data
      })
    }
    // 获取字典
    export function getEnumList(data) {
      return request({
        url: '/safety-v2/alarm-record/getEnumList',
        method: 'post',
        data:data
      })
    } 
    // 根据文件id 获取文件路径
export function getflieUrl(id) {
  return request({
      url: `/safety-v2/file/getFileUrl/${id}`,
      method: 'get'
  })
}
// 删除告警信息
export function deleteAlarm(ids) {
  return request({
      url: `/safety-v2/alarm-statistics/delete/${ids}`,
      method: 'DELETE'
  })
}
// 查看详情
export function alarmDetile(id) {
  return request({
      url: `/safety-v2/alarm-record/select-detail/${id}`,
      method: 'get'
  })
}
// 处理
export function handleAlarm(data) {
  return request({
    url: '/safety-v2/alarm-record/handleAlarm',
    method: 'post',
    data:data
  })
} 
// 关闭
export function closeAlarm(data) {
  return request({
    url: '/safety-v2/alarm-record/closeAlarm',
    method: 'post',
    data:data
  })
} 
// 查询对应企业对应账号
export function getBondUsersInfoByName(data) {
  return request({
      url: `/information-v2/enterprise-info/getBondUsersInfoByName`,
      method: 'post',
      params:data
  })
}
// 推送
export function sendOrder(data) {
  return request({
    url: '/safety-v2/alarm-statistics/sendOrder',
    method: 'post',
    data:data
  })
} 
// 告警新的分页列表
export function selectPageNew(data) {
  return request({
    url: '/safety-v2/alarm-statistics/select-page',
    method: 'post',
    data:data
  })
}
// 查看详情（新）
export function alarmDetileNew(id) {
  return request({
      url: `/safety-v2/alarm-statistics/select-detail/${id}`,
      method: 'get'
  })
}
// 关闭告警（新）
export function closeAlarmNew(data) {
  return request({
    url: '/safety-v2/alarm-statistics/closeAlarm',
    method: 'post',
    data:data
  })
} 
// 处理新
export function handleAlarmNew(data) {
  return request({
    url: '/safety-v2/alarm-statistics/handleAlarm',
    method: 'post',
    data:data
  })
} 
// 告警数量
export function alarmCount() {
  return request({
    url: '/api-v2/device_alarm_count',
    method: 'post'
  })
} 
// 周告警次数
export function alarmCountWeek() {
  return request({
    url: '/api-v2/device_alarm_count_week',
    method: 'post'
  })
} 
// 月告警次数
export function alarmCountMonth() {
  return request({
    url: '/api-v2/device_alarm_count_month',
    method: 'post'
  })
} 