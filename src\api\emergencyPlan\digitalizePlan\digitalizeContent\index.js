import request from '@/utils/request'

// 获取列表
export function getContentList(query) {
  return request({
    url: '/emergency-plan-content-digitization/list',
    method: 'get',
    params: query
  })
}
// 获取物资列表
export function getMaterialList(query) {
  return request({
    url: '/emergency-material/pageForPlan',
    method: 'get',
    params: query
  })
}
// 获取队伍列表
export function getContingentList(query) {
  return request({
    url: '/emergency_expert_contingent/pageForPlan',
    method: 'get',
    params: query
  })
}
// 获取专家列表
export function getExpertList(query) {
  return request({
    url: '/emergency-expert/list',
    method: 'get',
    params: query
  })
}
// 获取专家擅长类型
export function getEventTypeList(query) {
  return request({
    url: '/emergency-event-type/list',
    method: 'get',
    params: query
  })
}
// 保存
export function saveContent(data) {
  return request({
    url: '/emergency-plan-content-digitization/save',
    method: 'post',
    data
  })
}
// 删除
export function deleteContent(data) {
  return request({
    url: '/emergency-plan-content-digitization/delete',
    method: 'post',
    data
  })
}


