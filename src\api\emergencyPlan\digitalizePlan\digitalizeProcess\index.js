import request from '@/utils/request'

// 详情
export function getFlowDetail(query) {
  return request({
    url: '/emergency-plan-flow-digitization/detail',
    method: 'get',
    params: query
  })
}
// 保存
export function saveFlow(data) {
  return request({
    url: '/emergency-plan-flow-digitization/save',
    method: 'post',
    data
  })
}
// 保存
export function updateFlow(data) {
  return request({
    url: '/emergency-plan-flow-digitization/update',
    method: 'post',
    data
  })
}

