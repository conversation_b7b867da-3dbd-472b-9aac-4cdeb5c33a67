import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
// 获取tree
export function searchTreeData() {
  return request({
    url: '/emergency-event-type/tree',
    method: 'get',
  })
}
// 获取tree分支上的标签
export function getLabels(eventTypeId) {
  return request({
    url: '/emergency-event-type-label/selectById',
    method: 'get',
    params: {
      eventTypeId: eventTypeId
    }
  })
}
// 标签新增
export function tagAdd(eventTypeId, label) {
  return request({
    url: '/emergency-event-type-label/save',
    method: 'post',
    data: {
      eventTypeId: eventTypeId,
      label: label,
    }
  })
}
// 标签删除
export function tagDel(id) {
  return request({
    url: '/emergency-event-type-label/deleteById',
    method: 'post',
    data: {
      id: id,
    }
  })
}