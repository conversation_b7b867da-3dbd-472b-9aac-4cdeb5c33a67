import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
// 获取tree
export function searchTreeData() {
  return request({
    url: '/emergency-event-type/tree',
    method: 'get',
  })
}
// 获取节点事件参数详情
export function searchParameter(eventTypeId) {
  return request({
    url: '/emergency-event-type-parameter/selectById',
    method: 'get',
    params: {
      eventTypeId: eventTypeId
    }
  })
}
// 标签新增删除
export function tagAddOrDel(data) {
  return request({
    url: '/emergency-event-type-parameter/saveOrUpdate',
    method: 'post',
    data: data
  })
}