import request from '@/utils/request'

// 获取列表（分页）
export function getPlanList(query) {
  return request({
    url: '/emergency_plan/page',
    method: 'get',
    params: query
  })
}
// 获取所有预案
export function getAllPlanList(query = {}) {
  return request({
    url: '/emergency_plan/getPlans',
    method: 'get',
    params: query
  })
}
// 新增预案
export function planAdd(data) {
  return request({
    url: '/emergency_plan/save',
    method: 'post',
    data
  })
}
// 修改预案
export function planUpdate(data) {
  return request({
    url: '/emergency_plan/update',
    method: 'post',
    data
  })
}
// 删除
export function planDelete(data) {
  return request({
    url: '/emergency_plan/deleteById',
    method: 'post',
    data
  })
}
// 预案详情
export function planDetail(query) {
  return request({
    url: '/emergency_plan/detail',
    method: 'get',
    params: query
  })
}
// 责人列表下拉
export function getStaffList(query = {}) {
  return request({
    url: '/staff/list',
    method: 'get',
    params: query
  })
}
export function handledownload(arr) {
  return request({
      url: `/file/downloadFile?bucket=${arr[1]}&path=${arr[2]}&fileName=${arr[3]}`,
      method: 'get',
      responseType: 'blob',
  })
}