import request from '@/utils/request'

// 获取列表（分页）
export function getTemplateList(query) {
  return request({
    url: '/emergency_structured_template/page',
    method: 'get',
    params: query
  })
}
// 获取所有列表
export function getAllTemplateList() {
  return request({
    url: '/emergency_structured_template/List',
    method: 'get'
  })
}
// 新增预案模板
export function templateAdd(data) {
  return request({
    url: '/emergency_structured_template/save',
    method: 'post',
    data
  })
}
// 修改预案模板
export function templateUpdate(data) {
  return request({
    url: '/emergency_structured_template/update',
    method: 'post',
    data
  })
}
// 删除
export function templateDelete(data) {
  return request({
    url: '/emergency_structured_template/delete',
    method: 'post',
    data
  })
}
// 预案模板详情
export function templateDetail(query) {
  return request({
    url: '/emergency_structured_template/detail',
    method: 'get',
    params: query
  })
}
