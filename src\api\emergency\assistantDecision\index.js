import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
export function page(query) {
    return request({
        url: '/emergency-assistant-decision/list',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency-law/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/emergency_refuge/update',
        method: 'post',
        data: data
    })
}
export function deleteById(data) {
    return request({
        url: '/emergency-law/deleteById',
        method: 'post',
        data: data
    })
}
export function largeCategory(data) {
    return request({
        url: '/emergency-type/tree',
        method: 'get',
        params: data
    })
}
export function eventType(data) {
    return request({
        url: '/emergency-event-type/tree',
        method: 'get',
        params: data
    })
}

export function dictGetDict(params) {
  return request({
    url: '/dict/getDict',
    method: 'get',
    params:params
  })
}
export function dictGetMatterMsds(params) {
  return request({
    url: '/dict/getMatterMsds',
    method: 'get',
    params:params
  })
}
//次生分析
export function analysis(params) {
  return request({
    url: '/emergency-assistant-decision/analysis',
    method: 'get',
    params:params
  })
}
