import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
// 总览接口
export function view(query) {
    return request({
        url: '/platform_dict/selectResourceOverview',
        method: 'get',
        params: query
    })
}

// 节点查询
export function selectNodes(query) {
    return request({
        url: '/emergency-resource-overview/list',
        method: 'get',
        params: query
    })
}

// 获取视频播放流
export function getVideoStreaming(data) {
    return request({
        url: '/emergency-monitor/getVideoStreaming',
        method: 'post',
        data: data,
    })
}

// 应急物资仓库级别统计
export function supplyDepotLevel(query) {
    return request({
        url: '/emergency-resource-overview/supplyDepotLevel',
        method: 'get',
        params: query
    })
}