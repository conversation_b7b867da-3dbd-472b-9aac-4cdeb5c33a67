import request from '@/utils/request'
export function page(query) {
    return request({
        url: '/emergency-alarm-event/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency-alarm-event/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/emergency-alarm-event/report',
        method: 'post',
        data: data
    })
}
export function deleteById(data) {
    return request({
        url: '/emergency-alarm-event/deleteById',
        method: 'post',
        data: data
    })
}

export function handledownload(arr) {
    return request({
        url: `/file/downloadFile?bucket=${arr[1]}&path=${arr[2]}&fileName=${arr[3]}`,
        method: 'get',
        responseType: 'blob',
    })
}