import request from '@/utils/request'
// 左侧事件列表
export function page(query) {
    return request({
        url: '/emergency-drill-plan/list',
        method: 'get',
        params: query
    })
}

// 事件类型下拉
export function getTree(data) {
    return request({
        url: '/emergency-event-type/tree',
        method: 'get',
        params: data
    })
}

// 根据类型获取资源位置
export function overviewList(data) {
    return request({
        url: '/emergency-resource-overview/list',
        method: 'get',
        params: data
    })
}

// 事件报告按钮
export function reportDetail(query) {
    return request({
        url: '/emergency-event-submit-manage/detail',
        method: 'get',
        params: query
    })
}

// 预案查询
export function planList(query) {
    return request({
        url: '/emergency_plan/detail',
        method: 'get',
        params: query
    })
}

// 预案对应等级查询
export function levelList(query) {
    return request({
        url: '/emergency-plan-response-level/list',
        method: 'get',
        params: query
    })
}

// 启动预案确认提交
export function planSave(data) {
    return request({
        url: '/emergency-plan-response/save',
        method: 'post',
        data: data
    })
}

// 已启动预案查询
export function showPlan(query) {
    return request({
        url: '/emergency-plan-response/show-plan',
        method: 'get',
        params: query
    })
}

// 点击预案查询步骤
export function showProcess(query) {
    return request({
        url: '/emergency-plan-response/show-process',
        method: 'get',
        params: query
    })
}

// 点击预案查询时间线
export function showProcessRight(query) {
    return request({
        url: '/emergency-plan-response/show-process-right',
        method: 'get',
        params: query
    })
}

// 开始处置or结束步骤
export function stepStatus(data) {
    return request({
        url: '/emergency-plan-response/update',
        method: 'post',
        data: data
    })
}

// 队伍指派任务
export function contingentUpdate(data) {
    return request({
        url: '/emergency-plan-response-contingent/update',
        method: 'post',
        data: data
    })
}

// 获取事件可指派队伍
export function contingentList(query) {
    return request({
        url: '/emergency_expert_contingent/list',
        method: 'get',
        params: query
    })
}

// 增派临时任务确认
export function taskSave(data) {
    return request({
        url: '/emergency-temporary-task/save',
        method: 'post',
        data: data
    })
}

// 获取事件可调度仓库
export function depotList(query) {
    return request({
        url: '/emergency-supply-depot/list',
        method: 'get',
        params: query
    })
}

// 根据仓库id查询物资列表
export function listOfDepot(query) {
    return request({
        url: '/emergency-material/listOfDepot',
        method: 'get',
        params: query
    })
}

// 根据业务id获取人员调度及物资调度列表
export function temporaryTasklist(query) {
    return request({
        url: '/emergency-temporary-task/list',
        method: 'get',
        params: query
    })
}

// 根据id获取任务详情
export function taskDetail(query) {
    return request({
        url: '/emergency-temporary-task/detail',
        method: 'get',
        params: query
    })
}

// 临时任务状态变更
export function taskUpdate(data) {
    return request({
        url: '/emergency-temporary-task/update',
        method: 'post',
        data: data
    })
}

// 新增次生分析保存
export function analysisSave(data) {
    return request({
        url: '/emergency-secondary-analysis/save',
        method: 'post',
        data: data
    })
}

// 获取次生分析列表
export function analysisPage(query) {
    return request({
        url: '/emergency-secondary-analysis/page',
        method: 'get',
        params: query
    })
}

// 获取次生分析详情
export function analysisDetail(query) {
    return request({
        url: '/emergency-secondary-analysis/detail',
        method: 'get',
        params: query
    })
}

// 删除次生分析数据
export function analysisDelete(query) {
    return request({
        url: '/emergency-secondary-analysis/delete',
        method: 'get',
        params: query
    })
}

// 结束救援
export function recordSave(data) {
    return request({
        url: '/emergency-execution-record/save',
        method: 'post',
        data: data
    })
}

// 获取执行记录
export function recordDetail(query) {
    return request({
        url: '/emergency-execution-record/detail',
        method: 'get',
        params: query
    })
}