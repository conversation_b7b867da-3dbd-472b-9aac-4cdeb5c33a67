import request from '@/utils/request'
export function page(query) {
    return request({
        url: '/emergency-drill-assess/page',
        method: 'get',
        params: query
    })
}

export function detail(query) {
    return request({
        url: '/emergency-drill-assess/selectById',
        method: 'get',
        params: query
    })
}

export function update(data) {
    return request({
        url: '/emergency-drill-assess/update',
        method: 'post',
        data: data
    })
}

export function deleteById(data) {
    return request({
        url: '/emergency_hygiene/deleteById',
        method: 'post',
        data: data
    })
}
export function handledownload(arr) {
    return request({
        url: `/file/downloadFile?bucket=${arr[1]}&path=${arr[2]}&fileName=${arr[3]}`,
        method: 'get',
        responseType: 'blob',
    })
}