import request from '@/utils/request'
// 列表分页
export function page(query) {
    return request({
        url: '/emergency-drill-plan/page',
        method: 'get',
        params: query
    })
}

// 新增演练计划
export function save(data) {
    return request({
        url: '/emergency-drill-plan/save',
        method: 'post',
        data: data
    })
}

// 编辑演练计划
export function update(data) {
    return request({
        url: '/emergency-drill-plan/update',
        method: 'post',
        data: data
    })
}

// 事件类型下拉
export function getTree(data) {
    return request({
        url: '/emergency-event-type/tree',
        method: 'get',
        params: data
    })
}

// 根据事件类型id请求预案
export function plansOfEvent(data) {
    return request({
        url: '/emergency_plan/plansOfEventTypeId',
        method: 'get',
        params: data
    })
}

// 根据预案id请求响应等级
export function levelList(data) {
    return request({
        url: '/emergency-plan-response-level/list',
        method: 'get',
        params: data
    })
}

// 根据id删除应急演练计划
export function deleteById(data) {
    return request({
        url: '/emergency-drill-plan/remove',
        method: 'get',
        params: data
    })
}

// 根据id查询列表详情
export function drillDetail(data) {
    return request({
        url: '/emergency-drill-plan/detail',
        method: 'get',
        params: data
    })
}
export function handledownload(arr) {
    return request({
        url: `/file/downloadFile?bucket=${arr[1]}&path=${arr[2]}&fileName=${arr[3]}`,
        method: 'get',
        responseType: 'blob',
    })
}