import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
// 列表分页
export function page(query) {
    return request({
        url: '/emergency-duty-log/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency-duty-log/save',
        method: 'post',
        data: data
    })
}
// 编辑广播点位
export function getDetails(data) {
    return request({
        url: '/emergency-duty-log/selectById',
        method: 'post',
        data: data
    })
}
