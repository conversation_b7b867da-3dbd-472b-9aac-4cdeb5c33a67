import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
// 列表分页
export function page(query) {
    return request({
        url: '/emergency-event-submit-manage/page',
        method: 'get',
        params: query
    })
}
export function detail(query) {
    return request({
        url: '/emergency-event-submit-manage/detail',
        method: 'get',
        params: query
    })
}
// 暂存
export function save(data) {
    return request({
        url: '/emergency-event-submit-manage/staging',
        method: 'post',
        data: data
    })
}
// 事件类型结构树获取
export function getTree(data) {
    return request({
        url: '/emergency-event-type/tree',
        method: 'get',
        params: data
    })
}
// 事件标签获取
export function getLabel(data) {
    return request({
        url: '/emergency-event-type-label/selectById',
        method: 'get',
        params: data
    })
}
// 上报
export function escalation(data) {
    return request({
        url: '/emergency-event-submit-manage/escalation',
        method: 'post',
        data: data
    })
}

export function deleteById(data) {
    return request({
        url: '/emergency-event-submit-manage/delete',
        method: 'get',
        params: data
    })
}
// 下载接口
export function handledownload(arr) {
    return request({
        url: `/file/downloadFile?bucket=${arr[1]}&path=${arr[2]}&fileName=${arr[3]}`,
        method: 'get',
        responseType: 'blob',
    })
}
export function repeatNextevent(data) {
    return request({
        url: '/emergency-event-submit-manage/repeat',
        method: 'post',
        data: data
    })
}
export function detailEvent(id) {
    return request({
        url: `/emergency-event-submit-manage/repeatList?id=${id}`,
        method: 'post'
    })
}
