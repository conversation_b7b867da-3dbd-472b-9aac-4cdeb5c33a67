import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
export function page(query) {
    return request({
        url: '/emergency-classic-cases/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency-classic-cases/save',
        method: 'post',
        data: data
    })
}
export function handledownload(arr) {
    return request({
        url: `/file/downloadFile?bucket=${arr[1]}&path=${arr[2]}&fileName=${arr[3]}`,
        method: 'get',
        responseType: 'blob',
    })
}
export function deleteById(data) {
    return request({
        url: '/emergency-classic-cases/deleteById',
        method: 'post',
        data: data
    })
}

export function eventType(data) {
    return request({
        url: '/emergency-event-type/tree',
        method: 'get',
        params: data
    })
}
