import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
export function page(query) {
    return request({
        url: '/emergency-law/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency-law/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/emergency_refuge/update',
        method: 'post',
        data: data
    })
}
export function deleteById(data) {
    return request({
        url: '/emergency-law/deleteById',
        method: 'post',
        data: data
    })
}
export function largeCategory(data) {
    return request({
        url: '/emergency-type/tree',
        method: 'get',
        params: data
    })
}
export function getSubclass(data) {
    return request({
        url: '/emergency-type/list',
        method: 'get',
        params: data
    })
}
export function handledownload(arr) {
    return request({
        url: `/file/downloadFile?bucket=${arr[1]}&path=${arr[2]}&fileName=${arr[3]}`,
        method: 'get',
        responseType: 'blob',
    })
}
