import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
// 列表分页
export function page(query) {
    return request({
        url: '/emergency-broadcast/list',
        method: 'get',
        params: query
    })
}

// 编辑广播点位
export function update(data) {
    return request({
        url: '/emergency_broadcast/update',
        method: 'post',
        data: data
    })
}
// 所属区域结构树获取
export function getTree(data) {
    return request({
        url: '/emergency-monitor/tree',
        method: 'get',
        params: data
    })
}