import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
export function page(query) {
    return request({
        url: '/emergency_communication/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency_communication/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/emergency_communication/update',
        method: 'post',
        data: data
    })
}
export function deleteById(data) {
    return request({
        url: '/emergency_communication/deleteById',
        method: 'post',
        data: data
    })
}

