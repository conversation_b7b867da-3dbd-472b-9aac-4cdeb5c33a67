// 应急事件复盘
import request from "@/utils/request";

export function page(query) {
  return request({
      url: '/emergency_event_replay/page',
      method: 'get',
      params: query
  })
}
export function selectById(query) {
  return request({
      url: '/emergency_event_replay/selectById',
      method: 'get',
      params: query
  })
}
export function save(data) {
  return request({
      url: '/emergency_event_replay/save',
      method: 'post',
      data: data
  })
}
export function update(data) {
  return request({
      url: '/emergency_event_replay/update',
      method: 'post',
      data: data
  })
}

export function deleteById(data) {
  return request({
      url: '/emergency_event_replay/deleteById',
      method: 'post',
      data: data
  })
}

// 预案列表
export function selectEmergencyPlans() {
  return request({
      url: '/emergency_plan/selectEmergencyPlans',
      method: 'get',
  })
}
