// 应急知识库接口
import request from "@/utils/request";

export function page(query) {
  return request({
      url: '/emergency_knowledge/page',
      method: 'get',
      params: query
  })
}
export function save(data) {
  return request({
      url: '/emergency_knowledge/save',
      method: 'post',
      data: data
  })
}
export function update(data) {
  return request({
      url: '/emergency_knowledge/update',
      method: 'post',
      data: data
  })
}

export function deleteById(data) {
  return request({
      url: '/emergency_knowledge/deleteById',
      method: 'post',
      data: data
  })
}

export function download(query) {
  return request({
      url: '/api/file/downloadFile',
      method: 'get',
      params: query
  })
}
