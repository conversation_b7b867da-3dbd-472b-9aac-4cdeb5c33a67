import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
export function page(query) {
    return request({
        url: '/emergency-protective/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency-protective/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/emergency-protective/update',
        method: 'post',
        data: data
    })
}
export function deleteById(data) {
    return request({
        url: '/emergency-protective/deleteById',
        method: 'post',
        data: data
    })
}

