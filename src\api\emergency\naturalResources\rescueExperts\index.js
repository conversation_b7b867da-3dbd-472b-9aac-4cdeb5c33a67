import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
export function page(query) {
    return request({
        url: '/emergency-expert/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency-expert/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/emergency-expert/update',
        method: 'post',
        data: data
    })
}
export function deleteById(data) {
    return request({
        url: '/emergency-expert/deleteById',
        method: 'post',
        data: data
    })
}
export function eventType(data) {
    return request({
        url: '/emergency-event-type/list',
        method: 'get',
        params: data
    })
}
export function handledownload(arr) {
    return request({
        url: `/file/downloadFile?bucket=${arr[1]}&path=${arr[2]}&fileName=${arr[3]}`,
        method: 'get',
        responseType: 'blob',
    })
}
//
// //导出救援专家-模板
export function exportTemplateExpert() {
    return request({
        url: '/emergency-expert/exportTemplate',
        method: 'post',
        responseType: 'blob',
    })
}
//导出救援专家
export function exportExpert(params) {
    return request({
        url: '/emergency-expert/export',
        method: 'post',
        data:params,
        responseType: 'blob',
    })
}