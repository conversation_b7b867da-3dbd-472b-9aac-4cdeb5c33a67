import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
export function page(query) {
    return request({
        url: '/emergency-hidden-trouble/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency-hidden-trouble/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/emergency-hidden-trouble/update',
        method: 'post',
        data: data
    })
}
export function deleteById(data) {
    return request({
        url: '/emergency-hidden-trouble/deleteById',
        method: 'post',
        data: data
    })
}

