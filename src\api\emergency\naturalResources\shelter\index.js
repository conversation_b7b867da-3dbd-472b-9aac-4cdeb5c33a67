import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
export function page(query) {
    return request({
        url: '/emergency_refuge/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/emergency_refuge/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/emergency_refuge/update',
        method: 'post',
        data: data
    })
}
export function deleteById(data) {
    return request({
        url: '/emergency_refuge/deleteById',
        method: 'post',
        data: data
    })
}

