import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
// 列表分页
export function page(query) {
    return request({
        url: '/emergency_expert_contingent/queryByPage',
        method: 'get',
        params: query
    })
}
// 列表分-新
export function pageList(query) {
    return request({
        url: '/emergency_expert_contingent/pageList',
        method: 'post',
        data: query
    })
}

// 新增队伍类型
export function save(data) {
    return request({
        url: '/emergency_expert_contingent/save',
        method: 'post',
        data: data
    })
}

// 查询队伍人员
export function ranksDetail(query){
    return request({
        url: '/emergency_expert_contingent/selectById',
        method: 'get',
        params:query,
    })
}

// 新增队伍人员
export function saveStaff(data) {
    return request({
        url: '/emergency_expert_contingent/saveStaff',
        method: 'post',
        data: data
    })
}

// 删除队伍人员
export function removeStaff(data) {
    return request({
        url: '/emergency_expert_contingent/remove',
        method: 'post',
        data: data
    })
}

// 编辑队伍详情
export function update(data) {
    return request({
        url: '/emergency_expert_contingent/update',
        method: 'post',
        data: data
    })
}

// 删除队伍
export function deleteById(data) {
    return request({
        url: '/emergency_expert_contingent/deleteById',
        method: 'post',
        data: data
    })
}
