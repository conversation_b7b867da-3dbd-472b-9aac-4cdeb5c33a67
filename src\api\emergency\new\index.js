import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

//应急车辆-新增车辆
export function addVehicle(data) {
    return request({
        url: '/emergencyCar/add',
        method: 'post',
        data: data
    })
}

//应急车辆-分页查询
export function vehiclePage(params) {
  return request({
    url: '/emergencyCar/page',
    method: 'get',
    params:params
  })
}
//应急车辆-车辆详情
export function vehicleDetail(params) {
  return request({
    url: '/emergencyCar/detail',
    method: 'get',
    params:params
  })
}

//应急车辆-出动车辆
export function dispatchVehicle(data) {
    return request({
        url: '/emergencyCar/dispatch',
        method: 'post',
        data: data
    })
}
//应急车辆-删除车辆
export function deleteVehicle(data) {
    return request({
        url: '/emergencyCar/delete',
        method: 'post',
        data: data
    })
}
//应急车辆-出动记录
export function vehicleDispatchRecord(params) {
    return request({
      url: '/emergencyCar/record',
      method: 'get',
      params:params
    })
}
  //应急车辆-归还车辆
export function returnVehicle(data) {
    return request({
        url: '/emergencyCar/return',
        method: 'post',
        data: data
    })
}
//导出应急车辆-模板
export function exportTemplateCar() {
    return request({
        url: '/emergencyCar/exportTemplate',
        method: 'post',
        responseType: 'blob',
    })
}
//导出应急车辆
export function exportCar(params) {
    return request({
        url: '/emergencyCar/export',
        method: 'post',
        data:params,
        responseType: 'blob',
    })
}
//企业应急演练-列表
export function enterpriseDrillPage(params) {
    return request({
      url: '/enterpriseDrill/page',
      method: 'get',
      params:params
    })
}
//企业应急演练-新增
export function addEnterpriseDrill(params) {
    return request({
      url: '/enterpriseDrill/add',
      method: 'post',
      data:params
    })
}
//企业应急演练-修改
export function updateEnterpriseDrill(params) {
    return request({
      url: '/enterpriseDrill/update',
      method: 'post',
      data:params
    })
}
//区域树
export function emergencyAreaTree(params) {
    return request({
      url: '/emergencyArea/tree',
      method: 'get',
      params:params
    })
}
//企业应急演练-详情
export function enterpriseDrillDetail(params) {
    return request({
      url: '/enterpriseDrill/detail',
      method: 'get',
      params:params
    })
}
//企业应急演练-详情
export function enterpriseDrillDelete(params) {
    return request({
      url: '/enterpriseDrill/delete',
      method: 'post',
      data:params
    })
}
//导出企业应急演练-模板
export function exportTemplateDrill(params) {
    return request({
        url: '/enterpriseDrill/exportTemplate',
        method: 'post',
        data:params,
        responseType: 'blob',
    })
}
//导出企业应急演练
export function exportEnterpriseDrill(params) {
    return request({
        url: '/enterpriseDrill/export',
        method: 'post',
        data:params,
        responseType: 'blob',
    })
}
//部门树
export function emergencyOrganization(params) {
    return request({
      url: '/emergencyOrganization/tree',
      method: 'get',
      params:params
    })
}
//化工企业预案概览
export function overview(params) {
    return request({
      url: '/emergency-plan-manage-firm/overviewHead',
      method: 'get',
      params:params
    })
}
//化工企业预案-分析图
export function overviewLeft(params) {
    return request({
      url: '/emergency-plan-manage-firm/overviewLeft',
      method: 'get',
      params:params
    })
}
//化工企业预案-分析图
export function overviewRight(params) {
    return request({
      url: '/emergency-plan-manage-firm/overviewRight',
      method: 'get',
      params:params
    })
}
//化工企业预案列表
export function planManageFirmList(params) {
    return request({
        url: '/emergency-plan-manage-firm/pageList',
        method: 'post',
        data:params,
    })
}
//化工企业预案列表
export function savePlanManageFirm(params) {
    return request({
        url: '/emergency-plan-manage-firm/save',
        method: 'post',
        data:params,
    })
}
//获取字典
export function getDict(params) {
    return request({
      url: '/dict/getDict',
      method: 'get',
      params:params
    })
}
//化工企业预案详情
export function view(id) {
    return request({
        url: `/emergency-plan-manage-firm/view/${id}`,
        method: 'get',
    })
}
//化工企业预案删除
export function del(id) {
    return request({
        url: `/emergency-plan-manage-firm/del/${id}`,
        method: 'post',
    })
}
//化工企业预案编辑
export function editPlanManageFirm(params) {
    return request({
        url: '/emergency-plan-manage-firm/edit',
        method: 'post',
        data:params,
    })
}
//导出化工企业预案-模板
export function exportTemplatePlan() {
    return request({
        url: '/emergency-plan-manage-firm/exportTemplate',
        method: 'post',
        responseType: 'blob',
    })
}
//导出化工企业预案
export function exportPlan(params) {
    return request({
        url: '/emergency-plan-manage-firm/export',
        method: 'post',
        data:params,
        responseType: 'blob',
    })
}
//大气监测站
export function airEquipment(params) {
    return request({
      url: '/emergency-assistant-decision/airEquipment',
      method: 'get',
      params:params
    })
}
//气体详情
export function airData(params) {
    return request({
      url: '/emergency-assistant-decision/airData',
      method: 'get',
      params:params
    })
}
//避难疏解
export function dispatch(params) {
    return request({
      url: '/emergency_refuge/dispatch',
      method: 'post',
      data:params
    })
}
//气体详情
export function getPath(params) {
    return request({
      url: '/map/getPath',
      method: 'get',
      params:params
    })
}
//队伍详情
export function getTeamListCom(params) {
    return request({
      url: '/emergency_expert_contingent/getTeamList',
      method: 'get',
      params:params
    })
}
//参演专家
export function getExpertListNew(params) {
    return request({
      url: '/emergency-expert/getExpertList',
      method: 'get',
      params:params
    })
}

//导出救援队伍-模板
export function exportTemplateExpert() {
    return request({
        url: '/emergency_expert_contingent/exportTemplate',
        method: 'post',
        responseType: 'blob',
    })
}
//导出救援队伍
export function exportExpert(params) {
    return request({
        url: '/emergency_expert_contingent/export',
        method: 'post',
        data:params,
        responseType: 'blob',
    })
}