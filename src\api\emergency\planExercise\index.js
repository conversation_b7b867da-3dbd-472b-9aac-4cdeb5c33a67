import request from "@/utils/request";
export function page(query) {
  return request({
    url: "/emergency_plan/page",
    method: "get",
    params: query,
  });
}
export function save(data) {
  return request({
    url: "/emergency_plan/save",
    method: "post",
    data: data,
  });
}
export function update(data) {
  return request({
    url: "/emergency_plan/update",
    method: "post",
    data: data,
  });
}
export function deleteById(data) {
  return request({
    url: "/emergency_plan/deleteById",
    method: "post",
    data: data,
  });
}
// 获取专家树列表数据
export function getTree() {
  return request({
    url: "/emergency_expert_contingent/tree",
    method: "get",
  });
}
// 获取避难场所列表数据
export function getshelter() {
  return request({
    url: "/emergency_refuge/list",
    method: "get",
  });
}
// 预案推演资源分页
export function selectEmergencyMaterialByPage(query) {
  return request({
    url: "/emergency_material/selectEmergencyMaterialByPage",
    method: "get",
    params: query,
  });
}

// 预案详情
export function selectById(query) {
  return request({
    url: "/emergency_plan/selectById",
    method: "get",
    params: query,
  });
}

// 节点新增
export function nodeSave(data) {
  return request({
    url: "/emergency_plan_node/save",
    method: "post",
    data: data,
  });
}
