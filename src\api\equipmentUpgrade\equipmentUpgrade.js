import request from '@/utils/request'

//   按条件分页查询升级
export function selectUpgroade(pages, size,taskName,taskStatus) {
    return request({
      url: '/equipment/upgrade/select-page',
      method: 'post',
      data: {
        pages: pages,
        size: size,
        taskName: taskName,
        taskStatus: taskStatus,
  
      }
    })
  }
//   查看详情
  export function selectUpgroadeDetail(pages, size,taskId) {
    return request({
      url: '/equipment/upgrade/select-detail',
      method: 'post',
      data: {
        pages: pages,
        size: size,
        taskId:taskId
      }
    })
  }
//   新增任务
export function addTask(taskName, timePlan,originalVersion,upgradeVersion,remark,file,equipmentInfoList) {
    return request({
      url: '/equipment/upgrade/add',
      method: 'post',
      data: {
        taskName: taskName,
        timePlan: timePlan,
        originalVersion:originalVersion,
        upgradeVersion:upgradeVersion,
        remark:remark,
        file:file,
        equipmentInfoList:equipmentInfoList
      }
    })
  }
//   取消升级
export function cancelTask(taskId) {
    return request({
      url: '/equipment/upgrade/cancel',
      method: 'get',
      params: {
        taskId:taskId
      }
    })
  }
//   修改设备状态
export function changeStatus(equipmentId,status,taskId) {
    return request({
      url: '/equipment/upgrade/change-status',
      method: 'post',
      data: {
        equipmentId:equipmentId,
        status:status,
        taskId:taskId
      }
    })
  }
  // 上传文件
  export function uploadFile(file) {
    return request({
      url: '/equipment/upgrade/upload-file',
      method: 'get',
      params: {
        file:file
      }
    })
  }