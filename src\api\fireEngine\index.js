import request from '@/utils/request'
// 事件类型结构树获取
export function getTree(data) {
    return request({
        url: '/emergency/emergency-event-type/tree',
        method: 'get',
        params: data
    })
}
// 分页列表
export function alarmPage(data) {
    return request({
        url: '/firecontrol/alarm/page',
        method: 'get',
        params: data
    })
}
// 获取事件类型列表
export function typeList(data) {
    return request({
        url: '/firecontrol/alarm/typeList',
        method: 'get',
        params: data
    })
}
// 获取事件等级列表
export function levelList(data) {
    return request({
        url: '/firecontrol/alarm/levelList',
        method: 'get',
        params: data
    })
}
// 事件上报
export function handling(data) {
    return request({
        url: '/firecontrol/alarm/handling',
        method: 'post',
        data: data
    })
}
// 关闭告警
export function closeAlarm1(data) {
    return request({
        url: '/firecontrol/alarm/close',
        method: 'post',
        data: data
    })
}
// 事件标签列表
export function labelList(data) {
    return request({
        url: '/firecontrol/alarm/labelList',
        method: 'get',
        params: data
    })
}
// 事件详情
export function eventDetail(data) {
    return request({
        url: '/firecontrol/alarm/eventDetail',
        method: 'get',
        params: data
    })
}
// 文件下载
export function downloadOneFile(a,b,c) {
    return request({
        url: `/firecontrol/file/downloadFile?bucket=${a}&path=${b}&fileName=${c}`,
        method: 'get',
        responseType: 'blob'
    })
}