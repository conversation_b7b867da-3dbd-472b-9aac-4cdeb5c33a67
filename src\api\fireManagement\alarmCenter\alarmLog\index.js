import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-device-alarm/page',
        method: 'get',
        params: data
    })
}
export function detail(data) {
    return request({
        url: '/firecontrol-device-alarm/detail',
        method: 'get',
        params: data
    })
}
// 获取视频播放流
export function getVideoStreaming(data) {
    return request({
        url: '/monitor/getVideoStreaming',
        method: 'post',
        data: data,
    })
}