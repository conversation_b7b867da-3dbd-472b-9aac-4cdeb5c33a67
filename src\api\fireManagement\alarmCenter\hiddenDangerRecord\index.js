import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-hidden-danger/page',
        method: 'get',
        params: data
    })
}
export function list() {
    return request({
        url: '/firecontrol-area/list',
        method: 'get',

    })
}
export function save(data) {
    return request({
        url: '/firecontrol-hidden-danger/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-hidden-danger/update',
        method: 'post',
        data: data
    })
}
export function del(data) {
    return request({
        url: '/firecontrol-hidden-danger/delete',
        method: 'post',
        data: data
    })
}