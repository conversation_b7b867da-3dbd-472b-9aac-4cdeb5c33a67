import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-basic-configuration/page',
        method: 'get',
        params: data
    })
}
export function list(data) {
    return request({
        url: '/firecontrol-device-alarm/firecontrolDevice',
        method: 'get',
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-basic-configuration/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-basic-configuration/update',
        method: 'post',
        data: data
    })
}
export function del(data) {
    return request({
        url: '/firecontrol-basic-configuration/deleteById',
        method: 'post',
        data: data
    })
}