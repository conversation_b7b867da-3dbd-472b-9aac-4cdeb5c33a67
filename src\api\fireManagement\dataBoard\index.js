import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";
//信息统计
export function informationCount() {
    return request({
        url: '/firecontrol-board/informationCount',
        method: 'get',
    })
}

// 获取当前值班信息
export function getDuty(data) {
    return request({
        url: 'firecontrol-check-record/duty',
        megetthod: 'get',
        params:data,
    })
}

//消防事件查询条件
export function getFireAlarm(query) {
    return request({
        url: '/firecontrol-device-alarm/selectAlarmKanbanBoard',
        method: 'get',
        params: query
    })
}

//消防耗材
export function firecontrolResourceCount(query) {
    return request({
        url: '/firecontrol-board/firecontrolResourceCount',
        method: 'get',
        params: query
    })
}

//消防设备
export function devicePage(query) {
    return request({
        url: '/firecontrol-device/page',
        method: 'get',
        params: query
    })
}

//部门树
export function tree() {
    return request({
        url: '/organization/tree',
        method: 'get',
    })
}

//安装位置
export function areaList() {
    return request({
        url: '/area/tree',
        method: 'get',
    })
}

// 获取值班人员
export function getDutyPerson(data) {
    return request({
        url: '/firecontrol-check_post/getDutyPerson',
        method: 'get',
        params:data
    })
}

// 查询在岗情况
export function getDutyTime(data) {
    return request({
        url: '/firecontrol-check_post/getDutyTime',
        method: 'post',
        data:data,
    })
}

// 在岗率
export function getOnDutyRate(data) {
    return request({
        url: '/firecontrol-check_post/getOnDutyRate',
        method: 'post',
        data:data,
    })
}
// 消控室lsit
export function fireControl(data) {
    return request({
        url: '/firecontrol-room/list',
        method: 'get',
        params:data,
    })
}
// 在岗率
export function relatedMonitor(data) {
    return request({
        url: '/firecontrol-room/relatedMonitor',
        method: 'get',
        params:data,
    })
}
// 获取视频播放流
export function getVideoStreaming(data) {
    return request({
        url: '/monitor/getVideoStreaming',
        method: 'post',
        data: data,
    })
}