import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-check_post/page',
        method: 'get',
        params: data
    })
}

export function roomList() {
    return request({
        url: '/firecontrol-room/roomList',
        method: 'get',
    })
}

export function save(data) {
    return request({
        url: '/firecontrol-check_post/save',
        method: 'post',
        data:data,
    })
}

// 获取值班人员
export function getDutyPerson(data) {
    return request({
        url: '/firecontrol-check_post/getDutyPerson',
        method: 'get',
        params:data
    })
}

// 查询在岗情况
export function getDutyTime(data) {
    return request({
        url: '/firecontrol-check_post/getDutyTime',
        method: 'post',
        data:data,
    })
}

// 在岗率
export function getOnDutyRate(data) {
    return request({
        url: '/firecontrol-check_post/getOnDutyRate',
        method: 'post',
        data:data,
    })
}
// 查询是否有排班
export function checkDuty(data) {
    return request({
        url: '/firecontrol-check_post/checkDuty',
        method: 'post',
        data:data,
    })
}
// 获取当前值班信息
export function getDuty(data) {
    return request({
        url: 'firecontrol-check-record/duty',
        megetthod: 'get',
        params:data,
    })
}
// 获取视频播放流
export function getVideoStreaming(data) {
    return request({
        url: '/monitor/getVideoStreaming',
        method: 'post',
        data: data,
    })
}
// 在岗率
export function relatedMonitor(data) {
    return request({
        url: '/firecontrol-room/relatedMonitor',
        method: 'get',
        params:data,
    })
}
// 消控室lsit
export function fireControl(data) {
    return request({
        url: '/firecontrol-room/list',
        method: 'get',
        params:data,
    })
}