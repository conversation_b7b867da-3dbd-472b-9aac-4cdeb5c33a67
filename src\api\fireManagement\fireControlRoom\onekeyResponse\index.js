import request from '@/utils/request'

// 前端定时获取是否查岗
export function getCheckPost(query) {
    return request({
        url: '/firecontrol-check_post/getCheckPost',
        method: 'get',
        params: query
    })
}

// 应岗
export function checkPost(data) {
    return request({
        url: '/firecontrol-check_post/checkPost',
        method: 'post',
        data: data,
    })
}

// 未应岗
export function notResponse(data) {
    return request({
        url: '/firecontrol-check_post/notResponse',
        method: 'post',
        data: data,
    })
}