import request from '@/utils/request'

export function page(query) {
    return request({
        url: '/firecontrol-room/page',
        method: 'get',
        params: query
    })
}
export function devicePage(query) {
    return request({
        url: '/monitor/page',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-room/save',
        method: 'post',
        data: data
    })
}
export function department(data) {
    return request({
        url: 'organization/tree',
        method: 'get',
        params: data
    })
}

export function update(data) {
    return request({
        url: '/firecontrol-room/update',
        method: 'post',
        data: data
    })
}

export function remove(data) {
    return request({
        url: '/firecontrol-room/delete',
        method: 'post',
        data: data
    })
}

export function areaList() {
    return request({
        url: '/area/tree',
        method: 'get',
    })
}