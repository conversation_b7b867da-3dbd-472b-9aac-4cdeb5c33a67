import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-material/page',
        method: 'get',
        params: data
    })
}
export function areaPage(data) {
    return request({
        url: '/firecontrol-area/page',
        method: 'get',
        params: data
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-material/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-material/update',
        method: 'post',
        data: data
    })
}