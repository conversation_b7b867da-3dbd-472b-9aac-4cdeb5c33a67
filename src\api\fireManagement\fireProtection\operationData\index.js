import request from '@/utils/request'

export function pageDevice(data) {
    return request({
        url: '/firecontrol-real-time/pageForDeviceType',
        method: 'get',
        params: data
    })
}
export function pageEmergency(data) {
    return request({
        url: '/firecontrol-device-emergency/page',
        method: 'get',
        params: data
    })
}
export function pageStatus(data) {
    return request({
        url: '/firecontrol-device-status/page',
        method: 'get',
        params: data
    })
}
export function historyPageDevice(data) {
    return request({
        url: '/firecontrol-history/pageForDeviceId',
        method: 'get',
        params: data
    })
}