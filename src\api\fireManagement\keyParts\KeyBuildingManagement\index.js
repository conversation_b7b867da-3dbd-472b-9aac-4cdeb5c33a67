import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-key-buildings/page',
        method: 'get',
        params: data
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-key-buildings/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-key-buildings/update',
        method: 'post',
        data: data
    })
}
export function list(data) {
    return request({
        url: '/firecontrol-area/list',
        method: 'get',
        params: data
    })
}
export function dele(data) {
    return request({
        url: '/firecontrol-key-buildings/deleteById',
        method: 'post',
        data: data
    })
}