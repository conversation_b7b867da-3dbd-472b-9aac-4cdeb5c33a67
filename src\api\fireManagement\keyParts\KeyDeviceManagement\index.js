import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-key-devices/page',
        method: 'get',
        params: data
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-key-devices/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-key-devices/update',
        method: 'post',
        data: data
    })
}
export function msds(data) {
    return request({
        url: 'firecontrol-msds/list',
        method: 'get',
        params: data
    })
}
export function dele(data) {
    return request({
        url: '/firecontrol-key-devices/deleteById',
        method: 'post',
        data: data
    })
}