import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-rehearsal/page',
        method: 'get',
        params: data
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-rehearsal/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-rehearsal/update',
        method: 'post',
        data: data
    })
}
export function pageRecord(data) {
    return request({
        url: '/firecontrol-rehearsal-record/list',
        method: 'get',
        params: data
    })
}
export function saveRecord(data) {
    return request({
        url: '/firecontrol-rehearsal-record/save',
        method: 'post',
        data: data
    })
}
export function updateRecord(data) {
    return request({
        url: '/firecontrol-rehearsal-record/update',
        method: 'post',
        data: data
    })
}
export function dele(data) {
    return request({
        url: '/firecontrol-rehearsal-record/delete',
        method: 'post',
        data: data
    })
}