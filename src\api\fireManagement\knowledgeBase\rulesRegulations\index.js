import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-rule/page',
        method: 'get',
        params: data
    })
}
export function areaPage(data) {
    return request({
        url: '/firecontrol-area/page',
        method: 'get',
        params: data
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-rule/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-rule/update',
        method: 'post',
        data: data
    })
}
export function downloadFile(bucket, path, fileName) {
    return request({
        url: '/api/file/downloadFile?bucket=' + bucket + '&path=' + path + '&fileName=' + fileName,
        method: 'get',
        // params: {
        //     bucket: bucket,
        //     path: path,
        //     fileName: fileName,
        // }
    })
}