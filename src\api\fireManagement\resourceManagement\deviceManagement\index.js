import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-device/page',
        method: 'get',
        params: data
    })
}
export function areaPage(data) {
    return request({
        url: '/firecontrol-area/page',
        method: 'get',
        params: data
    })
}
export function treeList(query) {
    return request({
        url: '/organization/tree',
        method: 'get',
        params: query
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-device/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-device/update',
        method: 'post',
        data: data
    })
}
export function deviceData(data) {
    return request({
        url: '/firecontrol-device/data',
        method: 'post',
        data: data
    })
}
export function pageDevice(data) {
    return request({
        url: '/firecontrol-history/pageForDeviceId',
        method: 'get',
        params: data
    })
}
export function pageEmergency(data) {
    return request({
        url: '/firecontrol-device-emergency/page',
        method: 'get',
        params: data
    })
}
export function pageStatus(data) {
    return request({
        url: '/firecontrol-device-status/page',
        method: 'get',
        params: data
    })
}
export function diaDetail(data) {
    return request({
        url: '/firecontrol-device-emergency/detail',
        method: 'post',
        data: data
    })
}
export function saveDevice(data) {
    return request({
        url: '/firecontrol-device-emergency/deal',
        method: 'post',
        data: data
    })
}