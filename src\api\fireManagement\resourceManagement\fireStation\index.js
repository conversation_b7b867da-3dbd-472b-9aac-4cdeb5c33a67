import request from '@/utils/request'
export function page(data) {
    return request({
        url: '/firecontrol-station/page',
        method: 'get',
        params: data
    })
}
export function pageHuman(data) {
    return request({
        url: '/firecontrol-station/human/page',
        method: 'get',
        params: data
    })
}
export function pageEquipment(data) {
    return request({
        url: '/firecontrol-station/equipment/page',
        method: 'get',
        params: data
    })
}
export function areaPage(data) {
    return request({
        url: '/firecontrol-area/page',
        method: 'get',
        params: data
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-station/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-station/update',
        method: 'post',
        data: data
    })
}
export function saveHuman(data) {
    return request({
        url: '/firecontrol-station/human/save',
        method: 'post',
        data: data
    })
}
export function updateHuman(data) {
    return request({
        url: '/firecontrol-station/human/update',
        method: 'post',
        data: data
    })
}
export function saveEquipment(data) {
    return request({
        url: '/firecontrol-station/equipment/save',
        method: 'post',
        data: data
    })
}
export function updateEquipment(data) {
    return request({
        url: '/firecontrol-station/equipment/update',
        method: 'post',
        data: data
    })
}