import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-firefighter/page',
        method: 'get',
        params: data
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-firefighter/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-firefighter/update',
        method: 'post',
        data: data
    })
}
export function remove(data) {
    return request({
        url: '/firecontrol-firefighter/delete',
        method: 'post',
        data: data
    })
}
export function detail(data) {
    return request({
        url: '/firecontrol-firefighter/detail',
        method: 'get',
        params: data
    })
}
// 消防单位下拉
export function unitList(data) {
    return request({
        url: '/firecontrol-protection-unit/list',
        method: 'get',
        params: data
    })
}
// 变更记录
export function changeRecord(data) {
    return request({
        url: '/firecontrol-firefighter/changeRecord',
        method: 'get',
        params: data
    })
}