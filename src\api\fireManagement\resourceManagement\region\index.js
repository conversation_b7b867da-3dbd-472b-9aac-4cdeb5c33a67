import request from '@/utils/request'
export function page(data) {
    return request({
        url: '/firecontrol-area/page',
        method: 'get',
        params: data
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-area/save',
        method: 'post',
        data: data
    })
}
export function areaList() {
    return request({
        url: '/area/tree',
        method: 'get',
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-area/update',
        method: 'post',
        data: data
    })
}