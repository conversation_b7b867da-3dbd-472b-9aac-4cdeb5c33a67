import request from '@/utils/request'
export function areaPage(data) {
  return request({
    url: '/firecontrol-area/page',
    method: 'get',
    params: data
  })
}
export function pageWater(data) {
  return request({
    url: '/firecontrol-water/page',
    method: 'get',
    params: data
  })
}

export function saveWater(data) {
  return request({
    url: '/firecontrol-water/save',
    method: 'post',
    data: data
  })
}
export function updateWater(data) {
  return request({
    url: '/firecontrol-water/update',
    method: 'post',
    data: data
  })
}
export function pageNature(data) {
  return request({
    url: '/firecontrol-nature-water/page',
    method: 'get',
    params: data
  })
}

export function saveNature(data) {
  return request({
    url: '/firecontrol-nature-water/save',
    method: 'post',
    data: data
  })
}
export function updateNature(data) {
  return request({
    url: '/firecontrol-nature-water/update',
    method: 'post',
    data: data
  })
}
export function pageResource(data) {
  return request({
    url: '/firecontrol-resource/page',
    method: 'get',
    params: data
  })
}

export function saveResource(data) {
  return request({
    url: '/firecontrol-resource/save',
    method: 'post',
    data: data
  })
}
export function updateResource(data) {
  return request({
    url: '/firecontrol-resource/update',
    method: 'post',
    data: data
  })
}
export function pageRecord(data) {
  return request({
    url: '/firecontrol-resource/record/page',
    method: 'post',
    data: data
  })
}
export function saveRecord(data) {
  return request({
    url: '/firecontrol-resource/record/save',
    method: 'post',
    data: data
  })
}
