import request from '@/utils/request'

export function page(data) {
    return request({
        url: '/firecontrol-protection-unit/page',
        method: 'get',
        params: data
    })
}
export function save(data) {
    return request({
        url: '/firecontrol-protection-unit/save',
        method: 'post',
        data: data
    })
}
export function update(data) {
    return request({
        url: '/firecontrol-protection-unit/update',
        method: 'post',
        data: data
    })
}
export function remove(data) {
    return request({
        url: '/firecontrol-protection-unit/delete',
        method: 'post',
        data: data
    })
}
export function detail(data) {
    return request({
        url: '/firecontrol-protection-unit/detail',
        method: 'get',
        params: data
    })
}
// 关联消控室监控点查询
export function roomList(data) {
    return request({
        url: '/firecontrol-room/list',
        method: 'get',
        params: data
    })
}