import request from '@/utils/request'

// 查询子分组
export function selectSonGroup(parentId) {
    return request({
      url: '/equipment/group/select-son',
      method: 'get',
      params: {
        parentId:parentId
      }
    })
  }
  // 添加设备分组
  export function getStatistics(data) {
    return request({
      url: '/firecontrol-statistic-analysis/details',
      method: 'get',
      params:data
    })
  }