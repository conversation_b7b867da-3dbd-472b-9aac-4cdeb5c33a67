import request from '@/utils/request'
//分组树
export function tree() {
    return request({
        url: '/firecontrol-monitoring-directory/list',
        method: 'get',
    })
}

// 添加分组
export function save(data) {
    return request({
        url: '/firecontrol-monitoring-directory/save',
        method: 'post',
        data: data,
    })
}

// 编辑分组
export function update(data) {
    return request({
        url: '/firecontrol-monitoring-directory/update',
        method: 'post',
        data: data,
    })
}

// 删除分组
export function remove(query) {
    return request({
        url: '/firecontrol-monitoring-directory/delete',
        method: 'get',
        params: query,
    })
}

// 删除分组下摄像头
export function removeVideo(data) {
    return request({
        url: '/firecontrol-monitoring-directory-monitor/delete',
        method: 'post',
        data: data,
    })
}

// 添加分组下摄像头
export function saveVideo(data) {
    return request({
        url: '/firecontrol-monitoring-directory-monitor/save',
        method: 'post',
        data: data,
    })
}

// 根据分组id获得摄像头列表
export function monitorList(query) {
    return request({
        url: '/monitor/page',
        method: 'get',
        params: query
    })
}

// 根据设备id获取视频播放流
export function getVideoStreaming(data) {
    return request({
        url: '/monitor/getVideoStreaming',
        method: 'post',
        data: data,
    })
}

// 部门树
export function organizationTree(query) {
    return request({
        url: '/organization/tree',
        method: 'get',
        params: query
    })
}

