import request from '@/utils/request'

// 查询子分组
export function selectSonGroup(parentId) {
    return request({
      url: '/equipment/group/select-son',
      method: 'get',
      params: {
        parentId:parentId
      }
    })
  }
  // 添加设备分组
  export function addGroup(parentId,groupName) {
    return request({
      url: '/equipment/group/add',
      method: 'post',
      data: {
        parentId:parentId,
        groupName:groupName
      }
    })
  }
  // 调整设备分组
  export function adjustGroup(groupId,equipmentIdList) {
    return request({
      url: '/equipment/group/adjust',
      method: 'post',
      data: {
        groupId:groupId,
        equipmentIdList:equipmentIdList
      }
    })
  }
  // 删除分组
  export function deleteGroup(groupId) {
    return request({
      url: '/equipment/group/delete',
      method: 'get',
      params: {
        groupId:groupId
      }
    })
  }
  // 编辑分组
  export function updateGroup(groupId,groupName) {
    return request({
      url: '/equipment/group/update',
      method: 'post',
      data: {
        groupId:groupId,
        groupName:groupName
      }
    })
  }
  // 查询分组下所有设备
  export function selectByGroup(groupId) {
    return request({
      url: '/equipment/group/selectByGroup',
      method: 'get',
      params: {
        groupId:groupId
      }
    })
  }