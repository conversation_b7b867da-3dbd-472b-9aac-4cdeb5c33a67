import request from '@/utils/request'

// 查看控制记录
export function selectControl(pages,size,equipmentId,operateType,operateStatus,equipmentName,operateTime,areaId) {
    return request({
      url: '/equipment/intelligent-control/select-page',
      method: 'post',
      data: {
        pages:pages,
        size:size,
        equipmentId:equipmentId,
        operateType:operateType,
        operateStatus:operateStatus,
        equipmentName:equipmentName,
        operateTime:operateTime,
        areaId:areaId
      }
    })
  }
//   门禁操作
export function operateControl(equipmentId,operateType,equipmentIp) {
    return request({
      url: '/equipment/intelligent-control/operate',
      method: 'post',
      data: {
        equipmentId:equipmentId,
        operateType:operateType,
        equipmentIp:equipmentIp
      }
    })
  }