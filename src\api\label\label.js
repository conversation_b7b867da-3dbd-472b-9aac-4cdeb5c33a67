import request from '@/utils/request'

// 查询所有标签
export function selectAllLabel() {
    return request({
      url: '/equipment/tag/select-all',
      method: 'get',
     
    })
  }
  // 新增标签
  export function addTag(tagName) {
    return request({
      url: '/equipment/tag/add',
      method: 'post',
     data:{
      tagName:tagName
     }
    })
  }
  // 调整标签
  export function adjustTag(equipmentInfoIdList,tagIdList) {
    return request({
      url: '/equipment/tag/adjust',
      method: 'post',
     data:{
      equipmentInfoIdList:equipmentInfoIdList,
      tagIdList:tagIdList
     }
    })
  }
  // 编辑标签名称
  export function updateTag(tagId,tagName) {
    return request({
      url: '/equipment/tag/update',
      method: 'post',
     data:{
      tagId:tagId,
      tagName:tagName
     }
    })
  }
  // 删除标签
  export function deleteTag(tagId) {
    return request({
      url: '/equipment/tag/delete',
      method: 'get',
     params:{
      tagId:tagId,
     }
    })
  }
  // 根据标签分页查询设备
  export function selectEquipmentByTag(page,size,tagId,equipmentId,equipmentName) {
    return request({
      url: '/equipment/tag/select-equipment',
      method: 'post',
     data:{
      page:page,
      size:size,
      tagId:tagId,
      equipmentId:equipmentId,
      equipmentName:equipmentName
     }
    })
  }