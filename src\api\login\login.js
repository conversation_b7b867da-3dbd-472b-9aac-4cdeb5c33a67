import request from '@/utils/request'
import Vue from 'vue'
export function getUserMenu() {
    return request({
      url: `/getUserMenu`,
      method: 'get',
      // Headers:{
      //   "Authorization":token

      // }

    })
  }
  export function getInfo() {
    return request({
      url:`/getUserInfo`,
      method: 'get',
      // headers: {'Authorization': localStorage.token}
      // Headers:{
      //   Authorization:localStorage.token
      // }
    })
  }
export function getBoundUsers() {
  return request({
    url:`/information-v2/enterprise-info/getBoundUsers`,
    method: 'post',
  })
}
export function bondEnterprise(id) {
  return request({
    url:`/information-v2/enterprise-info/bond-enterprise/${id}`,
    method: 'get',
  })
}
