import request from '@/utils/request'

// 分页查询型号信息
export function selectAllModel(pages,size,typeName,modelName,vendorName,isEnabled) {
    return request({
      url: '/equipment/model/select-page',
      method: 'post',
      data:{
        pages:pages,
        size:size,
        typeName:typeName,
        modelName:modelName,
        vendorName:vendorName,
        isEnabled:isEnabled
      }
     
    })
  }
  // 获取查询条件信息
export function showClassifyInfo() {
    return request({
      url: '/equipment/maintain-standing-book/show-classifyInfo',
      method: 'get'
    })
  }
  // 根据厂商信息查询型号信息
export function showClassifyModel(vendorId) {
    return request({
      url: '/equipment/maintain-standing-book/show-classifyModel',
      method: 'get',
      params: {
        vendorId:vendorId
      }
    })
  }
  // 添加型号
  export function addModel(typeId,vendorId,modelName) {
    return request({
      url: '/equipment/model/add',
      method: 'post',
      data: {
        typeId:typeId,
        vendorId:vendorId,
        modelName:modelName
      }
    })
  }
  // 查看详情
  export function modelDetile(modelId) {
    return request({
      url: '/equipment/model/select-detail',
      method: 'get',
      params: {
        modelId:modelId
      }
    })
  }
  // 编辑
  export function updateModel(typeId,vendorId,modelId,modelName) {
    return request({
      url: '/equipment/model/update',
      method: 'post',
      data: {
        typeId:typeId,
        vendorId:vendorId,
        modelId:modelId,
        modelName:modelName
      }
    })
  }
  // 禁用
  export function modelEnabled(modelId) {
    return request({
      url: '/equipment/model/enabled',
      method: 'get',
      params: {
        modelId:modelId
      }
    })
  }