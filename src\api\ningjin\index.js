import request from '@/utils/request'

// 字典
export function getDictByType(data) {
  return request({
    url: '/security/dict/getDictByType',
    method: 'get',
    params: data
  })
}
// 人员信息
export function carBlackWhiteList(data) {
  return request({
    url: '/security/carBlackWhiteList/listPage',
    method: 'post',
    data
  })
}
// 告警记录 列表
export function alarmInfoPersonRecord(data) {
  return request({
    url: '/security/alarmInfoPerson/record',
    method: 'post',
    data
  })
}
// 卡口智能管理
export function maintainSelectPage(data) {
  return request({
    url: '/equipment/maintain-standing-book/select-page',
    method: 'post',
    data
  })
}
// 分区管控-查询
export function areaControlFindListPage(params) {
  return request({
    url: '/security/areaControl/findListPage',
    method: 'get',
    params
  })
}
// 根据车牌号查询车辆
export function selectCarByCarPlate(params) {
  return request({
    url: '/security/carGpsLink/selectCarByCarPlate',
    method: 'get',
    params
  })
}
// 查询所有行驶计划
export function drivePlanList(params) {
  return request({
    url: '/security/drivePlan/list',
    method: 'get',
    params
  })
}
// 查询车辆行驶轨迹
export function listCarLocation(data) {
  return request({
    url: '/security/carGpsLink/listCarLocation',
    method: 'post',
    data
  })
}
// 删除
export function carBlackWhiteListDelete(params) {
  return request({
    url: '/security/carBlackWhiteList/delete',
    method: 'get',
    params
  })
}
// 告警记录-删除
export function alarmInfoPersonDelete(data) {
  return request({
    url: '/security/alarmInfoPerson/delete',
    method: 'post',
    data
  })
}
// 分区管控-删除
export function areaControlDeleteById(data) {
  return request({
    url: '/security/areaControl/deleteById',
    method: 'post',
    data
  })
}
// 分区管控-删除
export function drivePlanDelById(data) {
  return request({
    url: '/security/drivePlan/delById',
    method: 'post',
    data
  })
}
// 人员详情
export function carBlackWhiteListDetail(data) {
  return request({
    url: '/security/carBlackWhiteList/detail',
    method: 'post',
    data
  })
}
// 告警列表-根据id查询详情
export function alarmInfoPersonDetail(params) {
  return request({
    url: '/security/alarmInfoPerson/detail',
    method: 'get',
    params
  })
}
// 分区管控-根据id查询详情
export function areaControlFindById(params) {
  return request({
    url: '/security/areaControl/findById',
    method: 'get',
    params
  })
}
// 分区管控-查询区域停车详情
export function parkCarListParkNow(params) {
  return request({
    url: '/security/parkCar/listParkNow',
    method: 'get',
    params
  })
}
// 人员更新
export function carBlackWhiteListSaveOrUpdate(data) {
  return request({
    url: '/security/carBlackWhiteList/saveOrUpdate',
    method: 'post',
    data
  })
}
// 告警记录-处理
export function alarmInfoPersonDispose(data) {
  return request({
    url: '/security/alarmInfoPerson/dispose',
    method: 'post',
    data
  })
}
// 分区管控-新增or修改
export function areaControlSaveOrUpdate(data) {
  return request({
    url: '/security/areaControl/saveOrUpdate',
    method: 'post',
    data
  })
}
// 新增目的地
export function drivePlanSaveDrivePlan(data) {
  return request({
    url: '/security/drivePlan/saveDrivePlan',
    method: 'post',
    data
  })
}
// 绑定gps编码
export function carGpsLinkBindGpsDevice(data) {
  return request({
    url: '/security/carGpsLink/bindGpsDevice',
    method: 'post',
    data
  })
}
// 解绑gps编码
export function carGpsLinkRemoveGpsDevice(data) {
  return request({
    url: '/security/carGpsLink/removeGpsDevice',
    method: 'post',
    data
  })
}
// 根据车牌号查询gps绑定信息
export function carGpsLinkFindByCarPlate(params) {
  return request({
    url: '/security/carGpsLink/findByCarPlate',
    method: 'get',
    params
  })
}
// 园企车辆-分页查询
export function carInfoListCarPage(data) {
  return request({
    url: '/security/carInfo/listCarPage',
    method: 'post',
    data
  })
}
// 园企车辆进出记录-分页查询
export function carPassRecordGetPassRecord(data) {
  return request({
    url: '/security/carPassRecord/getPassRecord',
    method: 'post',
    data
  })
}
// 信息登记-分页查询
export function listHeavyCarPage(data) {
  return request({
    url: '/security/heavyCarInfo/listHeavyCarPage',
    method: 'post',
    data
  })
}
// 告警记录-分页查询
export function dangerTransRecord(data) {
  return request({
    url: '/security/alarmInfo/record',
    method: 'post',
    data
  })
}
// 准入清单-分页查询
export function listHeavyCarAccessPage(data) {
  return request({
    url: '/security/heavyCarInfo/listHeavyCarAccessPage',
    method: 'post',
    data
  })
}
// 危化品记录-分页查询
export function heavyTruckVisitorList(data) {
  return request({
    url: '/security/heavyTruckEdit/visitorList',
    method: 'post',
    data
  })
}
// 访客进出记录列表-分页查询
export function visitorCarVisitorCarRecord(data) {
  return request({
    url: '/security/visitorCar/visitorCarRecord',
    method: 'post',
    data
  })
}
// 危化品记录-查询详细信息
export function heavyTruckVisitorDetil(data) {
  return request({
    url: '/security/heavyTruckEdit/visitorDetil',
    method: 'post',
    data
  })
}
// 访客进出详情
export function visitorCarVisitorCarDetail(data) {
  return request({
    url: '/security/visitorCar/visitorCarDetail',
    method: 'post',
    data
  })
}
// 园企车辆-详情
export function detailCarInfo(params) {
  return request({
    url: '/security/carInfo/detailCarInfo',
    method: 'get',
    params
  })
}
// 删除
export function carInfoDeleteById(data) {
  return request({
    url: '/security/carInfo/deleteById',
    method: 'post',
    data
  })
}
// 信息登记/删除
export function heavyCarInfoDeleteById(data) {
  return request({
    url: '/security/heavyCarInfo/deleteById',
    method: 'post',
    data
  })
}
// 危化品运输告警记录/删除
export function dangerTransDelete(data) {
  return request({
    url: '/security/alarmInfo/delete',
    method: 'post',
    data
  })
}
//获取导入模板
export function getImportTemplate() {
  return request({
    url: '/security/carInfo/getImportTemplate',
    method: 'get',
    responseType: "blob",
  })
}
//获取进出记录导入模板
export function exportDate(data) {
  return request({
    url: '/security/carPassRecord/exportDate',
    method: 'post',
    responseType: "blob",
    data:data
  })
}
//获取导入模板/信息登记
export function heavyCarInfoGetImportTemplate() {
  return request({
    url: '/security/heavyCarInfo/getImportTemplate',
    method: 'get',
    responseType: "blob",
  })
}
// 新增园企车辆
export function carInfoSaveCarInfo(data) {
  return request({
    url: '/security/carInfo/saveCarInfo',
    method: 'post',
    data
  })
}
// 新增信息登记
export function saveHeavyCarInfo(data) {
  return request({
    url: '/security/heavyCarInfo/saveHeavyCarInfo',
    method: 'post',
    data
  })
}
// 处理告警记录/生成工单
export function dangerTransDispose(data) {
  return request({
    url: '/security/alarmInfo/dispose',
    method: 'post',
    data
  })
}
// 修改园企车辆
export function carInfoUpdateAccess(data) {
  return request({
    url: '/security/carInfo/updateAccess',
    method: 'post',
    data
  })
}
// 修改园企车辆
export function carInfoUpdateInfo(data) {
  return request({
    url: '/security/carInfo/updateInfo',
    method: 'post',
    data
  })
}
// 修改园企车辆
export function heavyCarInfoUpdateInfo(data) {
  return request({
    url: '/security/heavyCarInfo/updateInfo',
    method: 'post',
    data
  })
}
// 申请延期
export function updateExpireDate(data) {
  return request({
    url: '/security/heavyCarInfo/updateExpireDate',
    method: 'post',
    data
  })
}
// 审核延期
export function checkPostpone(data) {
  return request({
    url: '/security/heavyCarInfo/checkPostpone',
    method: 'post',
    data
  })
}
// 修改信息登记
export function heavyCarInfoUpdateAccess(data) {
  return request({
    url: '/security/heavyCarInfo/updateAccess',
    method: 'post',
    data
  })
}
// 园企车辆详情
export function detailHeavyCarInfo(params) {
  return request({
    url: '/security/heavyCarInfo/detailHeavyCarInfo',
    method: 'get',
    params
  })
}
// 危化品运输告警记录详情
export function dangerTransDetail(params) {
  return request({
    url: '/security/alarmInfo/detail',
    method: 'get',
    params
  })
}
// 获取文件url
export function fileDetail(params) {
  return request({
    url: '/security/file/fileDetail',
    method: 'get',
    params
  })
}
// 园企车辆-批量授权
export function carInfoBatchAccess(data) {
  return request({
    url: '/security/carInfo/batchAccess',
    method: 'post',
    data
  })
}
// 信息登记-批量授权
export function batchAccess(data) {
  return request({
    url: '/security/heavyCarInfo/batchAccess',
    method: 'post',
    data
  })
}
// 时间车牌号获取路线
export function gpsLineByTimeCarPlate(data) {
  return request({
    url: '/api/gpsLineByTimeCarPlate',
    method: 'post',
    data
  })
}
// 时间车牌号获取路线
export function gpsLineByCarPlate(data) {
  return request({
    url: '/security/ioc/gpsLineByCarPlate',
    method: 'post',
    data
  })
}
// 时间车牌号获取路线
export function gpsCarPlate(data) {
  return request({
    url: '/security/ioc/gpsCarPlate',
    method: 'post',
    data
  })
}
// 时间车牌号获取路线
export function carGpsLine(params) {
  return request({
    url: '/security/carGpsLink/carGpsLine',
    method: 'get',
    params
  })
}
