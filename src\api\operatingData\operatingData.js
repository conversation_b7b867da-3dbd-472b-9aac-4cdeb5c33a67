import request from '@/utils/request'

// 读取实时数据
export function realTimeData(equipmentId) {
    return request({
      url: '/equipment/runtimeData/realTime',
      method: 'get',
      params:{
        equipmentId:equipmentId
      }
    })
  }
  // 查看历史数据指标
  export function selectIndex(equipmentId) {
    return request({
      url: '/equipment/runtimeData/selectIndex',
      method: 'get',
      params:{
        equipmentId:equipmentId
      }
    })
  }
  // 查看历史数据表格
  export function historyTable(pages,size,equipmentId,request1,isStatus,indexKey,startTime,finalTime) {
    return request({
      url: '/equipment/runtimeData/historyTable',
      method: 'post',
      data:{
        pages:pages,
        size:size,
        equipmentId:equipmentId,
        request:request1,
        isStatus:isStatus,
        indexKey:indexKey,
        startTime:startTime,
        finalTime:finalTime
      }
    })
  }