import request from '@/utils/request'

// 字典
export function getDictByType(data) {
  return request({
    url: '/security/dict/getDictByType',
    method: 'get',
    params: data
  })
}
// 字典
export function getLoginCompany(data) {
  return request({
    url: '/security/visit/getLoginCompany',
    method: 'get',
    params: data
  })
}
// 人员信息
export function getEmployees(data) {
  return request({
    url: '/security/employee/getEmployees',
    method: 'post',
    data
  })
}

// 人员管理-人员信息-新增人员
export function insertOtherPerson(data) {
  return request({
    url: '/security/employee/insertOtherPerson',
    method: 'post',
    data: data
  })
}

// 人脸上传图片到临时文件夹
export function uploadFacePic(file) {
  return request({
    url: '/security/employee/uploadFacePic',
    // headers:{
    //   'Content-Type': 'multipart/form-data'
    // },
    method: 'post',
    data: file
  })
}
// 人员详情
export function getDetail(params) {
  return request({
    url: '/security/employee/getDetail',
    method: 'GET',
    params: params
  })
}
// 人员详情
export function getOfficePlace(params) {
  return request({
    url: '/security/employee/getOfficePlace',
    method: 'GET',
    params: params
  })
}
// 人员信息-门禁授权列表
export function listPage(data) {
  return request({
    url: '/security/employeeAccess/listPage',
    method: 'post',
    data: data
  })
}
// 人员管理-门禁授权详情
export function getAccessDetail(params) {
  return request({
    url: '/security/employeeAccess/getAccessDetail',
    method: 'get',
    params: params
  })
}
// 根据id获取树状结构
export function getAllTree(params) {
  return request({
    url: '/property/sys_dict/getAllTree',
    method: 'get',
    params: params
  })
}
// 门禁授权编辑
export function updateAccessDetail(data) {
  return request({
    url: '/security/employeeAccess/updateAccessDetail',
    method: 'post',
    data: data
  })
}

//门禁管理-编辑权限-加入黑名单
export function joinBlacklist(data) {
  return request({
    url: '/security/employeeAccess/joinBlacklist',
    method: 'post',
    data: data
  })
}

//门禁管理-编辑权限-从黑名单移除
export function removeBlacklist(data) {
  return request({
    url: '/security/employeeAccess/removeBlacklist',
    method: 'post',
    data: data
  })
}

//门禁管理-编辑权限-加入白名单
export function joinWhitelist(data) {
  return request({
    url: '/security/employeeAccess/joinWhitelist',
    method: 'post',
    data: data
  })
}

//门禁管理-编辑权限-从白名单移除
export function removeWhitelist(data) {
  return request({
    url: '/security/employeeAccess/removeWhitelist',
    method: 'post',
    data: data
  })
}

//门禁管理-编辑权限-白名单重新授权
export function flushWhitelist(data) {
  return request({
    url: '/security/employeeAccess/flushWhitelist',
    method: 'post',
    data: data
  })
}

// 通行记录-已登记入库分页查询
export function listPageInSystem(data) {
  return request({
    url: '/security/passRecord/listPageInSystem',
    method: 'post',
    data: data
  })
}
// 通行记录-分页查询陌生人
export function listPageStranger(data) {
  return request({
    url: '/security/passRecord/listPageStranger',
    method: 'post',
    data: data
  })
}
// 通行记录-详情
export function getPassDetail(params) {
  return request({
    url: '/security/passRecord/getPassDetail',
    method: 'get',
    params: params
  })
}
// 以图搜图-入库列表
export function searchSystemByPic(data) {
  return request({
    url: '/security/employee/searchSystemByPic',
    method: 'post',
    data: data
  })
}
// 以图搜图-未入库列表
export function searchStrangerByPic(data) {
  return request({
    url: '/security/employee/searchStrangerByPic',
    method: 'post',
    data: data
  })
}
// 人员管理-名单列表查询
export function blackWhiteListPage(data) {
  return request({
    url: '/security/blackWhiteList/listPage',
    method: 'post',
    data: data
  })
}
// 人员管理-名单列表查询
export function getStrangerDetail(params) {
  return request({
    url: '/security/passRecord/getStrangerDetail',
    method: 'get',
    params: params
  })
}
// 以图搜图人员详情-定位详情
export function getPassPicDetail(data) {
  return request({
    url: '/security/passRecord/getPassPicDetail',
    method: 'post',
    data: data
  })
}
// 以图搜图-详情通行记录
export function getPassRecord(params) {
  return request({
    url: '/security/passRecord/getPassRecord',
    method: 'get',
    params: params
  })
}
// 特征搜索-已登记列表
export function searchSystemByTrait(data) {
  return request({
    url: '/security/employee/searchSystemByTrait',
    method: 'post',
    data: data
  })
}
// 特征搜索-未登记列表
export function searchStrangerByTrait(data) {
  return request({
    url: '/security/employee/searchStrangerByTrait',
    method: 'post',
    data: data
  })
}
// 以图搜图-未登记加入黑名单
export function joinBlackList(params) {
  return request({
    url: '/security/passRecord/joinBlackList',
    method: 'get',
    params: params
  })
}
// 名单列表删除数据
export function deleteList(data) {
  return request({
    url: '/security/blackWhiteList/deleteList',
    method: 'post',
    data: data
  })
}
// 通过编号获取信息
export function getInfoByNumber(data) {
  return request({
    url: '/property/sys_dict/getInfoByNumber',
    method: 'post',
    data: data
  })
}
// 名单管理新增/编辑
export function saveOrUpdateList(data) {
  return request({
    url: '/security/blackWhiteList/saveOrUpdateList',
    method: 'post',
    data: data
  })
}


// 车辆信息
export function listCarPage(data) {
  return request({
    url: '/security/carInfo/listCarPage',
    method: 'post',
    data: data
  })
}
// 车辆 新增查找员工
export function getEmployee(params) {
  return request({
    url: '/security/carInfo/getEmployee',
    method: 'get',
    params: params
  })
}
// 车辆 车辆详情
export function getCarDetail(params) {
  return request({
    url: '/security/carInfo/getCarDetail',
    method: 'get',
    params: params
  })
}
// 车辆 新增车辆基本信息
export function saveOrUpdateCarInfo(data) {
  return request({
    url: '/security/carInfo/saveOrUpdateCarInfo',
    method: 'post',
    data: data
  })
}
// 车辆 通行记录已登记入库分页查询
export function passListPageInSystem(data) {
  return request({
    url: '/security/carPassRecord/listPageInSystem',
    method: 'post',
    data: data
  })
}

//车辆信息 已登记入库列表车辆删除
export function deleteById(data) {
  return request({
    url: '/security/carInfo/deleteById',
    method: 'post',
    data: data
  })
}

// 车辆 通行记录未登记入库分页查询
export function passListPageStranger(data) {
  return request({
    url: '/security/carPassRecord/listPageStranger',
    method: 'post',
    data: data
  })
}
// 车辆通行记录已/未登记车辆信息
export function getInCarDetail(data) {
  return request({
    url: '/security/carPassRecord/getInCarDetail',
    method: 'get',
    params: data
  })
}
// 车辆通行记录已/未登记车辆信息
export function listCarDetailPage(params) {
  return request({
    url: '/security/carPassRecord/listCarDetailPage',
    method: 'get',
    params: params
  })
}
// 车辆名单列表
export function carListListCarPage(data) {
  return request({
    url: '/security/CarList/listCarPage',
    method: 'post',
    data: data
  })
}
// 车辆通行记录详情(表格、图片)
export function carPassRecordListCarDetailPage(params) {
  return request({
    url: '/security/carPassRecord/listCarDetailPage',
    method: 'get',
    params: params
  })
}
// 车辆通行记录详情(表格、图片)
export function getHeatMap(params) {
  return request({
    url: '/security/carPassRecord/getHeatMap',
    method: 'get',
    params: params
  })
}
// 车辆名单删除
export function deleteCarList(data) {
  return request({
    url: '/security/CarList/deleteCarList',
    method: 'post',
    data: data
  })
}
// 车辆名单详情
export function getCarListDetail(params) {
  return request({
    url: '/security/CarList/getCarListDetail',
    method: 'get',
    params: params
  })
}
// 防疫管理列表
export function quarantineListPage(data) {
  return request({
    url: '/security/quarantine/listPage',
    method: 'post',
    data: data
  })
}
// 防疫人员详情
export function getQuarantineDetail(params) {
  return request({
    url: '/security/quarantine/getQuarantineDetail',
    method: 'get',
    params: params
  })
}
// 防疫人员通行详情
export function getQuarantinePassDetail(params) {
  return request({
    url: '/security/quarantine/getQuarantinePassDetail',
    method: 'get',
    params: params
  })
}
// 查询人员布控规则
export function getPersonnelRules() {
  return request({
    url: '/security/personnelRules/getPersonnelRules',
    method: 'get',
  })
}
// 查询重点关注人员预警
export function getFocusPersonWarning(params) {
  return request({
    url: '/security/personnelRules/getFocusPersonWarning',
    method: 'get',
    params: params
  })
}
// 查询车辆布控规则
export function getCarRules() {
  return request({
    url: '/security/carRules/getCarRules',
    method: 'get',
  })
}
// 重点车辆预警查询
export function getFocusCarWarning() {
  return request({
    url: '/security/carRules/getFocusCarWarning',
    method: 'get',
  })
}
// 新增/修改人员布控规则
export function saveOrUpdateRules(data) {
  return request({
    url: '/security/personnelRules/saveOrUpdateRules',
    method: 'post',
    data: data
  })
}
// 新增/编辑重点关注人员预警
export function saveOrUpdatePersonWarn(data) {
  return request({
    url: '/security/personnelRules/saveOrUpdatePersonWarn',
    method: 'post',
    data: data
  })
}
// 删除布控人员
export function deletePersonWarn(data) {
  return request({
    url: '/security/personnelRules/deletePersonWarn',
    method: 'post',
    data: data
  })
}
// 删除重点车牌预警
export function deletePlateWarning(data) {
  return request({
    url: '/security/carRules/deletePlateWarning',
    method: 'post',
    data: data
  })
}
// 重点车牌预警查询
export function getFocusPlateWarning(params) {
  return request({
    url: '/security/carRules/getFocusPlateWarning',
    method: 'get',
    params: params
  })
}
// 新增/编辑重点车牌预警
export function saveOrUpdatePlateWarning(data) {
  return request({
    url: '/security/carRules/saveOrUpdatePlateWarning',
    method: 'post',
    data: data
  })
}
// 新增/修改重点车辆预警
export function saveOrUpdateCarWarning(data) {
  return request({
    url: '/security/carRules/saveOrUpdateCarWarning',
    method: 'post',
    data: data
  })
}
// 新增/修改车辆布控规则
export function saveOrUpdateCarRules(data) {
  return request({
    url: '/security/carRules/saveOrUpdateCarRules',
    method: 'post',
    data: data
  })
}
// 分页查询布控人员
export function listPageUnderControl(data) {
  return request({
    url: '/security/underControlPerson/listPageUnderControl',
    method: 'post',
    data: data
  })
}
// 陌生人 查询监控点位
export function selectByArea(params) {
  let pa = {
    areaId:params.areaId,
    typeId:'1592717090718388226'
  }
  return request({
    url: '/equipment/maintain-standing-book/selectByQuery',
    method: 'get',
    params: pa
  })
}
// 查询陌生人
export function listStranger(params) {
  return request({
    url: '/security/underControlPerson/listStranger',
    method: 'get',
    params: params
  })
}
// 今日陌生人抓拍统计
export function countStrangerToday() {
  return request({
    url: '/security/underControlPerson/countStrangerToday',
    method: 'get',
  })
}
// 今日陌生人抓拍
export function listStrangerToday(params) {
  return request({
    url: '/security/underControlPerson/listStrangerToday',
    method: 'get',
    params: params
  })
}
// 查询布控车辆
export function listPageUnderControlCar(data) {
  return request({
    url: '/security/underControlCar/listPageUnderControl',
    method: 'post',
    data: data
  })
}
// 查询陌生车辆
export function listStrangerCar(params) {
  return request({
    url: '/security/underControlCar/listStranger',
    method: 'get',
    params: params
  })
}
// 今日陌生车辆抓拍统计
export function countStrangerTodayCar() {
  return request({
    url: '/security/underControlCar/countStrangerToday',
    method: 'get',
  })
}
// 今日陌生车辆抓拍
export function listStrangerTodayCar(params) {
  return request({
    url: '/security/underControlCar/listStrangerToday',
    method: 'get',
    params: params
  })
}
// 分页查询布控车辆
export function listPageUnderCarControl(data) {
  return request({
    url: '/security/underControlCar/listPageUnderControl',
    method: 'post',
    data: data
  })
}
// 工作台 - 统计布控人员
export function listCountUnderControl(params) {
  return request({
    url: '/security/underControlPerson/listCountUnderControl',
    method: 'get',
    params: params
  })
}
// 工作台 - 统计布控车辆
export function listCountUnderControlCar(params) {
  return request({
    url: '/security/underControlCar/listCountUnderControl',
    method: 'get',
    params: params
  })
}
// 人员布控列表详情-历史捕捉详情
export function getHistoryCapture(params) {
  return request({
    url: '/security/underControlPerson/getHistoryCapture',
    method: 'get',
    params: params
  })
}
// 车辆布控-历史捕捉详情
export function getHistoryHeat(params) {
  return request({
    url: '/security/underControlCar/getHistoryHeat',
    method: 'get',
    params: params
  })
}
// 陌生人/车辆摄像头
export function getVideo(params) {
  return request({
    url: '/security/underControlPerson/getVideo',
    method: 'get',
    params: params
  })
}
// 车辆名单新增或编辑
export function saveOrUpdateCarList(data) {
  return request({
    url: '/security/CarList/saveOrUpdateCarList',
    method: 'post',
    data: data
  })
}
// 访客列表
export function comprehensiveSelect(params) {
  return request({
    url: '/security/visitorPc/comprehensive-select',
    method: 'get',
    params
  })
}
// 查询 危化品车辆
export function heavyTruckListPage(data) {
  return request({
    url: '/security/heavyTruckEdit/listPage',
    method: 'post',
    data
  })
}
// 查询 访客车辆
export function visitorCarListPage(data) {
  return request({
    url: '/security/visitorCar/pageLists',
    method: 'post',
    data
  })
}
// 新增 危化品车辆
export function heavyTruckAdds(data) {
  return request({
    url: '/security/heavyTruckEdit/add',
    method: 'post',
    data
  })
}
// 邀请 访客车辆
export function visitorCarInvite(data) {
  return request({
    url: '/security/visitorCar/invite',
    method: 'post',
    data
  })
}
// 新增 危化品车辆
export function updateStatus(data) {
  return request({
    url: '/security/heavyTruckEdit/updateStatus',
    method: 'post',
    data
  })
}
// 审批 访客车辆
export function visitorCarApproval(data) {
  return request({
    url: '/security/visitorCar/approval',
    method: 'post',
    data
  })
}
// 详细信息 危化品车辆
export function heavyTruckFindByIds(params) {
  return request({
    url: '/security/heavyTruckEdit/findById',
    method: 'get',
    params
  })
}
// 详细信息 访客概览
export function visitorCarVisitorDetail(params) {
  return request({
    url: '/security/visitorCar/visitorDetail',
    method: 'get',
    params
  })
}
// 访客列表操作
export function approval(params) {
  return request({
    url: '/security/visitorPc/approval',
    method: 'get',
    params
  })
}
// 新增邀约
export function add(data) {
  return request({
    url: '/security/visitorPc/add',
    method: 'post',
    data
  })
}
// 更新邀约
export function updateAccessDetails(data) {
  return request({
    url: '/security/visitorPc/updateAccessDetail',
    method: 'post',
    data
  })
}
//查询邀约
export function getAccessDetails(params) {
  return request({
    url: '/security/visitorPc/getAccessDetail',
    method: 'get',
    params
  })
}
// 查询部门树
export function findDeptTree(params) {
  return request({
    url: '/security/dept/findDeptTree',
    method: 'get',
    params
  })
}
// 部门授权分页查询
export function departListPage(data) {
  return request({
    url: '/security/deptAccess/listPage',
    method: 'post',
    data
  })
}
// 根据部门id查询权限详情
export function getDepartAccessDetail(params) {
  return request({
    url: '/security/deptAccess/getAccessDetail',
    method: 'get',
    params
  })
}
// 根据部门id查询权限详情
export function findEmpPageByDeptId(params) {
  return request({
    url: '/security/dept/findEmpPageByDeptIdIgnoreList',
    method: 'get',
    params
  })
}
// 部门授权分页查询
export function updateDepartAccessDetail(data) {
  return request({
    url: '/security/deptAccess/updateAccessDetail',
    method: 'post',
    data
  })
}
// 同步部门权限
export function syncDeptAccess(data) {
  return request({
    url: '/security/deptAccess/syncDeptAccess',
    method: 'post',
    data
  })
}
// 同步指定员工部门权限
export function syncDeptAccessSwitch(data) {
  return request({
    url: '/security/deptAccess/syncDeptAccessSwitch',
    method: 'post',
    data
  })
}
export function getVisited(params) {
  return request({
    url: '/security/visitorPc/getVisited',
    method: 'get',
    params
  })
}
//访客概览
export function getVisitorOverview() {
  return request({
    url: '/security/visitorPc/getVisitorOverview',
    method: 'get',
  })
}
//访客概览
export function visitorCarCount() {
  return request({
    url: '/security/visitorCar/count',
    method: 'get',
  })
}
//危化品车辆-统计
export function heavyTruckStatistic() {
  return request({
    url: '/security/heavyTruckEdit/statistic',
    method: 'get',
  })
}
//被访问部门排名
export function visitedDept(params) {
  return request({
    url: '/security/visitorPc/visitedDept',
    method: 'get',
    params
  })
}
//访问事由分类
export function visitedReason() {
  return request({
    url: '/security/visitorPc/visitedReason',
    method: 'get',
  })
}
//访问数量统计
export function visitorNum(data) {
  return request({
    url: '/security/visitorPc/visitorNum',
    method: 'post',
    data
  })
}
//今日访客
export function todayVisitorNum() {
  return request({
    url: '/security/visitorPc/todayVisitorNum',
    method: 'get',
  })
}
//陌生人统计
export function countStrangerOverview() {
  return request({
    url: '/security/report/countStrangerOverview',
    method: 'get',
  })
}
//陌生车辆统计
export function countStrangeCarOverview() {
  return request({
    url: '/security/report/countStrangeCarOverview',
    method: 'get',
  })
}
//陌生人统计(图表)
export function countStranger(params) {
  return request({
    url: '/security/report/countStranger',
    method: 'get',
    params
  })
}
//陌生车辆统计(图表)
export function countStrangeCar(params) {
  return request({
    url: '/security/report/countStrangeCar',
    method: 'get',
    params
  })
}
//获取导入模板
export function getImportTemplate() {
  return request({
    url: '/security/carInfo/getImportTemplate',
    method: 'get',
    responseType: "blob",
  })
}
//车辆导入
export function importCarInfo(data) {
  return request({
    url: '/security/carInfo/importCarInfo',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}
//新增区域围栏
export function areaFenceAdd(data) {
  return request({
    url: '/security/areaFence/add',
    method: 'post',
    data
  })
}
//查询边界围栏
export function areaFenceList(params) {
  return request({
    url: '/security/areaFence/list',
    method: 'get',
    params
  })
}
//查询边界围栏(新）
export function listAreas(params) {
  return request({
    url: '/security/carAccess/listAreas',
    method: 'get',
    params
  })
}
//查询边界围栏(新）
export function listAreasType(params) {
  return request({
    url: '/security/carAccessType/listAreas',
    method: 'get',
    params
  })
}
//修改区域围栏
export function areaFenceUpdate(data) {
  return request({
    url: '/security/areaFence/update',
    method: 'post',
    data
  })
}
//删除区域围栏
export function areaFenceDelete(data) {
  return request({
    url: '/security/areaFence/delete',
    method: 'post',
    data
  })
}
//新增重型卡车
export function heavyTruckAdd(data) {
  return request({
    url: '/security/heavyTruckEdit/add',
    method: 'post',
    data
  })
}
//分页查询重型卡车
export function heavyTruckList(data) {
  return request({
    url: '/security/heavyTruckEdit/listPage',
    method: 'post',
    data
  })
}
//修改重型卡车
export function heavyTruckUpdate(data) {
  return request({
    url: '/security/heavyTruckEdit/updateById',
    method: 'post',
    data
  })
}
//根据id查找详情重型卡车
export function heavyTruckFindById(params) {
  return request({
    url: '/security/heavyTruckEdit/findById',
    method: 'get',
    params
  })
}
//根据id查找详情车辆授权
export function findAccessById(params) {
  return request({
    url: '/security/carAccess/findAccessById',
    method: 'get',
    params
  })
}
//删除车辆
export function heavyTruckDeleteById(data) {
  return request({
    url: '/security/heavyTruckEdit/deleteById',
    method: 'post',
    data
  })
}
//分页查询告警中心
export function alarmInfoFindPage(data) {
  return request({
    url: '/security/alarmInfo/findPage',
    method: 'post',
    data
  })
}
//查询外部公司部门树
export function findOutsideDeptTree() {
  return request({
    url: '/security/dept/findOutsideDeptTree',
    method: 'get',
  })
}
//创建外部部门
export function createOutsideDept(data) {
  return request({
    url: '/security/dept/createOutsideDept',
    method: 'post',
    data
  })
}
//删除外部部门
export function deleteOutsideDeptById(data) {
  return request({
    url: '/security/dept/deleteOutsideDeptById',
    method: 'post',
    data
  })
}
//修改外部部门
export function updateOutsideDeptById(data) {
  return request({
    url: '/security/dept/updateOutsideDeptById',
    method: 'post',
    data
  })
}
//获取gps轨迹
export function getGpsRecords(params) {
  return request({
    url: '/security/carPassRecord/getGpsRecords',
    method: 'get',
    params
  })
}
//已入库车辆通行记录分页查询
export function pageInSystem(data) {
  return request({
    url: '/security/carPassRecord/pageInSystem',
    method: 'post',
    data
  })
}
//陌生车辆通行记录分页查询
export function pageStranger(data) {
  return request({
    url: '/security/carPassRecord/pageStranger',
    method: 'post',
    data
  })
}
//修改全局配置
export function changeValue(data) {
  return request({
    url: '/security/globalConfig/changeValue',
    method: 'post',
    data
  })
}
//查询全局配置
export function findValue(params) {
  return request({
    url: '/security/globalConfig/findValue',
    method: 'get',
    params
  })
}
//绑定gps设备
export function bindGpsDevice(data) {
  return request({
    url: '/security/carGpsLink/bindGpsDevice',
    method: 'post',
    data
  })
}
//查找gps设备编号
export function findByCarPlate(params) {
  return request({
    url: '/security/carGpsLink/findByCarPlate',
    method: 'get',
    params
  })
}
//移除gps设备
export function removeGpsDevice(data) {
  return request({
    url: '/security/carGpsLink/removeGpsDevice',
    method: 'post',
    data
  })
}
//车辆授权列表查询
export function carAccessListPage(data) {
  return request({
    url: '/security/carAccess/listPage',
    method: 'post',
    data
  })
}
//车辆授权列表加入黑名单
export function carAccessJoinBlacklist(data) {
  return request({
    url: '/security/carAccess/joinBlacklist',
    method: 'post',
    data
  })
}
//车辆授权列表移除黑名单
export function carAccessRemoveBlacklist(data) {
  return request({
    url: '/security/carAccess/removeBlacklist',
    method: 'post',
    data
  })
}
//车辆授权列表加入黑名单
export function carAccessJoinWhitelist(data) {
  return request({
    url: '/security/carAccess/joinWhitelist',
    method: 'post',
    data
  })
}
//车辆授权列表移除黑名单
export function carAccessRemoveWhitelist(data) {
  return request({
    url: '/security/carAccess/removeWhitelist',
    method: 'post',
    data
  })
}
//更新区域权限
export function updateArea(data) {
  return request({
    url: '/security/carAccess/updateArea',
    method: 'post',
    data
  })
}
//更新门禁权限
export function updateAccess(data) {
  return request({
    url: '/security/carAccess/updateAccess',
    method: 'post',
    data
  })
}
//停车数总览
export function overviewTotal(params) {
  return request({
    url: '/security/parkCar/overviewTotal',
    method: 'get',
    params
  })
}
//区域停车统计
export function getAreaParkDetail(params) {
  return request({
    url: '/security/parkCar/getAreaParkDetail',
    method: 'get',
    params
  })
}
//停车车次统计
export function getParkDetail(params) {
  return request({
    url: '/security/parkCar/getParkDetail',
    method: 'get',
    params
  })
}
//获取车位利用率
export function getParkUseRatio(params) {
  return request({
    url: '/security/parkCar/getParkUseRatio',
    method: 'get',
    params
  })
}
//停车车位使用率排行
export function getParkUseRatioRank(params) {
  return request({
    url: '/security/parkCar/getParkUseRatioRank',
    method: 'get',
    params
  })
}
