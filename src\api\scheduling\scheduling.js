import request from "@/utils/request";

// 排班管理-班次管理分页查询
export function getPageList(query) {
  return request({
    url: "/schedule/arrangement/pageList",
    method: "get",
    params: query,
  });
}

// 新增班次
export function addRole(data) {
  return request({
    url: "/schedule/arrangement/save",
    method: "post",
    data: data,
  });
}

// 调班管理分页
export function pageList(query) {
  return request({
    url: "/schedule/work-adjustment/pageList",
    method: "get",
    params: query,
  });
}

//我的排班表
export function mySchedule(query) {
    return request({
      url: "/schedule/schedule/mySchedule",
      method: "get",
      params: query,
    });
  }

// 调班新增
export function addsave(data) {
  return request({
    url: "/schedule/work-adjustment/save",
    method: "post",
    data: data,
  });
}
// 补卡新增
export function bkaddsave(data) {
    return request({
      url: "/schedule/repair-attend-apply/save",
      method: "post",
      data: data,
    });
}
// 补卡新增
export function uploadFile(data) {
    return request({
      url: "/schedule/file/uploadFile",
      method: "post",
      data: data,
    });
}


// 获取服务组
export function findList(query) {
  return request({
    url: "/schedule/service-group/findList",
    method: "get",
    params: query,
  });
}

// 获取服务组下拉框列表
export function getServiceGroupSelectList(query) {
  return request({
    url: "/schedule/service-group/getSelectList",
    method: "get",
    params: query,
  });
}


// 获取人员列表
export function findListMan(query) {
  return request({
    url: "/schedule/member/findList",
    method: "get",
    params: query,
  });
}


//排版详情获取日期
export function getShowData(query) {
    return request({
      url: "/schedule/schedule/getShowData",
      method: "get",
      params: query,
    });
  }


  //调班管理  -  详情
  export function tbglDetail(query) {
    return request({
      url: "/schedule/work-adjustment/detail",
      method: "get",
      params: query,
    });
  }
  //调班管理  -  流程详情
  export function tbgllcDetail(query) {
    return request({
      url: "/schedule/work-adjustment/getActInfo",
      method: "get",
      params: query,
    });
  }
// 调班管理 - 提交审批流程
export function submitAct(data) {
    return request({
      url: "/schedule/work-adjustment/submitAct",
      method: "post",
      params: data,
    });
  }
// 调班管理 - 撤销审批流程
export function withdrawAct(query) {
  return request({
    url: "/schedule/work-adjustment/withdrawAct",
    method: "get",
    params: query,
  });
}
//排班管理  -  分页查询
export function pbglPageList(query) {
    return request({
        url: "/schedule/schedule/pageList",
        method: "get",
        params: query,
    });
}
    //获取班次类型
export function getArrangementTypeList(query) {
    return request({
        url: "/schedule/arrangement/getArrangementTypeList",
        method: "get",
        params: query,
    });
}
//获取班次
export function bcfindList(query) {
    return request({
        url: "/schedule/arrangement/findList",
        method: "get",
        params: query,
    });
}
//获取班次管理，详情
export function bcDetail(query) {
    return request({
        url: "/schedule/arrangement/detail",
        method: "get",
        params: query,
    });
}
// 排班详情 自动排班保存接口
export function autoSetSchedule(data) {
    return request({
      url: "/schedule/schedule/autoSetSchedule",
      method: "post",
      data: data,
    });
}
  // 删除班次
export function deleteByIds(data) {
    return request({
      url: "/schedule/arrangement/deleteByIds",
      method: "post",
      params: data,
    });
}
    // 新增排班
export function addpb(data) {
    return request({
      url: "/schedule/schedule/save",
      method: "post",
      data: data,
    });
}
//导出排班接口
export function exportExcel(query) {
    return request({
        url: "/schedule/schedule/exportExcel",
        method: "get",
        params: query,
        responseType: "blob"
    });
}
//保存排班接口
export function saveEntitys(data) {
    return request({
        url: "/schedule/member-work/saveEntitys",
        method: "post",
        data: data,
    });
}
//导入接口排班接口
export function importExcel(data) {
    return request({
        url: "/schedule/schedule/import",
        method: "post",
        data: data,
    });
}
//补卡申请分页
export function bkPageList(query) {
    return request({
        url: "/schedule/repair-attend-apply/pageList",
        method: "get",
        params: query,
    });
}
//调班管理修改接口
export function tbUpdate(data) {
    return request({
        url: "/schedule/work-adjustment/update",
        method: "post",
        data: data,
    });
}
//补卡申请修改接口
export function bkUpdate(data) {
    return request({
        url: "/schedule/repair-attend-apply/update",
        method: "post",
        data: data,
    });
}
//班次申请编辑后修改
export function bcUpdate(data) {
    return request({
        url: "/schedule/arrangement/update",
        method: "post",
        data: data,
    });
}
//排班管理删除
export function pbdeleteByIds(data) {
    return request({
        url: "/schedule/schedule/deleteByIds",
        method: "post",
        params: data,
    });
}
//补卡申请的提交接口
export function bksubmitAct(data) {
    return request({
        url: "/schedule/repair-attend-apply/submitAct",
        method: "post",
        params: data,
    });
}
//调班管理删除接口
export function tbglDeleteByIds(data) {
    return request({
        url: "/schedule/work-adjustment/deleteByIds",
        method: "post",
        params: data,
    });
}
//排班管理删除
export function bkDeleteByIds(data) {
    return request({
        url: "/schedule/repair-attend-apply/deleteByIds",
        method: "post",
        params: data,
    });
}
//补卡申请 详情
export function bkDetail(query) {
    return request({
        url: "/schedule/repair-attend-apply/detail",
        method: "get",
        params: query,
    });
}
  //补卡申请  -  流程详情
  export function bksqlcDetail(query) {
    return request({
      url: "/schedule/repair-attend-apply/getActInfo",
      method: "get",
      params: query,
    });
  }
  //规则设置  -  查询状态
  export function gzszFindList(query) {
    return request({
      url: "/schedule/sys-rule/findList",
      method: "get",
      params: query,
    });
  }
//规则设置  -  修改状态
export function gzszUpdate(data) {
    return request({
        url: "/schedule/sys-rule/update",
        method: "post",
        params: data,
    });
}
// 获取当前登录人信息
export function getLoginMemberInfo(query) {
    return request({
      url: "/schedule/sysQuery/getLoginMemberInfo",
      method: "get",
      params: query,
    });
}
// 调班管理    同意or不同意
export function approvalOperation(data) {
    return request({
        url: "/schedule/work-adjustment/approvalOperation",
        method: "post",
        params: data,
    });
}


// 全员排班表
export function getAllShowData(query) {
    return request({
        url: "/schedule/schedule/getAllShowData",
        method: "get",
        params: query,
    });
}

// 考勤组  全量查询
export function getAllfindList(query) {
    return request({
        url: "/schedule/service-group/findList",
        method: "get",
    });
}


//班次是否可以被使用
export function changeClasseStatus(data){
  return request({
    url: "/schedule/arrangement/updateStatus",
    method: "post",
    params:data
  });
}


//是否开启消息提醒
export function updateRemind(data) {
  return request({
    url: "/schedule/service-group/updateRemind",
    method: "post",
    data: data
  });
}
