import request from "@/utils/request";

// 服务组分页查询
export function getPageList(query) {
  return request({
    url: "/schedule/service-group/pageList",
    method: "get",
    params: query,
  });
}
// 服务组新增
export function addServiceGroup(data) {
  return request({
    url: "/schedule/service-group/save",
    method: "post",
    data: data,
  });
}
// 服务组批量删除
export function deleteByIds(data) {
    return request({
      url: "/schedule/service-group/deleteByIds",
      method: "post",
      params: data,
    });
  }

  //查看所有部门
export function getDeptList() {
  return request({
    url: "/schedule/sysQuery/getDeptList",
    method: "get",
    params: {},
  });
}

//根据部门获取用户
export function getDeptByUserList(params) {
  return request({
    url: "/schedule/sysQuery/getDeptByUserList",
    method: "get",
    params: params,
  });
}


  // 设备信息
export function login(data) {
    return request({
      url: "/identification/login",
      method: "post",
      params: data,
    });
  }
// 新增
export function updates(data) {
  return request({
    url: "/schedule/service-group/update",
    method: "post",
    data: data,

  });
}

//多文件上传
export function uploads(data) {
  return request({
    url: process.env.VUE_APP_BASE_API + "/common/uploads",
    method: "post",
    data: data,
    headers: {'Content-Type': 'multipart/form-data'}
  });
}

// 状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status,
  };
  return request({
    url: "/system/building/changeStatus",
    method: "put",
    data: data,
  });
}

// 删除
export function delRole(roleId) {
  return request({
    url: "/system/building/" + roleId,
    method: "delete",
  });
}

// 查询角色已授权用户列表
export function allocatedUserList(query) {
  return request({
    url: "/system/building/authUser/allocatedList",
    method: "get",
    params: query,
  });
}

// 查询角色未授权用户列表
export function unallocatedUserList(query) {
  return request({
    url: "/system/building/authUser/unallocatedList",
    method: "get",
    params: query,
  });
}


