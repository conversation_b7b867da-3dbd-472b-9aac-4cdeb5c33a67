import request from '@/utils/request'

// 查询参数列表
export function  filterTime(time) {
    var date = new Date(time);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? "0" + m : m;
    var d = date.getDate();
    d = d < 10 ? "0" + d : d;
    var h = date.getHours();
    h = h < 10 ? "0" + h : h;
    var minute = date.getMinutes();
    minute = minute < 10 ? "0" + minute : minute;
    var s = date.getSeconds();
    s = s < 10 ? "0" + s : s;
    return y + "-" + m + "-" + d + " " + h + ":" + minute + ":" + s;
}
// 时分秒
export function  filterTimes(time) {
    var date = new Date(time);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? "0" + m : m;
    var d = date.getDate();
    d = d < 10 ? "0" + d : d;
    var h = date.getHours();
    h = h < 10 ? "0" + h : h;
    var minute = date.getMinutes();
    minute = minute < 10 ? "0" + minute : minute;
    var s = date.getSeconds();
    s = s < 10 ? "0" + s : s;
    return h + ":" + minute + ":" + s;
}
// 计算年月
export function  filterTimess(time) {
    var date = new Date(time);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? "0" + m : m;
    var d = date.getDate();
    d = d < 10 ? "0" + d : d;
    var h = date.getHours();
    h = h < 10 ? "0" + h : h;
    var minute = date.getMinutes();
    minute = minute < 10 ? "0" + minute : minute;
    var s = date.getSeconds();
    s = s < 10 ? "0" + s : s;
    return y + "-" + m ;
}
// 计算两段时间的时间差
export function  getDateDiff(statrTime, endTime, diffType){
    diffType = diffType.toLowerCase() // 大写转化为小写
    const sTime = statrTime //开始时间
    const eTime = endTime //结束时间
    let divNum = 1 //作为除数的数字
    if (diffType === 'second') {
        divNum = 1000
    } else if (diffType === 'minute') {
        divNum = 1000 * 60
    } else if (diffType === 'hour') {
        divNum = 1000 * 3600 
    } else { divNum = 1000 * 3600 * 24 }
    // parseInt()
    return ((eTime.getTime() - sTime.getTime()) / parseInt(divNum)).toFixed(2)
}
// 获取当前年月日
export function getNowDate() {
    const timeOne = new Date()
    const year = timeOne.getFullYear()
    let month = timeOne.getMonth() + 1
    let day = timeOne.getDate()
    month = month < 10 ? '0' + month : month
    day = day < 10 ? '0' + day : day
    const NOW_MONTHS_AGO = `${year}-${month}-${day}`
    return NOW_MONTHS_AGO
  }