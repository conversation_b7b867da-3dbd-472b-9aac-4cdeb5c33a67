import request from '@/utils/request'

// 获取查询条件信息
export function showClassifyInfo() {
  return request({
    url: '/equipment/maintain-standing-book/show-classifyInfo',
    method: 'get'
  })
}

//   添加设备类型
export function addType(typeName) {
  return request({
    url: '/equipment/type/add',
    method: 'post',
    data: {
        typeName:typeName
    }
  })
}
//   编辑设备类型
export function updateType(typeId,typeName) {
    return request({
      url: '/equipment/type/update',
      method: 'post',
      data: {
        typeId:typeId,
          typeName:typeName
      }
    })
  }
//   查询自定义属性
export function selectAttribute(typeId,page,size) {
    return request({
      url: '/equipment/type/select-attribute',
      method: 'post',
      data: {
        typeId:typeId,
        page:page,
        size:size
      }
    })
  }
//   新增自定义属性
export function addAttribute(typeId,attributeName,attributeDataType,attributeDefine,attributeUnit,isRequired) {
    return request({
      url: '/equipment/type/add-attribute',
      method: 'post',
      data: {
        typeId:typeId,
        attributeName:attributeName,
        attributeDataType:attributeDataType,
        attributeDefine:attributeDefine,
        attributeUnit:attributeUnit,
        isRequired:isRequired
      }
    })
  }
//   编辑自定义属性
export function updateAttribute(attributeId,attributeName,attributeDataType,attributeDefine,attributeUnit,isRequired) {
    return request({
      url: '/equipment/type/update-attribute',
      method: 'post',
      data: {
        attributeId:attributeId,
        attributeName:attributeName,
        attributeDataType:attributeDataType,
        attributeDefine:attributeDefine,
        attributeUnit:attributeUnit,
        isRequired:isRequired
      }
    })
  }
  // 删除属性
  export function deleteAttribute(attributeId) {
    return request({
      url: '/equipment/type/delete-attribute',
      method: 'get',
      params:{
        attributeId:attributeId
      }
    })
  }
  