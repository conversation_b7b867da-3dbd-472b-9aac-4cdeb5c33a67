import request from '@/utils/request'

// 按条件分页查询所有厂商
export function selectVendor(page,size,vendorName,isEnabled) {
    return request({
      url: '/equipment/vendor/select-page',
      method: 'post',
     data:{
        page:page,
        size:size,
        vendorName:vendorName,
        isEnabled:isEnabled
     }
    })
  }
//   查看厂商详情
export function selectDetail(vendorId) {
    return request({
      url: '/equipment/vendor/select-detail',
      method: 'get',
     params:{
        vendorId:vendorId
     }
    })
  }
//   添加厂商
export function addVendor(data) {
    return request({
      url: '/equipment/vendor/add',
      method: 'post',
     data:data
    })
  }
//   禁用/启动厂商
export function changeStatus(vendorId,isEnabled) {
    return request({
      url: '/equipment/vendor/change-status',
      method: 'post',
      data:{
        vendorId:vendorId,
        isEnabled:isEnabled
      }
     
    })
  }
  // 编辑厂商
  export function updateStatus(vendorId,vendorName,vendorFullName,vendorEngName,vendorContact,vendorTel,vendorAddress) {
    return request({
      url: '/equipment/vendor/update',
      method: 'post',
      data:{
        vendorId:vendorId,
        vendorName:vendorName,
        vendorFullName:vendorFullName,
        vendorEngName:vendorEngName,
        vendorContact:vendorContact,
        vendorTel:vendorTel,
        vendorAddress:vendorAddress
      }
     
    })
  }