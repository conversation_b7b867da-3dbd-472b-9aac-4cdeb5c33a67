<template>
  <section  :class="isQiankun ? 'app-main':'app-main1'">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view v-if="!$route.meta.link" :key="key" />
      </keep-alive>
    </transition>
    <iframe-toggle />
  </section>
</template>

<script>
import iframeToggle from "./IframeToggle/index"

export default {
  name: 'AppMain',
  components: { iframeToggle },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  },
  data(){
    return{
      isQiankun:null
    }
  },
   mounted(){
    console.log(window.__POWERED_BY_QIANKUN__);
    if(window.__POWERED_BY_QIANKUN__){
      this.isQiankun=true
    }else{
      this.isQiankun=false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 132px);
  height:calc(100vh - 132px) ;
  width: 100%;
  position: absolute;
  top: 84px;
  overflow-y: scroll;
  background-color: #F5F5F5;
}
.app-main1 {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  top:0;
  background-color: #F5F5F5;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 132px);
  }
  .app-main1 {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 50px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
