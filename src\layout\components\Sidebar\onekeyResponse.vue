<template>
  <div>
    <el-dialog
      :visible.sync="onekeyDialog"
      width="30%"
      top="50vh"
      append-to-body
      :show-close="false"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialogTitle">
        <div style="font-size: 24px">一键应岗</div>
        <el-button type="primary" @click="confirmDuty">确认在岗</el-button>
      </div>
      <div class="dialogMain">
        <div>
          <el-progress
            type="circle"
            :percentage="percentage"
            :color="colors"
            :width="150"
          ></el-progress>
        </div>
        <div>
          <div style="margin: 20px 0">查岗时间：{{ checkTime }}</div>
          <div>剩余时间：{{ minute }}分{{ second }}秒</div>
        </div>
      </div>
      <div class="dialogFooter">
        <i class="el-icon-alarm-clock" style="margin-right: 10px"></i>
        <span>请您尽快确认在岗</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCheckPost,
  checkPost,
  notResponse,
} from "@/api/fireManagement/fireControlRoom/onekeyResponse/index";
export default {
  name: "onekeyResponse",
  data() {
    return {
      onekeyDialog: false,
      id: undefined,
      checkTime: "2023-01-01 00:00:00",
      timeRemaining: "",
      minute: "",
      second: "",
      percentage: 0,
      colors: [
        { color: "#f56c6c", percentage: 20 },
        { color: "#e6a23c", percentage: 40 },
        { color: "#5cb87a", percentage: 60 },
        { color: "#1989fa", percentage: 80 },
        { color: "#6f7ad3", percentage: 100 },
      ],
    };
  },
  created() {
    getCheckPost({ checkTime: this.getCurrentTime()[0] }).then((res) => {
      console.log(res,'轮询');
      if (res.data) {
        this.onekeyDialog = true;
        this.checkTime = res.data.checkTime;
        this.id = res.data.id;
        var str = res.data.checkTime;
        var id = res.data.id;
        localStorage.setItem("checkTime", str);
        localStorage.setItem("id", this.id);
        var timestamp = Date.parse(new Date(res.data.checkTime));
        console.log(timestamp,'timestamp');
        this.timeRemaining = (Date.parse(new Date()) - timestamp) / 1000;
        setInterval(() => {
          if (this.timeRemaining <= 300) {
            this.timeRemaining++;
            this.percentage = parseInt((300 - this.timeRemaining) / 3);
            this.minute = parseInt((300 - this.timeRemaining) / 60);
            this.second = parseInt((300 - this.timeRemaining) % 60);
          } else {
            this.percentage = 0;
            this.minute = 0;
            this.second = 0;
          }
        }, 1000);
      }
    });
    setInterval(() => {
      // 首先判断本地有无应岗时间
      if (localStorage.getItem("checkTime")) {
        var timestamp = Date.parse(new Date(localStorage.getItem("checkTime")));
        var time = (Date.parse(new Date()) - timestamp) / 1000;
        // 判断是否已经超时
        if (time > 300) {
          // 已经超时(调用未应岗接口销毁本地存储的应岗信息)
          const data = {};
          data.id = localStorage.getItem("id");
          data.checkStatus = "4010803";
          notResponse(data).then((res) => {
            console.log(res,'轮询1');
            if (res.code == 200) {
              localStorage.removeItem("id");
              localStorage.removeItem("checkTime");
              getCheckPost({ checkTime: this.getCurrentTime()[0] }).then(
                (res) => {
                  if (res.data) {
                    this.onekeyDialog = true;
                    this.checkTime = res.data.checkTime;
                    this.id = res.data.id;
                    var str = res.data.checkTime;
                    localStorage.setItem("checkTime", str);
                    localStorage.setItem("id", this.id);
                    var timestamp = Date.parse(new Date(res.data.checkTime));
                    this.timeRemaining =
                      (Date.parse(new Date()) - timestamp) / 1000;
                    setInterval(() => {
                      if (this.timeRemaining <= 300) {
                        this.timeRemaining++;
                        this.percentage = parseInt(
                          (300 - this.timeRemaining) / 3
                        );
                        this.minute = parseInt((300 - this.timeRemaining) / 60);
                        this.second = parseInt((300 - this.timeRemaining) % 60);
                      } else {
                        this.percentage = 0;
                        this.minute = 0;
                        this.second = 0;
                      }
                    }, 1000);
                  }
                }
              );
            }
          });
        } else {
          // 暂未超时
          this.onekeyDialog = true;
          this.checkTime = localStorage.getItem("checkTime");
          this.id = localStorage.getItem("id");
          var timestamp = Date.parse(
            new Date(localStorage.getItem("checkTime"))
          );
          this.timeRemaining = (Date.parse(new Date()) - timestamp) / 1000;
          setInterval(() => {
            if (this.timeRemaining <= 300) {
              this.timeRemaining++;
              this.percentage = parseInt((300 - this.timeRemaining) / 3);
              this.minute = parseInt((300 - this.timeRemaining) / 60);
              this.second = parseInt((300 - this.timeRemaining) % 60);
            } else {
              const data = {};
              data.id = localStorage.getItem("id");
              data.checkStatus = "4010803";
              notResponse(data).then((res) => {
                if (res.code == 200) {
                  localStorage.removeItem("id");
                  localStorage.removeItem("checkTime");
                  this.onekeyDialog=false;
                }
              });
            }
          }, 1000);
        }
      } else {
        getCheckPost({ checkTime: this.getCurrentTime()[0] }).then((res) => {
          if (res.data) {
            console.log(res,'轮询2');
            this.onekeyDialog = true;
            this.checkTime = res.data.checkTime;
            this.id = res.data.id;
            var str = res.data.checkTime;
            localStorage.setItem("checkTime", str);
            localStorage.setItem("id", this.id);
            var timestamp = Date.parse(new Date(res.data.checkTime));
            this.timeRemaining = (Date.parse(new Date()) - timestamp) / 1000;
            setInterval(() => {
              if (this.timeRemaining <= 300) {
                this.timeRemaining++;
                this.percentage = parseInt((300 - this.timeRemaining) / 3);
                this.minute = parseInt((300 - this.timeRemaining) / 60);
                this.second = parseInt((300 - this.timeRemaining) % 60);
              } else {
                this.percentage = 0;
                this.minute = 0;
                this.second = 0;
              }
            }, 1000);
          }
        });
      }
    }, 60000);
  },
  methods: {
    open() {
      this.onekeyDialog = true;
    },
    //应岗
    confirmDuty() {
      const data = {};
      data.id = this.id;
      data.responseTime = this.getCurrentTime()[1];
      data.checkStatus = "4010802";
      this.$modal
        .confirm("是否确认应岗？")
        .then(() => {
          checkPost(data).then((res) => {
            if (res.code == 200) {
              this.onekeyDialog = false;
              localStorage.removeItem("id");
              localStorage.removeItem("checkTime");
              this.$message({
                message: "应岗成功！",
                type: "success",
              });
            }
          });
        })
        .catch(() => {});
    },
    getCurrentTime() {
      var date = new Date(); //当前时间
      var year = date.getFullYear(); //返回指定日期的年份
      var month = this.repair(date.getMonth() + 1); //月
      var day = this.repair(date.getDate()); //日
      var hour = this.repair(date.getHours()); //时
      var minute = this.repair(date.getMinutes()); //分
      var second = this.repair(date.getSeconds()); //秒

      //当前时间
      var curTime =
        year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + "00";
      var curTime1 =
        year +
        "-" +
        month +
        "-" +
        day +
        " " +
        hour +
        ":" +
        minute +
        ":" +
        second;
      return [curTime, curTime1];
    },

    //补0

    repair(i) {
      if (i >= 0 && i <= 9) {
        return "0" + i;
      } else {
        return i;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-dialog:not(.is-fullscreen) {
  margin-top: 36vh !important;
}
.dialogTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.dialogMain {
  margin: 20px 0;
  display: flex;
  justify-content: space-around;
  font-size: 18px;
}
.dialogFooter {
  text-align: center;
  font-size: 24px;
}
</style>