import Vue from 'vue'

import Cookies from 'js-cookie'
// qiankun
import VueRouter from 'vue-router';
import './public-path';
import actions from './action'
// 

import {Base64} from 'js-base64'
Vue.prototype.$Base64 = Base64;
//全局变量
import global from '@/global/global.js';
Vue.prototype.$global=global

import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css';
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/ruoyi.scss' // ruoyi css
import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'
import * as echarts from 'echarts';  //echarts
import handledownload from '@/utils/download'//get请求的通用下载
import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from "@/utils/ruoyi";

// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
// (新增)改变请求路径
import transitionUrl from '@/utils/transitionUrl'
Vue.use(transitionUrl)
// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handledownloadGet = handledownload
Vue.prototype.handleTree = handleTree
Vue.prototype.$echarts = echarts

// import AMapLoader from "@amap/amap-jsapi-loader";
// Vue.use(AMapLoader)
import AMapLoader from "@amap/amap-jsapi-loader";
Vue.prototype.AMapLoader = AMapLoader;

// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)

Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false;

//qiankun start 
let routers = null;
let instance = null;

function render(props) {
  if(window.__POWERED_BY_QIANKUN__){
    const { container } = props;
    if (props) {
      actions.setActions(props)
    }
  }
 
  routers = new VueRouter({
    mode: 'hash',
    router,
  });
  if(window.__POWERED_BY_QIANKUN__){
    instance = new Vue({
      router,
      store,
      render: h => h(App),
    }).$mount( '#app');
    
  }else{
    new Vue({
      el: '#app',
      router,
      store,
      render: h => h(App)
    })
  }
  
  
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

function storeTest(props) {
  props.onGlobalStateChange &&
    props.onGlobalStateChange(
      (value, prev) => console.log(`[onGlobalStateChange - ${props.name}]:`, value, prev),
      true,
    );
  props.setGlobalState &&
    props.setGlobalState({
      ignore: props.name,
      user: {
        name: props.name,
      },
    });
}

export async function bootstrap() {
  // console.log(instance.$store);

  console.log('[vue] vue app bootstraped');
}


export async function mount(props) {
  Vue.prototype.parentRouter = props.mainAppRouter
  console.log('[vue] props from main framework', props);
  storeTest(props);
  render(props);
}

export async function unmount() {
  // instance.$store.commit('SET_ROLES', [])
  // instance.$store.commit('SET_PERMISSIONS', [])
  // console.log(instance.$store);
  instance.$destroy();
  instance.$el.innerHTML = '';
  instance = null;
  routers = null;
}
// qiankun end