{"code": 0, "msg": "操作成功", "data": [{"id": 12931, "clientId": "ningjin-emergency-new", "name": "应急预案管理", "permission": null, "parentId": 12929, "icon": null, "path": "/portal/vue-emergency/planManagement", "sort": 1, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"Layout\",\n          \"name\": \"planManagement\",\n          \"alwaysShow\": true,\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"应急预案管理\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}", "deptId": 1, "weight": 1, "label": "应急预案管理", "children": [{"id": 12933, "clientId": "ningjin-emergency-new", "name": "企业预案", "permission": null, "parentId": 12931, "icon": null, "path": "chemicalEnterprisePlan", "sort": 1, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"emergency/new/chemicalEnterprisePlan/index\",\n          \"name\": \"chemicalEnterprisePlan\",\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"企业预案\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}\n", "deptId": 1, "weight": 1, "label": "企业预案", "children": null}, {"id": 13065, "clientId": "ningjin-emergency-new", "name": "园区预案", "permission": null, "parentId": 12931, "icon": null, "path": "parkPlan", "sort": 2, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"emergency/new/parkPlan/index\",\n          \"name\": \"parkPlan\",\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"园区预案\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}\n", "deptId": 1, "weight": 2, "label": "园区预案", "children": null}, {"id": 13067, "clientId": "ningjin-emergency-new", "name": "上级部门预案", "permission": null, "parentId": 12931, "icon": null, "path": "superiorDepartment", "sort": 3, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"emergency/new/superiorDepartment/index\",\n          \"name\": \"chemicalEnterprise\",\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"上级部门预案\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}\n", "deptId": 1, "weight": 3, "label": "上级部门预案", "children": null}, {"id": 12941, "clientId": "ningjin-emergency-new", "name": "数字化预案", "permission": null, "parentId": 12931, "icon": null, "path": "planManagement", "sort": 4, "type": "0", "keepAlive": "0", "external": "{\n                  \"path\": \"planManagement\",\n                  \"component\": \"emergency/emergencyPlan/structuredPlan/planManagement/index\",\n                  \"name\": \"PlanManagement\",\n                  \"hidden\": false,\n                  \"meta\": {\n                    \"title\": \"数字化预案\",\n                    \"noCache\": false,\n                    \"link\": null\n                  }\n                }", "deptId": 1, "weight": 4, "label": "数字化预案", "children": null}, {"id": 12943, "clientId": "ningjin-emergency-new", "name": "预案结构化", "permission": null, "parentId": 12931, "icon": null, "path": "structuredTemplate", "sort": 5, "type": "0", "keepAlive": "0", "external": " {\n                  \"path\": \"structuredTemplate\",\n                  \"component\": \"emergency/emergencyPlan/structuredPlan/structuredTemplate/index\",\n                  \"name\": \"StructuredTemplate\",\n                  \"hidden\": false,\n                  \"meta\": {\n                    \"title\": \"预案结构化\",\n                    \"noCache\": false,\n                    \"link\": null\n                  }\n                }", "deptId": 1, "weight": 5, "label": "预案结构化", "children": null}, {"id": 12945, "clientId": "ningjin-emergency-new", "name": "流程数字化", "permission": null, "parentId": 12931, "icon": null, "path": "digitalizeProcess", "sort": 6, "type": "0", "keepAlive": "0", "external": " {\n                  \"path\": \"digitalizeProcess\",\n                  \"component\": \"emergency/emergencyPlan/digitalizePlan/digitalizeProcess/index\",\n                  \"name\": \"DigitalizeProcess\",\n                  \"hidden\": false,\n                  \"meta\": {\n                    \"title\": \"流程数字化\",\n                    \"noCache\": false,\n                    \"link\": null\n                  }\n                }", "deptId": 1, "weight": 6, "label": "流程数字化", "children": null}, {"id": 12947, "clientId": "ningjin-emergency-new", "name": "内容数字化", "permission": null, "parentId": 12931, "icon": null, "path": "digitalizeContent", "sort": 7, "type": "0", "keepAlive": "0", "external": "{\n                  \"path\": \"digitalizeContent\",\n                  \"component\": \"emergency/emergencyPlan/digitalizePlan/digitalizeContent/index\",\n                  \"name\": \"DigitalizeContent\",\n                  \"hidden\": false,\n                  \"meta\": {\n                    \"title\": \"内容数字化\",\n                    \"noCache\": false,\n                    \"link\": null\n                  }\n                }", "deptId": 1, "weight": 7, "label": "内容数字化", "children": null}, {"id": 12937, "clientId": "ningjin-emergency-new", "name": "预案标签", "permission": null, "parentId": 12931, "icon": null, "path": "planLabel", "sort": 8, "type": "0", "keepAlive": "0", "external": "{\n                  \"path\": \"planLabel\",\n                  \"component\": \"emergency/emergencyPlan/planConfiguration/planLabel/index\",\n                  \"name\": \"PlanLabel\",\n                  \"hidden\": false,\n                  \"meta\": {\n                    \"title\": \"预案标签\",\n                    \"noCache\": false,\n                    \"link\": null\n                  }\n                }", "deptId": 1, "weight": 8, "label": "预案标签", "children": null}, {"id": 12939, "clientId": "ningjin-emergency-new", "name": "预案参数", "permission": null, "parentId": 12931, "icon": null, "path": "planParameters", "sort": 9, "type": "0", "keepAlive": "0", "external": " {\n                  \"path\": \"planParameters\",\n                  \"component\": \"emergency/emergencyPlan/planConfiguration/planParameters/index\",\n                  \"name\": \"PlanParameters\",\n                  \"hidden\": false,\n                  \"meta\": {\n                    \"title\": \"预案参数\",\n                    \"noCache\": false,\n                    \"link\": null\n                  }\n                }", "deptId": 1, "weight": 9, "label": "预案参数", "children": null}]}, {"id": 12949, "clientId": "ningjin-emergency-new", "name": "应急资源管理", "permission": null, "parentId": 12929, "icon": null, "path": "/portal/vue-emergency/emergency/naturalResources", "sort": 3, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"Layout\",\n          \"name\": \"NaturalResources\",\n          \"alwaysShow\": true,\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"应急资源管理\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}", "deptId": 1, "weight": 3, "label": "应急资源管理", "children": [{"id": 12951, "clientId": "ningjin-emergency-new", "name": "救援专家", "permission": null, "parentId": 12949, "icon": null, "path": "rescueExperts", "sort": 1, "type": "0", "keepAlive": "0", "external": "{\n              \"path\": \"rescueExperts\",\n              \"component\": \"emergency/naturalResources/rescueExperts/index\",\n              \"name\": \"rescueExperts\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"救援专家\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 1, "label": "救援专家", "children": null}, {"id": 12953, "clientId": "ningjin-emergency-new", "name": "救援队伍", "permission": null, "parentId": 12949, "icon": null, "path": "Specialist", "sort": 2, "type": "0", "keepAlive": "0", "external": "{\n \"component\": \"emergency/naturalResources/specialist/index\",\n              \"name\": \"Specialist\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"救援队伍\",\n                \"noCache\": false,\n                \"link\": null\n              }\n}", "deptId": 1, "weight": 2, "label": "救援队伍", "children": null}, {"id": 12955, "clientId": "ningjin-emergency-new", "name": "物资储备管理", "permission": null, "parentId": 12949, "icon": null, "path": "EmergencySupplies", "sort": 3, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"emergency/naturalResources/emergencySupplies/index\",\n              \"name\": \"EmergencySupplies\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"物资储备管理\",\n                \"noCache\": false,\n                \"link\": null\n              }\n}", "deptId": 1, "weight": 3, "label": "物资储备管理", "children": null}, {"id": 12999, "clientId": "ningjin-emergency-new", "name": "物资与装备", "permission": null, "parentId": 12949, "icon": null, "path": "MaterialEquipment", "sort": 11, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"emergency/naturalResources/materialEquipment/index\",\n              \"name\": \"MaterialEquipment\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"物资与装备\",\n                \"noCache\": false,\n                \"link\": null\n              }\n}", "deptId": 1, "weight": 11, "label": "物资与装备", "children": null}, {"id": 12957, "clientId": "ningjin-emergency-new", "name": "避难场所", "permission": null, "parentId": 12949, "icon": null, "path": "shelter", "sort": 4, "type": "0", "keepAlive": "0", "external": "{\n \"component\": \"emergency/naturalResources/shelter/index\",\n              \"name\": \"shelter\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"避难场所\",\n                \"noCache\": false,\n                \"link\": null\n              }\n}", "deptId": 1, "weight": 4, "label": "避难场所", "children": null}, {"id": 12959, "clientId": "ningjin-emergency-new", "name": "周边监控", "permission": null, "parentId": 12949, "icon": null, "path": "SupervisoryControl", "sort": 5, "type": "0", "keepAlive": "0", "external": "{\n \"component\": \"emergency/naturalResources/supervisoryControl/index\",\n              \"name\": \"SupervisoryControl\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"周边监控\",\n                \"noCache\": false,\n                \"link\": null\n              }\n}", "deptId": 1, "weight": 5, "label": "周边监控", "children": null}, {"id": 12961, "clientId": "ningjin-emergency-new", "name": "防护目标", "permission": null, "parentId": 12949, "icon": null, "path": "protectiveTargets", "sort": 6, "type": "0", "keepAlive": "0", "external": " {\n              \"component\": \"emergency/naturalResources/protectiveTargets/index\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"防护目标\",\n                \"noCache\": true,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 6, "label": "防护目标", "children": null}, {"id": 12963, "clientId": "ningjin-emergency-new", "name": "风险隐患部位", "permission": null, "parentId": 12949, "icon": null, "path": "riskPosition", "sort": 7, "type": "0", "keepAlive": "0", "external": "{\n              \"path\": \"riskPosition\",\n              \"component\": \"emergency/naturalResources/riskPosition/index\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"风险隐患部位\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 7, "label": "风险隐患部位", "children": null}, {"id": 12965, "clientId": "ningjin-emergency-new", "name": "通讯保障机构", "permission": null, "parentId": 12949, "icon": null, "path": "communicate", "sort": 8, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"communicate\",\n              \"component\": \"emergency/naturalResources/communicate/index\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"通讯保障机构\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 8, "label": "通讯保障机构", "children": null}, {"id": 12967, "clientId": "ningjin-emergency-new", "name": "医疗卫生机构", "permission": null, "parentId": 12949, "icon": null, "path": "medicalInstitution", "sort": 9, "type": "0", "keepAlive": "0", "external": "{\n              \"path\": \"medicalInstitution\",\n              \"component\": \"emergency/naturalResources/medicalInstitution/index\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"医疗卫生机构\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 9, "label": "医疗卫生机构", "children": null}, {"id": 12969, "clientId": "ningjin-emergency-new", "name": "统计分析", "permission": null, "parentId": 12949, "icon": null, "path": "bulletinBoard", "sort": 10, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"emergency/bulletinBoard/index\",\n          \"name\": \"BulletinBoard\",\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"统计分析\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}\n", "deptId": 1, "weight": 10, "label": "统计分析", "children": null}]}, {"id": 12981, "clientId": "ningjin-emergency-new", "name": "预警报警处置", "permission": null, "parentId": 12929, "icon": null, "path": "/portal/vue-emergency/emergency/alarmInformation", "sort": 5, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"Layout\",\n          \"name\": \"alarmInformation\",\n          \"alwaysShow\": true,\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"预警报警处置\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}", "deptId": 1, "weight": 5, "label": "预警报警处置", "children": [{"id": 13009, "clientId": "ningjin-emergency-new", "name": "预警事件管理", "permission": null, "parentId": 12981, "icon": null, "path": "alarmInformation", "sort": 1, "type": "0", "keepAlive": "0", "external": "{\n          \"path\": \"alarmInformation\",\n          \"component\": \"emergency/new/overview/alarmInformation/index\",\n          \"name\": \"alarmInformation\",\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"预警事件管理\",\n            \"noCache\": false,\n            \"link\": null\n          }\n        }", "deptId": 1, "weight": 1, "label": "指挥调度", "children": null}]}, {"id": 12981, "clientId": "ningjin-emergency-new", "name": "应急指挥调度", "permission": null, "parentId": 12929, "icon": null, "path": "/portal/vue-emergency/emergency/emergencyCommand", "sort": 5, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"Layout\",\n          \"name\": \"emergencyCommand\",\n          \"alwaysShow\": true,\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"应急指挥调度\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}", "deptId": 1, "weight": 5, "label": "应急指挥调度", "children": [{"id": 13005, "clientId": "ningjin-emergency-new", "name": "指挥调度", "permission": null, "parentId": 12981, "icon": null, "path": "accidentSimulation", "sort": 1, "type": "0", "keepAlive": "0", "external": "{\n          \"path\": \"accidentSimulation\",\n          \"component\": \"emergency/new/accidentSimulation/index\",\n          \"name\": \"accidentSimulation\",\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"指挥调度\",\n            \"noCache\": false,\n            \"link\": null\n          }\n        }", "deptId": 1, "weight": 1, "label": "指挥调度", "children": null}, {"id": 12999, "clientId": "ningjin-emergency-new", "name": "应急车辆出动", "permission": null, "parentId": 12981, "icon": null, "path": "vehicleDispatch", "sort": 9, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"vehicleDispatch\",\n              \"component\": \"emergency/new/vehicleDispatch/index\",\n              \"name\": \"vehicleDispatch\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"应急车辆出动\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 9, "label": "应急车辆出动", "children": null}]}, {"id": 13003, "clientId": "ningjin-emergency-new", "name": "应急辅助决策", "permission": null, "parentId": 12929, "icon": null, "path": "/portal/vue-emergency/emergency/assistantDecision", "sort": 6, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"Layout\",\n          \"name\": \"assistantDecision\",\n          \"alwaysShow\": true,\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"应急辅助决策\",\n            \"noCache\": false,\n            \"breadcrumb\": false,\n            \"link\": null\n          }\n}", "deptId": 1, "weight": 6, "label": "应急辅助决策", "children": [{"id": 13001, "clientId": "ningjin-emergency-new", "name": "指挥调度", "permission": null, "parentId": 13003, "icon": null, "path": "commandDispatch", "sort": 10, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"commandDispatch\",\n              \"component\": \"emergency/emergencyCommand/commandDispatch/index\",\n              \"name\": \"commandDispatch\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"指挥调度\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 10, "label": "指挥调度", "children": null}, {"id": 13071, "clientId": "ningjin-emergency-new", "name": "应急处置方案", "permission": null, "parentId": 13003, "icon": null, "path": "assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "sort": 11, "type": "0", "keepAlive": "0", "external": "{\n          \"path\": \"assistantDecisionIndex\",\n          \"component\": \"emergency/assistantDecisionNew/index\",\n          \"name\": \"assistantDecisionIndex\",\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"应急处置方案\",\n            \"noCache\": false,\n            \"link\": null\n          }\n        }", "deptId": 1, "weight": 11, "label": "应急处置方案", "children": null}]}, {"id": 12935, "clientId": "ningjin-emergency-new", "name": "应急事后管理", "permission": null, "parentId": 12929, "icon": null, "path": "/portal/vue-emergency/emergency/knowledgeBase", "sort": 7, "type": "0", "keepAlive": "0", "external": "{\n \"path\": \"/portal/vue-emergency/emergency/digitalizePlan\",\n              \"component\": \"Layout\",\n              \"name\": \"DigitalizePlan\",\n              \"alwaysShow\": true,\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"应急事后管理\",\n                \"noCache\": false,\n                \"link\": null\n              }\n}", "deptId": 1, "weight": 7, "label": "应急事后管理", "children": [{"id": 13115, "clientId": "ningjin-emergency-new", "name": "事件归档", "permission": null, "parentId": 12935, "icon": null, "path": "file", "sort": 999, "type": "0", "keepAlive": "0", "external": "{\n              \"path\": \"file\",\n              \"component\": \"emergency/knowledgeBase/file/index\",\n              \"name\": \"file\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"事件归档\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 999, "label": "事件归档", "children": null}]}, {"id": 13737, "clientId": "ningjin-emergency-new", "name": "应急值守", "permission": null, "parentId": 12929, "icon": null, "path": "/portal/vue-emergency/emergency/emergencyCommand1", "sort": 10, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"Layout\",\n          \"name\": \"emergencyCommand1\",\n          \"alwaysShow\": true,\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"应急值守\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}", "deptId": 1, "weight": 10, "label": "应急值守", "children": [{"id": 12993, "clientId": "ningjin-emergency-new", "name": "事件接报", "permission": null, "parentId": 13737, "icon": null, "path": "eventManagement", "sort": 1, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"eventManagement\",\n              \"component\": \"emergency/emergencyDuty/eventManagement/index\",\n              \"name\": \"EventManagement\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"事件接报\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 1, "label": "事件接报", "children": null}, {"id": 12983, "clientId": "ningjin-emergency-new", "name": "值班人员管理", "permission": null, "parentId": 13737, "icon": null, "path": "serviceGroup", "sort": 2, "type": "0", "keepAlive": "0", "external": "{\n                    \"name\": \"serviceGroup\",\n                    \"path\": \"serviceGroup\",\n                    \"hidden\": false,\n                    \"component\": \"serviceGroup/index\",\n                    \"meta\": {\n                        \"title\": \"值班人员管理\",\n                        \"noCache\": false,\n                        \"link\": null\n                    }\n                }", "deptId": 1, "weight": 2, "label": "值班人员管理", "children": null}, {"id": 12985, "clientId": "ningjin-emergency-new", "name": "新增班次", "permission": null, "parentId": 13737, "icon": null, "path": "shiftmanaDetail", "sort": 3, "type": "0", "keepAlive": "0", "external": "{\n                    \"name\": \"shiftmanaDetail\",\n                    \"path\": \"shiftmanaDetail\",\n                    \"hidden\": true,\n                    \"component\": \"scheduling/shiftmana/detail\",\n                    \"meta\": {\n                        \"title\": \"新增班次\",\n                        \"noCache\": false,\n                        \"link\": null\n                    }\n                }", "deptId": 1, "weight": 3, "label": "新增班次", "children": null}, {"id": 12987, "clientId": "ningjin-emergency-new", "name": "值班制度配置", "permission": null, "parentId": 13737, "icon": null, "path": "shiftmana", "sort": 5, "type": "0", "keepAlive": "0", "external": "{\n                    \"name\": \"shiftmana\",\n                    \"path\": \"shiftmana\",\n                    \"hidden\": false,\n                    \"component\": \"scheduling/shiftmana/index\",\n                    \"meta\": {\n                        \"title\": \"值班制度配置\",\n                        \"noCache\": false,\n                        \"link\": null\n                    }\n                }", "deptId": 1, "weight": 5, "label": "值班制度配置", "children": null}, {"id": 12989, "clientId": "ningjin-emergency-new", "name": "值排班", "permission": null, "parentId": 13737, "icon": null, "path": "<PERSON>mana", "sort": 6, "type": "0", "keepAlive": "0", "external": "{\n                    \"name\": \"schedulingmana\",\n                    \"path\": \"schedulingmana\",\n                    \"hidden\": false,\n                    \"component\": \"scheduling/schedulingmana/index\",\n                    \"meta\": {\n                        \"title\": \"值排班\",\n                        \"noCache\": false,\n                        \"link\": null\n                    }\n                }", "deptId": 1, "weight": 6, "label": "值排班", "children": null}, {"id": 12991, "clientId": "ningjin-emergency-new", "name": "排班详情", "permission": null, "parentId": 13737, "icon": null, "path": "schedulingmanaDetail", "sort": 7, "type": "0", "keepAlive": "0", "external": "{\n                    \"name\": \"schedulingmanaDetail\",\n                    \"path\": \"schedulingmanaDetail\",\n                    \"hidden\": true,\n                    \"component\": \"scheduling/schedulingmana/detail\",\n                    \"meta\": {\n                        \"title\": \"排班详情\",\n                        \"noCache\": false,\n                        \"link\": null\n                    }\n                }", "deptId": 1, "weight": 7, "label": "排班详情", "children": null}, {"id": 12997, "clientId": "ningjin-emergency-new", "name": "值班日志", "permission": null, "parentId": 13737, "icon": null, "path": "dutyLog", "sort": 8, "type": "0", "keepAlive": "0", "external": "{\n              \"path\": \"dutyLog\",\n              \"component\": \"emergency/emergencyDuty/dutyLog/index\",\n              \"name\": \"DutyLog\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"值班日志\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 8, "label": "值班日志", "children": null}, {"id": 12995, "clientId": "ningjin-emergency-new", "name": "值班情况", "permission": null, "parentId": 13737, "icon": null, "path": "dutySituation", "sort": 9, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"dutySituation\",\n              \"component\": \"emergency/emergencyDuty/dutySituation/index\",\n              \"name\": \"DutySituation\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"值班情况\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 9, "label": "值班情况", "children": null}]}, {"id": 12971, "clientId": "ningjin-emergency-new", "name": "应急演练管理", "permission": null, "parentId": 12929, "icon": null, "path": "/portal/vue-emergency/emergency/emergencyDrill", "sort": 11, "type": "0", "keepAlive": "0", "external": "{\n \"component\": \"Layout\",\n          \"name\": \"emergencyDrill\",\n          \"alwaysShow\": true,\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"应急演练管理\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}", "deptId": 1, "weight": 11, "label": "应急演练管理", "children": [{"id": 12979, "clientId": "ningjin-emergency-new", "name": "企业应急演练", "permission": null, "parentId": 12971, "icon": null, "path": "enterpriseEmergencyDrills", "sort": 1, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"enterpriseEmergencyDrills\",\n              \"component\": \"emergency/new/enterpriseEmergencyDrills/index\",\n              \"name\": \"enterpriseEmergencyDrills\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"企业应急演练\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 1, "label": "企业应急演练", "children": null}, {"id": 12973, "clientId": "ningjin-emergency-new", "name": "演练计划", "permission": null, "parentId": 12971, "icon": null, "path": "planManage", "sort": 2, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"planManage\",\n              \"component\": \"emergency/emergencyDrill/planManage/index\",\n              \"name\": \"planManage\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"演练计划\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 2, "label": "演练计划", "children": null}, {"id": 12975, "clientId": "ningjin-emergency-new", "name": "演练指挥", "permission": null, "parentId": 12971, "icon": null, "path": "commandDrill", "sort": 3, "type": "0", "keepAlive": "0", "external": "{\n              \"path\": \"commandDrill\",\n              \"component\": \"emergency/emergencyDrill/commandDrill/index\",\n              \"name\": \"commandDrill\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"演练指挥调度\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 3, "label": "演练指挥", "children": null}, {"id": 12977, "clientId": "ningjin-emergency-new", "name": "应急演练评估", "permission": null, "parentId": 12971, "icon": null, "path": "drillEvaluate", "sort": 4, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"drillEvaluate\",\n              \"component\": \"emergency/emergencyDrill/drillEvaluate/index\",\n              \"name\": \"drillEvaluate\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"应急演练评估\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 4, "label": "应急演练评估", "children": null}]}, {"id": 13105, "clientId": "ningjin-emergency-new", "name": "应急知识库", "permission": null, "parentId": 12929, "icon": null, "path": "/portal/vue-emergency/emergency/knowledgeBase", "sort": 12, "type": "0", "keepAlive": "0", "external": "{\n\"component\": \"Layout\",\n          \"name\": \"knowledgeBase\",\n          \"alwaysShow\": true,\n          \"hidden\": false,\n          \"meta\": {\n            \"title\": \"应急知识库\",\n            \"noCache\": false,\n            \"link\": null\n          }\n}", "deptId": 1, "weight": 12, "label": "应急知识库", "children": [{"id": 13107, "clientId": "ningjin-emergency-new", "name": "法律法规库", "permission": null, "parentId": 13105, "icon": null, "path": "lawsAndRegulations", "sort": 999, "type": "0", "keepAlive": "0", "external": "{\n              \"path\": \"lawsAndRegulations\",\n              \"component\": \"emergency/knowledgeBase/lawsAndRegulations/index\",\n              \"name\": \"lawsAndRegulations\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"法律法规库\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 999, "label": "法律法规库", "children": null}, {"id": 13109, "clientId": "ningjin-emergency-new", "name": "标准规范库", "permission": null, "parentId": 13105, "icon": null, "path": "technicalSpecifications", "sort": 999, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"technicalSpecifications\",\n              \"component\": \"emergency/knowledgeBase/technicalSpecifications/index\",\n              \"name\": \"technicalSpecifications\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"标准规范库\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 999, "label": "标准规范库", "children": null}, {"id": 13111, "clientId": "ningjin-emergency-new", "name": "经典案例库", "permission": null, "parentId": 13105, "icon": null, "path": "classicCase", "sort": 999, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"classicCase\",\n              \"component\": \"emergency/knowledgeBase/classicCases/index\",\n              \"name\": \"classicCases\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"经典案例库\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 999, "label": "经典案例库", "children": null}, {"id": 13113, "clientId": "ningjin-emergency-new", "name": "MSDS", "permission": null, "parentId": 13105, "icon": null, "path": "msds", "sort": 999, "type": "0", "keepAlive": "0", "external": " {\n              \"path\": \"msds\",\n              \"component\": \"emergency/knowledgeBase/MSDS/index\",\n              \"name\": \"MSDS\",\n              \"hidden\": false,\n              \"meta\": {\n                \"title\": \"MSDS\",\n                \"noCache\": false,\n                \"link\": null\n              }\n            }", "deptId": 1, "weight": 999, "label": "MSDS", "children": null}]}], "error": true, "success": false}