import { getUserList } from '@/api/publicApi/publicApi'
const state = {
    dict: new Array()
  }
  const mutations = {
    SET_DICT: (state, { key, value }) => {
      if (key !== null && key !== "") {
        state.dict.push({
          key: key,
          value: value
        })
      }
    },
    REMOVE_DICT: (state, key) => {
      try {
        for (let i = 0; i < state.dict.length; i++) {
          if (state.dict[i].key == key) {
            state.dict.splice(i, i)
            return true
          }
        }
      } catch (e) {
      }
    },
    CLEAN_DICT: (state) => {
      state.dict = new Array()
      },
      SET_USER: (state, val) => {
        if (val !== null && val !== []) {
          state.userList = val
          console.log(state.userList,'userList');
        }
        },
  }
  
  const actions = {
    // 设置字典
    setDict({ commit }, data) {
      commit('SET_DICT', data)
    },
    // 删除字典
    removeDict({ commit }, key) {
      commit('REMOVE_DICT', key)
    },
    // 清空字典
    cleanDict({ commit }) {
      commit('CLEAN_DICT')
      },
      setUserList({ commit }, data) {
        console.log(data,'data');
        const apiParams = {
        };
        let tempObj = {};
        getUserList(apiParams).then((res) => {
          console.log(res,'res');
          commit('SET_USER', res.data)
        })
        .finally(() => {
          commit('SET_USER', res.data)
        })
     
      },
  }
  
  export default {
    namespaced: true,
    state,
    mutations,
    actions
  }
  
  