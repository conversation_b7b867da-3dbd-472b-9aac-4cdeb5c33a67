import {login, logout} from '@/api/login'
import {bondEnterprise, getBoundUsers, getInfo} from '@/api/login/login'
import {getToken, setToken, removeToken} from '@/utils/auth'
import getUserInfo from '../mock/getInfo.json'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    limits: true,
    permissions: [],
    setText: '',
    enterprise: {
      id: null,
      enterpriseName: null
    }
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_LIMITS: (state, limits) => {
      state.limits = limits
    },
    SET_ENTERPRISE: (state, enterprise) => {
      state.enterprise = enterprise
    }
  },

  actions: {
    // 登录
    Login({commit}, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      return new Promise((resolve, reject) => {
        login(username, password).then(res => {
          console.log(res);
          commit('SET_TOKEN', res.access_token)
          console.log(res.access_token);
          localStorage.setItem('token', res.access_token);
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            // let res = getUserInfo
            getBoundUsers().then(response => {
              //true 企业管理员 false 园区管理员
              commit('SET_LIMITS', response.data.includes(res.data.userId))
              if (response.data.includes(res.data.userId)) {
                bondEnterprise(res.data.userId).then(result => {
                  commit('SET_ENTERPRISE', result.data)
                  // let res = getUserInfo
            const user = res.data
            // const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.jpg") : process.env.VUE_APP_BASE_API + user.avatar;
            if (res.data.subDeptIdList.length > 0) { // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', user.tenantName)
              // commit('SET_PERMISSIONS', res.permissions)
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT'])
            }
            commit('SET_NAME', user.username)
            // commit('SET_AVATAR', avatar)
            resolve(res)
                })
              }else{
                const user = res.data
                // const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.jpg") : process.env.VUE_APP_BASE_API + user.avatar;
                if (res.data.subDeptIdList.length > 0) { // 验证返回的roles是否是一个非空数组
                  commit('SET_ROLES', user.tenantName)
                  // commit('SET_PERMISSIONS', res.permissions)
                } else {
                  commit('SET_ROLES', ['ROLE_DEFAULT'])
                }
                commit('SET_NAME', user.username)
                // commit('SET_AVATAR', avatar)
                resolve(res)
              }
            })
            
          }).catch(error => {
            if (!window.__POWERED_BY_QIANKUN__) {
              localStorage.clear();
            }
          })
          // getInfo().then(res => {
  
  
            // let res = getUserInfo
            // // getBoundUsers().then(response => {
            //   //true 企业管理员 false 园区管理员
            //   let response=bondUser
            //   commit('SET_LIMITS', response.data.includes(res.data.userId))
            //   if (response.data.includes(res.data.userId)) {
            //     // bondEnterprise(res.data.userId).then(result => {
            //       let result=bondEnteriprise
            //       commit('SET_ENTERPRISE', result.data)
            //       // let res = getUserInfo
            // const user = res.data
            // // const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.jpg") : process.env.VUE_APP_BASE_API + user.avatar;
            // if (res.data.subDeptIdList.length > 0) { // 验证返回的roles是否是一个非空数组
            //   commit('SET_ROLES', user.tenantName)
            //   // commit('SET_PERMISSIONS', res.permissions)
            // } else {
            //   commit('SET_ROLES', ['ROLE_DEFAULT'])
            // }
            // commit('SET_NAME', user.username)
            // // commit('SET_AVATAR', avatar)
            // resolve(res)
            //     // })
            //   }else{
            //     const user = res.data
            //     // const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.jpg") : process.env.VUE_APP_BASE_API + user.avatar;
            //     if (res.data.subDeptIdList.length > 0) { // 验证返回的roles是否是一个非空数组
            //       commit('SET_ROLES', user.tenantName)
            //       // commit('SET_PERMISSIONS', res.permissions)
            //     } else {
            //       commit('SET_ROLES', ['ROLE_DEFAULT'])
            //     }
            //     commit('SET_NAME', user.username)
            //     // commit('SET_AVATAR', avatar)
            //     resolve(res)
            //   }
            // })
            
  
  
  
          // }).catch(error => {
          //   if (!window.__POWERED_BY_QIANKUN__) {
          //     localStorage.clear();
          //   }
          // })
  
  
        })
      },

    // 退出系统
    LogOut({commit, state}) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          localStorage.clear();
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({commit}) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
