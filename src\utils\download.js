import { blobValidate } from "@/utils/ruoyi";
import { saveAs } from 'file-saver'
export default async function(arr,res){
    const isLogin = await blobValidate(res);
    console.log(isLogin,res);
    if (isLogin) {
      const blob = new Blob([res])
      saveAs(blob, arr[3])
    } else {
      const resText = await res.text();
      const rspObj = JSON.parse(resText);
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      Message.error(errMsg);
    }
}