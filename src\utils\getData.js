export const getFirstWeekDay = (year, month) => {
    const date = new Date(year, month - 1, 1)
    return date.getDay()
}
 
export const getMonthLastDay = (year, month) => {
    const date = new Date(year, month, 0)
    return date.getDate()
}
 
export const prevMonthRestDay = (year, month) => { // [28, 29, 30]
    const firstDayWeek = getFirstWeekDay(year, month)
    let prevMonthLastDay = getMonthLastDay(year, month - 1)
    const lastDayArr = []
    for (let i = 0; i < firstDayWeek; i++) {
        lastDayArr.unshift(prevMonthLastDay)
        prevMonthLastDay--
    }
    return lastDayArr
}
 
export const nextMonthRestDay = (year, month) => { // [1, 2, 3]
    const firstDayWeek = getFirstWeekDay(year, month)
    const thisMonthLastDay = getMonthLastDay(year, month)
    const nextDayCount = 42 - (firstDayWeek + thisMonthLastDay)
    const nextDayArr = []
    for (let i = 1; i <= nextDayCount; i++) {
        nextDayArr.push(i)
    }
    return nextDayArr
}
 
export const getDateInfo = (timeStamp) => {
    const date = new Date(timeStamp)
    return {
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        date: date.getDate()
    }
 
}