import * as CryptoJS from "crypto-js";

/**
 *加密处理
 */
export const encryption = params => {
  let { data, type, param, key } = params;
  const result = JSON.parse(JSON.stringify(data));
  if (type === "Base64") {
    param.forEach(ele => {
      result[ele] = btoa(result[ele]);
    });
  } else {
    key = CryptoJS.enc.Latin1.parse(key);
    param.forEach(ele => {
      var data = result[ele];
      var iv = key;
      // 加密
      var encrypted = CryptoJS.AES.encrypt(data, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.ZeroPadding
      });
      result[ele] = encrypted.toString();
    });
  }
  return result;
};
export const Base64 = {
  //加密
   encode(str) {
       return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g,
           function toSolidBytes(match, p1) {
               return String.fromCharCode('0x' + p1);
           }));
   },
 //解密
   decode(str) {
       return decodeURIComponent(atob(str).split('').map(function (c) {
           return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
       }).join(''));
   }
}
