<!-- http://localhost/farm/project/massifinfo/index -->
<template>
  <div class="container">
    <div v-if="Tit">
      <div class="tit">
        <div class="tit_tit">
          提醒日历
        </div>
        <div>
          <svg t="1679877951761" class="icon" viewBox="0 0 1024 1024" version="1.1"
               xmlns="http://www.w3.org/2000/svg" p-id="9005" width="18" height="18"
          >
            <path
              d="M964.693333 343.701333c0.896-0.661333 1.962667-1.024 2.773333-1.834667 36.501333-36.437333 56.533333-84.928 56.533333-136.533333s-20.074667-100.074667-56.533333-136.533333c-36.458667-36.501333-84.928-56.576-136.512-56.576-51.605333 0-100.074667 20.096-136.469333 56.576-0.832 0.832-1.216 1.877333-1.856 2.794667C636.714667 49.216 575.786667 36.778667 512 36.778667c-63.786667 0-124.672 12.437333-180.565333 34.816-0.661333-0.917333-1.024-1.962667-1.856-2.794667-36.458667-36.501333-84.906667-56.576-136.512-56.576-51.584 0-100.074667 20.117333-136.512 56.576C20.074667 105.237333 0 153.706667 0 205.333333c0 51.605333 20.053333 100.074667 56.533333 136.533333 0.832 0.810667 1.877333 1.194667 2.773333 1.834667-22.378667 55.893333-34.816 116.8-34.794667 180.586667 0 109.674667 36.437333 210.986667 97.792 292.522667L74.368 984.533333c-2.346667 8.170667 0.362667 16.917333 6.890667 22.293333 3.925333 3.242667 8.768 4.949333 13.653333 4.949333 3.306667 0 6.549333-0.789333 9.557333-2.282667l147.008-73.472c75.413333 47.893333 164.757333 75.776 260.522667 75.776 95.744 0 185.066667-27.882667 260.48-75.754667l147.029333 73.450667c3.008 1.536 6.293333 2.282667 9.536 2.282667 4.885333 0 9.706667-1.706667 13.632-4.8 6.528-5.44 9.237333-14.165333 6.954667-22.314667l-47.936-167.872c61.376-81.536 97.813333-182.869333 97.813333-292.544C999.509333 460.48 987.072 399.573333 964.693333 343.701333zM830.933333 54.954667c40.128 0 77.888 15.637333 106.304 44.053333 28.416 28.373333 44.010667 66.112 44.010667 106.304 0 35.904-12.586667 69.824-35.498667 96.896-46.677333-90.773333-120.938667-165.056-211.712-211.712C761.109333 67.541333 795.008 54.954667 830.933333 54.954667zM42.730667 205.312c0-40.170667 15.616-77.930667 44.010667-106.304C115.157333 70.613333 152.917333 54.976 193.066667 54.976c35.882667 0 69.802667 12.586667 96.896 35.52-90.794667 46.656-165.077333 120.938667-211.733333 211.712C55.296 275.136 42.730667 241.216 42.730667 205.312zM128.789333 949.546667l26.666667-93.290667c17.408 18.688 36.288 35.989333 56.405333 51.754667L128.789333 949.546667zM67.242667 524.266667C67.242667 279.04 266.752 79.509333 512 79.509333c245.205333 0 444.757333 199.552 444.757333 444.8 0 245.184-199.552 444.757333-444.757333 444.757333C266.752 969.066667 67.242667 769.450667 67.242667 524.266667zM895.189333 949.674667l-83.114667-41.6c20.138667-15.786667 39.04-33.109333 56.448-51.818667L895.189333 949.674667z"
              p-id="9006" fill="#1296db"
            ></path>
            <path
              d="M683.754667 502.890667l-127.744 0c-4.821333-9.877333-12.757333-17.834667-22.634667-22.656L533.376 180.8c0-11.776-9.536-21.354667-21.376-21.354667-11.797333 0-21.354667 9.557333-21.354667 21.354667l0 299.434667c-16.362667 7.957333-27.690667 24.576-27.690667 44.010667 0 27.114667 21.909333 49.088 49.045333 49.088 19.413333 0 36.010667-11.349333 43.968-27.712l127.786667 0c11.797333 0 21.376-9.536 21.376-21.376C705.130667 512.448 695.573333 502.890667 683.754667 502.890667z"
              p-id="9007" fill="#1296db"
            ></path>
          </svg>
          提醒管理
        </div>
      </div>
      <el-divider></el-divider>
    </div>
    <div class="calendar" @click="events">
      <div class="control-area" v-if="year">
        <div>
          <span class="control-btn btn-month-lt"></span>
          <span class="control-show">
            <span class="control-title">
              <span class="title-year">{{ dateInfo.year }}</span>
            </span>
            &nbsp;
            年
            <span>
              <span class="title-month">{{ dateInfo.month }}</span>
            </span>
            月
          </span>
          <span class="control-btn btn-month-gt"></span>
        </div>
      </div>
      <table>
        <thead v-if="data.timeType == 'DATE'" class="weeklist">
        <tr>
          <th>日</th>
          <th>一</th>
          <th>二</th>
          <th>三</th>
          <th>四</th>
          <th>五</th>
          <th>六</th>
        </tr>
        </thead>
        <tbody>
        <template>
          <tr v-for="(tr, index) in data.dateTrs" :key="index">
            <td v-for="(td, dex) in tr" :key="dex">
              <div
                :class="{'current-day':td.isThis,'iseleted':td.seleted,'last-day':td.type=='prev','next-day':td.type=='next'}"
                @click="clickOneDay(td)"
              >
                {{ td.name }}
                <span class="marker" v-if="td.type=='2'"></span>
                <span class="markerTwo" v-if="td.type=='1'">休</span>
              </div>
            </td>
          </tr>
        </template>
        </tbody>
      </table>
      <div class="btm_day">
        当日班次：{{ todayList.arrangementName ? todayList.arrangementName:'未排班' }}
<!--        <span style="margin-left: 5px" v-if="todayList.workTimeList" v-for="(item,index) in todayList.workTimeList"-->
<!--              :key="index"-->
<!--        >-->
<!--          {{ item }}-->
<!--        </span>-->
      </div>
    </div>
  </div>
</template>
<script>
import { getDateInfo, prevMonthRestDay, nextMonthRestDay, getMonthLastDay } from '../../utils/getData'
import { mySchedule } from '@/api/scheduling/scheduling'

export default {
  props: {
    timeStemp: String,
    isTit: Boolean,
    isDate: Boolean,
    nowDataList: Array
  },
  data() {
    return {
      TIME_TYPE: {
        'DATE': 'DATE',
        'YEAR': 'YEAR',
        'MONTH': 'MONTH'
      },
      data: {
        throwDate: '',
        dateTrs: [],
        timeType: 'DATE'
      },
      dateInfo: null,
      Tit: true,
      year: true,
      datInfo: true,
      todayList: {},
      isinit: true,
      changeMonth: false
    }
  },
  created() {
    this.dateInfo = getDateInfo(this.timeStemp)
    this.isTit ? this.Tit = true : this.Tit = this.isTit
    this.isDate ? this.datInfo = true : this.datInfo = this.isDate
    this.data.throwDate = this.dateInfo.year + '-' + (this.dateInfo.month < 10 ? '0' + this.dateInfo.month : this.dateInfo.month) + '-' + (this.dateInfo.date < 10 ? '0' + this.dateInfo.date : this.dateInfo.date)
    this.allNowData = this.nowDataList
    this.createDateTrs()
  },
  mounted() {

  },
  watch: {
    timeStemp(newValue, oldValue) {
      this.dateInfo = getDateInfo(this.timeStemp)
      this.createDateTrs()
    },
    nowDataList(newValue, oldValue) {
      this.allNowData = this.nowDataList
      this.dateInfo = getDateInfo(this.timeStemp)
      this.createDateTrs()
    }
  },
  methods: {
    createDateTrs() {
      const date = new Date()
      this.data.dateTrs = []
      const prevMonthRest = prevMonthRestDay(this.dateInfo.year, this.dateInfo.month)
      const currentMonthDay = getMonthLastDay(this.dateInfo.year, this.dateInfo.month)
      const nextMonthRest = nextMonthRestDay(this.dateInfo.year, this.dateInfo.month)
      const prevMonthRestTd = []
      const currentMonthDayTd = JSON.parse(JSON.stringify(this.allNowData))
      const nextMonthRestTd = []
      prevMonthRest.forEach(i => prevMonthRestTd.push({ name: i, type: 'prev', isThis: false }))
      currentMonthDayTd.forEach((item, index) => {
        item['name'] = index + 1
        item['isThis'] = true
        item['thisDayTime'] = this.dateInfo.year + '年' + this.dateInfo.month + '月' + (index + 1) + '日'
        if (index + 1 == date.getDate() && this.dateInfo.year == date.getFullYear() && this.dateInfo.month == date.getMonth() + 1) {
          currentMonthDayTd[index]['seleted'] = true
          this.todayList = item
          if (this.isinit) {
            this.isinit = false
            this.clickOneDay(item)
          }
        } else {
          currentMonthDayTd[index]['seleted'] = false
        }
      })
      console.log(currentMonthDayTd, '本月')
      nextMonthRest.forEach(i => nextMonthRestTd.push({ name: i, type: 'next', isThis: false }))
      this.integTrs(prevMonthRestTd, currentMonthDayTd, nextMonthRestTd)
    },
    integTrs(...args) {
      const dateTds = [...(args.flat())]
      let index = 0
      for (let i = 0; i < 6; i++) {
        if (!this.data.dateTrs[i]) this.data.dateTrs[i] = []
        for (let j = 0; j < 7; j++) {
          this.data.dateTrs[i].push(dateTds[index])
          index++
        }
      }
    },
    events(e) {
      switch (this.data.timeType) {
        case (this.TIME_TYPE.DATE):
          this.dateEvent(e.target)
          break
        case (this.TIME_TYPE.YEAR):

          break
        case (this.TIME_TYPE.MONTH):

          break

      }
    },
    dateEvent(target) {
      switch (target.className) {
        case 'control-btn btn-year-gt':
          this.dateInfo.year += 1
          break
        case 'control-btn btn-year-lt':
          this.dateInfo.year -= 1
          break
        case 'control-btn btn-month-gt':
          if (this.dateInfo.month === 12) {
            this.dateInfo.month = 1
            this.dateInfo.year += 1
          } else {
            this.dateInfo.month += 1
          }
          this.changeMonth = true
          break
        case 'control-btn btn-month-lt':
          if (this.dateInfo.month === 1) {
            this.dateInfo.month = 12
            this.dateInfo.year -= 1
          } else {
            this.dateInfo.month -= 1
          }
          this.changeMonth = true
          break
      }
      if (!target.className.includes('last-day') || !target.className.includes('next-day')) {
        if (this.changeMonth) {
          let params = {
            monthStr: JSON.stringify(this.dateInfo.month).length == 1 ? `${this.dateInfo.year}-0${this.dateInfo.month}` : `${this.dateInfo.year}-${this.dateInfo.month}`,
            userId: 17
          }
          mySchedule(params).then(res => {
            this.allNowData = res.data.infoList[0].memberWorkVos
            this.changeMonth = false
            if (target.className.includes('current-day')) {
              document.querySelectorAll('.current-day').forEach(td => {
                td.className = td.className.replace(' iseleted', '')
              })
              target.className += ' iseleted'
              this.dateInfo.date = Number(target.innerText)
              this.data.throwDate = this.dateInfo.year + '-' + (this.dateInfo.month < 10 ? '0' + this.dateInfo.month : this.dateInfo.month) + '-' + (this.dateInfo.date < 10 ? '0' + this.dateInfo.date : this.dateInfo.date)
              console.log(this.data.throwDate)
              // this.$emit('throwDate', this.data.throwDate)
            }
            this.createDateTrs()
          })
        } else {
          if (target.className.includes('current-day')) {
            document.querySelectorAll('.current-day').forEach(td => {
              td.className = td.className.replace(' iseleted', '')
            })
            target.className += ' iseleted'
            this.dateInfo.date = Number(target.innerText)
            this.data.throwDate = this.dateInfo.year + '-' + (this.dateInfo.month < 10 ? '0' + this.dateInfo.month : this.dateInfo.month) + '-' + (this.dateInfo.date < 10 ? '0' + this.dateInfo.date : this.dateInfo.date)
            this.$emit('throwDate', this.data.throwDate)
          }
        }
      }
    },
    clickOneDay(dayMap) {
      if (dayMap.isThis) {
        this.$set(this, 'todayList', dayMap)
        this.$emit('getId', dayMap)
      }

    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-width: 240px;
  height: 450px;
  background: #fff;
  border-radius: 10px;

  .el-divider--horizontal {
    margin: 0;
  }

  .tit {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .tit_tit {
      color: #333333;
      font-size: 20px;
    }
  }

  @keyframes identifier {
    0% {
      height: 0;
    }

    100% {
      height: 300px;
    }
  }

  .calendar {
    width: 100%;
    animation: identifier .3s ease-out 1 normal forwards;
  }

  table {
    width: 100%;
    font-size: 12px;

    th {
      font-weight: 400;
    }

    tr {
      height: 38px;
    }

    td {
      text-align: center;

      div {
        width: 45px;
        height: 45px;
        display: inline-block;
        text-align: center;
        line-height: 45px;
        position: relative;
        cursor: pointer;
      }

      &.rest-day {
        color: #ccc;
      }

      &.current-day {
        cursor: pointer;
      }

      div.messge {
        color: #fff !important;
        background: #1892DD !important;
        border-radius: 50%;
        width: 45px;
        height: 45px;
      }

      div.current {
        color: #fff;
        font-weight: 700;
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: #24CB7B;
      }

      div.iseleted {
        background: #1A8CFF !important;
        border-radius: 3px;
        color: #fff;
        width: 45px;
        height: 45px;
        font-size: 14px;
      }

      div.last-day {
        color: #999999 !important;
      }

      div.next-day {
        color: #999999;
      }
    }

    .marker {
      width: 5px;
      height: 5px;
      border-radius: 50px;
      background: #24CB7B;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 5px;
    }

    .markerTwo {
      position: absolute;
      bottom: -3px;
      left: 50%;
      transform: translateX(-50%);
      height: 10px;
      line-height: 0px;
      color: red;
    }

    border-collapse: collapse;

  }

  .weeklist {
    background: #F7FAFC;
    color: #666666;
  }

  .btm_day {
    width: 100%;
    font-size: 14px;
    color: #8F97A2;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .control-area {
    height: 76px;
    width: 100%;
    display: flex;
    align-items: center;

    > div {
      width: 100%;
      display: flex;
      justify-content: space-around;
    }

    .control-btn {
      float: left;
      display: block;
      width: 30px;
      height: 30px;
      color: #131414;
      text-align: center;
      line-height: 30px;
      cursor: pointer;
    }

    .control-show {
      float: left;
      display: block;
      width: 110px;
      height: 30px;
      text-align: left;
      line-height: 30px;
      cursor: pointer;
      color: #131414;
      font-weight: 700;
      font-size: 18px;
      display: flex;
      justify-content: center;
      margin-left: 5px;
      margin-right: 5px;
    }
  }
}

input {
  outline: none;
  border: 1px solid pink;
  width: 100%;
  height: 25px;
  box-sizing: border-box;
  padding-left: 10px;
  cursor: pointer;
}

.addtx {
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    margin-right: 8px;
  }
}

.btn-month-gt {
  background: url("../../assets/images/arrow-circle-right.png") center;
  background-size: 100%;
}

.btn-month-lt {
  background: url("../../assets/images/arrow-circle-left.png") center;
  background-size: 100%;
}
</style>
