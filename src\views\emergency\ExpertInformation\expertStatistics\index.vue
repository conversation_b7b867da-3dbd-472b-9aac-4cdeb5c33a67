<template>
  <div class="app-container">
    <div class="top">
        <el-row :gutter="16">
                    <el-col :span="6">
                        <div class="grid-content" style="cursor:pointer">
                            <div class="grid-wrapper">
                                <img class="grid-img" src="../../../../assets/images/building.svg"/>
                                <div class="grid-text">
                                    <div class="name">专家总人数</div>
                                    <div class="num">{{statisticalData.buildNum}}<span class="unit">个</span></div>
                                </div>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6"><div class="grid-content">
                        <div class="grid-wrapper">
                                <img class="grid-img" src="../../../../assets/images/area.svg"/>
                                <div class="grid-text">
                                    <div class="name">待命中人数</div>
                                    <div class="num">{{statisticalData.businessAreaSum}}<span class="unit">个</span></div>
                                </div>
                            </div>
                    </div></el-col>
                    <el-col :span="6"><div class="grid-content">
                        <div class="grid-wrapper">
                                <img class="grid-img" src="../../../../assets/images/investment.svg"/>
                                <div class="grid-text">
                                    <div class="name">任务中人数</div>
                                    <div class="num">{{statisticalData.businessAreaWait}}<span class="unit">个</span></div>
                                </div>
                            </div>
                    </div></el-col>
                    <el-col :span="6"><div class="grid-content">
                        <div class="grid-wrapper">
                                <img class="grid-img" src="../../../../assets/images/space.svg"/>
                                <div class="grid-text">
                                    <div class="name">不可用人数</div>
                                    <div class="num">{{statisticalData.roomNum}}<span class="unit">个</span></div>
                                </div>
                            </div>
                    </div></el-col>
                </el-row>
    </div>
    <div class="second">
        <el-row :gutter="16">
            <el-col :span="12">
                <div class="top-left">
                    <div class="title">专家级别分析</div>
                    <second-one></second-one>
                </div>
            </el-col>
            <el-col :span="12">
                <div class="top-right">
                    <div class="title">擅长事故类型</div>
                    <second-two></second-two>
                </div>
            </el-col>
        </el-row>
    </div>
    <div class="fourth">
          <div class="title ">专家个人参与救援数量统计</div>
          <div class="topRadio">
        <div
          :class="radio1 === 1 ? 'sys-btn-icon-active' : 'sys-btn-icon'"
          @click="radioChange(1)"
          style="cursor: pointer"
        >
          最近一周
        </div>
        <div
          :class="radio1 === 2 ? 'sys-btn-icon-active' : 'sys-btn-icon'"
          @click="radioChange(2)"
          style="cursor: pointer"
        >
          最近30天
        </div>
        <div
          :class="radio1 === 3 ? 'sys-btn-icon-active' : 'sys-btn-icon'"
          @click="radioChange(3)"
          style="cursor: pointer"
        >
          最近半年
        </div>
        <div
          :class="radio1 === 4 ? 'sys-btn-icon-active' : 'sys-btn-icon'"
          @click="radioChange(4)"
          style="cursor: pointer"
        >
          最近一年
        </div>
      </div>
          <div id="entryRankChart"></div>
      </div>
  </div>
</template>

<script>
  import * as echarts from "echarts";
  import secondTwo from './subgroup/secondTwo.vue'
  import secondOne from './subgroup/secondOne.vue'

export default {
    name: 'expertStatistics',
    data() {
        return {
            statisticalData: {
                buildNum: 33,
                businessAreaSum: 52,
                businessAreaWait: 11,
                roomNum: 3,
            },
            radio1: 1,
            xData: ['王艺淞', '孙亚鸿', '杜艺城', '高嘉浩', '孙大其', '杨威炳'],
            yData: [3,2,2,2,1,0]

        }
           
    },
    components: {
        secondTwo,secondOne
},
    methods: {
        drawEntryRank() {
            var pieChart = this.$echarts.init(document.getElementById("entryRankChart"));
            var option = {
                color: ['#3398DB'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                    }
                },
                grid: {
                    left: 16,
                    right: 16,
                    bottom: 8,
                    top: 16,
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        data: this.xData,
                        axisTick: {
                            alignWithLabel: true
                        }
                    }
                ],
                yAxis: [
                    {
                        name: '%',
                        // type : 'category',
                        // data : ['10','20','30','40'],
                        axisTick: {
                            alignWithLabel: true
                        }
                    }
                ],
                series: [
                    {
                        name: '直接访问',
                        type: 'bar',
                        barWidth: '40%',
                        data: this.yData
                    },

                ],
                label: {
                    normal: {
                        show: true,
                        position: 'top',
                        formatter: '{c}'
                    }
                },
                itemStyle: {
                    normal: {

                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: '#198CFF'
                        }, {
                            offset: 1,
                            color: '#27CBFF'
                        }]),
                        shadowColor: 'rgba(0, 0, 0, 0.1)',
                        shadowBlur: 10
                    }
                }
            };
            pieChart.setOption(option);
            window.addEventListener("resize", () => {
                pieChart.resize();
            });
        },
        radioChange(value) {
            this.radio1 = value;
            if (this.radio1 == 1) {
                this.xData = ['王艺淞', '孙亚鸿', '杜艺城', '高嘉浩', '孙大其', '杨威炳']
                this.yData = [3,2,2,2,1,0]
            } else if (this.radio1 == 2) {
                this.xData = ['孙亚鸿', '杜艺城','王艺淞', '高嘉浩', '孙大其', '杨威炳']
                this.yData = [5,2,2,2,1,1]
            } else if (this.radio1 == 3) {
                this.xData = ['孙亚鸿', '杜艺城','王艺淞',  '孙大其','高嘉浩', '杨威炳']
                this.yData = [15,12,10,10,9,6]
            } else {
                this.xData = ['孙亚鸿', '杜艺城','王艺淞',  '孙大其','高嘉浩', '杨威炳']
                this.yData = [33,19,15,15,11,9]
            }
            
            this.drawEntryRank()

        }
    },
    mounted() {
        this.drawEntryRank()
    }
}
</script>
<style scoped>
.topRadio {
    padding-right: 16px;;
    width: 100%;
  display: flex;
  margin-right: 16px;
  justify-content: flex-end;
  /* text-align: right; */
  
}
.sys-btn-icon-active {
    margin-left: 8px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px 12px;
    gap: 10px;

    width: 88px;
    height: 40px;

    background: #1a8cff;
    border-radius: 4px;
    font-family: "Noto Sans SC";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #ffffff;
  }
  .sys-btn-icon {
    margin-left: 8px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px 12px;
    gap: 10px;
    width: 88px;
    height: 40px;
    background: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 4px;
    font-family: "Noto Sans SC";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #999999;
  }
.grid-content{
    height: 104px;
    border: 1px solid #EBEBEB;
    padding: 23px, 24px, 23px, 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    background-color: #fff;
   

}
.grid-wrapper{
        height: 58px;
        margin: auto 0;
        margin-left: 24px;
        display: flex;
        gap:8px;

        
        
    }
    .grid-img{
            width: 56px;
            height: 56px;
            border-radius: 8px;

        }
        .grid-text{
            font-family: Noto Sans SC;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            letter-spacing: 0em;
            text-align: left;
            color:#999999;
            
            
        }
        .unit{
                font-family: Noto Sans SC;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                letter-spacing: 0em;
                text-align: left;
                color:#333333;

            }
            .num{
                font-family: DINPro;
                font-size: 24px;
                font-weight: 700;
                line-height: 40px;
                letter-spacing: 0em;
                text-align: left;
                color:#333333;
            }
            .fourth{
                margin-top: 16px;
          width: 100%;
          height: 410px;
          border-radius: 4px;
          border: 1px solid #EBEBEB;
          background: #FFF;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
          
      }
      #entryRankChart{
              width: 100%;
              height: 310px;
              padding:16px;
          }
          .title{
          display: flex;
          height: 56px;
          padding: 0px 16px;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;
          gap: 10px;
          align-self: stretch;
          border-bottom: 1px solid #EBEBEB;
  background: #FFF;
  color: #333;
  font-family: Noto Sans SC;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px; /* 144.444% */
      }
      .second{
            height: 320px;
            width: 100%;
            margin-top: 16px;
        
      }
      .top-left{
                border-radius: 4px;
                border: 1px solid #EBEBEB;
                background: #FFF;
                height: 320px;
            }
            .top-right{
                border-radius: 4px;
                border: 1px solid #EBEBEB;
                background: #FFF;
                height: 320px;
            }
</style>
