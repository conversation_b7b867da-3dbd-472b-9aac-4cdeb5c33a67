<template>
    <div id="secondOne" ref="secondOne"></div>
  </template>
  
  <script>
  import * as echarts from "echarts";
  const data = [{
        name: '数据1',
        value: 36,
        rate: 12
    },
    {
        name: '数据2',
        value: 20,
        rate: 20
    },
    {
        name: '数据3',
        value: 16,
        rate: -40
    },
    {
        name: '数据4',
        value: 10,
        rate: -15
    },
    {
        name: '数据5',
        value: 9,
        rate: 12
    },
    {
        name: '数据6',
        value: 9,
        rate: -6
    }
]
  export default {
      name: 'centerTwo',
      methods: {
          drawSpaceResources() {
              var pieChart = this.$echarts.init(document.getElementById("secondOne"));
              var colors = [new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
        offset: 0,
        color: '#0AD1EC00'
    },
    {
        offset: 1,
        color: '#0AD1EC'
    }]), new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
        offset: 0,
        color: ' #0AEC7F00'
    },
    {
        offset: 1,
        color: '#0AEC7F'
    }]), new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
        offset: 0,
        color: '#1CEC0A00'
    },
    {
        offset: 1,
        color: ' #1CEC0A'
    }]),  new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
        offset: 0,
        color: '#ECE30A00'
    },
    {
        offset: 1,
        color: ' #ECE30A'
    }]), new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
        offset: 0,
        color: '#EC9F0A00'
    },
    {
        offset: 1,
        color: '#EC9F0A'
    }]), '#9692ff'];



var lineargroup = [{
        value: 100,
        name: '县级',
        oriname: "借出",
        number: 36,
        //color: ["rgba(29,211,137,0.8)", "rgba(29,211,137,0)"]
    },
    {
        value: 80,
        name: '市级',
        oriname: "领用",
        number: 22,
       // color: ["rgba(102,142,255,0.7)", "rgba(102,142,255,0)"]
    },
    {
        value: 60,
        name: '省级',
        oriname: "借出",
        number: 8,
       // color: ["rgba(255,198,82,0.6)", "rgba(255,198,82,0)"]
    },
    {
        value: 40,
        name: '成交率',
        oriname: "借出",
        number: 6,
       // color: ["rgba(255,110,115,0.5)", "rgba(255,110,115,0)"]
    },
    {
        value: 20,
        name: '国家级',
        oriname: "借出",
        number: 2,
      //  color: ["rgba(134,131,230,0.4)", "rgba(134,131,230,0)"]
    }
];

var data1 = [];
var data2 = [];

for (var i = 0; i < lineargroup.length; i++) {
    var obj1 = {
        value: lineargroup[i].number,
        num: lineargroup[i].number,
        name: lineargroup[i].name
    };

    var obj2 = {
        value: lineargroup[i].value,
        name: lineargroup[i].name,
        itemStyle: {
             opacity: 0,
            /*normal: {
                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [{
                    offset: 0,
                    color: lineargroup[i].color[1]
                }, {
                    offset: 1,
                    color: lineargroup[i].color[0]
                }]),
                borderWidth: 0,
                opacity: 1
            }*/
        },
    };
    data1.push(obj1);
    data2.push(obj2);
}

var option = {
    color: colors,
    series: [{
            top: 20,
            type: 'funnel',
            height: 200,
            gap: 10,
            minSize:150 ,
            left: '10%',
            width: '60%',
            label: {
               normal: {
                formatter: function(params) {
                    return '{aa|'+params.name + '  ' + params.value+'}';
                },
                position: 'right',
                rich: {
                        aa: {
                            align: 'center',
                            color: '#000',
                            fontSize: 16,
                            fontWhite:500,
                            lineHeight: 30
                        }
                    }
            },
            },
            itemStyle: {  //去掉默认白色边框线
                borderWidth: 0,
                borderColor: '#000'
            },
            data: data1
        },
        {
            top: 0,
            type: 'funnel',
            height: 400,
            gap: -1,
            minSize: 150,
            left: '20%',
            width: '60%',
            z: 2,
            label: {
                
                normal: {
                    show:false,
                    color: '#333',
                    position: 'right',
                    formatter: function(d) {
                        var ins = '{aa|' + d.name + '}\n{bb|' + d.percent + '%}';
                        return ins
                    },
                    rich: {
                        aa: {
                            align: 'center',
                            color: '#666',
                            fontSize: 12,
                            lineHeight:30
                        },
                        bb: {
                            align: 'center',
                            color: '#333',
                            fontSize: 22
                        }
                    }
                }
            },
            labelLine: {
                show: false
            },
              itemStyle: { // 添加 itemStyle 属性

                normal: {

                    lineStyle: { // 配置 LineStyle

                        color: 'transparent' // 将颜色设置为透明

                    }

                }

            },
            data: data2
        }
    ]
};
              pieChart.setOption(option);
              window.addEventListener("resize", () => {
                  pieChart.resize();
              });
          },
      },
      mounted() {
      this.drawSpaceResources()
  }
  }
  </script>
  
  <style scoped>
  #secondOne{
      width:100%;
      height: 256px;
  }
  </style>