<template>
  <div id="secondTwo" ref="secondTwo"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
    name: 'centerTwo',
    methods: {
        drawSpaceResources() {
            var pieChart = this.$echarts.init(document.getElementById("secondTwo"));
            var option = {
	    tooltip : {
	        trigger: 'item',
	        formatter: "{a} <br/>{b} : {c} ({d}%)"
	    },
	    color:['#8fc31f','#f35833','#00ccff','#ffcc00'],
	    // legend: {
	    //     orient: 'vertical',
	    //     x: 'right',
	    //     data: ['准时','迟到','请假','未到'],
	    //     formatter:function(name){
	    //     	var oa = option.series[0].data;
	    //     	var num = oa[0].value + oa[1].value + oa[2].value + oa[3].value;
	    //     	for(var i = 0; i < option.series[0].data.length; i++){
        //             if(name==oa[i].name){
        //             	return name + '     ' + oa[i].value + '     ' + (oa[i].value/num * 100).toFixed(2) + '%';
        //             }
	    //     	}
	    //     }
	    // },
	    series : [
	        {
	            name: '签到比例分析',
	            type: 'pie',
	            radius : '55%',
	            center: ['50%', '50%'],
	            data:[
	                {value:2, name:'科学技术和技术服务'},
	                {value:5, name:'电力、热力、燃气及水生产和供应业'},
	                {value:4, name:'交通运输、仓储和邮政业'},
	                {value:6, name:'未知'}
	            ],
	            itemStyle: {
	                emphasis: {
	                    shadowBlur: 10,
	                    shadowOffsetX: 0,
	                    shadowColor: 'rgba(0, 0, 0, 0.5)'
	                }
	            },
	            itemStyle: {
	                normal: {
	                    label:{ 
                            show: true, 
//	                            position:'inside',
                            formatter: "{a|{b} \n {c}家 {d}%}",
                            rich: {
                                a: {
                                    color: '#6F7275',
                                    fontSize: 14,
                                    fontWeight: 400,
                                    lineheight: 22
                                },
                            }
                        }
	                },
                    labelLine :{show:true}
	            }
	        }
	    ]
	};
            pieChart.setOption(option);
            window.addEventListener("resize", () => {
                pieChart.resize();
            });
        },
    },
    mounted() {
    this.drawSpaceResources()
}
}
</script>

<style scoped>
#secondTwo{
    width:100%;
    height: 256px;
}
</style>