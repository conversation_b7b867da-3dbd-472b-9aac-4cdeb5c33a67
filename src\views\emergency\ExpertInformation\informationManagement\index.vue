<template>
    <div class="app-container">
      <el-row :gutter="20">
        <!--部门数据-->
        <!--用户数据-->
        <el-col :span="24" :xs="24">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>数据筛选</span>
            </div>
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              v-show="showSearch"
              label-position="left"
              style="display: flex; justify-content: space-between"
            >
              <div>
                <el-form-item label="队伍名称">
                  <el-input
                    v-model="queryParams.contingentName"
                    placeholder="请输入队伍名称"
                    clearable
                    style="width: 10vw"
                    maxlength="20"
                    @keyup.enter.native="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="队伍类型 :" prop="contingentType">
                  <el-select
                    style="width: 10vw"
                    v-model="queryParams.contingentType"
                    placeholder="请选择队伍类型"
                  >
                    <el-option
                      v-for="dict in dict.type.team_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div style="min-width: 166px">
                <el-form-item>
                  <el-button
                    class="resetQueryStyle"
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    class="resetQueryStyle"
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                </el-form-item>
              </div>
            </el-form>
          </el-card>
          <el-card>
            <div slot="header" class="clearfix">
              <span>应急队伍展示列表</span>
              <el-button
                type="primary"
                
                size="mini"
                @click="handleAdd"
                icon="el-icon-plus"
                class="queryBtnT"
                >新增队伍</el-button
              >
            </div>
            <el-table
              v-loading="loading"
              :data="userList"
              :cell-style="{ padding: '0px' }"
              :row-style="{ height: '48px' }"
            >
              <!-- <el-table-column type="selection" width="50" align="center" /> -->
              <el-table-column
                label="序号"
                type="index"
                width="70"
                align="center"
              >
                <template slot-scope="scope">
                  <span>{{
                    (queryParams.current - 1) * queryParams.size +
                    scope.$index +
                    1
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="队伍名称"
                align="center"
                prop="contingentName"
                show-overflow-tooltip
              />
              <el-table-column label="队伍类型" align="center">
                <template slot-scope="scope">
                  {{ getNameById(scope.row) }}
                </template>
              </el-table-column>
              <el-table-column
                label="队伍人数"
                align="center"
                prop="contingentNumber"
                show-overflow-tooltip
              />
              <el-table-column
                label="队伍负责人"
                align="center"
                prop="contact"
                show-overflow-tooltip
              />
              <el-table-column label="联系方式" align="center" prop="phone" />
              <el-table-column
                label="操作"
                align="center"
                width="300"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <!-- <el-button size="mini" type="text" icon="el-icon-plus" 
                  @click="handleRankadd(scope.row)">新增人员</el-button> -->
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-view"
                    @click="handleLook(scope.row)"
                    >查看</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    >编辑</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    >删除</el-button
                  >
                  <!-- <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  >呼叫</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  >短信</el-button
                > -->
                </template>
              </el-table-column>
            </el-table>
  
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.current"
              :limit.sync="queryParams.size"
              @pagination="getList"
            />
          </el-card>
        </el-col>
      </el-row>
      <!--  -->
      <!-- 添加或修改队伍配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="abilityOpen"
        width="960px"
        append-to-body
      >
        <el-form
          ref="ranksForm"
          :model="ranksForm"
          :rules="abilityRules"
          label-width="110px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="队伍名称 :" prop="contingentName">
                <el-input
                  style="width: 245px"
                  v-model="ranksForm.contingentName"
                  placeholder="请输入队伍名称"
                  maxlength="20"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="队伍类型 :" prop="contingentType">
                <el-select
                  style="width: 245px"
                  v-model="ranksForm.contingentType"
                  placeholder="请选择队伍类型"
                  :disabled="disabled"
                >
                  <el-option
                    v-for="dict in dict.type.team_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="所属单位 :" prop="companyName">
                <el-input
                  style="width: 245px"
                  v-model="ranksForm.companyName"
                  placeholder="请输入所属单位"
                  maxlength="20"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="总人数 :" prop="contingentNumber">
                <el-input
                  style="width: 245px"
                  v-model="ranksForm.contingentNumber"
                  type="number"
                  min="0"
                  placeholder="请输入总人数"
                  maxlength="20"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="负责人 :" prop="contact">
                <el-input
                  style="width: 245px"
                  v-model="ranksForm.contact"
                  placeholder="请输入负责人"
                  maxlength="20"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式 :" prop="phone">
                <el-input
                  style="width: 245px"
                  v-model="ranksForm.phone"
                  placeholder="请输入联系方式"
                  maxlength="20"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="经纬度 :" prop="lngAndLat">
                <el-button
                  type="primary"
                  plain
                  @click="openMap()"
                  >{{ lngAndLat ? lngAndLat : "点击选择" }}</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="专长描述 :" prop="description">
                <el-input
                  v-model="ranksForm.description"
                  placeholder="请输入专长描述"
                  maxlength="200"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="应急经历 :" prop="experienced">
                <el-input
                  v-model="ranksForm.experienced"
                  placeholder="请输入应急经历"
                  maxlength="200"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="主要职责 :" prop="responsibilities">
                <el-input
                  v-model="ranksForm.responsibilities"
                  placeholder="请输入主要职责"
                  maxlength="200"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="主要装备描述 :" prop="equipmentDescription">
                <el-input
                  v-model="ranksForm.equipmentDescription"
                  placeholder="请输入主要装备描述"
                  maxlength="200"
                  :disabled="disabled"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            class="popupButton"
            type="primary"
            @click="confirm('ranksForm')"
            >确 定</el-button
          >
          <el-button class="popupButton" @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
      <el-dialog title="队伍人员" :visible.sync="ranksOpen" width="560px">
        <el-table
          :data="tableData"
          style="width: 100%"
          :cell-style="{ padding: '0px' }"
          :row-style="{ height: '48px' }"
        >
          <el-table-column align="center" prop="contact" label="姓名">
          </el-table-column>
          <el-table-column align="center" prop="phone" label="联系方式">
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button
                plain
                type="primary"
                size="mini"
                @click="removeStaff(scope.row)"
                >移除人员</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button class="popupButton" type="primary" @click="ranksOpen = false"
            >确 定</el-button
          >
        </span>
      </el-dialog>
      <el-dialog title="新增人员" :visible.sync="ranksAdd" width="560px">
        <el-form
          ref="rankaddForm"
          :model="rankaddForm"
          :rules="rankaddRules"
          label-width="110px"
        >
          <el-form-item label="队伍类型" prop="dictKey">
            <el-select
              style="width: 245px"
              v-model="rankaddForm.dictKey"
              placeholder="请选择队伍类型"
              disabled
            >
              <el-option
                v-for="dict in dict.type.team_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系人" prop="contact">
            <el-input
              style="width: 245px"
              v-model="rankaddForm.contact"
              placeholder="请输入联系人"
              maxlength="20"
            />
          </el-form-item>
  
          <el-form-item label="联系电话" prop="phone">
            <el-input
              style="width: 245px"
              v-model="rankaddForm.phone"
              placeholder="请输入联系电话"
              maxlength="20"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            class="popupButton"
            type="primary"
            @click="confirm('rankaddForm')"
            >确 定</el-button
          >
          <el-button class="popupButton" @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 地图展示 -->
      <Map
        @mapConfirm="mapConfirm"
        :ranksForm="ranksForm"
        :mapVisible="mapVisible"
        :disabled="disabled"
        @mapCancellation="mapCancellation"
        ref="mapRef"
        :url="url"
      ></Map>
    </div>
  </template>
  
  <script>
  import { getToken } from "@/utils/auth";
  import Map from "../../../map/index.vue";
  import {
    page,
    save,
    ranksDetail,
    saveStaff,
    removeStaff,
    update,
    deleteById,
  } from "@/api/emergency/naturalResources/specialist/index";
  
  export default {
    name: "Specialist",
    dicts: ["team_type"],
    components: { Map },
    data() {
      const validCode = (rule, value, callback) => {
        console.log(rule, value, "value");
        if (this.lngAndLat) {
          callback();
        } else {
          callback(new Error("请选择经纬度"));
        }
      };
      let checkPhone = (rule, value, callback) => {
        let reg = /^1[345789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error("请输入11位手机号"));
        } else {
          callback();
        }
      };
      return {
        // 地图点标记图标地址
        url: require("@/assets/icons/name.png"),
        // 地图遮罩层
        mapVisible: false,
        lngAndLat: "",
        // 遮罩层d
        loading: false,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 表格数据
        userList: null,
        tableData: null,
        // 是否显示弹出层
        abilityOpen: false,
        ranksOpen: false,
        ranksAdd: false,
        title: "新增事件类型",
        // 查询参数
        queryParams: {
          current: 1,
          size: 10,
          contingentType: undefined,
          contingentName: undefined,
        },
  
        ranksForm: {},
        rankaddForm: {
          dictKey: undefined,
          contact: undefined,
          phone: undefined,
        },
        disabled: false,
        // 表单校验
        abilityRules: {
          contingentName: [
            { required: true, message: "队伍名称不能为空", trigger: "blur" },
          ],
          contingentNumber: [
            { required: true, message: "总人数不能为空", trigger: "blur" },
          ],
          contact: [
            { required: true, message: "负责人不能为空", trigger: "blur" },
          ],
          phone: [
            {
              type: "number",
              validator: checkPhone,
              message: "请输入正确的手机号",
              trigger: "change",
              required: true,
            },
          ],
          contingentType: [
            { required: true, message: "请选择队伍类型", trigger: "change" },
          ],
          lngAndLat: [{ required: true, validator: validCode, trigger: "blur" }],
        },
        rankaddRules: {
          contact: [
            { required: true, message: "联系人不能为空", trigger: "blur" },
          ],
          phone: [
            { required: true, message: "联系方式不能为空", trigger: "blur" },
          ],
          dictKey: [
            { required: true, message: "请选择队伍类型", trigger: "change" },
          ],
        },
      };
    },
    watch: {},
    created() {
      this.getList();
    },
    mounted() {},
    methods: {
      /** 查询专家队伍列表 */
      getList() {
        this.loading = true;
        page(this.queryParams).then((response) => {
          if (response.data != null) {
            this.userList = response.data.records;
            console.log(response.data.records);
            this.total = response.data.total;
          }
          this.loading = false;
        });
      },
      // 查看人员
      handleLook(row) {
        this.reset();
        this.abilityOpen = true;
        this.ranksForm = JSON.parse(JSON.stringify(row));
        this.title = "查看应急队伍";
        this.disabled = true;
        this.lngAndLat = row.longitude + "," + row.latitude;
        console.log(this.abilityForm);
      },
      // 编辑人员
      handleUpdate(row) {
        console.log(row, "row");
        this.abilityOpen = true;
        this.reset();
        this.ranksForm = row;
        this.disabled = false;
        this.lngAndLat = row.longitude + "," + row.latitude;
        // this.ranksForm.id = row.id;
        // this.ranksForm.dictKey = row.id;
        // this.ranksForm.contingentName=row.contingentName
        // this.ranksForm.contingentType=row.contingentType
        // this.ranksForm.phone = row.phone;
        // this.ranksForm.contingentNumber = row.contingentNumber;
        // this.ranksForm.companyName = row.companyName;
        // this.ranksForm.contact = row.contact;
        // this.ranksForm.phone = row.phone;
        this.title = "编辑队伍";
      },
      handleAdd() {
        this.reset();
        this.abilityOpen = true;
        this.title = "新增队伍";
        this.disabled = false;
      },
      handleRankadd(row) {
        this.reset();
        this.rankaddForm.dictKey = row.id;
        this.ranksAdd = true;
      },
      handleDelete(row) {
        const eventAbilityId = row.id;
        this.$modal
          .confirm("是否确认删除当前数据")
          .then(function () {
            return deleteById({ id: eventAbilityId });
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => {});
      },
      // 取消按钮
      cancel() {
        this.abilityOpen = false;
        this.reset();
      },
      getNameById(res) {
        // console.log(res, this.dict.type.team_type);
        if (
          res.contingentType != undefined &&
          res.contingentType != "" &&
          res.contingentType != null
        ) {
          return this.dict.type.team_type.filter(
            (item) => item.value == res.contingentType
          )[0].label;
        }
      },
      /*  确认保存新增*/
      confirm(formName) {
        if (formName == "ranksForm") {
          this.$refs[formName].validate((valid) => {
            if (valid) {
              if (this.ranksForm.id != undefined) {
                update(this.ranksForm).then((response) => {
                  console.log(response, "编辑");
                  if (response.code == 200) {
                    this.$modal.msgSuccess("编辑成功");
                    this.abilityOpen = false;
                    this.getList();
                  }
                });
              } else {
                save(this.ranksForm).then((response) => {
                  console.log(this.ranksForm, "新增");
                  if (response.code == 200) {
                    this.$modal.msgSuccess("新增成功");
                    this.abilityOpen = false;
                    this.getList();
                  }
                });
              }
            }
          });
        } else {
          this.$refs[formName].validate((valid) => {
            if (valid) {
              saveStaff(this.rankaddForm).then((response) => {
                console.log(response, "新增");
                if (response.code == 200) {
                  this.$modal.msgSuccess("新增成功");
                  this.ranksAdd = false;
                  this.getList();
                }
              });
            }
          });
        }
      },
      removeStaff(row) {
        this.$modal
          .confirm("是否确认删除当前数据")
          .then(function () {
            return removeStaff({ contingentId: row.contingentId });
          })
          .then(() => {
            this.ranksOpen = false;
            this.getList();
            this.$modal.msgSuccess("删除队伍人员成功！");
          })
          .catch(() => {});
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.current = 1;
        this.getList();
      },
  
      // 取消按钮
      // 表单重置
      reset() {
        this.ranksForm = {};
        this.rankaddForm = {
          dictKey: undefined,
          contact: undefined,
          phone: undefined,
        };
        this.lngAndLat = "";
  
      },
  
      /** 重置按钮操作 */
      resetQuery() {
        (this.queryParams = {
          current: 1,
          size: 10,
          liabilityUser: undefined,
          phone: undefined,
        }),
          this.resetForm("queryForm");
        this.handleQuery();
      },
      // 打开地图按钮
      openMap() {
        this.mapVisible = true;
        this.$nextTick(() => {
          this.$refs.mapRef.initMap();
        });
      },
      // 地图返回经纬度的回调
      mapConfirm(lng, lat) {
        if (lng && lat) {
          this.mapVisible = false;
          this.lngAndLat = lng + "," + lat;
          this.ranksForm.longitude = lng;
          this.ranksForm.latitude = lat;
          // 获取到经纬度就取消验证提示
          this.$nextTick(() => {
            this.$refs.ranksForm.clearValidate();
          });
        } else {
          this.$modal.msgSuccess("请选择经纬度");
        }
      },
      // 取消地图的回调
      mapCancellation() {
        this.mapVisible = false;
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .left_title {
    color: rgba(56, 56, 56, 1);
    font-size: 24px;
    font-weight: bold;
    padding-bottom: 14px;
  }
  
  ::v-deep.el-table .el-table__header-wrapper th {
    background: rgba(25, 159, 255, 0.15);
    font-family: Noto Sans SC;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #007baf;
  }
  
  .clearfix:after,
  .clearfix:before {
    display: table;
    content: "";
  }
  
  .clearfix:after {
    clear: both;
  }
  
  .box-card-bottom {
    margin: 20px;
  }
  
  .box-card {
    margin-bottom: 20px;
    z-index: 2;
  }
  
  ::v-deep.box-card .topBottom {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: distribute;
    justify-content: space-around;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  
  ::v-deep.box-card .topBottom .descriptions {
    -webkit-box-flex: 6;
    -ms-flex: 6;
    flex: 6;
  }
  
  ::v-deep.box-card .topBottom .tabButton {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
  }
  
  ::v-deep.box-card .topBottom .tabButton button {
    float: right;
    margin: 0 5px;
  }
  
  .queryBtnT {
    height: 32px;
    border: 1px solid #cccccc;
    border-radius: 2px;
    font-size: 13px;
    float: right;
    margin-right: 10px;
  }
  
  .resetQueryStyle {
    width: 88px;
    height: 32px;
    border: 1px solid #cccccc;
    border-radius: 2px;
    font-size: 13px;
  }
  
  .popupButton {
    width: 96px;
    height: 40px;
    border-radius: 2px;
  }
  ::v-deep .el-form-item__label {
    width: 100px;
    height: 32px;
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 32px;
    text-align: right;
    color: #333;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: auto;
  }
  </style>