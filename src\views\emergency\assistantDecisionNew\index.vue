<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="24" :xs="24">
                <!-- <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>数据筛选</span>
                    </div>
                    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                        label-position="left" style="display: flex; justify-content: space-between">
                        <div>
                            <el-form-item label="事件类型">
                                <el-select style="width: 10vw; " placeholder="请选择事件类型" v-model="queryParams.eventTypeName">
                                    <el-option :value="queryParams.eventTypeName" class="option">
                                        <el-tree :data="treeData" :show-checkbox="true" node-key="id" :props="defaultProps"
                                            class="tree" @check="handleNodeClick" ref="tree">
                                        </el-tree>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div style="min-width: 166px">
                            <el-form-item>
                                <el-button class="resetQueryStyle" type="primary" icon="el-icon-search" size="mini"
                                    @click="handleQuery">搜索</el-button>
                                <el-button class="resetQueryStyle" icon="el-icon-refresh" size="mini"
                                    @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </div>
                    </el-form>
                </el-card> -->
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>辅助决策列表</span>
                    </div>
                    <div class="programme" v-loading="loading">
                        <div v-for="item in data" class="programme_box" :key="item.planId">
                            <el-tooltip class="item" effect="dark" :content="item.eventTypeName" placement="top-start">
                                <p>事件类型：{{ item.eventTypeName }}</p>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" :content="(item.planName+'')" placement="top-start">
                                <p>预案名称：<span>{{ item.planName }}</span></p>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" :content="item.accidentCaseNo" placement="top-start">
                                <p>事故案例：<span>{{ item.accidentCaseNo }}</span></p>
                            </el-tooltip>
                            <div style="text-align: center; margin-bottom: 10px;">
                                <el-button class="resetQueryStyle" type="primary" size="mini"
                                    @click="handleAdd(item)">详情</el-button>
                            </div>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        <!--  -->
        <!-- 添加或修改预警事件对话框 -->
        <el-dialog :title="title" :visible.sync="abilityOpen" width="960px" append-to-body>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form ref="abilityForm" :model="abilityForm" label-width="110px">

                        <el-form-item label="事件类型 :" prop="accidentName">
                            <el-tag>{{ eventTypeName }}</el-tag>
                        </el-form-item>
                        <el-form-item label="关联预案 :" prop="eventTypeName">
                            <el-button class="resetQueryStyle" type="primary" size="mini"
                                @click="planDetails">查看详情</el-button>
                        </el-form-item>
                        <div style="margin:0px 0px 20px 35px">事故案例 :</div>
                        <div class="programme" v-loading="loading">
                            <div v-for="item in caseData" class="programme_box" :key="item.id">
                                <el-tooltip class="item" effect="dark" :content="item.accidentName" placement="top-start">
                                    <p>事故名称：{{ item.accidentName }}</p>
                                </el-tooltip>
                                <el-tooltip class="item" effect="dark" :content="item.eventLevel" placement="top-start">
                                    <p>事件等级：<span>{{ item.eventLevel }}</span></p>
                                </el-tooltip>
                                <el-tooltip class="item" effect="dark" :content="item.happenTime" placement="top-start">
                                    <p>发生时间：<span>{{ item.happenTime }}</span></p>
                                </el-tooltip>
                                <el-tooltip class="item" effect="dark" :content="item.longitude+','+item.latitude" placement="top-start">
                                    <!-- <p>事故地点：<span>{{ item.accidentPlace }}</span></p> -->
                                    <p>事故地点：<span>{{ item.longitude }},{{ item.latitude }}</span></p>
                                </el-tooltip>
                                <!-- <div style="text-align: center; margin-bottom: 10px;">
                                    <el-button class="resetQueryStyle" type="primary" size="mini"
                                        @click="caseDetails">详情</el-button>
                                </div> -->
                            </div>
                        </div>
                    </el-form>
                </el-col>
            </el-row>
            <div slot="footer" class="dialog-footer">
                <el-button class="popupButton" @click="cancel">关 闭</el-button>
            </div>
        </el-dialog>
    </div>
</template>
      
<script>
import { getToken } from "@/utils/auth";
import {
    page,
    save,
    handledownload,
    deleteById,
    eventType
} from "@/api/emergency/assistantDecision/index";
export default {
    name: "EmergencySupplies",
    dicts: ["event_level", 'plan_type'],
    data() {
        let checkPhone = (rule, value, callback) => {
            let reg = /^1[345789]\d{9}$/;
            if (!reg.test(value)) {
                callback(new Error("请输入11位手机号"));
            } else {
                callback();
            }
        };
        const validCode = (rule, value, callback) => {
            console.log(rule, value, 'value');
            if (this.lngAndLat) {
                callback()
            } else {
                callback(new Error('请选择经纬度'))
            }
        }
        return {
            container: {},
            // 遮罩层d
            loading: false,
            // 显示搜索条件
            showSearch: true,
            // 是否显示弹出层
            abilityOpen: false,
            title: "新增经典案例",
            activeNames: ['1'],
            abilityForm: {},
            // 查询参数
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                eventTypeId: undefined,
                eventTypeName: undefined,
            },
            data: [],
            total: undefined,
            treeData: [],
            caseData: [],
            eventTypeName: undefined,
            planId: undefined,
            defaultProps: {
                children: 'children',
                label: 'nodeName'
            },
        };
    },
    watch: {},
    created() {
        this.getList();
    },
    mounted() {
        this.getEventType()
    },
    methods: {
        /** 查询场所列表 */
        getList() {
            this.loading = true
            page(this.queryParams).then((response) => {
                console.log(response, 'ssss');
                if (response.data != null) {
                    this.data = response.data;
                    this.data.forEach(item => {
                        item.accidentCaseNo=item.accidentCaseNo+''
                    });
                    this.total = response.data;
                }
                this.loading = false;
            });
        },
        handleAdd(item) {
            this.abilityOpen = true;
            this.title = "辅助决策方案详情";
            this.caseData = item.emergencyClassicCasesVoList
            this.planId = item.planId
            this.eventTypeName = item.eventTypeName
        },
        // 取消按钮
        cancel() {
            this.abilityOpen = false;
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.getList();
        },
        // 跳转预案详情
        planDetails() {
            this.abilityOpen = false;
            this.$router.push({
                path: 'emergency/emergencyPlan/structuredPlan/planManagement',
                name: 'PlanManagement',
                query: {
                    planId: this.planId
                }
            })
        },
        caseDetails() {
            this.abilityOpen = false;
            this.$router.push({
                path: 'emergency/knowledgeBase/classicCase',
                name: 'classicCases',
                query: {
                    planId: this.planId
                }
            })
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.queryParams = {
                current: 1,
                size: 10,
                eventTypeId: undefined,
                eventTypeName: undefined,
            };
            this.handleQuery();
        },
        handleNodeClick(data, res, item) {
            console.log(data, res, item, '树结构');
            if (!res) {
                this.queryParams.eventTypeId = undefined
                this.queryParams.eventTypeName = undefined
            } else {
                this.$refs.tree.setCheckedNodes([data]);
                this.queryParams.eventTypeId = data.id
                this.queryParams.eventTypeName = data.nodeName
            }
        },
        getEventType() {
            eventType().then(res => {
                console.log(res, '事件类型');
                if (res.code == 200) {
                    this.treeData = res.data
                }
            })
        },
    },
}
</script>
<style lang="scss" scoped>
.left_title {
    color: rgba(56, 56, 56, 1);
    font-size: 24px;
    font-weight: bold;
    padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
    background: rgba(25, 159, 255, 0.15);
    font-family: Noto Sans SC;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #007baf;
}

.clearfix:after,
.clearfix:before {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both;
}

.box-card-bottom {
    margin: 20px;
}

.box-card {
    margin-bottom: 20px;
    z-index: 2;
}

.queryBtnT {
    height: 32px;
    border: 1px solid #cccccc;
    border-radius: 2px;
    font-size: 13px;
    float: right;
    margin-right: 10px;
}

.resetQueryStyle {
    width: 88px;
    height: 32px;
    border: 1px solid #CCCCCC;
    border-radius: 2px;
    font-size: 13px;
}

.popupButton {
    width: 96px;
    height: 40px;
    border-radius: 2px;
}

.option {
    height: auto;
    line-height: 1;
    padding: 0;
    background-color: #fff;
}

.tree {
    padding: 4px 20px;
    font-weight: 400;
}

.programme {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-wrap: wrap;

}

.programme_box {
    border: rgba(204, 204, 204, 1) solid 1px;
    min-width: 300px;
    min-height: 200px;

    .resetQueryStyle {
        margin-top: 20px;
    }

    p {
        overflow: hidden; //块元素超出隐藏
        width: 300px;
        height: 30px;
        text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
        margin-left: 20px;
        white-space: nowrap; //规定段落中的文本不进行换行
    }
}
</style>
