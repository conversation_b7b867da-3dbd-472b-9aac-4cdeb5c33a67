<template>
  <div class="top-cards">
    <div class="cards-grid">
      <div
        v-for="item in cardList"
        :key="item.key"
        class="card-item"
      >
        <div class="stat-card">
          <img  class="card-icon" :src="item.img"/>
          <div class="card-content">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-number">{{ item.value }}<span class="card-unit">{{ item.unit }}</span></div>
            
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  selectNodes
} from "@/api/emergency/bulletinBoard/index";
import expertNumberImg from '@/assets/images/bulletinNew/expertNumber.svg';
import contingentNumberImg from '@/assets/images/bulletinNew/contingentNumber.svg';
import warehouseNumberImg from '@/assets/images/bulletinNew/warehouseNumber.svg';
import materialNumberImg from '@/assets/images/bulletinNew/materialNumber.svg';
import refugeNumberImg from '@/assets/images/bulletinNew/refugeNumber.svg';
import monitorNumberImg from '@/assets/images/bulletinNew/monitorNumber.svg';
import protectiveTargetNumberImg from '@/assets/images/bulletinNew/protectiveTargetNumber.svg';
import hiddenTroubleNumberImg from '@/assets/images/bulletinNew/hiddenTroubleNumber.svg';
import communicationNumberImg from '@/assets/images/bulletinNew/communicationNumber.svg';
import hygieneNumberImg from '@/assets/images/bulletinNew/hygieneNumber.svg';

export default {
  name: 'TopCard',
  data() {
    return {
      // 原始数据
      statisticsData: {
        expertNumber: 0,
        contingentNumber: 0,
        warehouseNumber: 0,
        materialNumber: 0,
        refugeNumber: 0,
        monitorNumber: 0,
        protectiveTargetNumber: 0,
        hiddenTroubleNumber: 0,
        communicationNumber: 0,
        hygieneNumber: 0
      },
      // 卡片配置
      cardConfig: [
        {
          key: 'expertNumber',
          title: '救援专家',
          img:expertNumberImg,
          unit: '人'
        },
        {
          key: 'contingentNumber',
          title: '救援队伍',
          img:contingentNumberImg,
          unit: '支'
        },
        {
          key: 'warehouseNumber',
          title: '物资仓库',
          img:warehouseNumberImg,
          unit: '个'
        },
        {
          key: 'materialNumber',
          title: '物资总量',
          img:materialNumberImg,
          unit: '套'
        },
        {
          key: 'refugeNumber',
          title: '避难场所',
          img:refugeNumberImg,
          unit: '处'
        },
        {
          key: 'monitorNumber',
          title: '防护目标',
          img:monitorNumberImg,
          unit: '个'
        },
        {
          key: 'protectiveTargetNumber',
          title: '刺险隐患',
          img:protectiveTargetNumberImg,
          unit: '个'
        },
        {
          key: 'hiddenTroubleNumber',
          title: '通讯保障',
          img:hiddenTroubleNumberImg,
          unit: '个'
        },
        {
          key: 'communicationNumber',
          title: '医疗机构',
          img:communicationNumberImg,
          unit: '家'
        },
        {
          key: 'hygieneNumber',
          title: '卫生防疫',
          img:hygieneNumberImg,
          unit: '家'
        }
      ]
    }
  },
  computed: {
    // 计算属性：将数据和配置合并
    cardList() {
      return this.cardConfig.map(config => ({
        ...config,
        value: this.statisticsData[config.key] || 0
      }));
    }
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        const res = await selectNodes();
        if (res && res.data) {
          this.statisticsData = { ...this.statisticsData, ...res.data };
        }
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.top-cards {

  .cards-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
  }

  .card-item {
    width: 100%;
  }

  .stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    height: 84px;
    background: #fff;
    border: 1px solid #EBEBEB;
border-radius: 4px;
display: flex;
gap: 8px;
    .card-icon {
     width: 56px;
height: 56px;

    }

    .card-content {
      flex: 1;

      .card-title {
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
color: #999999;

      }

      .card-number {
        font-family: DINPro;
font-weight: 700;
font-size: 24px;
line-height: 32px;
color: #333333;

      }

      .card-unit {
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 16px;
line-height: 24px;
color: #333333;

      }
    }
  }
}

// 移动端样式调整
@media (max-width: 768px) {
  .top-cards {
    padding: 10px;

    .stat-card {
      padding: 15px;

      .card-icon {
        width: 50px;
        height: 50px;
        margin-right: 12px;

        i {
          font-size: 20px;
        }
      }

      .card-content {
        .card-title {
          font-size: 13px;
        }

        .card-number {
          font-size: 20px;
        }

        .card-unit {
          font-size: 11px;
        }
      }
    }
  }
}
</style>