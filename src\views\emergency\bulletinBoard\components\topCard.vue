<template>
  <div class="top-cards">
    <el-row :gutter="20">
      <el-col
        v-for="item in cardList"
        :key="item.key"
        :xs="12"
        :sm="8"
        :md="6"
        :lg="4"
        :xl="4"
        class="card-col"
      >
        <div class="stat-card">
          <div class="card-icon" :style="{ backgroundColor: item.color }">
            <i :class="item.icon"></i>
          </div>
          <div class="card-content">
            <div class="card-title">{{ item.title }}</div>
            <div class="card-number">{{ item.value }}</div>
            <div class="card-unit">{{ item.unit }}</div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  selectNodes
} from "@/api/emergency/bulletinBoard/index";

export default {
  name: 'TopCard',
  data() {
    return {
      // 原始数据
      statisticsData: {
        expertNumber: 0,
        contingentNumber: 0,
        warehouseNumber: 0,
        materialNumber: 0,
        refugeNumber: 0,
        monitorNumber: 0,
        protectiveTargetNumber: 0,
        hiddenTroubleNumber: 0,
        communicationNumber: 0,
        hygieneNumber: 0
      },
      // 卡片配置
      cardConfig: [
        {
          key: 'expertNumber',
          title: '救援专家',
          icon: 'el-icon-user',
          color: '#409EFF',
          unit: '人'
        },
        {
          key: 'contingentNumber',
          title: '救援队伍',
          icon: 'el-icon-s-custom',
          color: '#67C23A',
          unit: '支'
        },
        {
          key: 'warehouseNumber',
          title: '物资仓库',
          icon: 'el-icon-office-building',
          color: '#E6A23C',
          unit: '个'
        },
        {
          key: 'materialNumber',
          title: '物资总量',
          icon: 'el-icon-goods',
          color: '#67C23A',
          unit: '套'
        },
        {
          key: 'refugeNumber',
          title: '避难场所',
          icon: 'el-icon-location',
          color: '#909399',
          unit: '处'
        },
        {
          key: 'monitorNumber',
          title: '防护目标',
          icon: 'el-icon-view',
          color: '#F56C6C',
          unit: '个'
        },
        {
          key: 'protectiveTargetNumber',
          title: '刺险隐患',
          icon: 'el-icon-warning',
          color: '#909399',
          unit: '个'
        },
        {
          key: 'hiddenTroubleNumber',
          title: '通讯保障',
          icon: 'el-icon-phone',
          color: '#67C23A',
          unit: '个'
        },
        {
          key: 'communicationNumber',
          title: '医疗机构',
          icon: 'el-icon-first-aid-kit',
          color: '#F56C6C',
          unit: '家'
        },
        {
          key: 'hygieneNumber',
          title: '卫生防疫',
          icon: 'el-icon-first-aid-kit',
          color: '#F56C6C',
          unit: '家'
        }
      ]
    }
  },
  computed: {
    // 计算属性：将数据和配置合并
    cardList() {
      return this.cardConfig.map(config => ({
        ...config,
        value: this.statisticsData[config.key] || 0
      }));
    }
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        const res = await selectNodes();
        if (res && res.data) {
          this.statisticsData = { ...this.statisticsData, ...res.data };
        }
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.top-cards {
  padding: 20px;

  .card-col {
    margin-bottom: 20px;
  }

  .stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;

      i {
        font-size: 24px;
        color: #fff;
      }
    }

    .card-content {
      flex: 1;

      .card-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .card-number {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        line-height: 1;
        margin-bottom: 4px;
      }

      .card-unit {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .top-cards {
    padding: 10px;

    .stat-card {
      padding: 15px;

      .card-icon {
        width: 50px;
        height: 50px;
        margin-right: 12px;

        i {
          font-size: 20px;
        }
      }

      .card-content {
        .card-number {
          font-size: 20px;
        }
      }
    }
  }
}
</style>