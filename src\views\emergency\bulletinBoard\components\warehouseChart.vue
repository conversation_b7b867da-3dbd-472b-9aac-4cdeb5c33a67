<template>
  <div class="warehouse-chart-wrapper">
    <div class="chart-container" v-show="show">
      <div class="chart-left">
        <div id="warehousePieChart" ref="warehousePieChart"></div>
      </div>
      <div class="chart-right">
        <div class="legend-list">
          <div
            v-for="(item, index) in legendData"
            :key="index"
            class="legend-item"
          >
            <div class="legend-dot" :style="{ backgroundColor: item.color }"></div>
            <span class="legend-name">{{ item.name }}</span>
            <span class="legend-value">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
    <el-empty description="暂无数据" v-show="!show"></el-empty>
  </div>
</template>

<script>
import * as echarts from "echarts";
import {
  supplyDepotLevel
} from "@/api/emergency/bulletinBoard/index";

export default {
  name: 'WarehouseChart',
  data() {
    return {
      pieData: [],
      legendData: [],
      show: false,
      chartInstance: null,
      // 预定义颜色
      colors: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#17B3A3', '#722ED1', '#EB2F96']
    }
  },
  methods: {
    // 获取数据
    async getOverviewLeft() {
      try {
        const res = await supplyDepotLevel();
        if (res && res.code === 200 && res.data && res.data.length > 0) {
          this.processData(res.data);
          this.show = true;
          this.$nextTick(() => {
            this.drawChart();
          });
        } else {
          this.show = false;
        }
      } catch (error) {
        console.error('获取仓库数据失败:', error);
        this.show = false;
      }
    },

    // 处理数据
    processData(data) {
      this.pieData = [];
      this.legendData = [];

      data.forEach((item, index) => {
        const color = this.colors[index % this.colors.length];

        // 饼图数据
        this.pieData.push({
          name: item.object,
          value: item.count,
          itemStyle: {
            color: color
          }
        });

        // 图例数据
        this.legendData.push({
          name: item.object,
          value: item.count,
          color: color
        });
      });
    },

    // 绘制图表
    drawChart() {
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }

      this.chartInstance = echarts.init(this.$refs.warehousePieChart);

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        series: [{
          name: '仓库分布',
          type: 'pie',
          radius: ['50%', '80%'], // 环形饼图
          center: ['50%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          emphasis: {
            scale: true,
            scaleSize: 5
          },
          data: this.pieData
        }]
      };

      this.chartInstance.setOption(option);

      // 监听窗口大小变化
      window.addEventListener("resize", this.handleResize);
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    }
  },

  mounted() {
    this.getOverviewLeft();
  },

  beforeDestroy() {
    // 清理事件监听和图表实例
    window.removeEventListener("resize", this.handleResize);
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
  }
}
</script>

<style lang="scss" scoped>
.warehouse-chart-wrapper {
  width: 100%;
  height: 300px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .chart-container {
    display: flex;
    align-items: center;
    height: 100%;
    gap: 30px;

    .chart-left {
      flex: 1;
      height: 100%;
      min-height: 260px;

      #warehousePieChart {
        width: 100%;
        height: 100%;
      }
    }

    .chart-right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .legend-list {
        .legend-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }

          .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
          }

          .legend-name {
            color: #666;
            margin-right: 20px;
            min-width: 80px;
          }

          .legend-value {
            font-weight: bold;
            color: #333;
            font-size: 16px;
          }
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .warehouse-chart-wrapper {
    padding: 15px;
    height: auto;
    min-height: 300px;

    .chart-container {
      flex-direction: column;
      gap: 20px;

      .chart-left {
        height: 200px;
        min-height: 200px;
      }

      .chart-right {
        .legend-list {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 15px;

          .legend-item {
            margin-bottom: 0;
            flex-direction: column;
            text-align: center;
            min-width: 80px;

            .legend-name {
              margin-right: 0;
              margin-bottom: 4px;
              font-size: 12px;
            }

            .legend-value {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
</style>