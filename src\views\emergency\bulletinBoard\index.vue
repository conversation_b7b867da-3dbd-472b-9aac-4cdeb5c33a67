<template>
  <div class="app-container">
    <top-card></top-card>
    <div class="center-cards">
      <el-row
      :gutter="16">
        <el-col :span="8">
          <div class="three-inner">
            <div class="three-inner-title">物资储备库级别统计</div>
            <warehouse-chart></warehouse-chart>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="three-inner">
            <div class="three-inner-title">避难场所类型统计</div>

          </div>
        </el-col>
        <el-col :span="8">
          <div class="three-inner">
            <div class="three-inner-title">企业资源数量TOP5</div>

          </div>
        </el-col>
      </el-row>
    </div>
  </div>


</template>

<script>
import topCard from './components/topCard.vue';
import warehouseChart from './components/warehouseChart.vue';
export default {
  components:{
    topCard,warehouseChart
  }
}
</script>

<style lang="scss" scoped>
.center-cards{
  margin-top: 16px;
  :deep(.el-card__body){
    padding: 0px !important;
  }
  .three-inner {
    border-radius: 4px;
    border: 1px solid #EBEBEB;
    background: #FFF;
    height: 360px;

    
  }
  .three-inner-title {
      display: flex;
      height: 56px;
      padding: 0px 16px;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      gap: 10px;
      align-self: stretch;
      border-bottom: 1px solid #EBEBEB;
      background: #FFF;
      color: #333;
      font-family: Noto Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 26px; /* 144.444% */
    }
}
</style>