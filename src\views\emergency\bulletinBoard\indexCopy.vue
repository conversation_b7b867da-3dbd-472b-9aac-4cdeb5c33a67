<template>
  <div class="app-container" ref="container">
    <el-card shadow="always" style="height: 130px">
      <el-row :gutter="20" style="padding-bottom: 20px">
        <el-col :span="5" class="card_col">
          <p class="card_title">物资仓库</p>
          <p class="card_num">{{ cardArr.warehouseNumber }}</p>
        </el-col>
        <el-col :span="5" class="card_col">
          <p class="card_title">应急队伍</p>
          <p class="card_num">{{ cardArr.contingentNumber }}</p>
        </el-col>
        <el-col :span="5" class="card_col">
          <p class="card_title">避难场所</p>
          <p class="card_num">{{ cardArr.refugeNumber }}</p>
        </el-col>
        <el-col :span="5" class="card_col">
          <p class="card_title">应急监控</p>
          <p class="card_num">{{ cardArr.monitorNumber }}</p>
        </el-col>
        <!-- <el-col :span="4" class="card_col">
          <p class="card_title">应急广播</p>
          <p class="card_num">{{ cardArr.broadcastNumber }}</p>
        </el-col> -->
      </el-row>
      <el-row :gutter="20">
        <el-col :span="5" class="card_col">
          <p class="card_title">隐患部位</p>
          <p class="card_num">{{ cardArr.hiddenTroubleNumber }}</p>
        </el-col>
        <el-col :span="5" class="card_col">
          <p class="card_title">通讯保障</p>
          <p class="card_num">{{ cardArr.communicationNumber }}</p>
        </el-col>
        <el-col :span="5" class="card_col">
          <p class="card_title">防护目标</p>
          <p class="card_num">{{ cardArr.protectiveTargetNumber }}</p>
        </el-col>
        <el-col :span="5" class="card_col">
          <p class="card_title">医疗卫生</p>
          <p class="card_num">{{ cardArr.hygieneNumber }}</p>
        </el-col>
      </el-row>
    </el-card>
    <!--  -->
    <div style="padding: 20px 0; height: 80px">
      <span style="margin-right: 20px">资源类型 :</span>
      <el-select
        multiple
        v-model="resourceType"
        placeholder="请选择资源类型"
        style="width: 10vw"
        @change="resourceTypeChange"
        collapse-tags
      >
        <el-option
          v-for="item in dict.type.emergency_resource_type"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </div>
    <el-dialog
      title="实时视频"
      :visible.sync="dialogVisible"
      width="560px"
      style="margin: 30vh 0 0 0vw"
      destroy-on-close
      :modal="false"
    >
      <div id="videoElement" style="width: 100%; height: 100%">
        <jessibuca-player
          id="videoElement1"
          ref="videoPlayer"
          :videoUrl="videoUrl"
          :error="videoError"
          :message="videoError"
          :height="false"
          :hasAudio="hasAudio"
          fluent
          autoplay
          live
        >
        </jessibuca-player>
        <!-- <div class="directionKeys">
            <div class="dk-cell dk-up">
                <img src="../img/up.png" alt="" style="width:50px;height:50px" title="上" @mousedown="operate(8)" @mouseup="operate(0)">
            </div>
            <div class="dk-cell dk-down">
                <img src="../img/down.png" alt="" style="width:50px;height:50px" title="下" @mousedown="operate(4)" @mouseup="operate(0)">
            </div>
            <div class="dk-cell dk-left">
                <img src="../img/left.png" alt="" style="width:50px;height:50px" title="左"  @mousedown="operate(2)" @mouseup="operate(0)">
            </div>
            <div class="dk-cell dk-right">
                <img src="../img/right.png" alt="" style="width:50px;height:50px" title="右"  @mousedown="operate(1)" @mouseup="operate(0)">
            </div>
            <div class="dk-cell dk-square">
                <img src="../img/center.png" alt="" style="width:50px;height:50px">
            </div>
            <div class="dk-cell dk-zoomin">
                <img src="../img/zoomin.png" alt="" style="width:50px;height:50px" title="放大"  @mousedown="operate(16)" @mouseup="operate(0)">
            </div>
            <div class="dk-cell dk-zoomout">
                <img src="../img/zoomout.png" alt="" style="width:50px;height:50px" title="缩小"  @mousedown="operate(32)" @mouseup="operate(0)">
            </div>
          </div> -->
      </div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span> -->
    </el-dialog>
    <!-- 高德地图  -->
    <div id="gao-de-map">
      <!-- 技术支持和联系方式  -->
    </div>
  </div>
</template>
<script>
import {
  view,
  selectNodes,
  getVideoStreaming,
} from "@/api/emergency/bulletinBoard/index";
import src from "@/assets/icons/broadcast.png";
// import AMapLoader from "@amap/amap-jsapi-loader";
import jessibucaPlayer from "./jessibuca.vue";
export default {
  name: "Emergency",
  dicts: [
    "emergency_resource_type",
    "team_type",
    "risk_level",
    "risk_type",
    "target_type",
  ],
  components: {
    jessibucaPlayer,
  },
  props: {
    equipmentId: {
      type: String,
    },
    remark: {
      type: String,
    },
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      cardArr: {},
      resourceType: [],
      querForm: {
        resourceType: "",
      },
      frequency: 0,
      areas: [],
      height: "74%",
      mapFlag: false,
      flvPlayer: null,
      dialogVisible: false,
      // 视频流地址
      firstCreate: true,
      hasAudio: false,
      videoUrl: "",
    };
  },
  created() {},
  mounted() {
    // let height = this.$refs.container.offsetHeight;
    // console.log(height);
    // this.height = height - 250 + "px";
    this.selectNodes();
  },
  beforeMount() {
    window._AMapSecurityConfig = {
      // 设置安全密钥
      securityJsCode: "072a0e6e84770c0ca585ffee9c3c5f5b",
    };
  },
  methods: {
    typeChange(value) {},
    initMap() {
      this.firstCreate = true;
      if (window.__POWERED_BY_QIANKUN__) {
        AMapLoader.reset();
        AMapLoader.load({
          key: "f5b5f1ec4cfa2ff350df220e82acd09e",
          version: "2.0", // 申请好的Web端开发者Key，首次调用 load 时必填
          plugins: [
            "AMap.Autocomplete", // 输入提示插件
            "AMap.PlaceSearch", // POI搜索插件
            "AMap.Scale", // 右下角缩略图插件 比例尺
            "AMap.OverView", // 地图鹰眼插件
            "AMap.ToolBar", // 地图工具条
            "AMap.MapType", // 类别切换控件，实现默认图层与卫星图、实施交通图层之间切换的控制
            "AMap.PolyEditor", // 编辑 折线多，边形
            "AMap.CircleEditor", // 圆形编辑器插件
            "AMap.Geolocation", // 定位控件，用来获取和展示用户主机所在的经纬度位置
            "AMap.Geocoder",
            "AMap.Heatmap",
            "AMap.SimpleMarker",
            "AMap.PolygonEditor",
            "AMap.ControlBar",
            "AMap.MouseTool",
            "AMap.MarkerCluster",
          ],
          AMapUI: {
            // 是否加载 AMapUI，缺省不加载
            // version: "1.1", // AMapUI 缺省 1.1
            plugins: ["misc/PositionPicker", "overlay/SimpleMarker"], // 需要加载的 AMapUI ui插件
          },
        })
          .then((AMap) => {
            this.map = new AMap.Map("gao-de-map", {
              viewMode: "2D", //  是否为3D地图模式
              zoom: 13, // 初始化地图级别
              center: [115.159226, 37.606716], //中心点坐标  郑州
              resizeEnable: true,
            });
            this.marker();
            this.firstCreate = false;
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        this.AMapLoader.load({
          key: "f5b5f1ec4cfa2ff350df220e82acd09e",
          version: "2.0", // 申请好的Web端开发者Key，首次调用 load 时必填
          plugins: [
            "AMap.Autocomplete", // 输入提示插件
            "AMap.PlaceSearch", // POI搜索插件
            "AMap.Scale", // 右下角缩略图插件 比例尺
            "AMap.OverView", // 地图鹰眼插件
            "AMap.ToolBar", // 地图工具条
            "AMap.MapType", // 类别切换控件，实现默认图层与卫星图、实施交通图层之间切换的控制
            "AMap.PolyEditor", // 编辑 折线多，边形
            "AMap.CircleEditor", // 圆形编辑器插件
            "AMap.Geolocation", // 定位控件，用来获取和展示用户主机所在的经纬度位置
            "AMap.Geocoder",
            "AMap.Heatmap",
            "AMap.SimpleMarker",
            "AMap.PolygonEditor",
            "AMap.ControlBar",
            "AMap.MouseTool",
            "AMap.MarkerCluster",
          ],
          AMapUI: {
            // 是否加载 AMapUI，缺省不加载
            // version: "1.1", // AMapUI 缺省 1.1
            plugins: ["misc/PositionPicker", "overlay/SimpleMarker"], // 需要加载的 AMapUI ui插件
          },
        })
          .then((AMap) => {
            this.map = new AMap.Map("gao-de-map", {
              viewMode: "2D", //  是否为3D地图模式
              zoom: 13, // 初始化地图级别
              center: [115.159226, 37.606716], //中心点坐标  郑州
              resizeEnable: true,
            });
            this.marker();
            this.firstCreate = false;
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    marker() {
      this.markersList = [];
      this.mapFlag = false;
      this.infoWindow = new AMap.InfoWindow({
        offset: new AMap.Pixel(0, 0),
        autoMove: true,
      });
      console.log(this.areas, "点标记");
      // 配置marker
      this.areas.forEach((item) => {
        if (item.longitude != null && item.latitude != null) {
          this.markers = new AMap.Marker({
            icon: new AMap.Icon({
              image: this.iconScreen(item).url,
              imageSize: new AMap.Size(25, 25),
            }),
            position: [item.longitude, item.latitude],
            offset: new AMap.Pixel(0, 0),
          });
          this.markers.content = this.iconScreen(item).text;
          this.markers.deviceId = item.deviceId ? item.deviceId : "";
          this.markers.on("click", this.markerClick);
          this.markers.emit("click", { target: this.markers });
          this.markersList.push(this.markers);
        }
      });
      this.map.add(this.markersList); //将marker添加到map中
      this.map.setFitView();
      this.mapFlag = true;
    },
    markerClick(e) {
      if (this.firstCreate) {
        return;
      }
      if (e.target.content.indexOf("设备名称") != -1) {
        this.dialogVisible = false;
        this.getvideoFlv(e.target.deviceId);
      }
      if (!this.mapFlag) {
        return;
      }
      this.infoWindow.setContent(e.target.content);
      this.infoWindow.open(this.map, e.target.getPosition());
    },
    selectNodes() {
      this.dialogVisible = false;
      console.log(this.querForm);
      selectNodes(this.querForm).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.cardArr = res.data;
          this.areas = res.data.list;
          this.initMap();
        }
      });
    },
    iconScreen(item) {
      switch (item.resourceType) {
        case "5013002":
          return {
            url: require("@/assets/icons/name.png"),
            text: `<div>
                    <div class='markerLabel'>
                        <p>队伍名称：${item.name || ""}</p><p>队伍类型：${
              this.dict.type.team_type.find(
                (ele) => ele.value == item.contingentType
              ) == undefined
                ? ""
                : this.dict.type.team_type.find(
                    (ele) => ele.value == item.contingentType
                  ).label
            }</p>

                    </div>
                    <div class='markerLabel'>
                      <p>队伍人数：${
                        item.contingentNumber || ""
                      }</p><p>主要职责：${item.responsibilities || ""}</p>
                    </div>
                    <div class='markerLabel'>
                      <p>责任人：${item.liabilityUser || ""}</p><p>联系方式：${
              item.phone || ""
            }</p>
                    </div>
                  </div>`,
          };
        case "5013003":
          return {
            url: require("@/assets/icons/shelter.png"),
            text: `<div>
                    <div class='markerLabel'>
                      <p>避难所名称：${item.name || ""}</p><p>场所面积：${
              item.refugeArea || ""
            }</p>
                    </div>
                    <div class='markerLabel'>
                      <p>容纳人数：${item.holdsNumber || ""}</p><p>负责人：${
              item.liabilityUser || ""
            }</p>
                    </div>
                    <div class='markerLabel'>
                      </p><p>联系方式：${item.phone || ""}</p>
                    </div>
                  </div>`,
          };
        case "5013004":
          if (item.isOnline == 1) {
            return {
              url: require("@/assets/icons/monitor.png"),
              text: `<div>
                    <div class='markerLabel'>
                      <p>设备名称：${item.name || ""}</p><p>设备编号：${
                item.deviceId || ""
              }</p>
                    </div>
                    <div class='markerLabel'>
                      <p>责任人：${item.liabilityUser || ""}</p><p>联系方式：${
                item.phone || ""
              }</p>
                    </div>
                  </div>`,
            };
          } else if (item.isOnline == 0) {
            return {
              url: require("@/assets/icons/monitor1.png"),
              text: `<div>
                    <div class='markerLabel'>
                      <p>设备名称：${item.name || ""}</p><p>设备编号：${
                item.deviceId || ""
              }</p>
                    </div>
                    <div class='markerLabel'>
                      <p>责任人：${item.liabilityUser || ""}</p><p>联系方式：${
                item.phone || ""
              }</p>
                    </div>
                  </div>`,
            };
          }

        // case "5013005":
        //   return {
        //     url: require("@/assets/icons/broadcast.png"),
        //     text: `<div>
        //             <div class='markerLabel'>
        //               <p>广播设备：${item.name}</p><p>设备编号：${item.deviceId}</p>
        //             </div>
        //             <div class='markerLabel'>
        //               <p>责任人：${item.liabilityUser}</p><p>联系方式：${item.phone}</p>
        //             </div>
        //           </div>`,
        //   };
        case "5013006":
          return {
            url: require("@/assets/icons/riskPosition.png"),
            text: `<div>
                    <div class='markerLabel'>
                        <p>单位名称：${item.name || ""}</p><p>风险类型：${
              this.dict.type.risk_type.find(
                (ele) => ele.value == item.riskType
              ) == undefined
                ? ""
                : this.dict.type.risk_type.find(
                    (ele) => ele.value == item.riskType
                  ).label
            }</p>

                    </div>
                    <div class='markerLabel'>
                        <p>风险等级：${
                          this.dict.type.risk_level.find(
                            (ele) => ele.value == item.riskGrade
                          ) == undefined
                            ? ""
                            : this.dict.type.risk_level.find(
                                (ele) => ele.value == item.riskGrade
                              ).label
                        }</p><p>主要因素：${item.riskFactors || ""}</p>

                    </div>
                    <div class='markerLabel'>
                      <p>责任人：${item.liabilityUser || ""}</p><p>联系方式：${
              item.phone || ""
            }</p>
                    </div>
                  </div>`,
          };
        case "5013007":
          return {
            url: require("@/assets/icons/communication.png"),
            text: `<div>
                    <div class='markerLabel'>
                      <p>机构名称：${item.name || ""}</p><p>能力情况：${
              item.capacityStatus || ""
            }</p>
                    </div>
                    <div class='markerLabel'>
                      <p>责任人：${item.liabilityUser || ""}</p><p>联系方式：${
              item.phone || ""
            }</p>
                    </div>
                  </div>`,
          };
        case "5013008":
          return {
            url: require("@/assets/icons/protection.png"),
            text: `<div>
                    <div class='markerLabel'>
                        <p>防护目标名称：${item.name || ""}</p><p>类型：${
              this.dict.type.target_type.find(
                (ele) => ele.value == item.targetType
              ) == undefined
                ? ""
                : this.dict.type.target_type.find(
                    (ele) => ele.value == item.targetType
                  ).label
            }</p>

                    </div>
                    <div class='markerLabel'>
                      <p>责任人：${item.liabilityUser || ""}</p><p>联系方式：${
              item.phone || ""
            }</p>
                    </div>
                    <div class='markerLabel'>
                      <p>基本情况：${item.basicSituation || ""}</p>
                    </div>
                  </div>`,
          };
        case "5013009":
          return {
            url: require("@/assets/icons/medical.png"),
            text: `<div>
                    <div class='markerLabel'>
                      <p>机构名称：${item.name || ""}</p><p>能力情况：${
              item.capacityStatus || ""
            }</p>
                    </div>
                    <div class='markerLabel'>
                      <p>责任人：${item.liabilityUser || ""}</p><p>联系方式：${
              item.phone || ""
            }</p>
                    </div>
                  </div>`,
          };
        default:
          return {
            url: require("@/assets/icons/warehouse.png"),
            text: `<div>
                    <div class='markerLabel'>
                      <p>物资库名称：${item.name || ""}</p><p>物资库说明：${
              item.illustrate || ""
            }</p>
                    </div>
                    <div class='markerLabel'>
                      <p>责任人：${item.liabilityUser || ""}</p><p>联系方式：${
              item.phone || ""
            }</p>
                    </div>
                  </div>`,
          };
      }
    },
    resourceTypeChange(res) {
      console.log(res);
      this.querForm.resourceType = res.join(",");
      this.selectNodes();
    },
    // 视频流播放相关
    videoError: function (e) {
      console.log("播放器错误：" + JSON.stringify(e));
    },
    getvideoFlv(item) {
      this.dialogVisible = true;
      getVideoStreaming({
        equipmentIdList: [item],
      }).then((res) => {
        console.log(res.data[0].flvAddress);
        this.$nextTick(() => {
          this.videoUrl = res.data[0].flvAddress;
          this.$refs.videoPlayer.play(this.videoUrl);
        });
      });
    },
    closeVideo() {
      this.$refs.videoPlayer.destroy();
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  height: 100%;
  position: absolute;
  width: 100%;
  overflow-y: auto;
}

::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}

.card_col {
  display: flex;

  .card_title {
    color: rgba(56, 56, 56, 1);
    font-size: 20px;
    font-weight: bold;
    margin: 0;
    height: 40px;
    line-height: 40px;
  }

  .card_num {
    color: rgba(42, 130, 228, 1);
    font-size: 25px;
    font-weight: bold;
    margin: 0;
    height: 40px;
    line-height: 40px;
    padding-left: 5%;
  }
}

#gao-de-map {
  overflow: hidden;
  width: 100%;
  height: 80vh;
  margin: 0;
  font-family: "微软雅黑";
}
</style>
<style>
/* 隐藏高德logo  */
.amap-logo {
  display: none !important;
}

/* 隐藏高德版权  */
.amap-copyright {
  display: none !important;
}

.markerLabel {
  min-width: 500px;
  min-height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.markerLabel p {
  min-width: 250px;
}
#videoElement1 {
  width: 550px;
  height: 300px;
}
.directionKeys {
  width: 150px;
  height: 150px;
  text-align: center;
  position: relative;
  font-size: 24px;
  margin-left: 40%;
  margin-top: 10px;
}
.dk-cell {
  width: 50px;
  height: 50px;
  line-height: 50px;
  position: absolute;
}
.dk-up {
  top: 0;
  left: 40px;
}
.dk-down {
  top: 80px;
  left: 40px;
}
.dk-left {
  top: 40px;
  left: 0px;
}
.dk-right {
  top: 40px;
  left: 80px;
}
.dk-square {
  top: 40px;
  left: 40px;
}
.dk-zoomin {
  top: 130px;
  left: 10px;
}
.dk-zoomout {
  top: 130px;
  left: 70px;
}
</style>