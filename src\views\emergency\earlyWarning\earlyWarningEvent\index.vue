<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-position="left"
            style="display: flex; justify-content: space-between"
          >
            <div>
              <el-form-item label="预警名称">
                <el-input
                  v-model.number="queryParams.name"
                  placeholder="请输入预警名称"
                  clearable
                  style="width: 10vw"
                  maxlength="20"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="上报人">
                <el-input
                  v-model="queryParams.reporter"
                  placeholder="请输入上报人"
                  clearable
                  maxlength="20"
                  style="width: 10vw"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 10vw"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button
                  class="resetQueryStyle"
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  class="resetQueryStyle"
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>预警事件展示列表</span>
            <el-button
              type="primary"
              plain
              size="mini"
              @click="handleAdd"
              icon="el-icon-plus"
              class="queryBtnT"
              >新增预警事件</el-button
            >
          </div>
          <el-table
            v-loading="loading"
            :data="shelter"
            :cell-style="{ padding: '0px' }"
            :row-style="{ height: '48px' }"
          >
            <el-table-column label="序号" align="center">
              <template slot-scope="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size + scope.$index + 1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="预警名称"
              align="center"
              prop="name"
              show-overflow-tooltip
            />
            <el-table-column
              label="上报人"
              align="center"
              prop="reporter"
              show-overflow-tooltip
            />
            <el-table-column
              label="创建时间"
              align="center"
              prop="createTime"
              show-overflow-tooltip
            />
            <el-table-column label="事件状态" align="center">
              <template slot-scope="scope">
                {{ getNameById(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="160"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleLook(scope.row)"
                  >查看</el-button
                >
                <el-button
                  v-if="scope.row.status == '5011504'"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  >重新上报</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.current"
            :limit.sync="queryParams.size"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改预警事件对话框 -->
    <el-dialog :title="title" :visible.sync="abilityOpen" width="960px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form
            ref="abilityForm"
            :model="abilityForm"
            :rules="abilityRules"
            label-width="110px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="经纬度 :" prop="lngAndLat">
                  <el-button
                    type="primary"
                    :disabled="disabled"
                    plain
                    @click="openMap()"
                    >{{ lngAndLat ? lngAndLat : "点击选择" }}</el-button
                  >
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预警名称 :" prop="name">
                  <el-input
                    v-model="abilityForm.name"
                    placeholder="请输入预警名称"
                    maxlength="20"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="上报人 :" prop="reporter">
                  <el-input
                    v-model="abilityForm.reporter"
                    placeholder="请输入上报人"
                    maxlength="20"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话 :" prop="phone">
                  <el-input
                    v-model="abilityForm.phone"
                    placeholder="请输入联系电话"
                    maxlength="20"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="附件 :">
                  <el-upload
                    class="upload-demo"
                    :action="uploadImgUrl"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeAvatarUpload"
                    :file-list="fileList"
                    :disabled="disabled"
                    :on-preview="handledownload"
                    :on-remove="handleRemove"
                    v-model="abilityForm.fileUrl"
                    :limit="1"
                    :headers="headers"
                    :on-exceed="handleExceed"
                  >
                    <el-button size="small" :disabled="disabled" type="primary"
                      >点击上传</el-button
                    >
                    <div slot="tip" class="el-upload__tip"><div slot="tip" class="el-upload__tip">支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB</div></div>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件描述 :" prop="eventDescription">
                  <el-input
                    v-model="abilityForm.eventDescription"
                    placeholder="请输入事件描述"
                    type="textarea"
                    maxlength="200"
                    :disabled="disabled"
                    style="width: 245px"
                  >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button
          class="popupButton"
          type="primary"
          @click="confirm('abilityForm')"
          :disabled="disabled"
          >上报</el-button
        >
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 地图展示 -->
    <Map
      @mapConfirm="mapConfirm"
      :ranksForm="abilityForm"
      :disabled="disabled"
      :mapVisible="mapVisible"
      @mapCancellation="mapCancellation"
      ref="mapRef"
      :url="url"
    ></Map>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import {
  page,
  save,
  update,
  deleteById,
  handledownload,
} from "@/api/emergency/earlyWarning/earlyWarningEvent/index";
import Map from "../../../map/index.vue";
var stage;
var layer;
var editor;
var toNode = undefined;
var nodeOrLink = undefined;
export default {
  name: "EmergencySupplies",
  dicts: ["alarm_status"],
  components: { Map },
  data() {
    let checkPhone = (rule, value, callback) => {
      let reg = /^1[345789]\d{9}$/;
      if (!reg.test(value)) {
        callback(new Error("请输入11位手机号"));
      } else {
        callback();
      }
    };
    const validCode = (rule, value, callback) => {
      console.log(rule, value, this.lngAndLat, "value");
      if (this.lngAndLat) {
        callback();
      } else {
        callback(new Error("请选择经纬度"));
      }
    };
    return {
      // 地图点标记图标地址
      url: require("../../../../assets/icons/shelter.png"),
      container: {},
      // 地图遮罩层
      mapVisible: false,
      lngAndLat: "",
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      shelter: null,
      // 是否显示弹出层
      abilityOpen: false,
      title: "新增预警事件",
      fileList: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        refugeName: undefined,
        liabilityUser: undefined,
        phone: undefined,
      },
      frequency: 0,
      abilityForm: {},
      disabled: false,
      // 表单校验
      abilityRules: {
        name: [{ required: true, message: "预警名称不能为空", trigger: "blur" }],
        reporter: [{ required: true, message: "上报人不能为空", trigger: "blur" }],
        eventDescription: [
          { required: true, message: "事件描述不能为空", trigger: "blur" },
        ],
        lngAndLat: [{ required: true, validator: validCode, trigger: "blur" }],
        phone: [
          {
            type: "number",
            validator: checkPhone,
            message: "请输入正确的手机号",
            trigger: "change",
            required: true,
          },
        ],
      },
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/emergency-v2/file/uploadFile",
      dateRange: [],
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    /** 查询场所列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        console.log(response);
        if (response.data != null) {
          this.shelter = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    handledownload(row) {
      console.log(row);
      const _this = this;
      let arr1 = [];
      if (this.title == "查看预警事件详情") {
        arr1 = row.url.split(",");
      } else {
        arr1 = row.response.split(",");
      }
      arr1.map((res) => {
        let arr = res.split("/");
        handledownload(arr).then(async (res) => {
          _this.handledownloadGet(arr, res);
        });
      });
    },
    //获取场所详情

    handleLook(row) {
      this.reset();
      this.abilityOpen = true;
      this.abilityForm = JSON.parse(JSON.stringify(row));
      this.title = "查看预警事件详情";
      this.disabled = true;
      this.lngAndLat = row.longitude + "," + row.latitude;
      let array = [];
      if (row.fileUrl != "") {
        array = row.fileUrl.split("/");
        this.fileList.push({
          name: array[array.length - 1],
          url: row.fileUrl,
        });
      }
    },
    handleUpdate(row) {
      this.reset();
      this.abilityOpen = true;
      this.title = "编辑预警事件";
      this.disabled = false;
      this.lngAndLat = row.longitude + "," + row.latitude;
      this.abilityForm = JSON.parse(JSON.stringify(row));
      let array = [];
      if (row.fileUrl != "") {
        array = row.fileUrl.split("/");
        this.fileList.push({
          name: array[array.length - 1],
          url: row.fileUrl,
        });
      }
    },
    beforeAvatarUpload(file) {
      console.log(file);
      let array = [
        "jpeg",
        "jpg",
        "png",
        "gif",
        "bmp",
        "tiff",
        "webp",
        "svg",
        "mp4",
        "avi",
        "mkv",
        "mov",
        "wmv",
        "flv",
        "webm",
        "mpeg",
        "mp3",
        "wav",
        "aac",
        "flac",
        "ogg",
        "wma",
        "pdf",
        "word",
        "excel",
        "txt",
        "doc",
        "docx",
        "xlsx",
        "xls",
        "pptx",
        "ppt",
      ];
      let type = file.name.split(".");
      const isLt2M = file.size / 1024 / 1024 < 100;
      const isType = array.indexOf(type[1]) == -1;
      console.log(isType);
      if (isType) {
        this.$message.error(
          "仅支持 jpeg|jpg|png|gif|bmp|tiff|webp|svg|mp4|avi|mkv|mov|wmv|flv|webm|mpeg|mp3|wav|aac|flac|ogg|wma|pdf|word|excel|txt|doc|docx|xlsx|xls|pptx|ppt| 格式!"
        );
      }
      if (!isLt2M) {
        this.$message.error("上传附件大小不能超过 100MB!");
      }
      return !isType && isLt2M;
    },
    handleAdd() {
      this.reset();
      this.abilityOpen = true;
      this.title = "新增预警事件";
      this.disabled = false;
    },
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => {});
    },
    jtopoDel() {
      this.abilityForm.node = undefined;
      toNode.remove();
      toNode = undefined;
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    /*  确认保存新增*/
    confirm(formName) {
      console.log(this.abilityForm);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.abilityForm.id != undefined) {
            update(this.abilityForm).then((response) => {
              // console.log(response, "编辑");
              if (response.code == 200) {
                this.$modal.msgSuccess("编辑成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          } else {
            save(this.abilityForm).then((response) => {
              // console.log(response, "新增");
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          }
        }
      });
      // console.log(this.evaluateData, "evaluateData");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.startTime = undefined;
      this.queryParams.endTime = undefined;
      this.queryParams.current = 1;
      if (this.dateRange) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      this.getList();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.abilityForm = {};
      this.fileList = [];
      this.lngAndLat = "";
      this.resetForm("abilityForm");
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        current: 1,
        size: 10,
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 打开地图按钮
    openMap() {
      this.mapVisible = true;
      this.$nextTick(() => {
        this.$refs.mapRef.initMap();
      });
    },
    // 地图返回经纬度的回调
    mapConfirm(lng, lat) {
      if (lng && lat) {
        this.mapVisible = false;
        this.lngAndLat = lng + "," + lat;
        this.abilityForm.longitude = lng;
        this.abilityForm.latitude = lat;
        // 获取到经纬度就取消验证提示
        this.$nextTick(() => {
          this.$refs.abilityForm.clearValidate();
        });
      } else {
        this.$modal.msgSuccess("请选择经纬度");
      }
    },
    // 取消地图的回调
    mapCancellation() {
      this.mapVisible = false;
    },
    handleAvatarSuccess(response, res, file) {
      console.log(response, res, file);
      this.abilityForm.fileUrl = response;
      this.abilityForm.fileName = res.name;
    },
    handleRemove(file, fileList) {
      this.abilityForm.fileUrl = "";
      this.abilityForm.fileName = "";
    },
    handleExceed() {
      this.$modal.msgSuccess("请不要上传多个文件");
    },
    getNameById(res) {
      // console.log(res, this.dict.type.alarm_status);
      if (res.status != undefined && res.status != "" && res.status != null) {
        return this.dict.type.alarm_status.filter((item) => item.value == res.status)[0]
          .label;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
  float: right;
  margin-right: 10px;
}

.resetQueryStyle {
  width: 88px;
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}
::v-deep .el-form-item__label {
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>
