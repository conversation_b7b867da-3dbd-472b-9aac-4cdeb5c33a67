<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form
            v-show="showSearch"
            ref="queryForm"
            :model="queryParams"
            size="small"
            :inline="true"
            label-position="left"
            style="display: flex; justify-content: space-between"
          >
            <div>
              <el-form-item label="事件编码">
                <el-input
                  v-model="queryParams.eventNo"
                  placeholder="请输入事件编码"
                  style="width: 10vw"
                  maxlength="20"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="事件名称">
                <el-input
                  v-model="queryParams.eventName"
                  placeholder="请输入事件名称"
                  clearable
                  maxlength="20"
                  style="width: 10vw"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="事件类型">
                <el-select
                  v-model="queryParams.eventTypeName"
                  style="width: 10vw"
                  placeholder="请选择事件类型"
                >
                  <el-option :value="queryParams.eventTypeName" class="option">
                    <el-tree
                      ref="Addtree"
                      :data="treeData"
                      :show-checkbox="true"
                      node-key="id"
                      :props="defaultProps"
                      class="tree"
                      @check="handleNodeClick"
                    />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="事件等级">
                <el-select
                  v-model="queryParams.eventLevel"
                  style="width: 10vw"
                  placeholder="请选择事件等级"
                >
                  <el-option
                    v-for="dict in dict.type.event_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="调度进度">
                <el-select
                  v-model="queryParams.dispatchStateList"
                  style="width: 10vw"
                  placeholder="请选择调度进度"
                  clearable 
                  multiple
                >
                  <el-option
                    v-for="dict in dict.type.emergency_dispatch_state"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="事件时间">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  format="yyyy-MM-dd HH:mm:ss"
                />
              </el-form-item>
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card mapBox">
          <div slot="header" class="clearfix">
            <span>应急事件展示列表</span>
          </div>
          <el-button
            v-show="!eventListDialog"
            style="position: absolute; z-index: 1"
            type="primary"
            
            @click="eventListDialog = true"
            >事件列表</el-button
          >
          <div v-show="eventListDialog" class="eventListBox">
            <div class="boxTitle">
              <div class="content">事件列表</div>
              <i
                class="el-icon-close"
                style="color: #fff; cursor: pointer"
                @click="eventListDialog = false"
              />
            </div>
            <div class="boxContent">
              <div
                v-for="(item, index) in eventList"
                :key="index"
                class="event"
                @click="eventClick(item)"
              >
                <!-- <div class="overlength" :title="item.eventNo">
                  <span class="name">事件编码：</span>{{ item.eventNo }}
                </div> -->
                <div class="overlength" :title="item.eventName">
                  <span class="name">事件名称：</span>{{ item.eventName }}
                </div>
                <div>
                  <span class="name">事件类型：</span
                  ><el-tag type="success">{{ item.eventTypeName }}</el-tag>
                </div>
                <div>
                  <span class="name">事件等级：</span
                  >{{
                    item.eventLevel
                      ? dict.type.event_level.find((ele) => ele.value == item.eventLevel)
                          .label
                      : ""
                  }}
                </div>
                <div class="overlength" :title="item.occurrenceTime">
                  <span class="name">事件时间：</span>{{ item.occurrenceTime }}
                </div>
                <div class="overlength">
                  <span class="name">调度进度：</span
                  >{{
                    item.dispatchState
                      ? dict.type.emergency_dispatch_state.find(
                          (ele) => ele.value == item.dispatchState
                        ).label
                      : ""
                  }}
                </div>
              </div>
            </div>
          </div>
          <div v-show="eventDetaildialog" class="eventDetailBox">
            <div class="boxTitle">
              <div class="content">事件名称：{{ eventDetail.eventName }}</div>
              <i
                class="el-icon-close"
                style="color: #fff; cursor: pointer"
                @click="eventDetaildialog = false"
              />
            </div>
            <div class="detailContent">
              <div class="eventDetail">
                <div>
                  <span class="name">事件类型：</span>{{ eventDetail.eventTypeName }}
                </div>
                <div>
                  <span class="name">事件描述：</span>{{ eventDetail.eventDescription }}
                </div>
                <div>
                  <span class="name">事件位置：</span
                  >{{ eventDetail.longitude + "," + eventDetail.latitude }}
                </div>
                <div>
                  <span class="name">事件时间：</span>{{ eventDetail.occurrenceTime }}
                </div>
              </div>
              <div class="btnBox1">
                <el-button type="primary" @click="handleReport">事故报告</el-button>
                <el-button type="primary" @click="handleCommand"
                  >预案调度</el-button
                >
                <el-button type="primary" @click="handleRecord">执行记录</el-button>

                <!-- <el-button type="primary" @click="handleSecond">次生分析</el-button> -->
              </div>
              <div class="btnBox">
                <!-- <el-button type="primary" @click="handleInformation"
                  >信息发布</el-button
                > -->
              </div>
            </div>
          </div>
          <div v-show="resourcefulSearch" class="searchBox">
            <div class="boxTitle">
              <div class="content">周边资源搜索</div>
              <i
                class="el-icon-close"
                style="color: #fff; cursor: pointer"
                @click="resourcefulSearch = false"
              />
            </div>
            <div class="searchContent">
              <div class="searchTitle">距离范围筛选</div>
              <div style="margin: 20px 0">
                <el-radio-group v-model="range" @change="rangeChange">
                  <el-radio-button label="200">200m</el-radio-button>
                  <el-radio-button label="500">500m</el-radio-button>
                  <el-radio-button label="1000">1km</el-radio-button>
                  <el-radio-button label="5000">5km</el-radio-button>
                </el-radio-group>
              </div>
              <div class="searchTitle">资源筛选</div>
              <div style="margin-top: 20px">
                <el-checkbox-group v-model="checkList" @change="checkListChange">
                  <el-checkbox label="5013003">避难场所</el-checkbox>
                  <el-checkbox label="5013005">应急广播</el-checkbox>
                  <el-checkbox label="5013004">周边监控</el-checkbox>
                  <el-checkbox label="5013008">防护目标</el-checkbox>
                  <el-checkbox label="5013009">医疗机构</el-checkbox>
                  <el-checkbox label="5013006">风险隐患</el-checkbox>
                  <el-checkbox label="5013007">通讯保障</el-checkbox>
                </el-checkbox-group>
              </div>
              <div v-show="!eventDetail.isEndRescue">
                <el-radio-group v-model="dispatch">
                  <el-radio-button label="rank">队伍调度</el-radio-button>
                  <el-radio-button label="good">物资调度</el-radio-button>
                </el-radio-group>
              </div>
              <div
                v-show="dispatch == 'rank' && !eventDetail.isEndRescue"
                class="rankAndgood"
              >
                <div
                  v-for="(item, index) in ranksList"
                  v-show="item.show"
                  :key="index"
                  class="dispatch"
                >
                  <div class="rankName">
                    {{ item.name }}
                  </div>
                  <div>
                    <el-button type="text" @click="rankPosition(item)">定位</el-button>
                    <el-button type="text" @click="rankDispatch(item)">调度</el-button>
                  </div>
                </div>
              </div>
              <div v-show="dispatch == 'good'" class="rankAndgood">
                <div
                  v-for="(item, index) in goodsList"
                  v-show="item.show"
                  :key="index"
                  class="dispatch"
                >
                  <div class="rankName">
                    {{ item.name }}
                  </div>
                  <div>
                    <el-button type="text" @click="goodPosition(item)">定位</el-button>
                    <el-button type="text" @click="goodDispatch(item)">调度</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <Map ref="mapRef" :url="url" @detail="detailDialog" />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 事故报告对话框 -->
    <el-dialog width="960px" append-to-body :visible.sync="reportDialog" title="事故报告">
      <el-descriptions title="基本信息">
        <el-descriptions-item label="事件名称">{{
          reportDetail.eventName
        }}</el-descriptions-item>
        <el-descriptions-item label="上报人">{{
          reportDetail.submitPerson
        }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{
          reportDetail.contactNumber
        }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="事件详情" style="margin: 20px 0">
        <el-descriptions-item label="事件描述">{{
          reportDetail.eventDescription
        }}</el-descriptions-item>
        <el-descriptions-item label="发生时间">{{
          reportDetail.occurrenceTime
        }}</el-descriptions-item>
        <el-descriptions-item label="事件附件">
          <el-button
            v-if="reportDetail.attachmentAddress"
            type="primary"
            size="mini"
            @click="handledownload(reportDetail)"
            >下载附件</el-button
          >
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="事件报告">
        <el-descriptions-item label="事件类型">
          <el-tag class="tag" size="small">{{ reportDetail.eventTypeName }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="事件等级">{{
            reportDetail.eventLevel
                      ? dict.type.event_level.find((ele) => ele.value == reportDetail.eventLevel)
                          .label
                      : ""
        }}</el-descriptions-item>
        <el-descriptions-item label="经济损失">{{
          reportDetail.economicLoss
        }}</el-descriptions-item>
        <el-descriptions-item label="受灾面积"
          >{{ reportDetail.disasterArea }}公顷</el-descriptions-item
        >
        <el-descriptions-item label="死亡人数">{{
          reportDetail.deathNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="受伤人数">{{
          reportDetail.injuredNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="失踪人数">{{
          reportDetail.missingNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="受困人数">{{
          reportDetail.trappedNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="事件标签">
          <el-tag v-for="item in reportDetail.eventTypeLabels" class="tag" size="small">{{
            item.label
          }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">返 回</el-button>
      </div>
    </el-dialog>
    <!-- 指挥调度对话框 -->
    <el-dialog
      width="960px"
      append-to-body
      :visible.sync="commandDialog"
      :show-close="false"
      :close-on-click-modal="false"
      title="指挥调度"
    >
      <el-tabs v-model="activeName" @tab-click="commandClick">
        <el-tab-pane label="预案响应" name="first">
          <el-row>
            <el-col :span="7">
              <div class="planList">
                <el-button
                  v-show="!eventDetail.isEndRescue"
                  type="primary"
                  @click="launchPlan"
                  >启动预案</el-button
                >
                <div class="planCard">
                  <div
                  v-for="(item, index) in planBox"
                  :key="index"
                  class="plan"
                  @click="planClick(item)"
                >
                  <div>{{ item.planName }}</div>
                  <el-tag style="margin: 10px 0" type="danger">{{
                    item.responseName
                  }}</el-tag>
                  <div style="white-space: nowrap">启动时间：{{ item.createTime }}</div>
                </div>
                </div>
              </div>
            </el-col>
            <el-col :span="17">
              <el-row>
                <el-col :span="16">
                  <div class="stepBox">
                    <div v-for="(item, index) in stepList" :key="index" class="step">
                      <span>步骤{{ index + 1 }}</span>
                      <div class="rank">
                        <div>
                          响应名称:{{ item.responseName }}
                          {{
                            dict.type.step_status.find(
                              (ele) => ele.value == item.processStatus
                            ).label
                          }}
                        </div>
                        <el-button
                          v-show="item.processStatus == 5011801"
                          type="text"
                          @click="processUpdate(item.id, '5011802')"
                          >开始处置</el-button
                        >
                        <el-button
                          v-show="item.processStatus == 5011802"
                          type="text"
                          @click="processUpdate(item.id, '5011803')"
                          >结束步骤</el-button
                        >
                      </div>
                      <div>注意事项:{{ item.announcement }}</div>
                      <div
                        v-for="(ele, ind) in item.contingentList"
                        :key="ind"
                        class="rank"
                      >
                        <div>
                          响应队伍:{{ ele.contingentName }}
                          {{
                            dict.type.contingent_status.find(
                              (el) => el.value == ele.contingentStatus
                            ).label
                          }}
                        </div>
                        <el-button
                          v-show="
                            ele.contingentStatus == 5011901 &&
                            item.processStatus == 5011802
                          "
                          type="text"
                          @click="assign(ele)"
                          >指派</el-button
                        >
                        <el-button
                          v-show="
                            ele.contingentStatus == 5011902 &&
                            item.processStatus == 5011802
                          "
                          type="text"
                          @click="finish(ele, '5011902')"
                          >完成</el-button
                        >
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <el-timeline style="height: 54vh; overflow-y: auto">
                    <el-timeline-item
                      v-for="(activity, index) in activities"
                      :key="index"
                      :timestamp="'步骤' + (index + 1)"
                      placement="top"
                    >
                      开始时间：{{ activity.startTime }}
                      <br />
                      <div v-for="(ite, ind) in activity.contingentList" :key="ind">
                        指派队伍：{{ ite.contingentName }}
                        <br />
                        任务描述：{{ ite.taskDescription }}
                        <br />
                        <span v-show="ite.contingentStatus == '5011902'">
                          预期完成时间：{{ ite.finishTime }}
                        </span>
                        <span v-show="ite.contingentStatus == '5011903'">
                          完成时间：{{ ite.endTime }}
                        </span>
                      </div>
                      <br />
                      <el-tag v-show="activity.processStatus == 5011803" type="success">
                        {{
                          dict.type.step_status.find(
                            (ele) => ele.value == activity.processStatus
                          ).label
                        }}
                      </el-tag>
                      <el-tag v-show="activity.processStatus == 5011802" type="warning">
                        {{
                          dict.type.step_status.find(
                            (ele) => ele.value == activity.processStatus
                          ).label
                        }}
                      </el-tag>
                      <el-tag v-show="activity.processStatus == 5011801" type="info">
                        {{
                          dict.type.step_status.find(
                            (ele) => ele.value == activity.processStatus
                          ).label
                        }}
                      </el-tag>
                    </el-timeline-item>
                  </el-timeline>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="临时任务" name="second">
          <el-button type="primary" @click="sendTask">增派临时任务</el-button>
          <el-table :data="tasksData" border style="width: 100%; margin: 20px 0">
            <el-table-column prop="createTime" label="创建时间" align="center" />
            <el-table-column prop="updateTime" label="更新时间" align="center" />
            <el-table-column prop="taskType" label="任务类型" align="center">
              <template slot-scope="scope">
                <div>
                  {{
                    dict.type.task_type.find((ele) => ele.value == scope.row.taskType)
                      ? dict.type.task_type.find((ele) => ele.value == scope.row.taskType)
                          .label
                      : ""
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="leader" label="负责人" align="center" />
            <el-table-column prop="taskRemark" label="任务备注" align="center" />
            <el-table-column prop="finishTime" label="期望完成时间" align="center" />
            <el-table-column prop="taskStatus" label="任务状态" align="center">
              <template slot-scope="scope">
                <div>
                  {{
                    dict.type.task_status.find((ele) => ele.value == scope.row.taskStatus)
                      ? dict.type.task_status.find(
                          (ele) => ele.value == scope.row.taskStatus
                        ).label
                      : ""
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleTaskDetail(scope.row)"
                  >详情</el-button
                >
                <el-button
                  v-show="scope.row.taskStatus == 5012601"
                  type="text"
                  size="small"
                  @click="handleTaskUpdate(scope.row, '5012602')"
                  >完成</el-button
                >
                <el-button
                  v-show="scope.row.taskStatus == 5012601"
                  type="text"
                  size="small"
                  @click="handleTaskUpdate(scope.row, '5012603')"
                  >取消</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-show="!eventDetail.isEndRescue"
          type="primary"
          
          @click="endRescue"
          >结束救援</el-button
        >
        <el-button @click="commandDialog = false">返 回</el-button>
      </div>
    </el-dialog>
    <!-- 启动预案对话框 -->
    <el-dialog
      width="720px"
      append-to-body
      :visible.sync="launchPlanDialog"
      :show-close="false"
      :close-on-click-modal="false"
      title="启动预案"
    >
      <el-form :inline="true" :model="planForm">
        <el-form-item label="预案名称">
          <el-input
            v-model="planForm.planName"
            placeholder="请输入预案名称"
            style="width: 10vw"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="预案类型">
          <el-select
            v-model="planForm.planType"
            placeholder="请选择预案类型"
            style="width: 10vw"
          >
            <el-option
              v-for="item in dict.type.plan_deduction"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" @click="planSearch">搜索</el-button>
          <el-button size="mini" @click="planReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div v-loading="planLoading" style="display: flex; flex-wrap: wrap">
        <el-radio
          v-for="(item, index) in planList"
          :key="index"
          v-model="planRadio"
          :label="item.id"
          style="width: 320px; margin: 0 0 10px 10px"
          border
          @input="planInput"
          >
          <template>
            <el-tooltip :content="item.planName+'-'+item.planDescription" placement="top" >
                <span class="tooltip">{{ item.planName }}-{{ item.planDescription }}</span>
            </el-tooltip>
        </template>
          
          </el-radio
        >
      </div>
      <el-form
        ref="levelFrom"
        :model="levelFrom"
        :rules="levelRules"
        :inline="true"
        style="margin-top: 20px"
      >
        <el-form-item label="响应等级" prop="level">
          <el-select v-model="levelFrom.level" placeholder="请选择响应等级">
            <el-option
              v-for="(item, index) in levelBox"
              :key="index"
              :label="item.responseName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 认</el-button>
        <el-button @click="launchPlanDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 指派任务对话框 -->
    <el-dialog title="指派任务" :visible.sync="assignDialog" width="560px" append-to-body>
      <el-form
        ref="assignFrom"
        :model="assignFrom"
        :rules="assignRules"
        label-width="120px"
      >
        <el-form-item label="任务描述" prop="taskDescription">
          <el-input v-model="assignFrom.taskDescription" type="textarea" maxlength="200"/>
        </el-form-item>
        <el-form-item label="期望完成时间" prop="finishTime">
          <el-date-picker
            v-model="assignFrom.finishTime"
            type="datetime"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="是否短信通知" prop="isSmsNotice">
          <el-radio-group v-model="assignFrom.isSmsNotice">
            <el-radio label="true">是</el-radio>
            <el-radio label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="assignConfirm">确 定</el-button>
        <el-button @click="assignDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <!-- 增派临时任务对话框 -->
    <el-dialog :title="sendTitle" :visible.sync="sendDialog" width="960px" append-to-body>
      <el-radio-group v-model="sendFlag" style="margin-bottom: 20px">
        <el-radio-button label="rank">队伍调度</el-radio-button>
        <el-radio-button label="good">物资调度</el-radio-button>
      </el-radio-group>
      <el-form
        v-if="sendFlag == 'rank'"
        ref="sendrankFrom"
        :model="sendrankFrom"
        :rules="sendrankRules"
        label-width="120px"
      >
        <el-form-item key="rankId" label="指派队伍" prop="contingentId">
          <el-select
            v-model="sendrankFrom.contingentId"
            placeholder="请选择指派队伍"
            :disabled="rankDisabled"
          >
            <el-option
              v-for="(item, index) in ranksOptions"
              :key="index"
              :label="item.contingentName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item key="rankRemark" label="任务备注" prop="taskRemark">
          <el-input
            v-model="sendrankFrom.taskRemark"
            type="textarea"
            style="width: 60%"
            maxlength="200"
            :disabled="rankDisabled"
          />
        </el-form-item>
        <el-form-item key="rankFinish" label="期望完成时间" prop="finishTime">
          <el-date-picker
            v-model="sendrankFrom.finishTime"
            type="datetime"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            :disabled="rankDisabled"
          />
        </el-form-item>
        <el-form-item key="rankisIn" label="是否短信通知" prop="isInform">
          <el-radio-group v-model="sendrankFrom.isInform" :disabled="rankDisabled">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-form
        v-if="sendFlag == 'good'"
        ref="sendgoodFrom"
        :model="sendgoodFrom"
        :rules="sendgoodRules"
        label-width="120px"
      >
        <el-form-item key="goodDepot" label="调度仓库" prop="depotId">
          <el-select
            v-model="sendgoodFrom.depotId"
            placeholder="请选择调度仓库"
            :disabled="goodDisabled"
            @change="depotChange"
          >
            <el-option
              v-for="(item, index) in depotOptions"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="调度物资">
          <el-table :data="goodsData" border style="width: 100%">
            <el-table-column
              align="center"
              prop="materialName"
              label="物资名称"
              show-overflow-tooltip
            />
            <el-table-column prop="materialType" align="center" label="物资类型">
              <template slot-scope="scope">
                <el-tag>{{
                  dict.type.materiel_type.find(
                    (ele) => ele.value == scope.row.materialType
                  ).label
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!this.goodDisabled"
              prop="inventory"
              align="center"
              label="库存量"
            />
            <el-table-column prop="dispatchQuantity" align="center" label="调度量">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.dispatchQuantity"
                  @input="checkNum(scope.row)"
                  type="number"
                  maxlength="20"
                  :disabled="goodDisabled"
                />
                <!-- @change="(value) => numberLimit(value, scope.row)" -->
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item key="goodRemark" label="任务备注" prop="taskRemark">
          <el-input
            v-model="sendgoodFrom.taskRemark"
            maxlength="200"
            type="textarea"
            :disabled="goodDisabled"
          />
        </el-form-item>
        <el-form-item key="goodFinish" label="期望完成时间" prop="finishTime">
          <el-date-picker
            v-model="sendgoodFrom.finishTime"
            type="datetime"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            :disabled="goodDisabled"
          />
        </el-form-item>
        <el-form-item key="goodisIn" label="是否短信通知" prop="isInform">
          <el-radio-group v-model="sendgoodFrom.isInform" :disabled="goodDisabled">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span v-show="!goodDisabled" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendConfirm">确 定</el-button>
        <el-button @click="sendDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="次生分析" :visible.sync="secondDialog" width="960px" append-to-body>
      <el-button
        style="margin-bottom: 20px"
        type="primary"
        plain
        size="mini"
        @click="secondDataAdd"
        >新增次生分析</el-button
      >
      <el-table v-loading="loading" :data="secondData" border style="width: 100%">
        <el-table-column prop="createTime" align="center" label="上传时间" />
        <el-table-column prop="name" align="center" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleSecondDetail(scope.row)"
              >查看</el-button
            >
            <el-button type="text" size="small" @click="handleSecondDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="secondQueryParams.current"
        :limit.sync="secondQueryParams.size"
        @pagination="getSecondData"
      />
    </el-dialog>
    <el-dialog
      :title="secondaryAnalysisTitle"
      :visible.sync="analysisDialog"
      width="560px"
      append-to-body
    >
      <el-form
        ref="analysisForm"
        :model="analysisForm"
        :rules="analysisRules"
        label-width="100px"
      >
        <el-form-item label="次生灾害" prop="secondaryDisaster">
          <el-input
            v-model="analysisForm.secondaryDisaster"
            placeholder="请输入次生灾害"
            maxlength="20"
            style="width: 245px"
            :disabled="analysisDisabled"
          />
        </el-form-item>
        <el-form-item label="通知人员" prop="noticePersonId">
          <el-select
            v-model="analysisForm.noticePersonId"
            placeholder="请选择通知人员"
            style="width: 245px"
            :disabled="analysisDisabled"
          >
            <el-option label="人员一" value="shanghai" />
            <el-option label="人员二" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="要素分析" prop="elementAnalysis">
          <el-input
            v-model="analysisForm.elementAnalysis"
            type="textarea"
            maxlength="200"
            style="width: 245px"
            :disabled="analysisDisabled"
          />
        </el-form-item>
        <el-form-item label="承载体情况" prop="bearingSituation">
          <el-input
            v-model="analysisForm.bearingSituation"
            type="textarea"
            maxlength="200"
            style="width: 245px"
            :disabled="analysisDisabled"
          />
        </el-form-item>
        <el-form-item label="应急措施" prop="emergencyMeasures">
          <el-input
            v-model="analysisForm.emergencyMeasures"
            type="textarea"
            style="width: 245px"
            maxlength="200"
            :disabled="analysisDisabled"
          />
        </el-form-item>
      </el-form>
      <span v-show="!analysisDisabled" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="analysisSubmit">确 定</el-button>
        <el-button @click="analysisDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="执行记录" :visible.sync="recordDialog" width="720px" append-to-body>
      <div class="block">
        <el-timeline>
          <el-timeline-item timestamp="事件接报" placement="top">
            <el-card>
              <div>
                上报时间：{{ eventReport.reportTime ? eventReport.reportTime : "" }}
              </div>
              <div>
                上报人：{{ eventReport.submitPerson }}
                {{ eventReport.contactNumber }}
              </div>
              <div>审核时间：{{ eventReport.auditTime }}</div>
              <div>审核人：{{ eventReport.auditPerson }}</div>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="预案启动" placement="top">
            <el-card>
              <div
                v-for="(item, index) in executionRecord.planStarts"
                :key="index"
                style="margin: 10px 0"
              >
                <div>预案名称：{{ item.planName }}</div>
                <div>开始时间：{{ item.startTime }}</div>
                <div>处理人员：{{ item.handler }}</div>
              </div>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="持续调度" placement="top">
            <el-card>
              <div
                v-for="(item, index) in executionRecord.continuousDispatch"
                :key="index"
                style="margin: 10px 0"
              >
              <div
                  :style="item.assignTaskType == 5012901 ? 'color:red' : 'color:green'"
                >
                  {{
                    dict.type.assign_task_type.find(
                      (ele) => ele.value == item.assignTaskType
                    )
                      ? dict.type.assign_task_type.find(
                          (ele) => ele.value == item.assignTaskType
                        ).label
                      : ""
                  }}
                </div>
                <div>任务开始时间：{{ item.startTime ? item.startTime : "" }}</div>

                <div>指派队伍：{{ item.contingentName }}</div>
                <div >任务{{item.taskStatus=='5012603'?'取消':'结束'}}时间：{{item.taskStatus=='5012601'?'':item.updateTime}}</div>
              </div>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="结束救援" placement="top">
            <el-card>
              <div >
                结束时间：{{
                  executionRecord.endRescue ? executionRecord.endRescue.endTime : ""
                }}
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
    <!-- 信息发布对话框 -->
    <el-dialog
      title="信息发布"
      :visible.sync="informationDialog"
      width="560px"
      append-to-body
    >
      <div style="margin-bottom: 20px">
        <el-button type="primary" :plain="plain1" @click="programClick(1)"
          >信息发布节目</el-button
        >
        <el-button type="primary" :plain="plain2" @click="programClick(2)"
          >广播节目</el-button
        >
      </div>
      <el-form
        ref="informationForm"
        :model="informationForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item v-if="plain2" label="应急发布名称" prop="name">
          <el-input
            v-model="informationForm.name"
            maxlength="20"
            placeholder="请输入应急发布名称"
            :disabled="disabled"
          />
        </el-form-item>
        <el-form-item v-if="plain1" label="应急广播名称" prop="emergencyName">
          <el-input
            v-model="informationForm.emergencyName"
            maxlength="20"
            placeholder="请输入应急发布名称"
            :disabled="disabled"
          />
        </el-form-item>
        <el-form-item label="发布素材" prop="materialId" v-if="plain2">
          <div style="display: flex">
            <el-select
              v-model="informationForm.materialId"
              placeholder="发布素材"
              clearable
              style="width: 100%; margin-right: 10px"
              :disabled="disabled"
              @change="getUrl"
            >
              <el-option
                v-for="dict in materialData"
                :key="dict.id"
                :label="dict.materialName"
                :value="dict.id"
              />
            </el-select>
            <el-button type="primary" @click="upload" size="mini" :disabled="disabled"
              >上 传</el-button
            >
          </div>
        </el-form-item>
        <el-row v-if="plain1">
          <el-col :span="18" >
            <el-form-item v-if="!textFlag" label="发布素材" prop="audioMaterialId" >
              <div style="display: flex">
                <el-select
                  v-model="informationForm.audioMaterialId"
                  placeholder="发布素材"
                  clearable
                  style="width: 100%; margin-right: 10px"
                  :disabled="disabled"
                  @change="getUrl"
                >
                  <el-option
                    v-for="dict in materialData"
                    :key="dict.id"
                    :label="dict.materialName"
                    :value="dict.id"
                  />
                </el-select>
                <el-button type="primary" @click="upload" size="mini" :disabled="disabled"
                  >上 传</el-button
                >
              </div>
            </el-form-item>
          </el-form-item>
        <el-form-item v-if="textFlag" label="输入文字素材" prop="textMaterial">
          <el-input
            v-model="informationForm.textMaterial"
            type="textarea"
            maxlength="200"
            placeholder="输入文字素材"
            :disabled="disabled"
          />
        </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align:center;margin-top:3px;">
            <el-button  type="primary" @click="textConten" size="mini" :disabled="disabled"
              >{{textFlag?'发布素材':'文字内容'}}</el-button
            ></el-col
          >
        </el-row>
        <el-form-item v-if="plain2" label="发布终端" prop="terminalIdArr">
          <el-select
            v-model="informationForm.terminalIdArr"
            placeholder="发布终端"
            clearable
            multiple
            :disabled="disabled"
            style="width: 100%"
          >
            <el-option
              v-for="dict in terminalList"
              :key="dict.terminalId"
              :label="dict.terminalName"
              :value="dict.terminalId"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="plain1" label="发布终端" prop="terminalIdArr1">
          <el-select
            v-model="informationForm.terminalIdArr1"
            placeholder="发布终端"
            clearable
            multiple
            :disabled="disabled"
            style="width: 100%"
          >
            <el-option
              v-for="dict in terminalList1"
              :key="dict.terminalId"
              :label="dict.terminalName"
              :value="dict.terminalId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发布时间" prop="dange">
          <el-date-picker
            v-model="informationForm.dange"
            style="width: 100%"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled="disabled"
          />
        </el-form-item>
        <el-form-item v-if="plain2" label="应急发布原因" prop="emergencyReason">
          <el-input
            v-model="informationForm.emergencyReason"
            type="textarea"
            maxlength="200"
            placeholder="应急发布原因"
            :disabled="disabled"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-show="!disabled" type="primary" @click="submitForm('6010703')"
          >创 建</el-button
        >
        <!-- <el-button v-show="!disabled" type="primary" @click="submitForm('6010701')">暂 存</el-button> -->
        <el-button v-show="!disabled" @click="informationCancel">取 消</el-button>
      </div>
      <!-- 素材 -->
      <el-dialog
        title="新增素材"
        :visible.sync="openmaterial"
        width="560px"
        append-to-body
      >
        <el-form
          ref="formmaterial"
          :model="formmaterial"
          :rules="rulesmaterial"
          label-width="100px"
        >
          <el-form-item label="素材名称" prop="materialName">
            <el-input v-model="formmaterial.materialName" placeholder="请输入素材名称"  maxlength="20"/>
          </el-form-item>
          <el-form-item label="素材类型" prop="materialType">
            <el-select
              v-model="formmaterial.materialType"
              placeholder="素材类型"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.information_material_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="素材上传" prop="url">
            <el-upload
              ref="my-upload"
              :action="uploadFile"
              :on-remove="handleRemove"
              :on-success="handlePreview"
              :file-list="fileList"
              :limit="1"
              :before-upload="beforeUpload"
              :disabled="!formmaterial.materialType"
            >
              <el-button size="mini" type="primary" icon="el-icon-upload2"
                >选择素材(*只能上传一份)</el-button
              >
            </el-upload>
            <span v-show="formmaterial.materialType == '6010101'" class="limit">
              图片(支持:jpeg/jpg、png、gif、bmp、tiff、webp、svg
            </span>
            <span v-show="formmaterial.materialType == '6010102'" class="limit">
              视频(支持:mp4、avi、mkv、mov、wmv、flv、webm、mpeg
            </span>
            <span v-show="formmaterial.materialType == '6010103'" class="limit"
              >音频(支持:mp3、wav、aac、flac、ogg、wma</span
            >
            <span v-show="formmaterial.materialType == '6010104'" class="limit"
              >文档(支持:pdf、word、excel、ppt、txt</span
            >
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFormmaterial">确 定</el-button>
          <el-button @click="cancelmaterial">取 消</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  getTree,
  overviewList,
  reportDetail,
  planList,
  levelList,
  planSave,
  showPlan,
  showProcess,
  showProcessRight,
  stepStatus,
  contingentUpdate,
  contingentList,
  depotList,
  taskSave,
  listOfDepot,
  temporaryTasklist,
  taskDetail,
  taskUpdate,
  analysisPage,
  analysisSave,
  analysisDelete,
  analysisDetail,
  recordSave,
  recordDetail,
  materialList,
  savematerial,
  InformationList,
  broadcastList,
  save,
  handledownload,
} from "@/api/emergency/emergencyCommand/commandDispatch/index";
import Map from "../../../map/index1.vue";
export default {
  name: "EmergencySupplies",
  dicts: [
    "plan_deduction",
    "response_level",
    "step_status",
    "contingent_status",
    "materiel_type",
    "task_type",
    "task_status",
    "assign_task_type",
    "event_level",
    "information_material_type",
    "emergency_dispatch_state",
  ],
  components: { Map },
  data() {
    return {
      // 地图点标记图标地址
      url: `${require("@/assets/icons/location.png")}`,
      rankUrl: `${require("@/assets/icons/name.png")}`,
      goodUrl: `${require("@/assets/icons/warehouse.png")}`,
      // 事件类型树
      treeData: [],
      defaultProps: {
        children: "children",
        label: "nodeName",
      },
      // 遮罩层d
      loading: false,
      planLoading: false,
      // 显示搜索条件
      showSearch: true,
      resourcefulSearch: false,
      // 事件列表数据
      eventList: [],
      text: undefined,
      dateRange: [],
      // 事故报告弹框
      reportDialog: false,
      reportDetail: {},
      // 指挥调度弹框
      commandDialog: false,
      activeName: "first",
      // 启动预案弹框
      launchPlanDialog: false,
      planForm: {
        planName: undefined,
        planType: undefined,
      },
      planList: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 1000,
        eventNo: undefined,
        eventName: undefined,
        eventTypeName: undefined,
        eventTypeId: undefined,
        eventLevel: undefined,
        startTime: "",
        endTime: "",
        dispatchStateList: [],
      },
      // 事件列表弹框
      eventListDialog: true,
      // 事件详情
      eventDetaildialog: false,
      eventDetail: {},
      // 搜索弹框
      range: "200m",
      checkList: [],
      dispatch: "rank",
      ranksList: [],
      goodsList: [],
      // 启动预案
      planRadio: "",
      levelFrom: {
        level: undefined,
      },
      levelBox: [],
      levelRules: {
        level: [{ required: true, message: "请选择响应等级", trigger: "blur" }],
      },
      // 指派弹框
      assignDialog: false,
      assignFrom: {
        taskDescription: undefined,
        finishTime: undefined,
        isSmsNotice: undefined,
      },
      assignRules: {
        taskDescription: [
          { required: true, message: "请输入任务描述", trigger: "blur" },
        ],
        time: [{ required: true, message: "请选择完成时间", trigger: "blur" }],
        isSmsNotice: [
          { required: true, message: "请选择是否短信通知", trigger: "blur" },
        ],
      },
      // 预案列表
      planBox: [],
      // 步骤列表
      planRow: undefined,
      stepList: [],
      // 时间线数据
      activities: [],
      // 增派任务数据
      tasksData: [],
      // 增派临时任务
      sendDialog: false,
      rankDisabled: false,
      goodDisabled: false,
      sendTitle: "",
      sendFlag: "rank",
      ranksOptions: [],
      sendrankFrom: {
        contingentId: undefined,
        taskRemark: undefined,
        finishTime: undefined,
        isInform: undefined,
      },
      sendrankRules: {
        contingentId: [
          { required: true, message: "请选择指派队伍", trigger: "change" },
        ],
        taskRemark: [
          { required: true, message: "请输入任务备注", trigger: "blur" },
        ],
        finishTime: [
          { required: true, message: "请选择期望时间", trigger: "change" },
        ],
        isInform: [
          { required: true, message: "请选择是否通知", trigger: "change" },
        ],
      },
      depotOptions: [],
      sendgoodFrom: {
        depotId: undefined,
        taskRemark: undefined,
        finishTime: undefined,
        isInform: undefined,
      },
      goodsData: [],
      sendgoodRules: {
        depotId: [
          { required: true, message: "请选择指派队伍", trigger: "change" },
        ],
        taskRemark: [
          { required: true, message: "请输入任务备注", trigger: "blur" },
        ],
        finishTime: [
          { required: true, message: "请选择期望时间", trigger: "change" },
        ],
        isInform: [
          { required: true, message: "请选择是否通知", trigger: "change" },
        ],
      },
      // 次生分析弹框
      secondDialog: false,
      analysisDialog: false,
      secondaryAnalysisTitle: undefined,
      secondData: [], // 次生分析列表数据
      secondQueryParams: {
        current: 1,
        size: 10,
      },
      total: 0,
      analysisDisabled: false,
      analysisForm: {
        secondaryDisaster: undefined,
        noticePersonId: [],
        elementAnalysis: undefined,
        bearingSituation: undefined,
        emergencyMeasures: undefined,
      }, // 新增次生分析表单
      analysisRules: {},
      // 执行记录弹框
      recordDialog: false,
      executionRecord: {},
      eventReport: {},
      // 信息发布相关
      informationDialog: false,
      informationForm: {},
      formmaterial: {},
      rulesmaterial: {
        materialName: [
          { required: true, message: "素材名称不能为空", trigger: "blur" },
        ],
        materialType: [
          { required: true, message: "素材类型不能为空", trigger: "blur" },
        ],
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "请输入应急发布名称", trigger: "blur" },
        ],
        emergencyName: [
          { required: true, message: "请输入应急发布名称", trigger: "blur" },
        ],
        materialId: [
          { required: true, message: "发布素材不能为空", trigger: "change" },
        ],
        audioMaterialId: [
          { required: true, message: "发布素材不能为空", trigger: "change" },
        ],
        terminalIdArr: [
          { required: true, message: "发布终端不能为空", trigger: "blur" },
        ],
        terminalIdArr1: [
          { required: true, message: "发布终端不能为空", trigger: "blur" },
        ],
        dange: [
          { required: true, message: "发布时间不能为空", trigger: "change" },
        ],
        emergencyReason: [
          { required: true, message: "应急发布原因不能为空", trigger: "blur" },
        ],
        textMaterial: [
          { required: true, message: "文字素材不能为空", trigger: "blur" },
        ],
      },
      plain1: false,
      plain2: true,
      textFlag: false,
      fileList: [],
      uploadFile: process.env.VUE_APP_BASE_API + "/file/informationUploadFile",
      terminalList: [],
      terminalList1: [],
      openmaterial: false,
      disabled: false,
      materialData: [],
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  mounted() {
    console.log(this.dict.type.event_level, "ssssssssssssssss");
    this.getTree();
    this.$refs.mapRef.initMap();
    overviewList({ resourceType: 5013001 }).then((res) => {
      this.goodsList = res.data.list;
    });
    overviewList({ resourceType: 5013002 }).then((res) => {
      this.ranksList = res.data.list;
    });
  },
  methods: {
    /** 右侧悬框专用模块 */
    // 范围框选
    rangeChange(value) {
      this.$refs.mapRef.circleRange(
        this.eventDetail.longitude,
        this.eventDetail.latitude,
        value
      );
      this.$refs.mapRef.clearAllList();
      this.checkList = [];
      this.ranksList.forEach((element) => {
        if (element.distance < value) {
          element.show = true;
        } else {
          element.show = false;
        }
      });
      this.goodsList.forEach((element) => {
        if (element.distance < value) {
          element.show = true;
        } else {
          element.show = false;
        }
      });
    //   overviewList({ resourceType: 5013003 }).then((res) => {
    //     res.data.list.forEach((element) => {
    //       element.distance = this.$refs.mapRef.getDistance(
    //         element.longitude,
    //         element.latitude,
    //         this.eventDetail.longitude,
    //         this.eventDetail.latitude
    //       );
    //       if (element.distance < value) {
    //         this.$refs.mapRef.markerList(
    //           element.longitude,
    //           element.latitude,
    //           "shelter",
    //           element
    //         );
    //       }
    //     });
    //   });
    //   overviewList({ resourceType: 5013004 }).then((res) => {
    //     res.data.list.forEach((element) => {
    //       element.distance = this.$refs.mapRef.getDistance(
    //         element.longitude,
    //         element.latitude,
    //         this.eventDetail.longitude,
    //         this.eventDetail.latitude
    //       );
    //       if (element.distance < value) {
    //         this.$refs.mapRef.markerList(
    //           element.longitude,
    //           element.latitude,
    //           "monitor",
    //           element
    //         );
    //       }
    //     });
    //   });
    //   overviewList({ resourceType: 5013005 }).then((res) => {
    //     res.data.list.forEach((element) => {
    //       element.distance = this.$refs.mapRef.getDistance(
    //         element.longitude,
    //         element.latitude,
    //         this.eventDetail.longitude,
    //         this.eventDetail.latitude
    //       );
    //       if (element.distance < value) {
    //         this.$refs.mapRef.markerList(
    //           element.longitude,
    //           element.latitude,
    //           "broadcast",
    //           element
    //         );
    //       }
    //     });
    //   });
    //   overviewList({ resourceType: 5013006 }).then((res) => {
    //     res.data.list.forEach((element) => {
    //       element.distance = this.$refs.mapRef.getDistance(
    //         element.longitude,
    //         element.latitude,
    //         this.eventDetail.longitude,
    //         this.eventDetail.latitude
    //       );
    //       if (element.distance < value) {
    //         this.$refs.mapRef.markerList(
    //           element.longitude,
    //           element.latitude,
    //           "risk",
    //           element
    //         );
    //       }
    //     });
    //   });
    //   overviewList({ resourceType: 5013007 }).then((res) => {
    //     res.data.list.forEach((element) => {
    //       element.distance = this.$refs.mapRef.getDistance(
    //         element.longitude,
    //         element.latitude,
    //         this.eventDetail.longitude,
    //         this.eventDetail.latitude
    //       );
    //       if (element.distance < value) {
    //         this.$refs.mapRef.markerList(
    //           element.longitude,
    //           element.latitude,
    //           "communicate",
    //           element
    //         );
    //       }
    //     });
    //   });
    //   overviewList({ resourceType: 5013008 }).then((res) => {
    //     res.data.list.forEach((element) => {
    //       element.distance = this.$refs.mapRef.getDistance(
    //         element.longitude,
    //         element.latitude,
    //         this.eventDetail.longitude,
    //         this.eventDetail.latitude
    //       );
    //       if (element.distance < value) {
    //         this.$refs.mapRef.markerList(
    //           element.longitude,
    //           element.latitude,
    //           "protection",
    //           element
    //         );
    //       }
    //     });
    //   });
    //   overviewList({ resourceType: 5013009 }).then((res) => {
    //     res.data.list.forEach((element) => {
    //       element.distance = this.$refs.mapRef.getDistance(
    //         element.longitude,
    //         element.latitude,
    //         this.eventDetail.longitude,
    //         this.eventDetail.latitude
    //       );
    //       if (element.distance < value) {
    //         this.$refs.mapRef.markerList(
    //           element.longitude,
    //           element.latitude,
    //           "medical",
    //           element
    //         );
    //       }
    //     });
    //   });

          this.$forceUpdate();
      },
    overview(value){
      let array=['shelter','monitor','broadcast','risk','communicate','protection','medical']
      for (let index = 0; index <7; index++) {
        overviewList({ resourceType: 5013003+index }).then((res) => {
        res.data.list.forEach((element) => {
          element.distance = this.$refs.mapRef.getDistance(
            element.longitude,
            element.latitude,
            this.eventDetail.longitude,
            this.eventDetail.latitude
          );
          if (value&&element.distance < value) {
            this.$refs.mapRef.markerList(
              element.longitude,
              element.latitude,
              array[index],
              element
            );
          }
        });
      });
      }
    },

    checkNum(res) {
      let z_reg = /^\+?[1-9][0-9]*$/;
      if (!z_reg.test(res.dispatchQuantity)) {
        res.dispatchQuantity = "";
      }
      console.log(res);
    },
    // 队伍定位
    rankPosition(item) {
      this.$refs.mapRef.marker(
        item.longitude,
        item.latitude,
        this.rankUrl,
        false,
        false,
        item
      );
    },
    // 仓库定位
    goodPosition(item) {
      this.$refs.mapRef.marker(
        item.longitude,
        item.latitude,
        this.goodUrl,
        false,
        false,
        item
      );
    },
    // 队伍调度
    rankDispatch(item) {
      // console.log(item);
      this.sendTask();
      this.sendrankFrom.contingentId = item.id.toString();
    },
    // 队伍调度
    goodDispatch(item) {
      // console.log(item);
      this.sendTask();
      this.sendFlag = "good";
      this.sendgoodFrom.depotId = item.id.toString();
      this.depotChange(item.id);
    },
    checkListChange(value) {
      if (value.indexOf("5013003") != -1) {
        this.$refs.mapRef.markerListAdd("shelter");
      } else {
        this.$refs.mapRef.markerListRemove("shelter");
      }
      if (value.indexOf("5013005") != -1) {
        this.$refs.mapRef.markerListAdd("broadcast");
      } else {
        this.$refs.mapRef.markerListRemove("broadcast");
      }
      if (value.indexOf("5013004") != -1) {
        this.$refs.mapRef.markerListAdd("monitor");
      } else {
        this.$refs.mapRef.markerListRemove("monitor");
      }
      if (value.indexOf("5013008") != -1) {
        this.$refs.mapRef.markerListAdd("protection");
      } else {
        this.$refs.mapRef.markerListRemove("protection");
      }
      if (value.indexOf("5013009") != -1) {
        this.$refs.mapRef.markerListAdd("medical");
      } else {
        this.$refs.mapRef.markerListRemove("medical");
      }
      if (value.indexOf("5013006") != -1) {
        this.$refs.mapRef.markerListAdd("risk");
      } else {
        this.$refs.mapRef.markerListRemove("risk");
      }
      if (value.indexOf("5013007") != -1) {
        this.$refs.mapRef.markerListAdd("communicate");
      } else {
        this.$refs.mapRef.markerListRemove("communicate");
      }
    },
    /** 查询场所列表 */
    getList() {
      this.loading = true;
      page({
        ...this.queryParams,
        dispatchStateList: this.queryParams.dispatchStateList.join(","),
      }).then((response) => {
        console.log(response, "response");
        if (response.data != null) {
          this.eventList = response.data;
        }
        this.loading = false;
      });
    },
    // 获取事件类型树结构
    getTree() {
      getTree().then((res) => {
        console.log(res, "测试杀杀杀");
        if (res.code == 200) {
          this.recursion(res.data);
          this.treeData = res.data;
        }
      });
    },
    handleNodeClick(data, res, item) {
      // console.log(data, res, item, "树结构");
      if (!res) {
        this.queryParams.eventTypeId = undefined;
        this.queryParams.eventTypeName = undefined;
      } else {
        this.$refs.Addtree.setCheckedNodes([data]);
        this.queryParams.eventTypeId = data.id;
        this.queryParams.eventTypeName = data.nodeName;
      }
    },
    // 解决因为切换叶子节点清空值导致不能选择的问题
    eventLabelChange(res) {
      this.$forceUpdate();
    },
    // 通过递归给叶子节点添加只读属性
    recursion(data) {
      data.forEach((item, index) => {
        if (item.children) {
          item.disabled = true;
          return this.recursion(item.children);
        } else {
          item.disabled = false;
        }
      });
    },
    // 事件列表点击事件
    eventClick(item) {
      this.range = "";
      this.checkList = [];
      this.$refs.mapRef.clearCircle();
      this.stepList = []; //切换事件清空预案信息
      this.activities = [];
      // this.eventDetail.isEndRescue=true
      this.eventDetail = item;
      console.log(this.eventDetail.isEndRescue, "ssssss");
      this.$refs.mapRef.marker(
        item.longitude,
        item.latitude,
        this.url,
        true,
        true
      );
      this.ranksList.forEach((element) => {
        element.show = true;
        element.distance = this.$refs.mapRef.getDistance(
          element.longitude,
          element.latitude,
          item.longitude,
          item.latitude
        );
      });
      this.goodsList.forEach((element) => {
        element.show = true;
        element.distance = this.$refs.mapRef.getDistance(
          element.longitude,
          element.latitude,
          item.longitude,
          item.latitude
        );
      });
      overviewList({ resourceType: 5013003 }).then((res) => {
        res.data.list.forEach((element) => {
          this.$refs.mapRef.markerList(element.longitude, element.latitude, "shelter",element);
        });
      });
      overviewList({ resourceType: 5013004 }).then((res) => {
        res.data.list.forEach((element) => {
          this.$refs.mapRef.markerList(element.longitude, element.latitude, "monitor",element);
        });
      });
      overviewList({ resourceType: 5013005 }).then((res) => {
        res.data.list.forEach((element) => {
          this.$refs.mapRef.markerList(element.longitude, element.latitude, "broadcast",element);
        });
      });
      overviewList({ resourceType: 5013006 }).then((res) => {
        res.data.list.forEach((element) => {
          this.$refs.mapRef.markerList(element.longitude, element.latitude, "risk",element);
        });
      });
      overviewList({ resourceType: 5013007 }).then((res) => {
        res.data.list.forEach((element) => {
          this.$refs.mapRef.markerList(
            element.longitude,
            element.latitude,
            "communicate",
            element
          );
        });
      });
      overviewList({ resourceType: 5013008 }).then((res) => {
        res.data.list.forEach((element) => {
          this.$refs.mapRef.markerList(element.longitude, element.latitude, "protection",element);
        });
      });
      overviewList({ resourceType: 5013009 }).then((res) => {
        res.data.list.forEach((element) => {
          this.$refs.mapRef.markerList(element.longitude, element.latitude, "medical",element);
        });
      });

      this.resourcefulSearch = true;
    },
    detailDialog(value) {
      this.eventDetaildialog = value;
    },
    handleReport() {
      this.reportDialog = true;
      reportDetail({ id: this.eventDetail.id }).then((res) => {
        console.log(res);
        this.reportDetail = res.data;
      });
    },
    // 指挥调度按钮
    handleCommand() {
      this.commandDialog = true;
      console.log(this.eventDetail.businessTypeId, "ssss");
      showPlan({ businessTypeId: this.eventDetail.businessTypeId }).then(
        (res) => {
          res.data.forEach((item) => {
            item.responseName = this.dict.type.response_level.find(
              (ele) => ele.value == item.responseLevel
            ).label;
          });
          console.log(res.data, "sssssswww");
          this.planBox = res.data;
        }
      );
      temporaryTasklist({
        businessTypeId: this.eventDetail.businessTypeId,
      }).then((res) => {
        res.data.forEach((item) => {
          if (item.depotVo) {
            item = Object.assign(item, item.depotVo);
          } else if (item.contingentVo) {
            item = Object.assign(item, item.contingentVo);
          }
        });
        console.log(res.data);
        this.tasksData = res.data;
      });
    },
    // 点击左侧预案获取步骤
    planClick(row) {
      this.planRow = row;
      console.log(this.eventDetail.businessTypeId);
      showProcess({
        responseLevelId: row.responseLevelId,
        businessTypeId: this.eventDetail.businessTypeId,
      }).then((res) => {
        // console.log(res.data);
        this.stepList = res.data;
      });
      showProcessRight({
        responseLevelId: row.responseLevelId,
        businessTypeId: this.eventDetail.businessTypeId,
      }).then((res) => {
        console.log(res.data);
        this.activities = res.data;
      });
    },
    commandClick(tab, event) {
      console.log(tab, event);
    },
    // 结束救援
    endRescue() {
      var that = this;
      this.$modal
        .confirm("是否确认结束当前救援")
        .then(function () {
          return recordSave({
            businessTypeId: that.eventDetail.businessTypeId,
          });
        })
        .then((res) => {
          console.log(res);
          if (
            res.msg != "请确认临时任务状态不是未开始" &&
            res.msg != "无法结束救援" &&
            res.msg != "步骤状态不能是进行中"
          ) {
            this.reportDialog = false;
            this.$modal.msgSuccess("救援已成功结束！");
            this.commandDialog = false;
            this.eventDetaildialog = false;
            this.$refs.mapRef.clearMap();
            this.getList();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((error) => {});
    },
    // 返回按钮
    cancel() {
      this.reportDialog = false;
    },
    // 启动预案
    launchPlan() {
      this.planList = [];
      this.planRadio = undefined;
      this.levelFrom = {};
      this.launchPlanDialog = true;
      planList({ businessTypeId: this.eventDetail.businessTypeId }).then(
        (res) => {
          this.planList = res.data;
        }
      );
    },
    // 搜索预案
    planSearch() {
      this.planLoading = true;
      planList(
        Object.assign(
          { businessTypeId: this.eventDetail.businessTypeId },
          this.planForm
        )
      ).then((res) => {
        this.planList = res.data;
        this.planLoading = false;
      });
    },
    // 重置预案
    planReset() {
      this.planForm.planName = undefined;
      this.planForm.planType = undefined;
      this.planLoading = true;
      planList(this.planForm).then((res) => {
        this.planList = res.data;
        this.planLoading = false;
      });
    },
    // 切换单选预案
    planInput(value) {
      levelList({ planId: value }).then((res) => {
        res.data.forEach((item) => {
          item.responseName = this.dict.type.response_level.find(
            (ele) => ele.value == item.responseLevel
          ).label;
        });
        this.levelBox = res.data;
      });
    },
    // 预案确认选择
    confirm() {
      if (this.planRadio) {
        this.$refs["levelFrom"].validate((valid) => {
          if (valid) {
            const data = {};
            data.responseLevelId = this.levelFrom.level;
            data.businessTypeId = this.eventDetail.businessTypeId;
            planSave(data).then((res) => {
              if (res.code == 200) {
                this.launchPlanDialog = false;
                this.$modal.msgSuccess("预案启动成功！");
                console.log(this.eventDetail.businessTypeId, "sssssss");
                showPlan({
                  businessTypeId: this.eventDetail.businessTypeId,
                }).then((res) => {
                  res.data.forEach((item) => {
                    console.log(item, "planBox");
                    item.responseName = this.dict.type.response_level.find(
                      (ele) => ele.value == item.responseLevel
                    ).label;
                  });
                  this.planBox = res.data;
                });
              }
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        this.$modal.msgWarning("请先选择预案！");
      }
    },
    // 开始处置or结束步骤
    processUpdate(id, status) {
      stepStatus({ id: id, processStatus: status }).then((res) => {
        if (status == 5011802) {
          this.$modal.msgSuccess("当前流程开始处置！");
        } else {
          this.$modal.msgSuccess("当前流程结束步骤！");
        }
        this.planClick(this.planRow);
      });
    },
    // 指派任务
    assign(ele) {
      if (this.$refs.assignFrom) {
        this.$refs.assignFrom.resetFields();
      }
      this.assignFrom.id = ele.id;
      this.assignFrom.contingentId = ele.contingentId;
      this.assignDialog = true;
    },
    // 完成任务
    finish(ele, status) {
      this.$modal
        .confirm("是否确认完成当前指派")
        .then(function () {
          return contingentUpdate({
            id: ele.id,
            contingentId: ele.contingentId,
            contingentStatus: 5011903,
          });
        })
        .then(() => {
          this.planClick(this.planRow);
          this.$modal.msgSuccess("指派已完成!");
        })
        .catch((error) => {});
    },
    // 指派任务提交
    assignConfirm() {
      this.$refs["assignFrom"].validate((valid) => {
        if (valid) {
          this.assignFrom.contingentStatus = "5011902";
          contingentUpdate(this.assignFrom).then((res) => {
            if (res.code == 200) {
              this.assignDialog = false;
              this.$modal.msgSuccess("任务指派成功！");
              this.planClick(this.planRow);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 增派临时任务
    sendTask() {
      this.sendTitle = "增派临时任务";
      this.sendFlag = "rank";
      this.rankDisabled = false;
      this.goodDisabled = false;
      // if (this.$refs.sendrankFrom) {
      //   this.$refs.sendrankFrom.resetFields();
      // }
      // if (this.$refs.sendgoodFrom) {
      //   this.$refs.sendgoodFrom.resetFields();
      // }
      this.sendrankFrom = {
        contingentId: undefined,
        taskRemark: undefined,
        finishTime: undefined,
        isInform: undefined,
      };
      this.sendgoodFrom = {
        depotId: undefined,
        taskRemark: undefined,
        finishTime: undefined,
        isInform: undefined,
      };
      this.goodsData = [];
      this.sendDialog = true;
      contingentList().then((res) => {
        console.log(res, "手机号");
        this.ranksOptions = res.data;
      });
      depotList().then((res) => {
        this.depotOptions = res.data;
      });
    },
    // 仓库变更查询物资
    depotChange(value) {
      listOfDepot({ supplyDepotId: value }).then((res) => {
        this.goodsData = res.data;
        this.goodsData.forEach((item) => {
          this.$set(item, "dispatchQuantity", "");
        });
      });
    },
    // numberLimit(value, row) {
    //   console.log(value);
    //   console.log(row.inventory);
    //   if (parseInt(value) < 0) {
    //     value = 0;
    //   } else if (parseInt(value) > parseInt(row.inventory)) {
    //     value = parseInt(row.inventory);
    //   }
    // },
    // 增派临时任务确认
    sendConfirm() {
      if (this.sendFlag == "rank") {
        this.$refs["sendrankFrom"].validate((valid) => {
          if (valid) {
            this.ranksOptions.forEach((item) => {
              if (this.sendrankFrom.contingentId == item.id) {
                this.sendrankFrom.phone = item.phone;
              }
            });
            taskSave({
              businessTypeId: this.eventDetail.businessTypeId,
              contingentVo: this.sendrankFrom,
              taskType: 5012401,
            }).then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess("队伍指派成功！");
                this.sendDialog = false;
                temporaryTasklist({
                  businessTypeId: this.eventDetail.businessTypeId,
                }).then((res) => {
                  res.data.forEach((item) => {
                    if (item.depotVo) {
                      item = Object.assign(item, item.depotVo);
                    } else if (item.contingentVo) {
                      item = Object.assign(item, item.contingentVo);
                    }
                  });
                  // console.log(res.data);
                  this.tasksData = res.data;
                });
              }
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        this.$refs["sendgoodFrom"].validate((valid) => {
          if (valid) {
            this.sendgoodFrom.materialVos = [];
            let flag = false;
            this.goodsData.forEach((item) => {
              if (item.dispatchQuantity) {
                console.log(item.dispatchQuantity);
                if (item.dispatchQuantity > item.inventory) {
                  flag = true;
                }
                this.sendgoodFrom.materialVos.push({
                  materialId: item.id,
                  dispatchQuantity: item.dispatchQuantity,
                });
              }
            });
            console.log(flag);
            if (flag) {
              this.$message({
                message: "调度量不能大于库存量",
                type: "warning",
              });
              return;
            }
            taskSave({
              businessTypeId: this.eventDetail.businessTypeId,
              depotVo: this.sendgoodFrom,
              taskType: 5012402,
            }).then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess("物资调度成功！");
                this.sendDialog = false;
                temporaryTasklist({
                  businessTypeId: this.eventDetail.businessTypeId,
                }).then((res) => {
                  res.data.forEach((item) => {
                    if (item.depotVo) {
                      item = Object.assign(item, item.depotVo);
                    } else if (item.contingentVo) {
                      item = Object.assign(item, item.contingentVo);
                    }
                  });
                  // console.log(res.data);
                  this.tasksData = res.data;
                });
              }
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    // 临时任务详情
    handleTaskDetail(row) {
      this.sendTitle = "临时任务详情";
      contingentList().then((res) => {
        this.ranksOptions = res.data;
      });
      depotList().then((res) => {
        this.depotOptions = res.data;
      });
      taskDetail({ id: row.id, taskType: row.taskType }).then((res) => {
        // console.log(res.data);
        this.rankDisabled = true;
        this.goodDisabled = true;
        // 队伍
        if (row.taskType == 5012401) {
          this.sendFlag = "rank";
          this.sendDialog = true;
          this.sendrankFrom = res.data.contingentVo;
          this.sendrankFrom.contingentId =
            this.sendrankFrom.contingentId.toString();
        } else {
          this.sendFlag = "good";
          this.sendDialog = true;
          this.sendgoodFrom = res.data.depotVo;
          this.sendgoodFrom.depotId = this.sendgoodFrom.depotId.toString();
          // this.depotChange(this.sendgoodFrom.depotId);
          this.goodsData = res.data.depotVo.materialVos;
        }
      });
    },
    // 临时任务完成or取消
    handleTaskUpdate(row, status) {
      if (status == 5012602) {
        this.$modal
          .confirm("是否确认完成当前任务")
          .then(function () {
            return taskUpdate({ id: row.id, taskStatus: status });
          })
          .then(() => {
            this.$modal.msgSuccess("任务已完成!");
            temporaryTasklist({
              businessTypeId: this.eventDetail.businessTypeId,
            }).then((res) => {
              res.data.forEach((item) => {
                if (item.depotVo) {
                  item = Object.assign(item, item.depotVo);
                } else if (item.contingentVo) {
                  item = Object.assign(item, item.contingentVo);
                }
              });
              this.tasksData = res.data;
            });
          })
          .catch((error) => {});
      } else if (status == 5012603) {
        this.$modal
          .confirm("是否确认取消当前任务")
          .then(function () {
            return taskUpdate({ id: row.id, taskStatus: status });
          })
          .then(() => {
            this.$modal.msgSuccess("任务已取消!");
            temporaryTasklist({
              businessTypeId: this.eventDetail.businessTypeId,
            }).then((res) => {
              res.data.forEach((item) => {
                if (item.depotVo) {
                  item = Object.assign(item, item.depotVo);
                } else if (item.contingentVo) {
                  item = Object.assign(item, item.contingentVo);
                }
              });
              // console.log(res.data);
              this.tasksData = res.data;
            });
          })
          .catch((error) => {});
      }
    },
    // 次生分析按钮点击
    handleSecond() {
      this.secondDialog = true;
      this.getSecondData();
    },
    // 查询次生分析列表
    getSecondData() {
      this.secondQueryParams.businessTypeId = this.eventDetail.businessTypeId;
      this.loading = true;
      analysisPage(this.secondQueryParams).then((response) => {
        // console.log(response.data);
        if (response.data != null) {
          this.secondData = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    // 次生分析详情
    handleSecondDetail(row) {
      analysisDetail({ id: row.id }).then((res) => {
        console.log(res.data);
        this.analysisDialog = true;
        this.analysisDisabled = true;
        this.analysisForm = res.data;
      });
    },
    // 次生分析删除
    handleSecondDelete(row) {
      this.$modal
        .confirm("是否确认删除当前分析")
        .then(function () {
          return analysisDelete({ id: row.id });
        })
        .then(() => {
          this.$modal.msgSuccess("次生分析删除成功!");
          this.getSecondData();
        })
        .catch((error) => {});
    },
    // 新增次生分析弹框
    secondDataAdd() {
      this.secondaryAnalysisTitle = "新增次生分析";
      this.analysisDialog = true;
      this.analysisDisabled = false;
      this.analysisForm = {
        secondaryDisaster: undefined,
        noticePersonId: [],
        elementAnalysis: undefined,
        bearingSituation: undefined,
        emergencyMeasures: undefined,
      };
    },
    // 确认新增分析
    analysisSubmit() {
      this.analysisForm.businessTypeId = this.eventDetail.businessTypeId;
      analysisSave(this.analysisForm).then((res) => {
        if (res.code == 200) {
          this.$modal.msgSuccess("次生分析新增成功!");
          this.analysisDialog = false;
          this.getSecondData();
        }
      });
    },
    // 执行记录按钮
    handleRecord() {
      recordDetail({ businessTypeId: this.eventDetail.businessTypeId }).then(
        (res) => {
          console.log(res.data);
          this.recordDialog = true;
          this.executionRecord = res.data;
          this.eventReport = res.data.eventReport;
        }
      );
    },
    // 附件下载
    handledownload(row) {
      const _this = this;
      this.$modal
        .confirm("是否确认下载附件")
        .then(function () {
          console.log(row, "wwwwwww");
          let arr1 = row.attachmentAddress.split(",");
          arr1.map((res) => {
            let arr = res.split("/");
            handledownload(arr).then(async (res) => {
              _this.handledownloadGet(arr, res);
            });
          });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("下载成功");
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // 信息发布相关方法
    // 信息发布按钮
    handleInformation() {
      this.plain1 = false;
      this.plain2 = true;
      this.informationForm = {};
      this.informationDialog = true;
      this.getpagematerial();
      this.pagesterminal();
      this.$refs.informationForm.resetFields();
    },
    // 素材上传按钮
    upload() {
      this.openmaterial = true;
      // this.resetmaterial();
      this.fileList = [];
    },
    // 信息发布上传按钮
    textConten() {
      this.textFlag = !this.textFlag;
    },
    getUrl() {
      this.materialData.map((res) => {
        if (res.id == this.informationForm.materialId) {
          this.informationForm.url = res.url;
        }
      });
    },
    // 素材信息获取
    getpagematerial() {
      materialList({ isEmergency: 1 }).then((response) => {
        console.log(response);
        this.materialData = response.data;
      });
    },
    // 信息发布终端
    pagesterminal() {
      InformationList().then((response) => {
        console.log(response, "信息发布终端");
        this.terminalList = response.data;
      });
    },
    // 广播终端
    broadcastList() {
      broadcastList().then((response) => {
        console.log(response, "广播终端");
        this.terminalList1 = response.data;
      });
    },
    // 上传格式验证
    beforeUpload(file) {
      if (this.formmaterial.materialType == "6010101") {
        const isJPG = /(jpeg|jpg|png|gif|bmp|tiff|webp|svg)$/.test(file.name);
        if (!isJPG) {
          this.$message.error("上传素材格式只能是下列格式!");
        }
        const isLt2M = file.size / 1024 / 1024 < 100;

        if (!isLt2M) {
          this.$message.error("上传素材大小不能超过 100MB!");
        }
        return isJPG && isLt2M;
      } else if (this.formmaterial.materialType == "6010102") {
        const isJPG = /(mp4|avi|mkv|mov|wmv|flv|webm|mpeg)$/.test(file.name);
        if (!isJPG) {
          this.$message.error("上传素材格式只能是下列格式!");
        }
        const isLt2M = file.size / 1024 / 1024 < 100;

        if (!isLt2M) {
          this.$message.error("上传素材大小不能超过 100MB!");
        }
        return isJPG && isLt2M;
      } else if (this.formmaterial.materialType == "6010103") {
        const isJPG = /(mp3|wav|aac|flac|ogg|wma)$/.test(file.name);
        if (!isJPG) {
          this.$message.error("上传素材格式只能是下列格式!");
        }
        const isLt2M = file.size / 1024 / 1024 < 100;

        if (!isLt2M) {
          this.$message.error("上传素材大小不能超过 100MB!");
        }
        return isJPG && isLt2M;
      } else if (this.formmaterial.materialType == "6010104") {
        const isJPG = /(pdf|word|excel|txt|ppt)$/.test(file.name);
        if (!isJPG) {
          this.$message.error("上传素材格式只能是下列格式!");
        }
        const isLt2M = file.size / 1024 / 1024 < 100;

        if (!isLt2M) {
          this.$message.error("上传素材大小不能超过 100MB!");
        }
        return isJPG && isLt2M;
      }
    },
    // 上传失败回调
    handleRemove(file, fileList) {
      this.form.url = undefined;
    },
    // 上传成功回调
    handlePreview(response, file, fileList) {
      console.log(response, file, fileList);
      if (file.size == 0) {
        this.$modal.msgWarning("当前文件大小不符合规范");

        return true;
      }
      this.formmaterial.url = fileList[0].response;
    },
    /** 提交按钮 */
    submitFormmaterial: function () {
      this.$refs["formmaterial"].validate((valid) => {
        if (valid) {
          this.formmaterial.isEmergency = 1;
          console.log(this.formmaterial);
          savematerial(this.formmaterial).then((response) => {
            this.$modal.msgSuccess("新增成功");
            this.openmaterial = false;
            this.getpagematerial();
          });
        }
      });
    },
    /** 提交按钮 */
    submitForm: function (type) {
      const _this = this;
      this.$refs["informationForm"].validate((valid) => {
        if (valid) {
          this.formmaterial.isEmergency = 1;
          this.informationForm.startTime = this.informationForm.dange[0];
          this.informationForm.endTime = this.informationForm.dange[1];
          this.informationForm.saveFlag = type;
          if (this.plain1) {
            let arr = JSON.parse(
              JSON.stringify(this.informationForm.terminalIdArr1)
            );
            this.informationForm.deviceIds = arr.toString();
            this.informationForm.typeFlag = "6010602";
          } else {
            this.informationForm.deviceCount =
              this.informationForm.terminalIdArr.length;
            let arr = JSON.parse(
              JSON.stringify(this.informationForm.terminalIdArr)
            );
            this.informationForm.terminalId = arr.toString();
            this.informationForm.typeFlag = "6010601";
          }
          console.log(this.informationForm);
          if (this.informationForm.id != undefined) {
            update(this.informationForm).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.informationDialog = false;
              this.getList();
            });
          } else {
            if (type == 2) {
              this.$modal
                .confirm("发起创建的节目将直接发起审核,是否继续创建")
                .then(function () {
                  return save(_this.informationForm).then((response) => {
                    this.$modal.msgSuccess("新增成功");
                    this.informationDialog = false;
                    this.getList();
                  });
                });
            } else {
              save(this.informationForm).then((response) => {
                this.$modal.msgSuccess("新增成功");
                this.informationDialog = false;
                this.getList();
              });
            }
          }
        }
      });
    },
    // 取消按钮
    cancelmaterial() {
      this.openmaterial = false;
      this.resetmaterial();
    },
    resetmaterial() {
      this.formmaterial = {
        materialName: undefined,
        materialType: undefined,
        url: undefined,
      };
      this.resetForm("formmaterial");
    },
    // 取消按钮
    informationCancel() {
      this.informationDialog = false;
      this.reset();
    },
    programClick(type) {
      this.informationForm = {};
      if (type == 1) {
        (this.plain1 = false), (this.plain2 = true);
        this.textFlag = false;
        this.getpagematerial();
        this.pagesterminal();
      } else {
        (this.plain1 = true), (this.plain2 = false);
        this.getpagematerial();
        this.broadcastList();
      }
      this.$refs.informationForm.resetFields();
    },
    // materialList() {
    //   materialList({}).then(res => {
    //     console.log(res);
    //   })
    // },

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        current: 1,
        size: 10,
        eventNo: undefined,
        eventName: undefined,
        eventTypeName: undefined,
        eventTypeId: undefined,
        eventLevel: undefined,
        startTime: "",
        endTime: "",
        dispatchStateList: [],
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.tag {
  margin-right: 10px;
}

::v-deep .el-descriptions__title {
  font-weight: normal;
}

.queryBtnT {
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
  float: right;
  margin-right: 10px;
}

.planList {
  height: 54vh;
  border-right: 1px solid #a8a8a8;

  .plan {
    width: 96%;
    margin-top: 1vh;
    padding: 5px 10px;
    border: 2px solid #d6d6d6;
    cursor: pointer;
  }
}

.stepBox {
  height: 54vh;
  padding: 0 10px;
  overflow-y: auto;

  .step {
    margin-bottom: 20px;

    span {
      font-size: 20px;
    }

    div {
      margin-top: 5px;
    }

    .rank {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.mapBox {
  position: relative;

  .eventListBox {
    width: 15vw;
    height: 600px;
    background: #fff;
    position: absolute;
    z-index: 2;
    border-radius: 12px;
    overflow: hidden;

    .boxContent {
      width: 100%;
      height: 560px;
      overflow-y: auto;
      padding: 15px 5%;

      .event {
        width: 100%;
        background: #ecf2ff;
        margin-bottom: 10px;
        padding: 6px 20px;
        cursor: pointer;

        div {
          margin-bottom: 5px;
        }
      }

      .name {
        color: #91959b;
      }
    }
  }

  .boxTitle {
    width: 100%;
    height: 40px;
    background: linear-gradient(115deg, #aec1fb 0%, #386bf0 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;

    .content {
      height: 16px;
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      color: #ffffff;
      line-height: 16px;
      border-left: 3px solid #fff;
      padding-left: 10px;
    }
  }

  .eventDetailBox {
    width: 30%;
    height: 360px;
    border-radius: 12px;
    background: #fff;
    overflow: hidden;
    position: absolute;
    left: 30%;
    top: 220px;
    z-index: 1;

    .detailContent {
      padding: 20px 30px;

      .eventDetail {
        div {
          margin-bottom: 15px;
        }

        .name {
          color: #91959b;
        }
      }

      .btnBox {
        position: absolute;
        bottom: 20px;
      }
      .btnBox1 {
        position: absolute;
        bottom: 70px;
      }
    }
  }

  .searchBox {
    width: 20vw;
    height: 500px;
    background: #fff;
    position: absolute;
    top: 80px;
    right: 40px;
    z-index: 2;
    border-radius: 12px;
    overflow: auto;

    .searchContent {
      padding: 20px 15px;

      .searchTitle {
        height: 15px;
        line-height: 15px;
        border-left: 3px solid black;
        padding-left: 10px;
      }

      .rankAndgood {
        height: 20vh;
        overflow-y: auto;
        margin-top: 20px;
      }

      .el-radio-button {
        margin-right: 10px;
        border-radius: 3px;
      }

      .el-radio-button:not(:first-child) {
        border-left: 1px solid #dcdfe6;
      }

      .el-checkbox {
        margin-bottom: 20px;
      }

      .dispatch {
        background: #d9e1ff;
        padding: 0 10px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .rankName {
          font-size: 14px;
          color: #3c6ef1;
        }
      }
    }
  }
}

::v-deep input[type="number"]::-webkit-inner-spin-button,
::v-deep input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

::v-deep .el-timeline-item__timestamp.is-top {
  font-size: 20px;
  font-weight: 600;
}

.option {
  height: auto;
  line-height: 1;
  padding: 0;
  background-color: #fff;
}

.tree {
  padding: 4px 20px;
  font-weight: 400;
}

.limit {
  color: #ca3331;
  position: absolute;
  left: 0;
}
::v-deep .el-form-item__label {
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}

.tooltip {
  margin-top: -16px;
  margin-left: 20px;
  width: 320px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.planCard {
  overflow: auto;
  height: 400px;
}
</style>
