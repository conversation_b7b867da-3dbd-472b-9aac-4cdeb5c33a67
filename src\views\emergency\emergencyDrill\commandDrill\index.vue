<template>
    <div class="app-container">
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>数据筛选</span>
            </div>
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              v-show="showSearch"
              label-position="left"
              style="display: flex; justify-content: space-between"
            >
              <div style="width: 80%">
                <el-form-item label="演练名称">
                  <el-input
                    v-model="queryParams.planName"
                    placeholder="请输入演练名称"
                    clearable
                    maxlength="20"
                    style="width: 10vw"
                    @keyup.enter.native="handleQuery"
                  />
                </el-form-item>
                <!-- <el-form-item label="演练类型" >
                  <el-select
                    style="width: 10vw"
                    v-model="queryParams.drillType"
                    placeholder="请选择演练类型"
                    :disabled="disabled"
                  >
                    <el-option
                      v-for="dict in dict.type.drill_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="演练状态" >
                  <el-select
                    style="width: 10vw"
                    v-model="queryParams.drillStatus"
                    placeholder="请选择演练状态"
                  >
                    <el-option
                      v-for="dict in dict.type.drill_status"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item label="演练时间">
                  <el-date-picker
                    v-model="dateRange"
                    style="width: 10vw"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  ></el-date-picker>
                </el-form-item>
              </div>
              <div style="min-width: 166px">
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    style="font-size:13px"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                  style="font-size:13px"
                    >重置</el-button
                  >
                </el-form-item>
              </div>
            </el-form>
          </el-card>
          <el-card class="box-card mapBox">
            <div slot="header" class="clearfix">
              <span>演练计划展示列表</span>
            </div>
            <el-button
              v-show="!eventListDialog"
              style="position: absolute; z-index: 1"
              type="primary"
              plain
              @click="eventListDialog = true"
              >事件列表</el-button
            >
            <div v-show="eventListDialog" class="eventListBox">
              <div class="boxTitle">
                <div class="content">演练计划列表</div>
                <i
                  class="el-icon-close"
                  style="color: #fff; cursor: pointer"
                  @click="eventListDialog = false"
                ></i>
              </div>
              <div class="boxContent">
                <div
                  v-for="(item, index) in eventList"
                  :key="index"
                  class="event"
                  @click="eventClick(item)"
                >
                  <div class="overlength" :title="item.planName">
                    <span class="name">演练名称：</span>{{ item.planName }}
                  </div>
                  <div>
                    <span class="name">事件类型：</span
                    ><el-tag type="success">{{ item.eventTypeName }}</el-tag>
                  </div>
                  <div>
                    <span class="name">演练状态：</span
                    >{{
                      item.drillStatus
                        ? dict.type.drill_status.find(
                            (ele) => ele.value == item.drillStatus
                          ).label
                        : ""
                    }}
                  </div>
                  <div class="overlength" :title="item.drillTime">
                    <span class="name">演练时间：</span>{{ item.drillTime }}
                  </div>
                </div>
              </div>
            </div>
            <div v-show="eventDetaildialog" class="eventDetailBox">
              <div class="boxTitle">
                <div class="content">演练名称：{{ eventDetail.planName }}</div>
                <i
                  class="el-icon-close"
                  style="color: #fff; cursor: pointer"
                  @click="eventDetaildialog = false"
                ></i>
              </div>
              <div class="detailContent">
                <div class="eventDetail">
                  <div>
                    <span class="name">事件类型：</span>{{ eventDetail.eventTypeName }}
                  </div>
                  <div>
                    <span class="name">关联预案：</span>{{ eventDetail.externalPlanName }}
                  </div>
                  <div>
                    <span class="name">事件位置：</span
                    >{{ eventDetail.longitude + "," + eventDetail.latitude }}
                  </div>
                  <div><span class="name">演练时间：</span>{{ eventDetail.drillTime }}</div>
                </div>
                <div class="btnBox">
                  <el-button type="primary" plain @click="handleCommand"
                    >指挥调度</el-button
                  >
                  <el-button
                    style="margin-left: 30%"
                    type="primary"
                    plain
                    @click="handleRecord"
                    >执行记录</el-button
                  >
                </div>
              </div>
            </div>
            <div class="searchBox" v-show="resourcefulSearch">
              <div class="boxTitle">
                <div class="content">周边资源搜索</div>
                <i
                  class="el-icon-close"
                  style="color: #fff; cursor: pointer"
                  @click="resourcefulSearch = false"
                ></i>
              </div>
              <div class="searchContent">
                <div class="searchTitle">距离范围筛选</div>
                <div style="margin: 20px 0">
                  <el-radio-group v-model="range" @change="rangeChange">
                    <el-radio-button label="200">200m</el-radio-button>
                    <el-radio-button label="500">500m</el-radio-button>
                    <el-radio-button label="1000">1km</el-radio-button>
                    <el-radio-button label="5000">5km</el-radio-button>
                  </el-radio-group>
                </div>
                <div class="searchTitle">资源筛选</div>
                <div style="margin-top: 20px">
                  <el-checkbox-group v-model="checkList" @change="checkListChange">
                    <el-checkbox label="5013003">避难场所</el-checkbox>
                    <el-checkbox label="5013005">应急广播</el-checkbox>
                    <el-checkbox label="5013004">周边监控</el-checkbox>
                    <el-checkbox label="5013008">防护目标</el-checkbox>
                    <el-checkbox label="5013009">医疗机构</el-checkbox>
                    <el-checkbox label="5013006">风险隐患</el-checkbox>
                    <el-checkbox label="5013007">通讯保障</el-checkbox>
                  </el-checkbox-group>
                </div>
                <div>
                  <el-radio-group v-model="dispatch">
                    <el-radio-button label="rank">队伍调度</el-radio-button>
                    <el-radio-button label="good">物资调度</el-radio-button>
                  </el-radio-group>
                </div>
                <div v-show="dispatch == 'rank'" class="rankAndgood">
                  <div
                    v-for="(item, index) in ranksList"
                    :key="index"
                    class="dispatch"
                    v-show="item.show"
                  >
                    <div class="rankName">{{ item.name }}</div>
                    <div>
                      <el-button type="text" @click="rankPosition(item)">定位</el-button>
                      <el-button type="text" @click="rankDispatch(item)">调度</el-button>
                    </div>
                  </div>
                </div>
                <div v-show="dispatch == 'good'" class="rankAndgood">
                  <div
                    v-for="(item, index) in goodsList"
                    :key="index"
                    class="dispatch"
                    v-show="item.show"
                  >
                    <div class="rankName">
                      {{ item.name }}
                    </div>
                    <div>
                      <el-button type="text" @click="goodPosition(item)">定位</el-button>
                      <el-button type="text" @click="goodDispatch(item)">调度</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Map @detail="detailDialog" ref="mapRef" :url="url"></Map>
          </el-card>
        </el-col>
      </el-row>
      <!--  -->
      <!-- 指挥调度对话框 -->
      <el-dialog
        width="960px"
        append-to-body
        :visible.sync="commandDialog"
        :show-close="false"
        :close-on-click-modal="false"
        title="指挥调度"
      >
        <el-tabs v-model="activeName" @tab-click="commandClick">
          <el-tab-pane label="预案响应" name="first">
            <el-row>
              <el-col :span="6">
                <div class="planList">
                  <el-button
                    v-show="!eventDetail.isEndRescue"
                    type="primary"
                    @click="launchPlan"
                    >启动预案</el-button
                  >
                  <div
                    v-for="(item, index) in planBox"
                    :key="index"
                    class="plan"
                    @click="planClick(item)"
                  >
                    <div>{{ item.planName }}</div>
                    <el-tag style="margin: 10px 0" type="danger">{{
                      item.responseName
                    }}</el-tag>
                    <div style="white-space: nowrap">启动时间：{{ item.createTime }}</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="18">
                <el-row>
                  <el-col :span="16">
                    <div class="stepBox">
                      <div v-for="(item, index) in stepList" :key="index" class="step">
                        <span>步骤{{ index + 1 }}</span>
                        <div class="rank">
                          <div>
                            响应名称:{{ item.responseName }}
                            {{
                              dict.type.step_status.find(
                                (ele) => ele.value == item.processStatus
                              ).label
                            }}
                          </div>
                          <el-button
                            v-show="item.processStatus == 5011801"
                            type="text"
                            @click="processUpdate(item.id, '5011802')"
                            >开始处置</el-button
                          >
                          <el-button
                            v-show="item.processStatus == 5011802"
                            type="text"
                            @click="processUpdate(item.id, '5011803')"
                            >结束步骤</el-button
                          >
                        </div>
                        <div>注意事项:{{ item.announcement }}</div>
                        <div
                          v-for="(ele, ind) in item.contingentList"
                          :key="ind"
                          class="rank"
                        >
                          <div>
                            响应队伍:{{ ele.contingentName }}
                            {{
                              dict.type.contingent_status.find(
                                (el) => el.value == ele.contingentStatus
                              ).label
                            }}
                          </div>
                          <el-button
                            v-show="
                              ele.contingentStatus == 5011901 &&
                              item.processStatus == 5011802
                            "
                            type="text"
                            @click="assign(ele)"
                            >指派</el-button
                          >
                          <el-button
                            v-show="
                              ele.contingentStatus == 5011902 &&
                              item.processStatus == 5011802
                            "
                            type="text"
                            @click="finish(ele, '5011902')"
                            >完成</el-button
                          >
                        </div>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <el-timeline>
                      <el-timeline-item
                        v-for="(activity, index) in activities"
                        :key="index"
                        :timestamp="'步骤' + (index + 1)"
                        placement="top"
                      >
                        开始时间：{{ activity.startTime }}
                        <br />
                        <div v-for="(ite, ind) in activity.contingentList" :key="ind">
                          指派队伍：{{ ite.contingentName }}
                          <br />
                          任务描述：{{ ite.taskDescription }}
                          <br />
                          <span v-show="ite.contingentStatus == '5011902'">
                            预期完成时间：{{ ite.finishTime }}
                          </span>
                          <span v-show="ite.contingentStatus == '5011903'">
                            完成时间：{{ ite.endTime }}
                          </span>
                        </div>
                        <br />
                        <el-tag type="success" v-show="activity.processStatus == 5011803">
                          {{
                            dict.type.step_status.find(
                              (ele) => ele.value == activity.processStatus
                            ).label
                          }}
                        </el-tag>
                        <el-tag type="warning" v-show="activity.processStatus == 5011802">
                          {{
                            dict.type.step_status.find(
                              (ele) => ele.value == activity.processStatus
                            ).label
                          }}
                        </el-tag>
                        <el-tag type="info" v-show="activity.processStatus == 5011801">
                          {{
                            dict.type.step_status.find(
                              (ele) => ele.value == activity.processStatus
                            ).label
                          }}
                        </el-tag>
                      </el-timeline-item>
                    </el-timeline>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="临时任务" name="second">
            <el-button type="primary" @click="sendTask">增派临时任务</el-button>
            <el-table :data="tasksData" border style="width: 100%; margin: 20px 0">
              <el-table-column
                prop="updateTime"
                label="创建时间"
                align="center"
              ></el-table-column>
              <el-table-column prop="taskType" label="任务类型" align="center">
                <template slot-scope="scope">
                  <div>
                    {{
                      dict.type.task_type.find((ele) => ele.value == scope.row.taskType)
                        ? dict.type.task_type.find((ele) => ele.value == scope.row.taskType)
                            .label
                        : ""
                    }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="leader"
                label="负责人"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="taskRemark"
                label="任务备注"
                align="center"
              ></el-table-column>
              <el-table-column prop="finishTime" label="期望完成时间" align="center">
              </el-table-column>
              <el-table-column prop="taskStatus" label="任务状态" align="center">
                <template slot-scope="scope">
                  <div>
                    {{
                      dict.type.task_status.find((ele) => ele.value == scope.row.taskStatus)
                        ? dict.type.task_status.find(
                            (ele) => ele.value == scope.row.taskStatus
                          ).label
                        : ""
                    }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                  <el-button @click="handleTaskDetail(scope.row)" type="text" size="small"
                    >详情</el-button
                  >
                  <el-button
                    v-show="scope.row.taskStatus == 5012601"
                    type="text"
                    size="small"
                    @click="handleTaskUpdate(scope.row, '5012602')"
                    >完成</el-button
                  >
                  <el-button
                    v-show="scope.row.taskStatus == 5012601"
                    type="text"
                    size="small"
                    @click="handleTaskUpdate(scope.row, '5012603')"
                    >取消</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <div slot="footer" class="dialog-footer">
          <el-button
            v-show="!eventDetail.isEndRescue"
            type="primary"
            plain
            @click="endRescue"
            >结束演练</el-button
          >
          <el-button @click="commandDialog = false">返 回</el-button>
        </div>
      </el-dialog>
      <!-- 启动预案对话框 -->
      <el-dialog
        width="720px"
        append-to-body
        :visible.sync="launchPlanDialog"
        :show-close="false"
        :close-on-click-modal="false"
        title="启动预案"
      >
        <div style="display: flex; flex-wrap: wrap" v-loading="planLoading">
          <el-radio
            v-model="planRadio"
            :label="planList.id"
            style="width: 320px; margin: 0 0 10px 10px"
            @input="planInput"
            border
            >{{ planList.planName }}-{{ planList.planDescription }}</el-radio
          >
        </div>
        <el-form
          :model="levelFrom"
          :rules="levelRules"
          ref="levelFrom"
          :inline="true"
          style="margin-top: 20px"
        >
          <el-form-item label="响应等级" prop="level">
            <el-select v-model="levelFrom.level" placeholder="请选择响应等级">
              <el-option
                v-for="(item, index) in levelBox"
                :key="index"
                :label="item.responseName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="confirm">确 认</el-button>
          <el-button @click="launchPlanDialog = false">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 指派任务对话框 -->
      <el-dialog title="指派任务" :visible.sync="assignDialog" width="560px" append-to-body>
        <el-form
          :model="assignFrom"
          :rules="assignRules"
          ref="assignFrom"
          label-width="120px"
        >
          <el-form-item label="任务描述" prop="taskDescription">
            <el-input
              type="textarea"
              maxlength="200"
              v-model="assignFrom.taskDescription"
            ></el-input>
          </el-form-item>
          <el-form-item label="期望完成时间" prop="finishTime">
            <el-date-picker
              v-model="assignFrom.finishTime"
              type="datetime"
              placeholder="选择日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="是否短信通知" prop="isSmsNotice">
            <el-radio-group v-model="assignFrom.isSmsNotice">
              <el-radio label="true">是</el-radio>
              <el-radio label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="assignConfirm">确 定</el-button>
          <el-button @click="assignDialog = false">取 消</el-button>
        </span>
      </el-dialog>
      <!-- 增派临时任务对话框 -->
      <el-dialog :title="sendTitle" :visible.sync="sendDialog" width="960px" append-to-body>
        <el-radio-group v-model="sendFlag" style="margin-bottom: 20px">
          <el-radio-button label="rank">队伍调度</el-radio-button>
          <el-radio-button label="good">物资调度</el-radio-button>
        </el-radio-group>
        <el-form
          v-if="sendFlag == 'rank'"
          :model="sendrankFrom"
          :rules="sendrankRules"
          ref="sendrankFrom"
          label-width="120px"
        >
          <el-form-item key="rankId" label="指派队伍" prop="contingentId">
            <el-select
              v-model="sendrankFrom.contingentId"
              placeholder="请选择指派队伍"
              :disabled="rankDisabled"
            >
              <el-option
                v-for="(item, index) in ranksOptions"
                :key="index"
                :label="item.contingentName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item key="rankRemark" label="任务备注" prop="taskRemark">
            <el-input
              type="textarea"
              v-model="sendrankFrom.taskRemark"
              maxlength="200"
              style="width: 60%"
              :disabled="rankDisabled"
            ></el-input>
          </el-form-item>
          <el-form-item key="rankFinish" label="期望完成时间" prop="finishTime">
            <el-date-picker
              v-model="sendrankFrom.finishTime"
              type="datetime"
              placeholder="选择日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              :disabled="rankDisabled"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item key="rankisIn" label="短信通知负责人" prop="isInform">
            <el-radio-group :disabled="rankDisabled" v-model="sendrankFrom.isInform">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-form
          v-if="sendFlag == 'good'"
          :model="sendgoodFrom"
          :rules="sendgoodRules"
          ref="sendgoodFrom"
          label-width="120px"
        >
          <el-form-item key="goodDepot" label="调度仓库" prop="depotId">
            <el-select
              v-model="sendgoodFrom.depotId"
              placeholder="请选择调度仓库"
              @change="depotChange"
              :disabled="goodDisabled"
            >
              <el-option
                v-for="(item, index) in depotOptions"
                :key="index"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="调度物资">
            <el-table :data="goodsData" border style="width: 100%">
              <el-table-column
                align="center"
                prop="materialName"
                label="物资名称"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column prop="materialType" align="center" label="物资类型">
                <template slot-scope="scope">
                  <el-tag>{{
                    dict.type.materiel_type.find(
                      (ele) => ele.value == scope.row.materialType
                    ).label
                  }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                v-if="!this.goodDisabled"
                prop="inventory"
                align="center"
                label="库存量"
              ></el-table-column>
              <el-table-column prop="dispatchQuantity" align="center" label="调度量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.dispatchQuantity"
                    type="number"
                    maxlength="20"
                    :disabled="goodDisabled"
                  ></el-input>
                  <!-- @change="(value) => numberLimit(value, scope.row)" -->
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item key="goodRemark" label="任务备注" prop="taskRemark">
            <el-input
              type="textarea"
              v-model="sendgoodFrom.taskRemark"
              :disabled="goodDisabled"
              maxlength="200"
            ></el-input>
          </el-form-item>
          <el-form-item key="goodFinish" label="期望完成时间" prop="finishTime">
            <el-date-picker
              v-model="sendgoodFrom.finishTime"
              type="datetime"
              placeholder="选择日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              :disabled="goodDisabled"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item key="goodisIn" label="是否短信通知" prop="isInform">
            <el-radio-group v-model="sendgoodFrom.isInform" :disabled="goodDisabled">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <span v-show="!goodDisabled" slot="footer" class="dialog-footer">
          <el-button type="primary" @click="sendConfirm">确 定</el-button>
          <el-button @click="sendDialog = false">取 消</el-button>
        </span>
      </el-dialog>
      <el-dialog title="执行记录" :visible.sync="recordDialog" width="720px" append-to-body>
        <div class="block">
          <el-timeline>
            <el-timeline-item timestamp="事件接报" placement="top">
              <el-card>
                <div>
                  上报时间：{{ eventReport.reportTime ? eventReport.reportTime : "" }}
                </div>
                <div>
                  上报人：{{ eventReport.submitPerson }}
                  {{ eventReport.contactNumber }}
                </div>
                <!-- <div>审核时间：{{ eventReport.auditTime }}</div> -->
                <!-- <div>审核人：{{ eventReport.auditPerson }}</div> -->
              </el-card>
            </el-timeline-item>
            <el-timeline-item timestamp="预案启动" placement="top">
              <el-card>
                <div
                  v-for="(item, index) in executionRecord.planStarts"
                  :key="index"
                  style="margin: 10px 0"
                >
                  <div>预案名称：{{ item.planName }}</div>
                  <div>开始时间：{{ item.startTime }}</div>
                  <div>处理人员：{{ item.handler }}</div>
                </div>
              </el-card>
            </el-timeline-item>
            <el-timeline-item timestamp="持续调度" placement="top">
              <el-card>
                <div
                  v-for="(item, index) in executionRecord.continuousDispatch"
                  :key="index"
                  style="margin: 10px 0"
                >
                  <div
                    :style="item.assignTaskType == 5012901 ? 'color:red' : 'color:green'"
                  >
                    {{
                      dict.type.assign_task_type.find(
                        (ele) => ele.value == item.assignTaskType
                      )
                        ? dict.type.assign_task_type.find(
                            (ele) => ele.value == item.assignTaskType
                          ).label
                        : ""
                    }}
                  </div>
                  <div>任务开始时间：{{ item.startTime ? item.startTime : "" }}</div>
                  <div>指派队伍：{{ item.contingentName }}</div>
                  <div>
                    任务{{ item.taskStatus == "5012603" ? "取消" : "结束" }}时间：{{
                      item.taskStatus == "5012601" ? "" : item.updateTime
                    }}
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
            <el-timeline-item timestamp="结束演练" placement="top">
              <el-card>
                <div>
                  结束时间：{{
                    executionRecord.endRescue ? executionRecord.endRescue.endTime : ""
                  }}
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import {
    page,
    getTree,
    overviewList,
    planList,
    levelList,
    planSave,
    showPlan,
    showProcess,
    showProcessRight,
    stepStatus,
    contingentUpdate,
    contingentList,
    depotList,
    taskSave,
    listOfDepot,
    temporaryTasklist,
    taskDetail,
    taskUpdate,
    recordSave,
    recordDetail,
  } from "@/api/emergency/emergencyDrill/commandDrill/index";
  import Map from "../../../map/index1.vue";
  export default {
    name: "EmergencySupplies",
    dicts: [
      "plan_deduction",
      "response_level",
      "step_status",
      "contingent_status",
      "materiel_type",
      "task_type",
      "task_status",
      "assign_task_type",
      "event_level",
      "drill_type",
      "drill_status",
    ],
    components: { Map },
    data() {
      return {
        // 地图点标记图标地址
        url: `${require("@/assets/icons/location.png")}`,
        rankUrl: `${require("@/assets/icons/name.png")}`,
        goodUrl: `${require("@/assets/icons/warehouse.png")}`,
        // 事件类型树
        treeData: [],
        defaultProps: {
          children: "children",
          label: "nodeName",
        },
        // 遮罩层d
        loading: false,
        planLoading: false,
        // 显示搜索条件
        showSearch: true,
        resourcefulSearch: false,
        // 事件列表数据
        eventList: [],
        text: undefined,
        dateRange: [],
        // 指挥调度弹框
        commandDialog: false,
        activeName: "first",
        // 启动预案弹框
        launchPlanDialog: false,
        planList: [],
        // 查询参数
        queryParams: {
          current: 1,
          size: 1000,
          eventNo: undefined,
          eventName: undefined,
          eventTypeName: undefined,
          eventTypeId: undefined,
          eventLevel: undefined,
          startTime: "",
          endTime: "",
        },
        // 事件列表弹框
        eventListDialog: true,
        // 事件详情
        eventDetaildialog: false,
        eventDetail: {},
        // 搜索弹框
        range: "200m",
        checkList: [],
        dispatch: "rank",
        ranksList: [],
        goodsList: [],
        // 启动预案
        planRadio: "",
        levelFrom: {
          level: undefined,
        },
        levelBox: [],
        levelRules: {
          level: [{ required: true, message: "请选择响应等级", trigger: "blur" }],
        },
        // 指派弹框
        assignDialog: false,
        assignFrom: {
          taskDescription: undefined,
          finishTime: undefined,
          isSmsNotice: undefined,
        },
        assignRules: {
          taskDescription: [{ required: true, message: "请输入任务描述", trigger: "blur" }],
          time: [{ required: true, message: "请选择完成时间", trigger: "blur" }],
          isSmsNotice: [{ required: true, message: "请选择是否短信通知", trigger: "blur" }],
        },
        // 预案列表
        planBox: [],
        // 步骤列表
        planRow: undefined,
        stepList: [],
        // 时间线数据
        activities: [],
        // 增派任务数据
        tasksData: [],
        // 增派临时任务
        sendDialog: false,
        rankDisabled: false,
        goodDisabled: false,
        sendTitle: "",
        sendFlag: "rank",
        ranksOptions: [],
        sendrankFrom: {
          contingentId: undefined,
          taskRemark: undefined,
          finishTime: undefined,
          isInform: undefined,
        },
        sendrankRules: {
          contingentId: [{ required: true, message: "请选择指派队伍", trigger: "change" }],
          taskRemark: [{ required: true, message: "请输入任务备注", trigger: "blur" }],
          finishTime: [{ required: true, message: "请选择期望时间", trigger: "change" }],
          isInform: [{ required: true, message: "请选择是否通知", trigger: "change" }],
        },
        depotOptions: [],
        sendgoodFrom: {
          depotId: undefined,
          taskRemark: undefined,
          finishTime: undefined,
          isInform: undefined,
        },
        goodsData: [],
        sendgoodRules: {
          depotId: [{ required: true, message: "请选择指派队伍", trigger: "change" }],
          taskRemark: [{ required: true, message: "请输入任务备注", trigger: "blur" }],
          finishTime: [{ required: true, message: "请选择期望时间", trigger: "change" }],
          isInform: [{ required: true, message: "请选择是否通知", trigger: "change" }],
        },
        // 执行记录弹框
        recordDialog: false,
        executionRecord: {},
        eventReport: {},
      };
    },
    watch: {},
    created() {
      this.getList();
    },
    mounted() {
      this.getTree();
      this.$refs.mapRef.initMap();
      overviewList({ resourceType: 5013001 }).then((res) => {
        this.goodsList = res.data.list;
      });
      overviewList({ resourceType: 5013002 }).then((res) => {
        this.ranksList = res.data.list;
      });
    },
    methods: {
      /** 右侧悬框专用模块 */
      // 范围框选
      rangeChange(value) {
        this.$refs.mapRef.circleRange(
          this.eventDetail.longitude,
          this.eventDetail.latitude,
          value
        );
        this.$refs.mapRef.clearAllList();
        this.checkList = [];
        this.ranksList.forEach((element) => {
          if (element.distance < value) {
            element.show = true;
          } else {
            element.show = false;
          }
        });
        this.goodsList.forEach((element) => {
          if (element.distance < value) {
            element.show = true;
          } else {
            element.show = false;
          }
        });
        overviewList({ resourceType: 5013003 }).then((res) => {
          res.data.list.forEach((element) => {
            element.distance = this.$refs.mapRef.getDistance(
              element.longitude,
              element.latitude,
              this.eventDetail.longitude,
              this.eventDetail.latitude
            );
            if (element.distance < value) {
              this.$refs.mapRef.markerList(
                element.longitude,
                element.latitude,
                "shelter",
                element
              );
            }
          });
        });
        overviewList({ resourceType: 5013004 }).then((res) => {
          res.data.list.forEach((element) => {
            element.distance = this.$refs.mapRef.getDistance(
              element.longitude,
              element.latitude,
              this.eventDetail.longitude,
              this.eventDetail.latitude
            );
            if (element.distance < value) {
              this.$refs.mapRef.markerList(
                element.longitude,
                element.latitude,
                "monitor",
                element
              );
            }
          });
        });
        overviewList({ resourceType: 5013005 }).then((res) => {
          res.data.list.forEach((element) => {
            element.distance = this.$refs.mapRef.getDistance(
              element.longitude,
              element.latitude,
              this.eventDetail.longitude,
              this.eventDetail.latitude
            );
            if (element.distance < value) {
              this.$refs.mapRef.markerList(
                element.longitude,
                element.latitude,
                "broadcast",
                element
              );
            }
          });
        });
        overviewList({ resourceType: 5013006 }).then((res) => {
          res.data.list.forEach((element) => {
            element.distance = this.$refs.mapRef.getDistance(
              element.longitude,
              element.latitude,
              this.eventDetail.longitude,
              this.eventDetail.latitude
            );
            if (element.distance < value) {
              this.$refs.mapRef.markerList(
                element.longitude,
                element.latitude,
                "risk",
                element
              );
            }
          });
        });
        overviewList({ resourceType: 5013007 }).then((res) => {
          res.data.list.forEach((element) => {
            element.distance = this.$refs.mapRef.getDistance(
              element.longitude,
              element.latitude,
              this.eventDetail.longitude,
              this.eventDetail.latitude
            );
            if (element.distance < value) {
              this.$refs.mapRef.markerList(
                element.longitude,
                element.latitude,
                "communicate",
                element
              );
            }
          });
        });
        overviewList({ resourceType: 5013008 }).then((res) => {
          res.data.list.forEach((element) => {
            element.distance = this.$refs.mapRef.getDistance(
              element.longitude,
              element.latitude,
              this.eventDetail.longitude,
              this.eventDetail.latitude
            );
            if (element.distance < value) {
              this.$refs.mapRef.markerList(
                element.longitude,
                element.latitude,
                "protection",
                element
              );
            }
          });
        });
        overviewList({ resourceType: 5013009 }).then((res) => {
          res.data.list.forEach((element) => {
            element.distance = this.$refs.mapRef.getDistance(
              element.longitude,
              element.latitude,
              this.eventDetail.longitude,
              this.eventDetail.latitude
            );
            if (element.distance < value) {
              this.$refs.mapRef.markerList(
                element.longitude,
                element.latitude,
                "medical",
                element
              );
            }
          });
        });
        this.$forceUpdate();
      },
      // 队伍定位
      rankPosition(item) {
        this.$refs.mapRef.marker(
          item.longitude,
          item.latitude,
          this.rankUrl,
          false,
          false,
          item
        );
      },
      // 仓库定位
      goodPosition(item) {
        this.$refs.mapRef.marker(
          item.longitude,
          item.latitude,
          this.goodUrl,
          false,
          false,
          item
        );
      },
      // 队伍调度
      rankDispatch(item) {
        // console.log(item);
        this.sendTask();
        this.sendrankFrom.contingentId = item.id.toString();
      },
      // 队伍调度
      goodDispatch(item) {
        // console.log(item);
        this.sendTask();
        this.sendFlag = "good";
        this.sendgoodFrom.depotId = item.id.toString();
        this.depotChange(item.id);
      },
      checkListChange(value) {
        if (value.indexOf("5013003") != -1) {
          this.$refs.mapRef.markerListAdd("shelter");
        } else {
          this.$refs.mapRef.markerListRemove("shelter");
        }
        if (value.indexOf("5013005") != -1) {
          this.$refs.mapRef.markerListAdd("broadcast");
        } else {
          this.$refs.mapRef.markerListRemove("broadcast");
        }
        if (value.indexOf("5013004") != -1) {
          this.$refs.mapRef.markerListAdd("monitor");
        } else {
          this.$refs.mapRef.markerListRemove("monitor");
        }
        if (value.indexOf("5013008") != -1) {
          this.$refs.mapRef.markerListAdd("protection");
        } else {
          this.$refs.mapRef.markerListRemove("protection");
        }
        if (value.indexOf("5013009") != -1) {
          this.$refs.mapRef.markerListAdd("medical");
        } else {
          this.$refs.mapRef.markerListRemove("medical");
        }
        if (value.indexOf("5013006") != -1) {
          this.$refs.mapRef.markerListAdd("risk");
        } else {
          this.$refs.mapRef.markerListRemove("risk");
        }
        if (value.indexOf("5013007") != -1) {
          this.$refs.mapRef.markerListAdd("communicate");
        } else {
          this.$refs.mapRef.markerListRemove("communicate");
        }
      },
      /** 查询场所列表 */
      getList() {
        this.loading = true;
        page(this.queryParams).then((response) => {
          console.log(response);
          if (response.data != null) {
            this.eventList = response.data;
          }
          this.loading = false;
        });
      },
      // 获取事件类型树结构
      getTree() {
        getTree().then((res) => {
          // console.log(res);
          if (res.code == 200) {
            this.recursion(res.data);
            this.treeData = res.data;
          }
        });
      },
      handleNodeClick(data, res, item) {
        // console.log(data, res, item, "树结构");
        if (!res) {
          this.queryParams.eventTypeId = undefined;
          this.queryParams.eventTypeName = undefined;
        } else {
          this.$refs.Addtree.setCheckedNodes([data]);
          this.queryParams.eventTypeId = data.id;
          this.queryParams.eventTypeName = data.nodeName;
        }
      },
      // 解决因为切换叶子节点清空值导致不能选择的问题
      eventLabelChange(res) {
        this.$forceUpdate();
      },
      // 通过递归给叶子节点添加只读属性
      recursion(data) {
        data.forEach((item, index) => {
          if (item.children) {
            item.disabled = true;
            return this.recursion(item.children);
          } else {
            item.disabled = false;
          }
        });
      },
      // 事件列表点击事件
      eventClick(item) {
        this.eventDetail = item;
        this.range = "";
        this.checkList = [];
        this.$refs.mapRef.clearCircle();
        this.stepList = []; //切换事件清空预案信息
        this.activities = [];
        this.$refs.mapRef.marker(item.longitude, item.latitude, this.url, true, true);
        this.$refs.mapRef.marker(item.longitude, item.latitude, this.url, false, true);
        this.ranksList.forEach((element) => {
          element.show = true;
          element.distance = this.$refs.mapRef.getDistance(
            element.longitude,
            element.latitude,
            item.longitude,
            item.latitude
          );
        });
        this.goodsList.forEach((element) => {
          element.show = true;
          element.distance = this.$refs.mapRef.getDistance(
            element.longitude,
            element.latitude,
            item.longitude,
            item.latitude
          );
        });
        overviewList({ resourceType: 5013003 }).then((res) => {
          res.data.list.forEach((element) => {
            this.$refs.mapRef.markerList(
              element.longitude,
              element.latitude,
              "shelter",
              element
            );
          });
        });
        overviewList({ resourceType: 5013004 }).then((res) => {
          res.data.list.forEach((element) => {
            this.$refs.mapRef.markerList(
              element.longitude,
              element.latitude,
              "monitor",
              element
            );
          });
        });
        overviewList({ resourceType: 5013005 }).then((res) => {
          res.data.list.forEach((element) => {
            this.$refs.mapRef.markerList(
              element.longitude,
              element.latitude,
              "broadcast",
              element
            );
          });
        });
        overviewList({ resourceType: 5013006 }).then((res) => {
          res.data.list.forEach((element) => {
            this.$refs.mapRef.markerList(
              element.longitude,
              element.latitude,
              "risk",
              element
            );
          });
        });
        overviewList({ resourceType: 5013007 }).then((res) => {
          res.data.list.forEach((element) => {
            this.$refs.mapRef.markerList(
              element.longitude,
              element.latitude,
              "communicate",
              element
            );
          });
        });
        overviewList({ resourceType: 5013008 }).then((res) => {
          res.data.list.forEach((element) => {
            this.$refs.mapRef.markerList(
              element.longitude,
              element.latitude,
              "protection",
              element
            );
          });
        });
        overviewList({ resourceType: 5013009 }).then((res) => {
          res.data.list.forEach((element) => {
            this.$refs.mapRef.markerList(
              element.longitude,
              element.latitude,
              "medical",
              element
            );
          });
        });
        this.resourcefulSearch = true;
      },
      detailDialog(value) {
        this.eventDetaildialog = value;
      },
      // 指挥调度按钮
      handleCommand() {
        this.commandDialog = true;
        showPlan({ businessTypeId: this.eventDetail.businessTypeId }).then((res) => {
          res.data.forEach((item) => {
            item.responseName = this.dict.type.response_level.find(
              (ele) => ele.value == item.responseLevel
            ).label;
          });
          this.planBox = res.data;
        });
        temporaryTasklist({
          businessTypeId: this.eventDetail.businessTypeId,
        }).then((res) => {
          res.data.forEach((item) => {
            if (item.depotVo) {
              item = Object.assign(item, item.depotVo);
            } else if (item.contingentVo) {
              item = Object.assign(item, item.contingentVo);
            }
          });
          // console.log(res.data);
          this.tasksData = res.data;
        });
      },
      // 点击左侧预案获取步骤
      planClick(row) {
        this.planRow = row;
        showProcess({
          responseLevelId: row.responseLevelId,
          businessTypeId: this.eventDetail.businessTypeId,
        }).then((res) => {
          // console.log(res.data);
          this.stepList = res.data;
        });
        showProcessRight({
          responseLevelId: row.responseLevelId,
          businessTypeId: this.eventDetail.businessTypeId,
        }).then((res) => {
          console.log(res.data);
          this.activities = res.data;
        });
      },
      commandClick(tab, event) {
        console.log(tab, event);
      },
      // 结束演练
      endRescue() {
        var that = this;
        this.$modal
          .confirm("是否确认结束当前演练")
          .then(function () {
            return recordSave({
              businessTypeId: that.eventDetail.businessTypeId,
            });
          })
          .then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess(res.msg);
              this.commandDialog = false;
              this.eventDetaildialog = false;
              this.$refs.mapRef.clearMap();
              this.getList();
            }
          })
          .catch((error) => {});
      },
      // 启动预案
      launchPlan() {
        this.planList = [];
        this.planRadio = undefined;
        this.launchPlanDialog = true;
        planList({ id: this.eventDetail.planId }).then((res) => {
          this.planList = res.data;
        });
      },
      // 切换单选预案
      planInput(value) {
        levelList({ planId: value }).then((res) => {
          res.data.forEach((item) => {
            item.responseName = this.dict.type.response_level.find(
              (ele) => ele.value == item.responseLevel
            ).label;
          });
          this.levelBox = res.data;
        });
      },
      // 预案确认选择
      confirm() {
        if (this.planRadio) {
          this.$refs["levelFrom"].validate((valid) => {
            if (valid) {
              const data = {};
              data.responseLevelId = this.levelFrom.level;
              data.businessTypeId = this.eventDetail.businessTypeId;
              planSave(data).then((res) => {
                if (res.code == 200) {
                  this.launchPlanDialog = false;
                  this.$modal.msgSuccess("预案启动成功！");
                  showPlan({
                    businessTypeId: this.eventDetail.businessTypeId,
                  }).then((res) => {
                    res.data.forEach((item) => {
                      item.responseName = this.dict.type.response_level.find(
                        (ele) => ele.value == item.responseLevel
                      ).label;
                    });
                    this.planBox = res.data;
                  });
                }
              });
            } else {
              console.log("error submit!!");
              return false;
            }
          });
        } else {
          this.$modal.msgWarning("请先选择预案！");
        }
      },
      // 开始处置or结束步骤
      processUpdate(id, status) {
        stepStatus({ id: id, processStatus: status }).then((res) => {
          if (status == 5011802) {
            this.$modal.msgSuccess("当前流程开始处置！");
          } else {
            this.$modal.msgSuccess("当前流程结束步骤！");
          }
          this.planClick(this.planRow);
        });
      },
      // 指派任务
      assign(ele) {
        if (this.$refs.assignFrom) {
          this.$refs.assignFrom.resetFields();
        }
        this.assignFrom.id = ele.id;
        this.assignFrom.contingentId = ele.contingentId;
        this.assignDialog = true;
      },
      // 完成任务
      finish(ele, status) {
        this.$modal
          .confirm("是否确认完成当前指派")
          .then(function () {
            return contingentUpdate({
              id: ele.id,
              contingentId: ele.contingentId,
              contingentStatus: 5011903,
            });
          })
          .then(() => {
            this.planClick(this.planRow);
            this.$modal.msgSuccess("指派已完成!");
          })
          .catch((error) => {});
      },
      // 指派任务提交
      assignConfirm() {
        this.$refs["assignFrom"].validate((valid) => {
          if (valid) {
            this.assignFrom.contingentStatus = "5011902";
            contingentUpdate(this.assignFrom).then((res) => {
              if (res.code == 200) {
                this.assignDialog = false;
                this.$modal.msgSuccess("任务指派成功！");
                this.planClick(this.planRow);
              }
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      },
      // 增派临时任务
      sendTask() {
        this.sendTitle = "增派临时任务";
        this.sendFlag = "rank";
        this.rankDisabled = false;
        this.goodDisabled = false;
        // if (this.$refs.sendrankFrom) {
        //   this.$refs.sendrankFrom.resetFields();
        // }
        // if (this.$refs.sendgoodFrom) {
        //   this.$refs.sendgoodFrom.resetFields();
        // }
        this.sendrankFrom = {
          contingentId: undefined,
          taskRemark: undefined,
          finishTime: undefined,
          isInform: undefined,
        };
        this.sendgoodFrom = {
          depotId: undefined,
          taskRemark: undefined,
          finishTime: undefined,
          isInform: undefined,
        };
        this.goodsData = [];
        this.sendDialog = true;
        contingentList().then((res) => {
          this.ranksOptions = res.data;
        });
        depotList().then((res) => {
          this.depotOptions = res.data;
        });
      },
      // 仓库变更查询物资
      depotChange(value) {
        this.goodsData = [];
        listOfDepot({ supplyDepotId: value }).then((res) => {
          this.goodsData = res.data;
          this.goodsData.forEach((item) => {
            this.$set(item, "dispatchQuantity", "");
          });
        });
      },
      // numberLimit(value, row) {
      //   console.log(value);
      //   console.log(row.inventory);
      //   if (parseInt(value) < 0) {
      //     value = 0;
      //   } else if (parseInt(value) > parseInt(row.inventory)) {
      //     value = parseInt(row.inventory);
      //   }
      // },
      // 增派临时任务确认
      sendConfirm() {
        if (this.sendFlag == "rank") {
          this.$refs["sendrankFrom"].validate((valid) => {
            if (valid) {
              this.ranksOptions.forEach((item) => {
                if (this.sendrankFrom.contingentId == item.id) {
                  this.sendrankFrom.phone = item.phone;
                }
              });
              taskSave({
                businessTypeId: this.eventDetail.businessTypeId,
                contingentVo: this.sendrankFrom,
                taskType: 5012401,
              }).then((res) => {
                if (res.code == 200) {
                  this.$modal.msgSuccess("队伍指派成功！");
                  this.sendDialog = false;
                  temporaryTasklist({
                    businessTypeId: this.eventDetail.businessTypeId,
                  }).then((res) => {
                    res.data.forEach((item) => {
                      if (item.depotVo) {
                        item = Object.assign(item, item.depotVo);
                      } else if (item.contingentVo) {
                        item = Object.assign(item, item.contingentVo);
                      }
                    });
                    // console.log(res.data);
                    this.tasksData = res.data;
                  });
                }
              });
            } else {
              console.log("error submit!!");
              return false;
            }
          });
        } else {
          this.$refs["sendgoodFrom"].validate((valid) => {
            if (valid) {
              this.sendgoodFrom.materialVos = [];
              this.goodsData.forEach((item) => {
                if (item.dispatchQuantity) {
                  this.sendgoodFrom.materialVos.push({
                    materialId: item.id,
                    dispatchQuantity: item.dispatchQuantity,
                  });
                }
              });
              taskSave({
                businessTypeId: this.eventDetail.businessTypeId,
                depotVo: this.sendgoodFrom,
                taskType: 5012402,
              }).then((res) => {
                if (res.code == 200) {
                  this.$modal.msgSuccess("物资调度成功！");
                  this.sendDialog = false;
                  temporaryTasklist({
                    businessTypeId: this.eventDetail.businessTypeId,
                  }).then((res) => {
                    res.data.forEach((item) => {
                      if (item.depotVo) {
                        item = Object.assign(item, item.depotVo);
                      } else if (item.contingentVo) {
                        item = Object.assign(item, item.contingentVo);
                      }
                    });
                    // console.log(res.data);
                    this.tasksData = res.data;
                  });
                }
              });
            } else {
              console.log("error submit!!");
              return false;
            }
          });
        }
      },
      // 临时任务详情
      handleTaskDetail(row) {
        this.sendTitle = "临时任务详情";
        contingentList().then((res) => {
          this.ranksOptions = res.data;
        });
        depotList().then((res) => {
          this.depotOptions = res.data;
        });
        taskDetail({ id: row.id, taskType: row.taskType }).then((res) => {
          // console.log(res.data);
          this.rankDisabled = true;
          this.goodDisabled = true;
          // 队伍
          if (row.taskType == 5012401) {
            this.sendFlag = "rank";
            this.sendDialog = true;
            this.sendrankFrom = res.data.contingentVo;
            this.sendrankFrom.contingentId = this.sendrankFrom.contingentId.toString();
          } else {
            this.sendFlag = "good";
            this.sendDialog = true;
            this.sendgoodFrom = res.data.depotVo;
            this.sendgoodFrom.depotId = this.sendgoodFrom.depotId.toString();
            // this.depotChange(this.sendgoodFrom.depotId);
            this.goodsData = res.data.depotVo.materialVos;
          }
        });
      },
      // 临时任务完成or取消
      handleTaskUpdate(row, status) {
        if (status == 5012602) {
          this.$modal
            .confirm("是否确认完成当前任务")
            .then(function () {
              return taskUpdate({ id: row.id, taskStatus: status });
            })
            .then(() => {
              this.$modal.msgSuccess("任务已完成!");
              temporaryTasklist({
                businessTypeId: this.eventDetail.businessTypeId,
              }).then((res) => {
                res.data.forEach((item) => {
                  if (item.depotVo) {
                    item = Object.assign(item, item.depotVo);
                  } else if (item.contingentVo) {
                    item = Object.assign(item, item.contingentVo);
                  }
                });
                this.tasksData = res.data;
              });
            })
            .catch((error) => {});
        } else if (status == 5012603) {
          this.$modal
            .confirm("是否确认取消当前任务")
            .then(function () {
              return taskUpdate({ id: row.id, taskStatus: status });
            })
            .then(() => {
              this.$modal.msgSuccess("任务已取消!");
              temporaryTasklist({
                businessTypeId: this.eventDetail.businessTypeId,
              }).then((res) => {
                res.data.forEach((item) => {
                  if (item.depotVo) {
                    item = Object.assign(item, item.depotVo);
                  } else if (item.contingentVo) {
                    item = Object.assign(item, item.contingentVo);
                  }
                });
                // console.log(res.data);
                this.tasksData = res.data;
              });
            })
            .catch((error) => {});
        }
      },
      // 执行记录按钮
      handleRecord() {
        recordDetail({ businessTypeId: this.eventDetail.businessTypeId }).then((res) => {
          console.log(res.data);
          this.recordDialog = true;
          this.executionRecord = res.data;
          this.eventReport = res.data.eventReport;
        });
      },
      /** 搜索按钮操作 */
      handleQuery() {
        if (this.dateRange.length > 0) {
          this.queryParams.startTime = this.dateRange[0];
          this.queryParams.endTime = this.dateRange[1];
        }
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.queryParams = {
          current: 1,
          size: 10,
          eventNo: undefined,
          eventName: undefined,
          eventTypeName: undefined,
          eventTypeId: undefined,
          eventLevel: undefined,
          startTime: "",
          endTime: "",
        };
        this.resetForm("queryForm");
        this.handleQuery();
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .left_title {
    color: rgba(56, 56, 56, 1);
    font-size: 24px;
    font-weight: bold;
    padding-bottom: 14px;
  }
  
  ::v-deep.el-table .el-table__header-wrapper th {
    background: rgba(25, 159, 255, 0.15);
    font-family: Noto Sans SC;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #007baf;
  }
  
  .clearfix:after,
  .clearfix:before {
    display: table;
    content: "";
  }
  
  .clearfix:after {
    clear: both;
  }
  
  .box-card-bottom {
    margin: 20px;
  }
  
  .box-card {
    margin-bottom: 20px;
    z-index: 2;
  }
  
  .tag {
    margin-right: 10px;
  }
  
  ::v-deep .el-descriptions__title {
    font-weight: normal;
  }
  
  .queryBtnT {
    height: 32px;
    border: 1px solid #cccccc;
    border-radius: 2px;
    font-size: 13px;
    float: right;
    margin-right: 10px;
  }
  
  .planList {
    height: 54vh;
    border-right: 1px solid #a8a8a8;
    .plan {
      width: 96%;
      height: 11vh;
      margin-top: 1vh;
      padding: 5px 10px;
      border: 2px solid #d6d6d6;
      cursor: pointer;
    }
  }
  
  .stepBox {
    height: 54vh;
    padding: 0 10px;
    overflow-y: auto;
    .step {
      margin-bottom: 20px;
      span {
        font-size: 20px;
      }
      div {
        margin-top: 5px;
      }
      .rank {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  
  .mapBox {
    position: relative;
    .eventListBox {
      width: 15vw;
      height: 600px;
      background: #fff;
      position: absolute;
      z-index: 2;
      border-radius: 12px;
      overflow: hidden;
      .boxContent {
        width: 100%;
        height: 560px;
        overflow-y: auto;
        padding: 15px 5%;
        .event {
          width: 100%;
          background: #ecf2ff;
          margin-bottom: 10px;
          padding: 6px 20px;
          cursor: pointer;
          div {
            margin-bottom: 5px;
          }
        }
        .name {
          color: #91959b;
        }
      }
    }
    .boxTitle {
      width: 100%;
      height: 40px;
      background: linear-gradient(115deg, #aec1fb 0%, #386bf0 100%);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      .content {
        height: 16px;
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        color: #ffffff;
        line-height: 16px;
        border-left: 3px solid #fff;
        padding-left: 10px;
      }
    }
    .eventDetailBox {
      width: 30%;
      height: 360px;
      border-radius: 12px;
      background: #fff;
      overflow: hidden;
      position: absolute;
      left: 30%;
      top: 220px;
      z-index: 1;
      .detailContent {
        padding: 20px 30px;
        .eventDetail {
          div {
            margin-bottom: 15px;
          }
          .name {
            color: #91959b;
          }
        }
        .btnBox {
          width: 90%;
          text-align: center;
          position: absolute;
          bottom: 20px;
        }
      }
    }
    .searchBox {
      width: 20vw;
      height: 500px;
      overflow-y: auto;
      background: #fff;
      position: absolute;
      top: 80px;
      right: 40px;
      z-index: 2;
      border-radius: 12px;
      .searchContent {
        padding: 20px 15px;
        .searchTitle {
          height: 15px;
          line-height: 15px;
          border-left: 3px solid black;
          padding-left: 10px;
        }
        .rankAndgood {
          height: 20vh;
          overflow-y: auto;
          margin-top: 20px;
        }
        .el-radio-button {
          margin-right: 10px;
          border-radius: 3px;
        }
        .el-radio-button:not(:first-child) {
          border-left: 1px solid #dcdfe6;
        }
        .el-checkbox {
          margin-bottom: 20px;
        }
        .dispatch {
          background: #d9e1ff;
          padding: 0 10px;
          margin-bottom: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .rankName {
            font-size: 14px;
            color: #3c6ef1;
          }
        }
      }
    }
  }
  
  ::v-deep input[type="number"]::-webkit-inner-spin-button,
  ::v-deep input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  ::v-deep .el-timeline-item__timestamp.is-top {
    font-size: 20px;
    font-weight: 600;
  }
  
  .option {
    height: auto;
    line-height: 1;
    padding: 0;
    background-color: #fff;
  }
  
  .tree {
    padding: 4px 20px;
    font-weight: 400;
  }
  ::v-deep .el-form-item__label {
    width: 100px;
    height: 32px;
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 32px;
    text-align: right;
    color: #333;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: auto;
  }
  </style>
  