<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-position="left" style="display: flex; justify-content: space-between">
            <div>
              <el-form-item label="演练名称">
                <el-input v-model.number="queryParams.name" maxlength="20" placeholder="请输入演练名称" clearable style="width: 10vw"
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="演练时间">
                <el-date-picker v-model="dateRange" style="width: 10vw" format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss" type="daterange" range-separator="-" start-placeholder="开始日期"
                  end-placeholder="结束日期"></el-date-picker>
              </el-form-item>
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button class="resetQueryStyle" type="primary" icon="el-icon-search" size="mini"
                  @click="handleQuery">搜索</el-button>
                <el-button class="resetQueryStyle" icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>演练评估展示列表</span>
          </div>
          <el-table v-loading="loading" :data="shelter" :cell-style="{ padding: '0px' }" :row-style="{ height: '48px' }">
            <el-table-column label="应急演练名称" align="center" prop="drillName" />
            <el-table-column label="演练目标" align="center" prop="drillTarget" />
            <el-table-column label="关联预案" align="center" prop="planName" />
            <el-table-column label="演练时间" align="center" prop="drillTime" />
            <el-table-column label="评估状态" align="center" prop="evaluationStatus">
              <template slot-scope="scope">
                <div>
                  {{
                    scope.row.evaluationStatus
                    ? dict.type.evaluation_status.find(
                      (ele) => ele.value == scope.row.evaluationStatus
                    ).label
                    : ""
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button v-if="scope.row.evaluationStatus == 5013101" type="text" icon="el-icon-view"
                  @click="handleLook(scope.row)">详情</el-button>
                <el-button v-if="scope.row.evaluationStatus == 5013102" type="text" icon="el-icon-edit"
                  @click="handleUpdate(scope.row)">评估</el-button>
                <el-button v-if="scope.row.fileUrl" type="text" icon="el-icon-download"
                  @click="handledownload(scope.row)">下载附件</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
            @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改避难所信息对话框 -->
    <el-dialog :title="title" :visible.sync="abilityOpen" width="560px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form ref="abilityForm" :model="abilityForm" :rules="abilityRules" label-width="110px">
            <el-form-item label="演练名称">
              {{ row.drillName }}
            </el-form-item>
            <el-form-item label="演练目标">
              {{ row.drillTarget }}
            </el-form-item>
            <el-form-item label="应急预案">
              {{ row.planName }}
            </el-form-item>
            <el-form-item label="复盘结论" prop="reviewConclusion">
              <el-input v-model="abilityForm.reviewConclusion" placeholder="请输入复盘结论" maxlength="200" type="textarea"
                :disabled="disabled" style="width: 245px" />
            </el-form-item>
            <el-form-item label="是否修订预案" prop="isRevise">
              <el-radio :disabled="disabled" v-model="abilityForm.isRevise" label="0">是</el-radio>
              <el-radio :disabled="disabled" v-model="abilityForm.isRevise" label="1">否</el-radio>
            </el-form-item>
            <el-form-item label="是否通知" prop="isNotice">
              <el-radio :disabled="disabled" v-model="abilityForm.isNotice" label="0">是</el-radio>
              <el-radio :disabled="disabled" v-model="abilityForm.isNotice" label="1">否</el-radio>
            </el-form-item>
            <el-form-item label="附件 :">
              <el-upload class="upload-demo" :action="uploadImgUrl" :on-success="handleAvatarSuccess"
                :file-list="fileList" :disabled="disabled" :on-remove="handleRemove" v-model="abilityForm.fileUrl"
                :limit="1" :headers="headers" :on-exceed="handleExceed" :before-upload="beforeAvatarUpload">
                <el-button :disabled="disabled" size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip"><div slot="tip" class="el-upload__tip">支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB</div></div>
              </el-upload>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div v-show="!disabled" slot="footer" class="dialog-footer">
        <el-button class="popupButton" type="primary" @click="confirm('abilityForm')" :disabled="disabled">确 定</el-button>
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
    
<script>
import {
  page,
  detail,
  update,
  handledownload
} from "@/api/emergency/emergencyDrill/drillEvaluate/index.js";
export default {
  name: "EmergencySupplies",
  dicts: ["evaluation_status"],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      shelter: null,
      // 是否显示弹出层
      abilityOpen: false,
      title: "",
      text: undefined,
      imgUrl: `${require("@/assets/images/map.png")}`,
      dateRange: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        name: undefined,
        hygieneType: undefined,
      },
      row: {},
      abilityForm: {},
      disabled: false,
      // 表单校验
      abilityRules: {
        reviewConclusion: [
          { required: true, message: "复盘结论不能为空", trigger: "blur" },
        ],
        isRevise: [
          { required: true, message: "请选择是否修订", trigger: "blur" },
        ],
        isNotice: [
          { required: true, message: "请选择是否通知", trigger: "blur" },
        ],
      },
      // 文件上传部分内容
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/emergency-v2/file/uploadFile",
      fileList: [],
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    /** 查询场所列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        console.log(response);
        if (response.data != null) {
          this.shelter = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    beforeAvatarUpload(file) {
      console.log(file);
      let array=["jpeg","jpg","png","gif","bmp","tiff","webp","svg","mp4","avi","mkv","mov","wmv",
      "flv","webm","mpeg","mp3","wav","aac","flac","ogg","wma","pdf","word","excel","txt","doc","docx","xlsx","xls","pptx","ppt"]
      let type=file.name.split('.')
      const isLt2M = file.size / 1024 / 1024 < 100;
      const isType=array.indexOf(type[1])==-1
      console.log(isType);
      if(isType)
       {
        this.$message.error("仅支持 jpeg|jpg|png|gif|bmp|tiff|webp|svg|mp4|avi|mkv|mov|wmv|flv|webm|mpeg|mp3|wav|aac|flac|ogg|wma|pdf|word|excel|txt|doc|docx|xlsx|xls|pptx|ppt| 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传附件大小不能超过 100MB!");
      }
      return !isType && isLt2M;
    },
    //获取演练详情
    handleLook(row) {
      this.reset();
      this.abilityOpen = true;
      this.row = row;
      this.abilityForm = JSON.parse(JSON.stringify(row));
      this.title = "评估详情";
      this.disabled = true;
      // detail({ id: row.drillAssessId }).then((res) => {
      //   console.log(res.data);
      // });
      let array = [];
      if (row.fileUrl != "") {
        array = row.fileUrl.split("/");
        this.fileList.push({
          name: array[array.length - 1],
          url: row.fileUrl,
        });
      }
    },
    // 附件下载
    handledownload(row) {
      const _this=this
      this.$modal
        .confirm("是否确认下载附件")
        .then(function () {
          let arr1 = row.fileUrl.split(",");
          arr1.map((res) => {
            let arr1 = row.fileUrl.split(",");
            console.log(arr1);
            arr1.map((res) => {
              let arr = res.split("/");
              handledownload(arr).then(async res => {
                _this.handledownloadGet(arr, res)
              })
            });
          });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("下载成功");
        })
        .catch((error) => { });
    },
    handleUpdate(row) {
      this.reset();
      this.row = row;
      this.abilityOpen = true;
      this.title = "演练评估";
      this.disabled = false;
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    /*  确认保存新增*/
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.abilityForm.id = this.row.drillAssessId;
          this.abilityForm.drillName = this.row.drillName;
          this.abilityForm.drillPlanId = this.row.drillPlanId;
          this.abilityForm.evaluationStatus = 5013101;
          update(this.abilityForm).then((response) => {
            // console.log(response, "编辑");
            if (response.code == 200) {
              this.$modal.msgSuccess("评估成功！");
              this.abilityOpen = false;
              this.getList();
            }
          });
        }
      });
      // console.log(this.evaluateData, "evaluateData");
    },
    // 附件上传涉及代码
    handleAvatarSuccess(response, res, file) {
      console.log(response, res, file);
      this.abilityForm.fileUrl = response;
      this.abilityForm.fileName = res.name;
    },
    handleRemove(file, fileList) {
      this.abilityForm.fileUrl = "";
      this.abilityForm.fileName = "";
    },
    handleExceed() {
      this.$modal.msgSuccess("请不要上传多个文件");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.queryParams.startTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];
      this.getList();
    },
    // 表单重置
    reset() {
      this.abilityForm = {};
      this.fileList = [];
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        current: 1,
        size: 10,
      };
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
  float: right;
  margin-right: 10px;
}

.resetQueryStyle {
//   width: 88px;
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}
::v-deep .el-form-item__label{
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>