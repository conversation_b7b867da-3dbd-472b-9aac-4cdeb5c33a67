<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            label-position="left"
            style="display: flex; justify-content: space-between"
          >
            <div style="width: 80%">
              <el-form-item label="演练名称">
                <el-input
                  v-model="queryParams.planName"
                  placeholder="请输入演练名称"
                  clearable
                  maxlength="20"
                  style="width: 10vw"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="演练类型">
                <el-select
                  style="width: 10vw"
                  v-model="queryParams.drillType"
                  placeholder="请选择演练类型"
                  :disabled="disabled"
                >
                  <el-option
                    v-for="dict in dict.type.drill_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="演练状态">
                <el-select
                  style="width: 10vw"
                  v-model="queryParams.drillStatus"
                  placeholder="请选择演练状态"
                >
                  <el-option
                    v-for="dict in dict.type.drill_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="演练时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 10vw"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </div>
            <div style="min-width: 15%">
              <el-form-item>
                <el-button
                  class="resetQueryStyle"
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  class="resetQueryStyle"
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>计划管理展示列表</span>
            <el-button
              type="primary"
              
              size="mini"
              @click="handleAdd"
              icon="el-icon-plus"
              class="queryBtnT"
              >新增应急演练计划</el-button
            >
          </div>
          <el-table
            v-loading="loading"
            :data="shelter"
            :cell-style="{ padding: '0px' }"
            :row-style="{ height: '48px' }"
          >
            <el-table-column
              label="应急演练名称"
              align="center"
              prop="planName"
              show-overflow-tooltip
            />
            <el-table-column label="演练类型" align="center">
              <template slot-scope="scope">
                {{
                  dict.type.drill_type.find((ele) => ele.value == scope.row.drillType)
                    .label
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="演练目标"
              align="center"
              prop="drillTarget"
              show-overflow-tooltip
            />
            <el-table-column
              label="关联预案"
              align="center"
              prop="externalPlanName"
              show-overflow-tooltip
            />
            <el-table-column label="演练时间" align="center" prop="drillTime" />
            <el-table-column label="演练状态" align="center">
              <template slot-scope="scope">
                <el-tag
                  :type="
                    scope.row.drillStatus == 5011101
                      ? 'danger'
                      : scope.row.drillStatus == 5011102
                      ? 'warning'
                      : 'success'
                  "
                  >{{ getNameById(scope.row.drillStatus) }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="220"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  icon="el-icon-view"
                  @click="handleLook(scope.row)"
                  >详情</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  v-if="scope.row.drillStatus == '5011101'"
                  @click="handleUpdate(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.current"
            :limit.sync="queryParams.size"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改应急事件对话框 -->
    <el-dialog :title="title" :visible.sync="abilityOpen" width="960px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form
            ref="abilityForm"
            :model="abilityForm"
            :rules="abilityRules"
            label-width="110px"
          >
            <el-row>
              <el-col :span="24">演练基本信息</el-col>
              <el-col :span="12">
                <el-form-item label="演练名称" prop="planName">
                  <el-input
                    v-model="abilityForm.planName"
                    placeholder="请输入演练名称"
                    :disabled="disabled"
                    maxlength="20"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="演练类型" prop="drillType">
                  <el-select
                    style="width: 245px"
                    v-model="abilityForm.drillType"
                    placeholder="请选择演练类型"
                    :disabled="disabled"
                  >
                    <el-option
                      v-for="dict in dict.type.drill_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="演练目标" prop="drillTarget">
                  <el-input
                    v-model="abilityForm.drillTarget"
                    placeholder="请输入演练目标"
                    maxlength="20"
                    :disabled="disabled"
                    style="width: 90%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件类型" prop="eventTypeName">
                  <el-select
                    style="width: 245px"
                    placeholder="请选择事件类型"
                    v-model="abilityForm.eventTypeName"
                    :disabled="disabled"
                  >
                    <el-option :value="abilityForm.eventTypeName" class="option">
                      <el-tree
                        :data="AddtreeData"
                        :show-checkbox="true"
                        node-key="id"
                        :props="defaultProps"
                        class="tree"
                        @check="handleNodeClick"
                        ref="Addtree"
                      >
                      </el-tree>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件等级" prop="eventLevel">
                  <el-select
                    style="width: 245px"
                    v-model="abilityForm.eventLevel"
                    placeholder="请选择事件等级"
                    :disabled="disabled"
                  >
                    <el-option
                      v-for="dict in dict.type.event_level"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="关联预案" prop="planId">
                  <el-select
                    style="width: 245px"
                    v-model="abilityForm.planId"
                    placeholder="请选择关联预案"
                    :disabled="disabled"
                  >
                    <el-option
                      v-for="dict in planOptions"
                      :key="dict.id"
                      :label="dict.planName"
                      :value="dict.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="响应等级" prop="responseLevelId">
                  <el-select
                    style="width: 245px"
                    v-model="abilityForm.responseLevelId"
                    placeholder="请选择响应等级"
                    :disabled="disabled"
                  >
                    <el-option
                      v-for="dict in levelOptions"
                      :key="dict.id"
                      :label="dict.responseName"
                      :value="dict.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="演练时间" prop="drillTime">
                  <el-date-picker
                    v-model="abilityForm.drillTime"
                    type="datetime"
                    placeholder="选择日期"
                    :disabled="disabled"
                    style="width: 245px"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经纬度 :" prop="lngAndLat">
                  <el-button
                    type="primary"
                    plain
                    @click="openMap()"
                    :disabled="disabled"
                    >{{ lngAndLat ? lngAndLat : "点击选择" }}</el-button
                  >
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="其他备注" prop="otherRemark">
                  <el-input
                    v-model="abilityForm.otherRemark"
                    placeholder="请输入其他备注"
                    :disabled="disabled"
                    maxlength="200"
                    style="width: 90%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">初步研判</el-col>
              <el-col :span="12">
                <el-form-item label="经济损失" prop="economicLoss">
                  <el-input
                    v-model="abilityForm.economicLoss"
                    placeholder="请输入经济损失"
                    :disabled="disabled"
                    style="width: 245px"
                    maxlength="20"
                    oninput="value=value.match(/\d+\.?\d{0,2}/,'')"
                  />（万元）
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受灾面积" prop="disasterArea">
                  <el-input
                    v-model="abilityForm.disasterArea"
                    placeholder="请输入受灾面积"
                    maxlength="20"
                    :disabled="disabled"
                    style="width: 245px"
                    oninput="value=value.match(/\d+\.?\d{0,2}/,'')"
                  />公顷
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="死亡人数" prop="deathNumber">
                  <el-input
                    v-model="abilityForm.deathNumber"
                    placeholder="请输入死亡人数"
                    maxlength="20"
                    :disabled="disabled"
                    style="width: 245px"
                    type="number"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受伤人数" prop="injuredNumber">
                  <el-input
                    v-model="abilityForm.injuredNumber"
                    placeholder="请输入受伤人数"
                    maxlength="20"
                    :disabled="disabled"
                    style="width: 245px"
                    type="number"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="失踪人数" prop="missingNumber">
                  <el-input
                    v-model="abilityForm.missingNumber"
                    placeholder="请输入失踪人数"
                    maxlength="20"
                    :disabled="disabled"
                    style="width: 245px"
                    type="number"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受困人数" prop="trappedNumber">
                  <el-input
                    v-model="abilityForm.trappedNumber"
                    placeholder="请输入受困人数"
                    maxlength="20"
                    :disabled="disabled"
                    style="width: 245px"
                    type="number"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item  label="演练方案附件 :">
                  <el-upload
                    class="upload-demo"
                    :action="uploadImgUrl"
                    :on-success="handleAvatarSuccess"
                    :file-list="fileList"
                    :disabled="disabled"
                    :on-remove="handleRemove"
                    :before-upload="beforeAvatarUpload"
                    v-model="abilityForm.attachmentAddress"
                    :limit="1"
                    :headers="headers"
                    :on-preview="handledownload"
                    :on-exceed="handleExceed"
                  >
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip"><div slot="tip" class="el-upload__tip">支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB</div></div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
      <div v-show="!disabled" slot="footer" class="dialog-footer">
        <el-button
          class="popupButton"
          type="primary"
          @click="confirm('abilityForm')"
          :disabled="disabled"
          >确 定
        </el-button>
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 地图展示 -->
    <Map
      @mapConfirm="mapConfirm"
      :ranksForm="abilityForm"
      :disabled="disabled"
      :mapVisible="mapVisible"
      @mapCancellation="mapCancellation"
      ref="mapRef"
      :url="url"
    ></Map>
  </div>
</template>

<script>
import {
  page,
  getTree,
  plansOfEvent,
  levelList,
  save,
  update,
  deleteById,
  drillDetail,
  handledownload,
} from "@/api/emergency/emergencyDrill/planManage/index";
import Map from "../../../map/index.vue";
export default {
  name: "EmergencySupplies",
  dicts: [
    "event_level",
    "event_state",
    "drill_type",
    "drill_status",
    "response_level",
    "drill_status",
  ],
  components: { Map },
  data() {
    const validCode = (rule, value, callback) => {
      // console.log(rule, value, "value");
      if (this.lngAndLat) {
        callback();
      } else {
        callback(new Error("请选择经纬度"));
      }
    };
    return {
      // 地图点标记图标地址
      url: require("../../../../assets/icons/eventManagement.png"),
      container: {},
      // 地图遮罩层
      mapVisible: false,
      lngAndLat: "",
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      shelter: null,
      // 是否显示弹出层
      abilityOpen: false,
      title: "",
      text: undefined,
      imgUrl: `${require("@/assets/images/map.png")}`,
      dateRange: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        planName: undefined,
        drillType: undefined,
        drillStatus: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      // 事件类型下的预案
      planOptions: [],
      levelOptions: [],
      abilityForm: {
        eventTypeId: undefined,
        eventTypeName: undefined,
        eventLabel: undefined,
      },
      disabled: false,
      // 表单校验
      abilityRules: {
        planName: [{ required: true, message: "演练名称不能为空", trigger: "blur" }],
        drillType: [{ required: true, message: "演练类型不能为空", trigger: "blur" }],
        drillTarget: [{ required: true, message: "演练目标不能为空", trigger: "blur" }],
        eventTypeName: [{ required: true, message: "请选择事件类型", trigger: "blur" }],
        eventLevel: [{ required: true, message: "请选择事件等级", trigger: "blur" }],
        planId: [{ required: true, message: "请选择关联预案", trigger: "blur" }],
        // responseLevelId: [
        //   { required: true, message: "请选择响应等级", trigger: "blur" },
        // ],
        drillTime: [{ required: true, message: "请选择演练时间", trigger: "blur" }],
        lngAndLat: [{ required: true, validator: validCode, trigger: "blur" }],
      },
      AddtreeData: [],
      defaultProps: {
        children: "children",
        label: "nodeName",
      },
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/emergency-v2/file/uploadFile",
      fileList: [],
      fileNameData: [],
      fileUrl: undefined,
      fileName: undefined,
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.getTree();
    this.getList();
  },
  methods: {
    /** 查询场所列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        console.log(response, "表格数据");
        if (response.data != null) {
          this.shelter = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    handledownload(row) {
      console.log(row);
      const _this = this;
      let arr1 = [];
      if (this.title == "查看演练计划"||this.title == "应急演练计划编辑") {
        arr1 = row.url.split(",");
      } else {
        arr1 = row.response.split(",");
      }
      arr1.map((res) => {
        let arr = res.split("/");
        handledownload(arr).then(async (res) => {
          _this.handledownloadGet(arr, res);
        });
      });
    },
    beforeAvatarUpload(file) {
      console.log(file);
      let array = [
        "jpeg",
        "jpg",
        "png",
        "gif",
        "bmp",
        "tiff",
        "webp",
        "svg",
        "mp4",
        "avi",
        "mkv",
        "mov",
        "wmv",
        "flv",
        "webm",
        "mpeg",
        "mp3",
        "wav",
        "aac",
        "flac",
        "ogg",
        "wma",
        "pdf",
        "word",
        "excel",
        "txt",
        "doc",
        "docx",
        "xlsx",
        "xls",
        "pptx",
        "ppt",
      ];
      let type = file.name.split(".");
      const isLt2M = file.size / 1024 / 1024 < 100;
      const isType = array.indexOf(type[1]) == -1;
      console.log(isType);
      if (isType) {
        this.$message.error(
          "仅支持 jpeg|jpg|png|gif|bmp|tiff|webp|svg|mp4|avi|mkv|mov|wmv|flv|webm|mpeg|mp3|wav|aac|flac|ogg|wma|pdf|word|excel|txt|doc|docx|xlsx|xls|pptx|ppt| 格式!"
        );
      }
      if (!isLt2M) {
        this.$message.error("上传附件大小不能超过 100MB!");
      }
      return !isType && isLt2M;
    },
    //获取场所详情
    handleLook(row) {
      this.reset();
      this.abilityOpen = true;
      drillDetail({ id: row.id }).then((res) => {
        this.abilityForm = JSON.parse(JSON.stringify(res.data));
        this.abilityForm.planId = this.abilityForm.planId.toString();
        plansOfEvent({ eventTypeId: res.data.eventTypeId }).then((res) => {
          this.planOptions = res.data;
        });
        levelList({ planId: res.data.planId }).then((res) => {
          res.data.forEach((item) => {
            item.responseName = this.dict.type.response_level.find(
              (ele) => ele.value == item.responseLevel
            ).label;
          });
          this.levelOptions = res.data;
        });
        console.log(res,'查看演练计划');
        this.title = "查看演练计划";
        this.disabled = true;
        this.lngAndLat = res.data.longitude + "," + res.data.latitude;
        let array = [];
        if (res.data.attachmentUrl) {
          array = res.data.attachmentUrl.split("/");
          this.fileList.push({
            name: array[array.length - 1],
            url: res.data.attachmentUrl,
          });
        }
      });
    },
    handleUpdate(row) {
      this.reset();
      this.abilityOpen = true;
      drillDetail({ id: row.id }).then((res) => {
        this.abilityForm = JSON.parse(JSON.stringify(res.data));
        this.abilityForm.planId = this.abilityForm.planId.toString();
        plansOfEvent({ eventTypeId: res.data.eventTypeId }).then((res) => {
          this.planOptions = res.data;
        });
        levelList({ planId: res.data.planId }).then((res) => {
          res.data.forEach((item) => {
            item.responseName = this.dict.type.response_level.find(
              (ele) => ele.value == item.responseLevel
            ).label;
          });
          this.levelOptions = res.data;
        });
        this.title = "应急演练计划编辑";
        this.disabled = false;
        this.lngAndLat = res.data.longitude + "," + res.data.latitude;
        let array = [];
        if (res.data.attachmentUrl) {
          array = res.data.attachmentUrl.split("/");
          this.fileList.push({
            name: array[array.length - 1],
            url: res.data.attachmentUrl,
          });
        }
      });
    },
    handleAdd() {
      if (this.$refs.abilityForm) {
        this.$refs.abilityForm.resetFields();
      }
      this.reset();
      this.abilityOpen = true;
      this.title = "应急演练计划新增";
      this.disabled = false;
      this.planOptions = [];
      this.levelOptions = [];
      this.getTree();
    },
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => {});
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    /*  确认保存新增*/
    confirm(formName) {
      // console.log(this.abilityForm, "新增数据");
      // console.log(this.fileList);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.abilityForm.id) {
            update(this.abilityForm).then((response) => {
              // console.log(response, "编辑");
              if (response.code == 200) {
                this.$modal.msgSuccess("编辑成功");
                this.abilityOpen = false;
                this.getList();
              }
            });
          } else {
            console.log(this.abilityForm);
            save(this.abilityForm).then((response) => {
              // console.log(response, "新增");
              if (response.code == 200) {
                this.$modal.msgSuccess("保存成功");
                this.abilityOpen = false;
                this.getList();
              }
            });
          }
        }
      });
      // console.log(this.evaluateData, "evaluateData");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.queryParams.startTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];
      this.getList();
    },
    // 取消按钮
    // 表单重置
    reset() {
      // console.log(111111111111111);
      this.abilityForm = {
        eventTypeId: undefined,
        eventTypeName: undefined,
      };
      this.lngAndLat = "";
      this.fileList = [];
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {
        current: 1,
        size: 10,
        planName: undefined,
        drillType: undefined,
        drillStatus: undefined,
        startTime: undefined,
        endTime: undefined,
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 打开地图按钮
    openMap() {
      this.mapVisible = true;
      this.$nextTick(() => {
        this.$refs.mapRef.initMap();
      });
    },
    // 地图返回经纬度的回调
    mapConfirm(lng, lat) {
      if (lng && lat) {
        this.mapVisible = false;
        this.lngAndLat = lng + "," + lat;
        this.abilityForm.longitude = lng;
        this.abilityForm.latitude = lat;
        // 获取到经纬度就取消验证提示
        this.$nextTick(() => {
          this.$refs.abilityForm.clearValidate();
        });
      } else {
        this.$modal.msgSuccess("请选择经纬度");
      }
    },
    // 取消地图的回调
    mapCancellation() {
      this.mapVisible = false;
    },
    getNameById(res) {
      // console.log(res, this.dict.type.event_level);
      if (res != undefined && res != "" && res != null) {
        return this.dict.type.drill_status.filter((item) => item.value == res)[0].label;
      }
    },
    getTree() {
      getTree().then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.recursion(res.data);
          this.AddtreeData = res.data;
        }
      });
    },
    // 通过递归给叶子节点添加只读属性
    recursion(data) {
      data.forEach((item, index) => {
        if (item.children) {
          item.disabled = true;
          return this.recursion(item.children);
        } else {
          item.disabled = false;
        }
      });
    },
    handleNodeClick(data, res, item) {
      // console.log(data, res, item, "树结构");
      this.abilityForm.eventLabel = undefined;
      if (!res) {
        this.abilityForm.eventTypeId = undefined;
        this.abilityForm.eventTypeName = undefined;
        // console.log(this.abilityForm);
      } else {
        if (this.title == "应急演练计划新增" || this.title == "应急演练计划编辑") {
          this.$refs.Addtree.setCheckedNodes([data]);
          this.abilityForm.eventTypeId = data.id;
          this.abilityForm.eventTypeName = data.nodeName;
          plansOfEvent({ eventTypeId: data.id }).then((res) => {
            this.planOptions = res.data;
          });
        }
      }
    },
    // 解决因为切换叶子节点清空值导致不能选择的问题
    eventLabelChange(res) {
      this.$forceUpdate();
    },
    // // 根据预案查询响应等级
    // planChange(value) {
    //   levelList({ planId: value }).then((res) => {
    //     res.data.forEach((item) => {
    //       item.responseName = this.dict.type.response_level.find(
    //         (ele) => ele.value == item.responseLevel
    //       ).label;
    //     });
    //     this.levelOptions = res.data;
    //   });
    // },
    handleAvatarSuccess(response, res, file) {
      console.log(response, res, file);
      this.abilityForm.attachmentUrl = res.response;
      this.abilityForm.fileName = res.name;
    },
    handleRemove(file, fileList) {
      this.abilityForm.attachmentUrl = "";
      this.abilityForm.fileName = "";
    },
    handleExceed() {
      this.$modal.msgSuccess("请不要上传多个文件");
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
//   font-size: 13px;
  float: right;
  margin-right: 10px;
}

.resetQueryStyle {
//   width: 88px;
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}

.option {
  height: auto;
  line-height: 1;
  padding: 0;
  background-color: #fff;
}

.tree {
  padding: 4px 20px;
  font-weight: 400;
}
::v-deep .el-form-item__label {
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>
