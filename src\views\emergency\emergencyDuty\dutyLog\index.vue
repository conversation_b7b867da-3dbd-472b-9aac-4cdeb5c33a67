<template>
    <div class="app-container">
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>数据筛选</span>
            </div>
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              label-position="left"
              style="display: flex; justify-content: space-between"
            >
              <div>
                <el-form-item label="值班日期" prop="name">
                  <el-date-picker
                    v-model="queryParams.dutyTime"
                    type="date"
                    placeholder="选择日期"
                    style="width: 245px"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="填写时间" prop="name">
                  <el-time-picker
                    v-model="queryParams.outTime"
                    placeholder="任意时间点"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    @change="outTimeChange"
                  >
                  </el-time-picker>
                </el-form-item>
                <el-form-item label="填写人">
                  <el-input
                    v-model="queryParams.outUser"
                    placeholder="请输入填写人"
                    clearable
                    maxlength="20"
                    style="width: 10vw"
                    @keyup.enter.native="handleQuery"
                  />
                </el-form-item>
              </div>
              <div style="min-width: 15%;text-align: right;">
                <el-form-item>
                  <el-button
                    class="resetQueryStyle"
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    class="resetQueryStyle"
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                </el-form-item>
              </div>
            </el-form>
          </el-card>
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>值班日志展示列表</span>
              <el-button
                type="primary"
                size="mini"
                @click="handleAdd"
                icon="el-icon-plus"
                class="queryBtnT"
                >新增值班日志</el-button
              >
            </div>
            <el-table
              v-loading="loading"
              :data="shelter"
              :cell-style="{ padding: '0px' }"
              :row-style="{ height: '48px' }"
            >
              <el-table-column label="序号" align="center">
                <template slot-scope="scope">
                  <span>{{
                    (queryParams.current - 1) * queryParams.size + scope.$index + 1
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="值班日期"
                align="center"
                prop="dutyTime"
                show-overflow-tooltip
              />
              <el-table-column
                label="填写时间"
                align="center"
                prop="outTime"
                show-overflow-tooltip
              />
              <el-table-column
                label="填写人"
                align="center"
                prop="outUser"
                show-overflow-tooltip
              />
              <el-table-column
                label="操作"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    icon="el-icon-view"
                    @click="handleLook(scope.row)"
                    >查看日志详情</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.current"
              :limit.sync="queryParams.size"
              @pagination="getList"
            />
          </el-card>
        </el-col>
      </el-row>
      <!--  -->
      <!-- 添加或修改值班日志对话框 -->
      <el-dialog :title="title" :visible.sync="abilityOpen" width="960px" append-to-body>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form
              ref="abilityForm"
              :model="abilityForm"
              :rules="abilityRules"
              label-width="110px"
            >
              <el-row>
                <el-col :span="12">
                  <el-form-item label="值班日期" prop="dutyTime">
                    <el-date-picker
                      v-model="abilityForm.dutyTime"
                      type="date"
                      placeholder="选择日期"
                      :disabled="disabled"
                      style="width: 245px"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="填写时间" prop="outTime">
                    <el-time-picker
                      v-model="abilityForm.outTime"
                      placeholder="任意时间点"
                      style="width: 245px"
                      format="HH:mm:ss"
                      value-format="HH:mm:ss"
                      :disabled="disabled"
                    >
                    </el-time-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label=" 填写人" prop="outUser">
                    <el-input
                      v-model="abilityForm.outUser"
                      placeholder="请输入填写人"
                      maxlength="20"
                      :disabled="disabled"
                      style="width: 245px"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="今日工作情况" prop="jobDescription">
                    <quill-editor
                      class="ql-editor"
                      v-model="abilityForm.jobDescription"
                      ref="myQuillEditor"
                    >
                    </quill-editor>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>
        <div slot="footer" class="dialog-footer">
          <el-button
            class="popupButton"
            type="primary"
            @click="confirm('abilityForm')"
            :disabled="disabled"
            >确 定</el-button
          >
          <el-button class="popupButton" @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import { page, save, getDetails } from "@/api/emergency/emergencyDuty/dutyLog/index";
  import Map from "../../../map/index.vue";
  import { quillEditor } from "vue-quill-editor";
  import "quill/dist/quill.core.css";
  import "quill/dist/quill.snow.css";
  import "quill/dist/quill.bubble.css";
  export default {
    name: "EmergencySupplies",
    dicts: ["event_level"],
    components: { Map, quillEditor },
    data() {
      return {
        // 遮罩层d
        loading: false,
        // 总条数
        total: 0,
        // 用户表格数据
        shelter: null,
        // 是否显示弹出层
        abilityOpen: false,
        title: "新增值班日志",
        text: undefined,
        // 查询参数
        queryParams: {
          current: 1,
          size: 10,
          dutyTime: undefined,
          outTime: undefined,
          outUser: undefined,
          jobDescription: undefined,
        },
        frequency: 0,
        abilityForm: {},
        disabled: false,
        // 表单校验
        abilityRules: {
          dutyTime: [{ required: true, message: "请选择值班日期", trigger: "blur" }],
          outTime: [{ required: true, message: "请选择填写时间", trigger: "change" }],
          outUser: [{ required: true, message: "填写人不能为空", trigger: "blur" }],
          jobDescription: [
            { required: true, message: "今日工作情况不能为空", trigger: "blur" },
          ],
        },
        nodeObj: undefined,
        defaultProps: {
          children: "children",
          label: "nodeName",
        },
        // 富文本编辑器配置
        editorOption: {
          modules: {
            toolbar: [
              ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
              ["blockquote", "code-block"], // 引用  代码块
              [{ header: 1 }, { header: 2 }], // 1、2 级标题
              [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
              [{ script: "sub" }, { script: "super" }], // 上标/下标
              [{ indent: "-1" }, { indent: "+1" }], // 缩进
              [{ direction: "rtl" }], // 文本方向
              [{ size: ["12px", false, "16px", "18px", "20px", "30px"] }], // 字体大小
              [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
              [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
              [
                {
                  font: [
                    false,
                    "SimSun",
                    "SimHei",
                    "Microsoft-YaHei",
                    "KaiTi",
                    "FangSong",
                    "Arial",
                  ],
                },
              ], // 字体种类
              [{ align: [] }], // 对齐方式
              ["clean"], // 清除文本格式
              ["link", "image", "video"], // 链接、图片、视频
            ],
          },
        },
      };
    },
    watch: {},
    created() {},
    mounted() {
      this.getList();
      // this.recursion(this.treeData)
    },
    methods: {
      /** 查询场所列表 */
      getList() {
        this.loading = true;
        page(this.queryParams).then((response) => {
          console.log(response, "表格数据");
          if (response.data != null) {
            this.shelter = response.data.records;
            this.total = response.data.total;
          }
          this.loading = false;
        });
      },
      //获取场所详情
      handleLook(row) {
        this.reset();
        this.abilityOpen = true;
        this.abilityForm = JSON.parse(JSON.stringify(row));
        this.title = "查看值班日志";
        this.disabled = true;
        this.lngAndLat = row.longitude + "," + row.latitude;
        console.log(this.abilityForm);
        let arr = this.abilityForm.node.split(",");
      },
      handleAdd() {
        this.reset();
        this.abilityOpen = true;
        this.title = "新增值班日志";
        this.disabled = false;
      },
      // 取消按钮
      cancel() {
        this.abilityOpen = false;
        this.reset();
      },
      /*  确认保存新增*/
      confirm(formName) {
        console.log(this.abilityForm);
        this.$refs[formName].validate((valid) => {
          if (valid) {
            save(this.abilityForm).then((response) => {
              // console.log(response, "新增");
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");
  
                this.abilityOpen = false;
                this.getList();
              }
            });
          }
        });
        // console.log(this.evaluateData, "evaluateData");
      },
  
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.current = 1;
        if (this.queryParams.dutyTime||(!this.queryParams.dutyTime&&!this.queryParams.outTime)) {
          this.getList();
        }
      },
  
      // 取消按钮
      // 表单重置
      reset() {
        this.abilityForm = {};
        this.lngAndLat = "";
        this.resetForm("abilityForm");
      },
  
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParams = {
          current: 1,
          size: 10,
          refugeName: undefined,
          liabilityUser: undefined,
          phone: undefined,
        };
        this.resetForm("queryForm");
        this.handleQuery();
      },
      outTimeChange() {
        if (!this.queryParams.dutyTime) {
          this.$modal.msgError("请先选择值班日期在选择填写时间");
        }
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .left_title {
    color: rgba(56, 56, 56, 1);
    font-size: 24px;
    font-weight: bold;
    padding-bottom: 14px;
  }
  
  ::v-deep.el-table .el-table__header-wrapper th {
    background: rgba(25, 159, 255, 0.15);
    font-family: Noto Sans SC;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #007baf;
  }
  
  .clearfix:after,
  .clearfix:before {
    display: table;
    content: "";
  }
  
  .clearfix:after {
    clear: both;
  }
  
  .box-card-bottom {
    margin: 20px;
  }
  
  .box-card {
    margin-bottom: 20px;
    z-index: 2;
  }
  
  .queryBtnT {
    // height: 32px;
    // border: 1px solid #cccccc;
    // border-radius: 2px;
    // font-size: 13px;
    float: right;
    margin-right: 10px;
  }
  
  .resetQueryStyle {
    // width: 88px;
    // height: 32px;
    // border: 1px solid #cccccc;
    // border-radius: 2px;
    font-size: 13px;
  }
  
  .popupButton {
    width: 96px;
    height: 40px;
    border-radius: 2px;
  }
  
  .option {
    height: auto;
    line-height: 1;
    padding: 0;
    background-color: #fff;
  }
  
  .tree {
    padding: 4px 20px;
    font-weight: 400;
  }
  .ql-editor {
    padding: 0;
    margin-top: -10px;
    min-height: 200px;
  }
  ::v-deep .ql-blank {
    min-height: 150px;
  }
  ::v-deep .ql-toolbar.ql-snow + .ql-container.ql-snow {
    height: 100px;
  }
  ::v-deep .el-form-item__label {
    width: 100px;
    height: 32px;
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 32px;
    text-align: right;
    color: #333;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: auto;
  }
  </style>
  