<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            label-position="left"
            style="display: flex; justify-content: space-between"
          >
            <div>
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 10vw"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </div>
            <div style="min-width: 15%;text-align: right;">
              <el-form-item>
                <el-button
                  class="resetQueryStyle"
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  class="resetQueryStyle"
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>值班情况展示列表</span>
          </div>
          <el-table
            v-loading="loading"
            :data="tableData"
            :cell-style="{ padding: '0px' }"
            :row-style="{ height: '48px' }"
          >
            <el-table-column label="" align="center" prop="userName" fixed />
            <el-table-column
              v-for="(item, index) in headerData"
              :label="item.week"
              align="center"
              prop="eventNo"
              show-overflow-tooltip
              :key="index"
            >
              <el-table-column
                :label="item.workDate"
                align="center"
                :prop="`arrangementName${index}`"
                show-overflow-tooltip
              />
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
    
<script>
import {
  page,
  save,
  escalation,
  getTree,
  getLabel,
} from "@/api/emergency/emergencyDuty/dutySituation/index";
export default {
  data() {
    return {
      // 总条数
      total: 0,
      // 表头数据
      headerData: [],
      // 用户表格数据
      tableData: null,
      dateRange: [],
      loading: false,
      // 查询参数
      queryParams: {
        scheduleDateBg: undefined,
        scheduleDateEd: undefined,
      },
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.getList();
    // this.recursion(this.treeData)
  },
  methods: {
    /** 查询场所列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams);
      page(this.queryParams).then((response) => {
        console.log(response, "表格数据");
        if (response.data != null) {
          this.tableData = response.data.infoList;
          this.tableData.forEach((item, index) => {
            item.memberWorkVos.forEach((res, resIndex) => {
              this.$set(
                item,
                `arrangementName${resIndex}`,
                res.arrangementName
              );
            });
          });
          this.headerData = response.data.header;
        }
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      console.log(this.dateRange);
      this.queryParams.scheduleDateBg = this.dateRange[0];
      this.queryParams.scheduleDateEd = this.dateRange[1];
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams = {};
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
  float: right;
  margin-right: 10px;
}

.resetQueryStyle {
//   width: 88px;
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}

.option {
  height: auto;
  line-height: 1;
  padding: 0;
  background-color: #fff;
}

.tree {
  padding: 4px 20px;
  font-weight: 400;
}
::v-deep .el-form-item__label{
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>