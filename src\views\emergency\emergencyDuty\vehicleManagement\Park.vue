<template>
  <div class="body">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>数据筛选</span>
      </div>
      <div class="topBottom">
        <div class="descriptions">
          <el-descriptions :column="3" :colon="false">
            <el-descriptions-item :label="item.label" v-for="item in optionData"
                                  :contentStyle='contentStyle'>
              <div slot="label" class="labelStyle" v-if="item.label!==''">
                {{ item.label }}:
              </div>
              <el-input clearable  v-if="item.field==='name'" style="width: 205px;" v-model="params[item.field]" type="text" placeholder="请输入"
                        :maxlength="10" clearable/>
              <el-cascader clearable  v-else-if="item.field==='deptId'" style="width: 205px;"
                           :options="options"
                           v-model="option"
                           @change="changeOption"
                           :props="{ checkStrictly: true,label: 'name',value:'id' }"
                           clearable/>
              <el-input clearable  v-else-if="item.type==='input'&&!item.options" style="width: 205px;" placeholder="请输入"
                        v-model="params[item.field]" clearable/>
              <el-select clearable  v-if="item.type==='select'" style="width: 205px;" v-model="params[item.field]" clearable>
                <el-option v-for="i in item.options" :label="i.name" :value="i.code"/>
              </el-select>
              <div v-if="item.type==='date'">
                <el-date-picker
                  v-model="value1"
                  style="width: 205px;"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  format="yyyy-MM-dd HH:mm:ss"
                  @change="change(item.field1,item.field2)"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="pickerOptions">
                </el-date-picker>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="tabButton">
          <el-button class="button primary" type="primary" @click="Search">查询</el-button>
          <el-button class="button" @click="reset">重置</el-button>
          <!-- <el-button style="float: right; padding: 3px 0;margin-top: 8px;" type="text" @click="showMore">{{
            text
          }}
        </el-button> -->
        </div>
      </div>
    </el-card>
    <el-card class="box-card-bottom">
      <div slot="header" class="clearfix">
        <span>数据列表</span>
        <div style="display: flex;gap: 5px;float: right">
          <!-- <el-button type="primary" @click="clickAdd" class="buttonAdd">添加</el-button> -->
          <!-- <el-button type="primary" @click="getTemplate" class="buttonAdd">模板</el-button>
          <el-button type="primary" @click="clickLicensing" class="buttonAdd">批量授权</el-button>
          <el-button type="primary" @click="handleImport" class="buttonAdd">导入</el-button> -->
        </div>
      </div>
      <el-table
        @selection-change="handleSelectionChange"
        :row-key="(row) => row.$key"
        ref="dataTable1" :cell-style="{ padding: '0px' }" :row-style="{ height: '48px' }"
        :data="tableData"
        style="width: 100%">
        <el-table-column type="selection" width="55" align="center" :reserve-selection="true"/>
        <el-table-column label="车主姓名" align="center" prop="personName" show-overflow-tooltip/>
        <el-table-column label="联系方式" align="center" prop="phoneNumber" show-overflow-tooltip/>
        <el-table-column label="车牌号" align="center" prop="carPlate" show-overflow-tooltip/>
        <el-table-column label="车辆类型" align="center" prop="carType" show-overflow-tooltip>
          <template v-slot="scope">
            {{
              carType.find(item => {
                return item.code == scope.row.carType
              })===undefined?'':carType.find(item => {
                return item.code == scope.row.carType
              }).name
            }}
          </template>
        </el-table-column>
        <el-table-column label="车辆入库来源" align="center" prop="carInSource" show-overflow-tooltip>
          <template v-slot="scope">
            {{
              carInSource.find(item => {
                return item.code == scope.row.carInSource
              })===undefined?'':carInSource.find(item => {
                return item.code == scope.row.carInSource
              }).name
            }}
          </template>
        </el-table-column>
  
        <!-- <el-table-column
          prop="operation"
          align="center"
          width="200"
          label="操作">
          <template slot-scope="scope">
            <el-button @click="handleUpdate(scope.row)" type="text" size="small">编辑</el-button>
            <el-button @click="handleDelete(scope.row)" type="text" size="small">删除</el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <el-pagination
        background
        :hide-on-single-page="false"
        style="float: right;margin: 10px 0;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="params.pageNum"
        :page-sizes="[10, 20, 30, 50,100]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </el-card>
    <el-dialog :close-on-click-modal="false"  v-if="dialogDetile" append-to-body title="查看详情" :visible.sync="dialogDetile" width="1000px">
      <div class="dialogTitle">基础信息</div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="demo-form-inline">
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="车主姓名" prop="personName" title="车主姓名">
              <el-input clearable
                :disabled="true"
                v-model="form.personName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属公司" prop="companyName" title="所属公司">
              <el-input clearable
                :disabled="true"
                v-model="form.companyName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式" prop="phoneNumber" title="联系方式">
              <el-input clearable
                :disabled="true"
                v-model="form.phoneNumber"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车牌号" prop="carPlate" title="车牌号">
              <el-input clearable
                :disabled="true"
                v-model="form.carPlate"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车辆类型" prop="carType" title="车辆类型">
              <el-select clearable  :disabled="true" v-model="form.carType">
                <el-option v-for="(item,index) in optionData[1].options" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车辆入库来源" prop="carInSource" title="车辆入库来源">
              <el-select clearable  :disabled="true" v-model="form.carInSource">
                <el-option v-for="(item,index) in carInSource" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车身颜色" prop="carColor" title="车身颜色">
              <el-select clearable  :disabled="true" v-model="form.carColor">
                <el-option v-for="(item,index) in carColor" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div v-if="form.access==1">
        <div class="dialogTitle">授权</div>
        <div class="detailItem">
          <div class="detailTitle">授权有效期：</div>
          <el-date-picker
            :disabled="true"
            v-model="value3"
            style="width: 400px;"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            @change="change2('beginDate','expireDate')"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions">
          </el-date-picker>
        </div>
        <div class="detailItem">
          <div class="detailTitle">授权范围：</div>
          <div><el-tree
            class="treeModal"
            @check="currentChecked"
            :render-after-expand='false'
            :data="treeData"
            show-checkbox
            node-key="id"
            :default-expanded-keys="HeavyCheckedKeys"
            :default-checked-keys="parkList"
          ></el-tree></div>
        </div>
        <div class="detailItem">
          <div class="detailTitle">修改人：</div>
          <div>{{ form.updatedBy }}</div>
        </div>
        <div class="detailItem">
          <div class="detailTitle">修改时间：</div>
          <div>{{ form.updataTime }}</div>
        </div>
        <div class="dialogTitle">车辆进/出记录</div>
        <el-table :data="tableList"
                  ref="dataTable">
          <el-table-column label="车牌号码" align="center" prop="carPlate" show-overflow-tooltip/>
          <el-table-column label="车辆入库来源" align="center" prop="carInSource" show-overflow-tooltip>
            <template v-slot="scope">
              {{
                carInSource.find(item => {
                  return item.code == scope.row.carInSource
                }).name
              }}
            </template>
          </el-table-column>
          <el-table-column label="进出类型" align="center" prop="name" show-overflow-tooltip>
            <template v-slot="scope">
              {{ ['进', '出'][scope.row.passType] }}
            </template>
          </el-table-column>
          <el-table-column label="进出时间" align="center" prop="snapTime" show-overflow-tooltip/>
        </el-table>
        <div style="float: right">
          <el-pagination
            v-show="total2>0"
            @size-change="handleSizeChange2"
            @current-change="handleCurrentChange2"
            :current-page="page"
            background
            :page-size="limit"
            layout="total, prev, pager, next"
            :total="total2"
          >
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDetile = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog :close-on-click-modal="false"  v-if="open" append-to-body :title="title" :visible.sync="open" @close="closeDialog" width="840px">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="demo-form-inline">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="车主姓名" prop="personName" title="车主姓名">
              <el-input clearable
                
                v-model="form.personName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属公司" prop="companyName" title="所属公司">
              <el-input clearable
               
                v-model="form.companyName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式" prop="phoneNumber" title="联系方式">
              <el-input clearable
                v-model="form.phoneNumber"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车牌号" prop="carPlate" title="车牌号">
              <el-input clearable
                v-model="form.carPlate"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车辆类型" prop="carType" title="车辆类型">
              <el-select clearable  v-model="form.carType">
                <el-option v-for="(item,index) in carType" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车辆入库来源" prop="carInSource" title="车辆入库来源">
              <el-select clearable  v-model="form.carInSource">
                <el-option v-for="(item,index) in carInSource" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车身颜色" prop="carColor" title="车身颜色">
              <el-select clearable  v-model="form.carColor">
                <el-option v-for="(item,index) in carColor" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :close-on-click-modal="false"  v-if="open1" append-to-body title="授权" :visible.sync="open1" @close="closeDialog" width="840px">
      <div class="dialogTitle" style="color: #001A33">基础信息</div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="demo-form-inline">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :disabled="form.access==1" label="车主姓名" prop="personName" title="车主姓名">
              <el-input clearable
                v-model="form.personName"
                placeholder="请输入车主姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属公司" prop="companyName" title="所属公司">
              <el-input clearable
                v-model="form.companyName"
                placeholder="请输入所属公司"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式" prop="phoneNumber" title="联系方式">
              <el-input clearable
                v-model="form.phoneNumber"
                placeholder="请输入联系方式"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车牌号" prop="carPlate" title="车牌号">
              <el-input clearable
                v-model="form.carPlate"
                placeholder="请输入车牌号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车辆类型" prop="carType" title="车辆类型">
              <el-select clearable  v-model="form.carType">
                <el-option v-for="(item,index) in carType" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车辆入库来源" prop="carInSource" title="车辆入库来源">
              <el-select clearable  v-model="form.carInSource">
                <el-option v-for="(item,index) in carInSource" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车身颜色" prop="carColor" title="车身颜色">
              <el-select clearable  v-model="form.carColor">
                <el-option v-for="(item,index) in carColor" :key="item.code" :label="item.name"
                           :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="dialogTitle" style="color: #001A33">授权</div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="demo-form-inline">
        <el-form-item label="授权有效期" title="授权有效期">
          <el-date-picker
            v-model="validity2"
            style="width: 400px;"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="授权范围" title="授权范围">
          <el-tree
            class="treeModal"
            @check="currentChecked"
            :render-after-expand='false'
            :data="treeData"
            show-checkbox
            node-key="id"
            :default-expanded-keys="HeavyCheckedKeys"
            :default-checked-keys="parkList"
          ></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm1">确 定</el-button>
        <el-button @click="open1 = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :close-on-click-modal="false"  v-if="licensing" append-to-body title="批量授权" :visible.sync="licensing" width="650px">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="demo-form-inline">
        <el-form-item label="授权有效期" title="授权有效期">
          <el-date-picker
            v-model="validity2"
            style="width: 400px;"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="授权范围" title="授权范围">
          <el-tree
            class="treeModal"
            @check="currentChecked"
            :render-after-expand='false'
            :data="treeData"
            show-checkbox
            node-key="id"
            :default-expanded-keys="HeavyCheckedKeys"
            :default-checked-keys="parkList"
          ></el-tree>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="licensing = false">取 消</el-button>
        <el-button type="primary" @click="licensingSave">保 存</el-button>
      </span>
    </el-dialog>
    <el-dialog :close-on-click-modal="false"
      append-to-body
      title="导入"
      :visible.sync="isShow"
      width="30%">
      <el-upload
        style="text-align: center"
        class="upload-demo"
        drag
        action="/security/carInfo/importCarInfo"
        :file-list="fileList"
        :headers="headers"
        multiple>
        <i class="el-icon-upload"></i>
      </el-upload>
      <div style="text-align: right">
        <el-button type="primary" @click="isShow = false">确 定</el-button>
        <el-button @click="isShow = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  findDeptTree,
  getDictByType,
  getEmployees,
  getLoginCompany,
  listAreas,
  listAreasType,
  uploadFacePic
} from "@/api/protection";
import {
  carBlackWhiteListDelete,
  carBlackWhiteListDetail,
  carBlackWhiteListSaveOrUpdate, carInfoBatchAccess,
  carInfoDeleteById,
  carInfoListCarPage,
  carInfoSaveCarInfo, carInfoUpdateAccess, carInfoUpdateInfo,
  detailCarInfo,
  getImportTemplate
} from "@/api/ningjin";

export default {
  name: "index",
  data() {
    return {
      loginCompany:null,
      open1:false,
      ids:[],
      HeavyCheckedKeys: [],
      parkList: [],
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      tabsList: [],
      tableData2: [],
      tableList: [],
      total2: null,
      limit: 5,
      page: 1,
      isShow: false,
      buttonLoading: false,
      title: '',
      dialogDetile: false,
      dataList: [],
      licensing: false,
      open: false,
      detileForm: {},
      form: {},
      carInSource: [],
      carType: [],
      carColor: [],
      rules: {
        carPlate: [{required: true, message: '请输入车牌号', trigger: 'blur'},{min: 1, max: 20, message: "长度限制在1到20个字符", trigger: "blur",},],
        companyName: [{required: true, message: '请输入所属公司', trigger: 'blur'},{min: 1, max: 20, message: "长度限制在1到20个字符", trigger: "blur",},],
        carType: [{required: true, message: '请选择车辆类型', trigger: 'select'},],
        personName: [{required: true, message: '请输入车主姓名', trigger: 'blur'},{min: 1, max: 20, message: "长度限制在1到20个字符", trigger: "blur",},],
        phoneNumber: [{required: true, message: '请输入联系方式', trigger: 'blur'},{min: 1, max: 20, message: "长度限制在1到20个字符", trigger: "blur",},],
        listCarType: [{required: true, message: '请选择黑白名单操作', trigger: 'select'},],
        carInSource:[{required: true, message: '请选择车辆入库来源', trigger: 'select'},],
        carColor:[{required: true, message: '请选择车身颜色', trigger: 'select'},],
      },
      option: '',
      options: [],
      fileList: [],
      total: 0,
      params: {
        pageNum: 1,
        pageSize: 10,
        carPlate: '',
        personName: '',
        phoneNumber: '',
        carType: '',
        listCarType: '',
        carInSource: '',
      },
      paramsLiscen: {},
      text: '展开更多',
      value1: [],
      value3: [],
      validity: [],
      validity2:[],
      contentStyle: {
        'text-align': 'center',
      },
      labelStyle: {
        'width': '120px',
        'font-family': 'PingFang SC',
        'font-style': 'normal',
        'font-weight': '400',
        'font-size': '15px',
        'line-height': '14px',
        // 'text-align-last': 'justify',
        'text-align': 'right',
        'letter-spacing': '0.04em',
        'color': '#333333',
        'align-items': 'center',
      },
      contentStyle1: {
        'text-align': 'center',
        'height': '32px',
        'line-height': '32px'
      },
      labelStyle1: {'width': '120px'},
      optionData: [
        {
          label: '车牌号',
          field: 'carPlate',
          type: 'input'
        },
        {
          label: '车辆类型',
          field: 'carType',
          options: [],
          type: 'select'
        },
        {
          label: '车主姓名',
          field: 'personName',
          type: 'input'
        }
      ],
      columns: [
        {
          label: '车主姓名',
          props: 'personName'
        },
        {
          label: '联系方式',
          props: 'phoneNumber'
        },
        {
          label: '车牌号',
          props: 'carPlate'
        },
        {
          label: '车辆类型',
          props: 'carType'
        },
        {
          label: '车辆入库来源',
          props: 'carInSource'
        },
        {
          label: '是否授权',
          props: 'access'
        },
        {
          label: '授权开始时间',
          props: 'beginDate'
        },
        {
          label: '授权结束时间',
          props: 'expireDate'
        },
      ],
      tableData: [],
      display: false,
      paramsData: [],
      pickerOptions: {
        onPick: ({maxDate, minDate}) => {
          this.selectDate = minDate.getTime();
          if (maxDate) {
            this.selectDate = ''
          }
        },
        // disabledDate: (time) => {
        //   if (this.selectDate !== '') {
        //     const one = 30 * 24 * 3600 * 1000;
        //     const minTime = this.selectDate - one;
        //     const maxTime = this.selectDate + one;
        //     return time.getTime() < minTime || time.getTime() > maxTime
        //   }
        // }
      },
      typeField: [
        {
          index: 1,
          arr: 'optionData',
          field: 'carType'
        }
      ],
      CS: {
        'max-width': '100px',   //最小宽度
        'text-align': 'center',
        'align-item': 'center',
        'min-width': '100px',   //最小宽度
        'word-break': 'break-all'  //过长时自动换行
      },
      LS: {
        'color': '#000',
        'text-align': 'center',
        'align-item': 'center',
        'height': '40px',
        'max-width': '100px',
        'min-width': '100px',
        'word-break': 'keep-all'
      },
      treeData: [
        {
          id: 0,
          mark:0,
          label: '园区',
          disabled: true,
          children: [
            {
              id: -1,
              mark:-1,
              disabled: true,
              label: '卡口',
              children: []
            },
          ],
        }
      ],
    }
  },
  mounted() {
    this.getType()
    this.Search()
    this.getTree()
  },
  methods: {
    accredit(row){
      detailCarInfo({id: row.id}).then(res => {
        this.validity2 = []
        this.parkList = []
        this.treeData[0].children[0].children.map(item=>{return Object.assign(item,{disabled:false})})
        this.form = res.data
        this.open1 = true
      })
      console.log(row)
    },
    currentChecked(nodeObj, SelectedObj) {
      this.parkList = SelectedObj.checkedKeys.filter(item => ![0, -1, -2].includes(item))
    },
    clickLicensing() {
      this.validity2 = []
      this.parkList = []
      this.value3 = []
      this.treeData[0].children[0].children.map(item=>{return Object.assign(item,{disabled:false})})
      this.licensing = true
    },
    handleImport() {
      this.isShow = true
    },
    getTemplate() {
      getImportTemplate().then(res => {
        const url = window.URL.createObjectURL(new Blob([res]))
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', '导入模板.xlsx') // 指定下载后的文件名，防跳转
        document.body.appendChild(link)
        link.click()
      })
    },
    submitForm1() {
      if(this.validity2.length==0){
        this.$message.error('请选择授权时间')
      }else{
        this.$refs.form.validate((valid) => {
          if (valid) {
            carInfoUpdateAccess({
              ...this.form,
              areaNumList:this.parkList,
              beginDate:this.validity2[0],
              expireDate:this.validity2[1]
            }).then(res => {
              this.Search();
              this.open1 = false;
              this.parkList = []
              this.validity2 = []
              this.$message({
                message: "操作成功",
                type: "success",
              });
            })
          } else {
            return false
          }
        })
      }
    },
    submitForm() {
      console.log(this.form)
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.title == '添加') {
            carInfoSaveCarInfo(this.form).then(res => {
              this.Search();
              this.open = false;
              this.$message({
                message: "操作成功",
                type: "success",
              });
            })
          } else if (this.title == '修改') {
            if(this.form.access==1){
              if(this.value3.length==0){
                this.$message.error('请选择授权时间')
              }else {
                carInfoUpdateAccess({
                  ...this.form,
                  areaNumList:this.parkList,
                  beginDate:this.value3[0],
                  expireDate:this.value3[1]
                }).then(res => {
                  this.Search();
                  this.open = false;
                  this.$message({
                    message: "操作成功",
                    type: "success",
                  });
                })
              }
            }else{
              carInfoUpdateInfo({
                ...this.form,
              }).then(res => {
                this.Search();
                this.open = false;
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
              })
            }
          }
        } else {
          return false
        }
      })
    },
    "cancel"() {
      this.open = false;
      this.reset();
    },
    closeDialog() {
      this.resetForm("form");
      this.$refs.form.resetFields(); // 提交后重置，不能打开时重置
      this.$refs.form.clearValidate();
      this.form = {};
    },
    getTreePath(tree, func, path) {
      if (!tree) return []
      for (const data of tree) {
        console.log(data)
        path.push(data.mark)
        if (func(data)) return path
        if (data.children) {
          const findChildren = this.getTreePath(data.children, func, path)
          if (findChildren.length) return findChildren
        }
        path.pop()
      }
      return []
    },
    handleClick(row) {
      detailCarInfo({id: row.id}).then(res => {
        this.detileForm = res.data
        this.form = res.data
        this.parkList = res.data.areaNumList||[]
        this.parkList.forEach((i => {
          this.getTreePath(this.treeData, data => data.mark == i, this.HeavyCheckedKeys)
        }))
        this.treeData[0].children[0].children.map(item=>{return Object.assign(item,{disabled:true})})
        if (res.data.carPassRecordList !== null) {
          this.tableData2 = res.data.carPassRecordList
          this.pageList()
        }
        if (res.data.beginDate !== null)
          this.value3 = [res.data.beginDate, res.data.expireDate]
        this.dialogDetile = true
      })
    },
    //处理切换页码
    handleSizeChange2(val) {
      console.log(`每页 ${val} 条`);
      this.limit = val;
      this.pageList();
    },
    handleCurrentChange2(val) {
      console.log(`当前页: ${val}`);
      this.page = val;
      this.pageList();
    },
    pageList() {
      this.tableList = this.tableData2.filter(
        (item, index) =>
          index < this.page * this.limit &&
          index >= this.limit * (this.page - 1)
      );
      this.total2 = this.tableData2.length;
    },
    handleUpdate(row) {
      detailCarInfo({id: row.id}).then(res => {
        this.form = res.data
        this.title = '修改'
        if(res.data.access==1){
          this.parkList = res.data.areaNumList||[]
          this.parkList.forEach((i => {
            this.getTreePath(this.treeData, data => data.mark == i, this.HeavyCheckedKeys)
          }))
          this.treeData[0].children[0].children.map(item=>{return Object.assign(item,{disabled:false})})
          if (res.data.carPassRecordList !== null) {
            this.tableData2 = res.data.carPassRecordList
            this.pageList()
          }
          if (res.data.beginDate !== null)
            this.value3 = [res.data.beginDate, res.data.expireDate]
        }else{
          this.parkList = []
          this.value3 = []
        }
        this.open = true
      })
    },
    handleDelete(row) {
      const that = this
      this.$confirm("园企车辆信息被删除后无法恢复，是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const that = this
        carInfoDeleteById({id: row.id}).then((res) => {
          // that.getList();
          that.$message({
            message: "操作成功",
            type: "success",
          });
          that.Search()
        });
      });
    },
    changeOption() {
      if (this.option.length !== 0)
        this.$set(this.params, 'deptId', this.option.slice(-1)[0])
    },
    getTree() {
      // findDeptTree().then(res => {
      //   this.options = res.data
      // })
    },
    removeFile(file, fileList) {
      this.params.photoUrl = ''
      this.params1.photoUrl = ''
    },
    reset() {
      Object.keys(this.params).forEach(key => {
        this.params[key] = key === 'pageSize' || key === 'pageNum' ? this.params[key] : ''
      })
      this.$set(this.params,'pageNum',1)
      this.value1 = []
      this.fileList = []
      this.option = []
      this.Search()
    },
    getType() {
      getLoginCompany().then(res=>{
        this.loginCompany = res.data
      })
      listAreasType({
        type:'access'
      }).then(res=>{
        // this.$set(this.treeData[0].children[0], 'disabled', this.$route.params.disabled)
        res.data.forEach(item => {
          this.treeData[0].children[0].children.push({
            id: item.mark,
            disabled: this.$route.params.disabled,
            label: item.name,
          })
        })
      })
      getDictByType({
        type: 'carInSource'
      }).then(res => {
        this.carInSource = res.data.filter(e=>{
          return (e.code=='employeeCar'||e.code=='managementCar')
        })
      })
      getDictByType({
        type: 'carType'
      }).then(res => {
        this.carType = res.data
      })
      getDictByType({
        type: 'carColor'
      }).then(res => {
        this.carColor = res.data
      })
      this.typeField.forEach(i => {
        getDictByType({
          type: i.field
        }).then((res) => {
          this[i.arr][i.index].options = i.field === 'ageGroup' ? [{
            code: '',
            name: '全部'
          }] : []
          res.data.forEach(item => {
            this[i.arr][i.index].options.push(item)
          })
        })
      })
    },
    handleSizeChange(val) {
      this.params.pageSize = val
      carInfoListCarPage(this.params).then((res) => {
        let result = res.data
        this.tableData = []
        this.tableData = result.records
        this.total = parseInt(result.total)
      })
    },
    handleCurrentChange(val) {
      this.params.pageNum = val
      carInfoListCarPage(this.params).then((res) => {
        let result = res.data
        this.tableData = []
        this.tableData = result.records
        this.total = parseInt(result.total)
      })
    },
    handleelchange(file, fileList) {
      const isJPG = file.name.toLowerCase().split('.')[1] === 'jpg' || 'png'
      const isLt2M = file.size / 1024 / 1024 < 100;
      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG/PNG 格式!');
        fileList.splice(-1, 1);
        return false
      } else if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 100MB!');
        fileList.splice(-1, 1);
        return false
      } else {
        let formdata = new FormData()
        fileList.map((item, index) => { //fileList本来就是数组，就不用转为真数组了
          if (index === fileList.length - 1) {
            formdata.append("file", item.raw)  //将每一个文件图片都加进formdata
            uploadFacePic(formdata).then((res) => {
              this.params.photoUrl = res.data
              this.$message.success('上传成功!')
            })
          }
        })
      }
      return isJPG && isLt2M;
    },
    showMore() {
      if (this.text === '收起') {
        this.text = '展开更多'
        this.display = false
      } else {
        this.text = '收起'
        this.display = true
      }
    },
    Search() {
      this.$set(this.params,'pageNum',1)
      carInfoListCarPage(this.params).then((res) => {
        let result = res.data
        this.tableData = []
        this.tableData = result.records
        this.total = parseInt(result.total)
      })
    },
    change(a, b) {
      if (this.value1 !== null) {
        this.params[a] = this.value1[0]
        this.params[b] = this.value1[1]
      } else {
        this.params[a] = ''
        this.params[b] = ''
      }
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      // this.deleteId = this.ids.toString();
    },
    change2(a, b) {
      if (this.value3 !== null) {
        this.form[a] = this.value3[0]
        this.form[b] = this.value3[1]
      } else {
        this.form[a] = ''
        this.form[b] = ''
      }
    },
    change1(a, b) {
      if (this.validity !== null) {
        this.paramsLiscen[a] = this.validity[0]
        this.paramsLiscen[b] = this.validity[1]
      } else {
        this.paramsLiscen[a] = ''
        this.paramsLiscen[b] = ''
      }
    },
    clickAdd() {
      this.open = true
      this.title = '添加'
      this.form = {}
      this.$set(this.form,'companyName',this.loginCompany.companyName)
      this.validity2 = []
      this.parkList = []
      this.value3 = []
    },
    licensingSave(){
      if(this.validity2.length==0){
        this.$message.error('请选择授权时间')
      }else if(this.ids.length==0){
        this.$message.error('请选择园企车辆')
      }else{
        carInfoBatchAccess({
          ids:this.ids,
          areaNumList:this.parkList,
          beginDate:this.validity2[0],
          expireDate:this.validity2[1]
        }).then(res=>{
          this.$message.success('授权成功！')
          this.Search()
          this.ids = []
          this.$refs.dataTable1.clearSelection()
          this.parkList = []
          this.validity2 = []
          this.licensing = false
        })
      }
    }
  }
}
</script>

<style scoped>
.body {
  width: 100%;
}

.body::-webkit-scrollbar {
  width: 0 !important;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  z-index: 2;
  margin: 20px;

  
}
.topBottom {
    display: flex;
    justify-content: space-around;
    flex-direction: row;

   
  }
  .descriptions {
      flex: 5;
    }

    .tabButton {
     
      flex: 1;
    }
    button {
        float: right;
        margin: 0 5px;
      }

/deep/ .el-descriptions-item__container .el-descriptions-item__content {
  text-align: left !important;
}

/deep/ .el-card.is-always-shadow {
  box-shadow: inset 0px -1px 0px #ebebeb;
}

.labelStyle {
  width: 120px;
  height: 14px;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 14px;
  color: #333333;
  align-items: center;
  text-align: right;
  margin: auto;
}

.upload {
  //padding: 16px 0px;
  width: 244px;
  height: 68px;
  border: 1px dashed #CCCCCC;
  border-radius: 4px;
}

.uploadText {
  width: 244px;
  height: 14px;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 10px;
  line-height: 14px;
  color: #999999;
  text-align: center;
}


.tabButton {
  
}
.button {
    width: 88px;
    height: 32px;
    background: #FFFFFF;
    border: 1px solid #CCCCCC;
    border-radius: 2px;
  }

  .primary {
    width: 88px;
    height: 32px;
    border: 1px solid #CCCCCC;
    border-radius: 2px;
    background: #188CFF;
  }
/deep/ .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 400;
  font-size: 17px;
  line-height: 24px;
  color: #007BAF;
}

/deep/ .el-table--medium .el-table__cell {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 22px;
  color: #666666;
}

.buttonAdd {
  width: 88px;
  height: 32px;
  background: #1A8CFF;
  border-radius: 2px;
  float: right;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 5px 8px;
  gap: 10px;
}

::v-deep .el-form-item__label {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  /* padding-top: 5px; */
}

/deep/ .el-card__header {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 26px;
  color: #333333;
}

/deep/ .el-input--medium .el-input__inner {
  height: 32px;
  line-height: 32px;
}

/deep/ .el-descriptions--medium:not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 20px;
}

/deep/ .el-descriptions-item__label {
  line-height: 35px;
  margin-left: 10px;
}

::v-deep .demo-form-inline .el-form-item {
  margin-bottom: 20px !important;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding: 0 !important;
}

/deep/ .el-card__header {
  padding: 19px 15px 18px 20px;
}


.detailItem {
  display: flex;
  font-size: 15px;
}

.detailTitle {
  width: 90px;
  height: 30px;
}
</style>
