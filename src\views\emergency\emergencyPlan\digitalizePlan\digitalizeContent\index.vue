<template>
  <div class="app-container">
    <el-row :gutter="20" type="flex">
      <el-col :span="8">
        <el-card class="box-content">
          <div slot="header">选择预案</div>
          <el-input
            v-model="planName"
            placeholder="搜索"
            prefix-icon="el-icon-search"
            style="width: 100%"
            maxlength="20"
            @change.native="getLeftData"
          ></el-input>
          <template v-for="item in leftDataList">
            <el-card
              shadow="never"
              :key="item.id"
              :class="['card-item', currentPlanId === item.id ? 'actived' : '']"
              @click.native="handleClickPlan(item)"
            >
              <div>预案名称：{{ item.planName }}</div>
              <div>
                预案类型：{{
                  (
                    dict.type.plan_deduction.find(
                      (p) => p.value == item.planType
                    ) || {}
                  ).label
                }}
              </div>
            </el-card>
          </template>
        </el-card>
      </el-col>
      <el-col :span="16" class="box-content">
        <el-card>
          <div slot="header" class="clearfix">
            <span>专家队伍</span>
            <el-button
              class="addBtn"
              icon="el-icon-plus"
              type="primary"
              @click="handleAdd('5010801')"
              >新增</el-button
            >
          </div>
          <el-table :data="contingentList">
            <el-table-column
              v-for="column in contingentTableColumn"
              :key="column.prop"
              :label="column.label"
              :prop="column.prop"
              align="center"
            >
              <template slot-scope="{ row }">
                <span v-if="column.prop === 'contingentType'">{{
                  (
                    dict.type.team_type.find(
                      (t) => t.value == row.contingentType
                    ) || {}
                  ).label
                }}</span>
                <span v-else>{{ row[column.prop] }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button type="text" @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-card style="margin-top: 20px">
          <div slot="header" class="clearfix">
            <span>应急资源</span>
            <el-button
              class="addBtn"
              icon="el-icon-plus"
              type="primary"
              @click="handleAdd('5010802')"
              >新增</el-button
            >
          </div>
          <el-table :data="materialList">
            <el-table-column
              v-for="column in materialTableColumn"
              :key="column.prop"
              :label="column.label"
              :prop="column.prop"
              align="center"
            >
              <template slot-scope="{ row }">
                <span v-if="column.prop === 'materialType'">{{
                  (
                    dict.type.materiel_type.find(
                      (t) => t.value == row.materialType
                    ) || {}
                  ).label
                }}</span>
                <span v-else>{{ row[column.prop] }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button type="text" @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-card style="margin-top: 20px">
          <div slot="header" class="clearfix">
            <span>应急专家</span>
            <el-button
              class="addBtn"
              icon="el-icon-plus"
              type="primary"
              @click="handleAdd('5010803')"
              >新增</el-button
            >
          </div>
          <el-table :data="expertList">
            <el-table-column
              v-for="column in expertTableColumn"
              :key="column.prop"
              :label="column.label"
              :prop="column.prop"
              align="center"
            >
              <template slot-scope="{ row }">
                <span v-if="column.prop === 'expertLevel'">{{
                  (
                    dict.type.emergency_expert_level.find(
                      (t) => t.value == row.expertLevel
                    ) || {}
                  ).label
                }}</span>
                <span v-else-if="column.prop === 'eventType'">
                  {{
                    (eventTypeList.find((t) => t.id == row.eventType) || {})
                      .name
                  }}
                </span>
                <span v-else>{{ row[column.prop] }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button type="text" @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      :title="dialogInfo.title"
      :visible.sync="dialogInfo.show"
      v-loading="dialogInfo.loading"
      width="960px"
      append-to-body
    >
      <template v-if="dialogInfo.type === '5010801'">
        <el-form ref="form" label-width="80px" inline>
          <el-form-item label="队伍名称">
            <el-input
              v-model="dialogInfo.query.contingentName"
              placeholder="请输入队伍名称"
              maxlength="20"
              @change="getContingentList"
            ></el-input>
          </el-form-item>
          <el-form-item label="队伍类型">
            <el-select
              v-model="dialogInfo.query.contingentType"
              placeholder="请选择队伍类型"
              clearable
              @change="getContingentList"
            >
              <el-option
                v-for="dict in dict.type.team_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-table
          :data="dialogInfo.tableList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column
            v-for="column in contingentTableColumn"
            :key="column.prop"
            :label="column.label"
            :prop="column.prop"
            align="center"
          >
            <template slot-scope="{ row }">
              <span v-if="column.prop === 'contingentType'">{{
                (
                  dict.type.team_type.find(
                    (t) => t.value == row.contingentType
                  ) || {}
                ).label
              }}</span>
              <span v-else>{{ row[column.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template v-if="dialogInfo.type === '5010802'">
        <el-form ref="form" label-width="80px" inline>
          <el-form-item label="物资名称">
            <el-input
              v-model="dialogInfo.query.materialName"
              placeholder="请输入物资库名称"
              maxlength="20"
              @change="getMaterialList"
            ></el-input>
          </el-form-item>
          <el-form-item label="资源类型">
            <el-select
              v-model="dialogInfo.query.materialType"
              placeholder="请选择资源类型"
              clearable
              @change="getMaterialList"
            >
              <el-option
                v-for="dict in dict.type.materiel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-table
          :data="dialogInfo.tableList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column
            v-for="column in materialTableColumn"
            :key="column.prop"
            :label="column.label"
            :prop="column.prop"
            align="center"
          >
            <template slot-scope="{ row }">
              <span v-if="column.prop === 'materialType'">{{
                (
                  dict.type.materiel_type.find(
                    (t) => t.value == row.materialType
                  ) || {}
                ).label
              }}</span>
              <span v-else>{{ row[column.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template v-if="dialogInfo.type === '5010803'">
        <el-form ref="form" label-width="80px" inline>
          <el-form-item label="专家姓名">
            <el-input
              v-model="dialogInfo.query.name"
              placeholder="请输入专家姓名"
              @change="getExpertList"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item label="擅长事件">
            <el-select
              v-model="dialogInfo.query.eventType"
              placeholder="请选择事件类型"
              clearable
              @change="getExpertList"
            >
              <el-option
                v-for="dict in eventTypeList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-table
          :data="dialogInfo.tableList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column
            v-for="column in expertTableColumn"
            :key="column.prop"
            :label="column.label"
            :prop="column.prop"
            align="center"
          >
            <template slot-scope="{ row }">
              <span v-if="column.prop === 'expertLevel'">{{
                (
                  dict.type.emergency_expert_level.find(
                    (t) => t.value == row.expertLevel
                  ) || {}
                ).label
              }}</span>
              <span v-else-if="column.prop === 'eventType'">
                {{
                  (eventTypeList.find((t) => t.id == row.eventType) || {}).name
                }}
              </span>
              <span v-else>{{ row[column.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确定</el-button>
        <el-button @click="dialogInfo.show = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
    
<script>
import { getAllPlanList } from "@/api/emergencyPlan/structuredPlan/planManagement/index.js";
import {
  getContentList,
  getMaterialList,
  getContingentList,
  getExpertList,
  saveContent,
  deleteContent,
  getEventTypeList,
} from "@/api/emergencyPlan/digitalizePlan/digitalizeContent/index.js";

export default {
  dicts: [
    "plan_deduction",
    "team_type",
    "materiel_type",
    "emergency_expert_level",
  ],
  data() {
    return {
      planName: "",
      currentPlanId: 0,
      leftDataList: [],
      contingentTableColumn: [
        {
          label: "队伍名称",
          prop: "contingentName",
        },
        {
          label: "队伍类型",
          prop: "contingentType",
        },
        {
          label: "队伍人数",
          prop: "headcount",
        },
        {
          label: "队伍负责人",
          prop: "contact",
        },
        {
          label: "联系电话",
          prop: "phone",
        },
      ],
      materialTableColumn: [
        {
          label: "物资名称",
          prop: "materialName",
        },
        {
          label: "物资类型",
          prop: "materialType",
        },
        {
          label: "存放物资库",
          prop: "supplyDepot",
        },
        {
          label: "库存",
          prop: "inventory",
        },
        {
          label: "责任人",
          prop: "liabilityUser",
        },
        {
          label: "联系电话",
          prop: "phone",
        },
      ],
      expertTableColumn: [
        {
          label: "姓名",
          prop: "name",
        },
        {
          label: "性别",
          prop: "gender",
        },
        {
          label: "擅长事件类型",
          prop: "eventType",
        },
        {
          label: "专家级别",
          prop: "expertLevel",
        },
      ],
      contingentList: [], // 队伍列表
      materialList: [], // 物资列表
      expertList: [], // 专家队伍
      eventTypeList: [], // 专家擅长事件类型
      multipleSelection: [],
      dialogInfo: {
        title: "",
        type: "", // 5010801 专家队伍，5010802 应急物资
        show: false,
        loading: false,
        query: {},
        tableList: [],
      },
    };
  },
  created() {
    this.getLeftData();
    getEventTypeList().then((res) => {
      console.log(res.data);
      this.eventTypeList = res.data;
    });
  },
  methods: {
    // 获取左侧预案列表
    getLeftData() {
      getAllPlanList({
        planName: this.planName,
      }).then((res) => {
        this.leftDataList = res.data || [];
        console.log("左侧", this.leftDataList);
        this.handleClickPlan(this.leftDataList[0]);
      });
    },
    // 点击左侧预案
    handleClickPlan(item) {
      this.currentPlanId = item.id;
      this.getRightTables();
    },
    // 获取物资、队伍table数据
    getRightTables() {
      getContentList({ planId: this.currentPlanId }).then((res) => {
        const data = res.data || {};
        this.contingentList = data["5010801"] || [];
        this.materialList = data["5010802"] || [];
        this.expertList = data["5010803"] || [];
        console.log(data);
      });
    },
    // 可选的队伍列表
    getContingentList() {
      const { contingentName, contingentType } = this.dialogInfo.query;
      getContingentList({
        planId: this.currentPlanId,
        contingentName,
        contingentType,
      }).then((res) => {
        this.dialogInfo.tableList = res.data.records || [];
        console.log(res.data.records);
      });
    },
    // 可选的物资列表
    getMaterialList() {
      const { materialName, materialType } = this.dialogInfo.query;
      getMaterialList({
        planId: this.currentPlanId,
        materialName,
        materialType,
      }).then((res) => {
        this.dialogInfo.tableList = res.data.records || [];
        console.log(res.data.records);
      });
    },
    // 可选的专家列表
    getExpertList() {
      const { name, eventType } = this.dialogInfo.query;
      getExpertList({
        planId: this.currentPlanId,
        name,
        eventType,
      }).then((res) => {
        this.dialogInfo.tableList = res.data.records || [];
        console.log(res.data.records);
      });
    },
    // 新增
    handleAdd(type) {
      this.resetData();
      this.dialogInfo.show = true;
      this.dialogInfo.type = type;
      if (type === "5010801") {
        this.dialogInfo.title = "关联队伍";
        this.getContingentList();
      }
      if (type === "5010802") {
        this.dialogInfo.title = "关联物资";
        this.getMaterialList();
      }
      if (type === "5010803") {
        this.dialogInfo.title = "关联专家";
        this.getExpertList();
      }
    },
    // 删除
    handleDelete(data) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(() => {
          console.log({ id: data.id });
          return deleteContent({ id: data.id });
        })
        .then((res) => {
          if (res.code === 200) {
            this.getRightTables();
            this.$modal.msgSuccess("删除成功");
          }
        })
        .catch((error) => {});
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 提交
    confirm() {
      if (this.multipleSelection.length === 0) {
        this.$modal.msgWarning("请至少选择一条数据");
        return;
      }
      const arr = [];
      for (const item of this.multipleSelection) {
        arr.push({
          contentId: item.id,
          contentType: this.dialogInfo.type,
          planId: this.currentPlanId,
        });
      }
      console.log(arr);
      this.dialogInfo.loading = false;
      saveContent(arr).then((res) => {
        this.resetData();
        if (res.code === 200) {
          this.getRightTables();
          this.$modal.msgSuccess("新增成功");
        }
      });
    },
    // 重置数据
    resetData() {
      this.dialogInfo.show = false;
      this.dialogInfo.type = "";
      this.dialogInfo.loading = false;
      this.dialogInfo.query = {};
      this.dialogInfo.tableList = [];
      this.multipleSelection = [];
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  box-sizing: border-box;
  height: calc(100vh - 84px);
  overflow-y: auto;
}

.box-content {
  height: calc(100vh - 124px);
  overflow-y: auto;
}

.card-item {
  margin: 20px 0;
  line-height: 2;
  font-size: 15px;
  cursor: pointer;
  &.actived {
    background: #f5f7fa;
  }
}

.addBtn {
  float: right;
}
</style>