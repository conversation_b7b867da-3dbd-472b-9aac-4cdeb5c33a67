<template>
  <div class="app-container">
    <el-row :gutter="20" type="flex">
      <el-col :span="8">
        <el-card class="card-content">
          <div slot="header">选择预案</div>
          <el-input
            v-model="planName"
            placeholder="搜索"
            prefix-icon="el-icon-search"
            clearable
            maxlength="20"
            style="width: 100%"
            @change="getLeftData"
          ></el-input>
          <template v-for="item in leftDataList">
            <el-card
              shadow="never"
              :key="item.id"
              :class="['card-item', currentPlanId === item.id ? 'actived' : '']"
              @click.native="handleClickPlan(item)"
            >
              <div>预案名称：{{ item.planName }}</div>
              <div>
                预案类型：{{
                  (dict.type.plan_deduction.find((p) => p.value == item.planType) || {})
                    .label
                }}
              </div>
            </el-card>
          </template>
        </el-card>
      </el-col>

      <el-col :span="16">
        <el-card class="card-content">
          <el-form
            ref="ruleForm"
            v-loading="loading"
            :model="formData"
            label-width="120px"
            :disabled="!canEdit"
          >
            <div class="header">
              <span>基本信息</span>
              <el-button
                v-if="!canEdit"
                type="primary"
                class="btn"
                :disabled="false"
                @click="canEdit = true"
                >编辑</el-button
              >
              <div v-else>
                <el-button class="btn" @click="cancel">取消</el-button>
                <el-button type="primary" class="btn" @click="save">保存编辑</el-button>
              </div>
            </div>
            <el-form-item
              label="预案适用范围"
              prop="scope"
              :rules="{
                required: true,
                message: '预案适用范围不能为空',
                trigger: 'blur',
              }"
            >
              <el-input
                maxlength="200"
                v-model="formData.scope"
                type="textarea"
                placeholder="请输入预案适用范围"
              />
            </el-form-item>
            <el-form-item
              label="行动负责人"
              prop="principal"
              :rules="{
                required: true,
                message: '行动负责人不能为空',
                trigger: 'blur',
              }"
            >
              <el-select
                v-model="formData.principal"
                placeholder="请选择行动负责人"
                style="width: 100%"
              >
                <el-option
                  v-for="item in staffOptions"
                  :key="item.id"
                  :label="item.staffName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-divider></el-divider>
            <el-form>
              <div class="header">
                <span>响应级别</span>
                <el-radio-group
                  v-model="curResponseLevel"
                  @change="handleLevelChange"
                  :disabled="false"
                >
                  <el-radio-button
                    v-for="v in dict.type.response_level"
                    :key="v.value"
                    :label="v.value"
                    >{{ v.label }}</el-radio-button
                  >
                </el-radio-group>
              </div>
            </el-form>
            <div class="clearfix">
              <el-col :span="12">
                <el-form-item label="预案标签" label-width="80px">
                  <el-tag
                    class="tag-item"
                    v-for="label in labelList"
                    :key="label.id"
                    :effect="
                      (
                        ((formData.responseLevelVos || [])[curLevelIndex] || {}).labels ||
                        []
                      ).includes(label.id)
                        ? 'dark'
                        : 'light'
                    "
                    :type="
                      (
                        ((formData.responseLevelVos || [])[curLevelIndex] || {}).labels ||
                        []
                      ).includes(label.id)
                        ? ''
                        : 'info'
                    "
                    @click="handleClickTag('label', label)"
                    >{{ label.label }}</el-tag
                  >
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预案参数" label-width="80px">
                  <el-tag
                    class="tag-item"
                    v-for="(p, i) in paramsList"
                    :key="i"
                    :effect="
                      (
                        ((formData.responseLevelVos || [])[curLevelIndex] || {})
                          .parameters || []
                      ).includes(p)
                        ? 'dark'
                        : 'light'
                    "
                    :type="
                      (
                        ((formData.responseLevelVos || [])[curLevelIndex] || {})
                          .parameters || []
                      ).includes(p)
                        ? ''
                        : 'info'
                    "
                    @click="handleClickTag('params', p)"
                    >{{ p }}</el-tag
                  >
                </el-form-item>
              </el-col>
            </div>

            <div class="header">
              <span>响应步骤</span>
              <el-button type="primary" class="btn" @click="handleAddStep"
                >增加步骤</el-button
              >
            </div>
            <template v-if="formData.responseLevelVos">
              <el-card
                class="stepBox"
                shadow="never"
                v-for="(step, si) in formData.responseLevelVos[curLevelIndex].processes"
                :key="si"
              >
                <div slot="header" class="header noMargin">
                  <span>步骤{{ si + 1 }}</span>
                  <el-button
                    class="step-btn"
                    type="text"
                    icon="el-icon-close"
                    @click="handleSubStep(si)"
                  ></el-button>
                </div>
                <el-form-item
                  label="响应名称"
                  :prop="`responseLevelVos.${curLevelIndex}.processes.${si}.responseName`"
                  :rules="{
                    required: true,
                    message: '响应名称不能为空',
                    trigger: 'blur',
                  }"
                >
                  <el-input
                    maxlength="20"
                    v-model="step.responseName"
                    :disabled="formData.responseLevelVos[curLevelIndex].isUpdate == false"
                    placeholder="请输入响应名称"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="注意事项"
                  :prop="`responseLevelVos.${curLevelIndex}.processes.${si}.announcement`"
                  :rules="{
                    required: true,
                    message: '注意事项不能为空',
                    trigger: 'blur',
                  }"
                >
                  <el-input
                    maxlength="200"
                    v-model="step.announcement"
                    :disabled="formData.responseLevelVos[curLevelIndex].isUpdate == false"
                    type="textarea"
                    placeholder="请输入注意事项"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="响应队伍"
                  :prop="`responseLevelVos.${curLevelIndex}.processes.${si}.responseTeam`"
                  :rules="{
                    required: true,
                    message: '注意响应队伍不能为空',
                    trigger: 'blur',
                  }"
                >
                  <el-select
                    v-model="step.responseTeam"
                    multiple
                    placeholder="请选择响应队伍"
                    style="width: 100%"
                    :disabled="formData.responseLevelVos[curLevelIndex].isUpdate == false"
                  >
                    <el-option
                      v-for="item in contingentOptions"
                      :key="item.id"
                      :label="item.contingentName"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-card>
            </template>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { deepClone } from "@/utils/index.js";
import {
  getAllPlanList,
  getStaffList,
} from "@/api/emergencyPlan/structuredPlan/planManagement/index.js";
import { page } from "@/api/emergency/naturalResources/specialist/index.js";
import { getLabels } from "@/api/emergencyPlan/planConfiguration/planLabel/index.js";
import { searchParameter } from "@/api/emergencyPlan/planConfiguration/planParameters/index.js";
import {
  getFlowDetail,
  saveFlow,
  updateFlow,
} from "@/api/emergencyPlan/digitalizePlan/digitalizeProcess/index.js";

export default {
  dicts: ["plan_deduction", "response_level"],
  data() {
    return {
      planName: "",
      currentPlanId: 0,
      leftDataList: [],
      staffOptions: [], // 行动负责人下拉
      contingentOptions: [], // 响应队伍下拉
      curResponseLevel: "5011201",
      curLevelIndex: 0,
      labelList: [], // 标签
      paramsList: [], // 参数
      formData: {},
      cloneFormData: {}, // 保存原数据，用于点击“取消”时还原数据
      canEdit: false, // 是否可编辑
      loading: false,
    };
  },
  created() {
    this.getLeftData();
    Promise.all([getStaffList(), page({ current: 1, size: 500 })]).then((res) => {
      console.log(res, "sssssss");
      const [res1, res2] = res;
      if (res1.code === 200) {
        this.staffOptions = res1.data || [];
      }
      if (res2.code === 200) {
        this.contingentOptions = res2.data.records || [];
      }
    });
  },
  methods: {
    // 获取左侧预案列表
    getLeftData() {
      getAllPlanList({
        planName: this.planName,
      }).then((res) => {
        this.leftDataList = res.data || [];
        this.handleClickPlan(this.leftDataList[0]);
      });
    },
    // 点击左侧预案
    handleClickPlan(item) {
      this.canEdit = false;
      this.loading = false;
      this.curResponseLevel = "5011201";
      this.curLevelIndex = 0;
      this.formData = {};
      this.currentPlanId = item.id;
      getLabels(item.eventType).then((res) => {
        this.labelList = res.data || [];
      });
      searchParameter(item.eventType).then((res) => {
        if (res.code === 200) {
          const {
            deathNumber,
            durationTime,
            infectNumber,
            missingNumber,
            woundedNumber,
          } = res.data;
          this.paramsList = [
            ...(deathNumber || []),
            ...(durationTime || []),
            ...(infectNumber || []),
            ...(missingNumber || []),
            ...(woundedNumber || []),
          ];
        }
      });
      this.getFlowDetail();
    },
    getFlowDetail() {
      getFlowDetail({ planId: this.currentPlanId }).then((res) => {
        console.log(res, "w=w=w=w=w=w=w=ss=");
        const data = res.data || {};
        if (!data.id) {
          const responseLevelVos = [];
          this.dict.type.response_level.map((item) => {
            responseLevelVos.push({
              labels: [],
              parameters: [],
              responseLevel: item.value,
              processes: [
                {
                  responseTeam: [],
                },
              ],
            });
          });
          this.$set(this.formData, "responseLevelVos", responseLevelVos);
        } else {
          data.responseLevelVos.map((item) => {
            delete item.id;
            item.labels = item.labels ? item.labels.split(",") : [];
            item.parameters = item.parameters ? item.parameters.split(",") : [];
            item.processes = (item.processes || []).map((process) => {
              delete process.id;
              process.responseTeam = process.responseTeam
                ? process.responseTeam.split(",")
                : [];
              return process;
            });
            return item;
          });
          this.formData = data;
        }
        this.cloneFormData = deepClone(this.formData);
        console.log("详情", this.formData);
      });
    },
    // 选择响应级别
    handleLevelChange(val) {
      this.$refs.ruleForm.clearValidate();
      this.curLevelIndex = this.dict.type.response_level.findIndex(
        (item) => item.value === val
      );
      if (!this.formData.responseLevelVos[this.curLevelIndex]) {
        this.formData.responseLevelVos.push({
          labels: "",
          parameters: "",
          responseLevel: val,
          processes: [
            {
              responseTeam: [],
            },
          ],
        });
      }
    },
    handleClickTag(type, data) {
      if (!this.canEdit) return;
      const item = this.formData.responseLevelVos[this.curLevelIndex];
      if (type === "label") {
        const index = item.labels.findIndex((i) => i === data.id);
        if (index !== -1) {
          item.labels.splice(index, 1);
        } else {
          item.labels.push(data.id);
        }
      }
      if (type === "params") {
        const index = item.parameters.findIndex((i) => i === data);
        if (index !== -1) {
          item.parameters.splice(index, 1);
        } else {
          item.parameters.push(data);
        }
      }
    },
    cancel() {
      this.canEdit = false;
      this.formData = this.cloneFormData;
      this.$refs.ruleForm.clearValidate();
    },
    // 右上角，保存编辑
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.formData.responseLevelVos, "this.formData.responseLevelVos");
          if (this.formData.responseLevelVos[this.curLevelIndex].processes.length < 1) {
            this.$message({
              message: "请添加步骤",
              type: "warning",
            });
            return;
          }
          this.loading = true;
          const { id, principal, scope, responseLevelVos: levels } = this.formData;
          const responseLevelVos = deepClone(levels);
          let array1 = [];
          responseLevelVos.forEach((item, index) => {
            item.labels = (item.labels || []).join();
            item.parameters = (item.parameters || []).join();
            item.processes.forEach((process, pi) => {
              process.processNo = pi + 1;
              process.responseTeam = (process.responseTeam || []).join();
              if (!process.responseName) {
                item.processes.splice(pi, 1);
              }
            });
          });
          console.log(responseLevelVos, "responseLevelVos");
          array1 = responseLevelVos.filter((item) => item.isUpdate != false);
          const submitData = {
            planId: this.currentPlanId,
            scope,
            principal,
            responseLevelVos: array1,
          };
          console.log(submitData);
          if (this.cloneFormData.id) {
            updateFlow({
              id,
              ...submitData,
            }).then((res) => {
              if (res.code === 200) {
                this.$modal.msgSuccess("保存成功");
                this.canEdit = false;
                this.loading = false;
                this.getFlowDetail();
              }
            });
          } else {
            saveFlow(submitData).then((res) => {
              if (res.code === 200) {
                this.$modal.msgSuccess("保存成功");
                this.canEdit = false;
                this.loading = false;
                this.getFlowDetail();
              }
            });
          }
        }
      });
    },
    // 增加响应步骤
    handleAddStep() {
      this.formData.responseLevelVos[this.curLevelIndex].processes.push({
        responseTeam: [],
      });
    },
    // 减少响应步骤
    handleSubStep(index) {
      this.formData.responseLevelVos[this.curLevelIndex].processes.splice(index, 1);
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  box-sizing: border-box;
  height: calc(100vh - 84px);
  overflow-y: auto;
}

.card-content {
  height: calc(100vh - 124px);
  overflow-y: auto;
}

.card-item {
  margin: 20px 0;
  line-height: 2;
  font-size: 15px;
  cursor: pointer;
  &.actived {
    background: #f5f7fa;
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  &.noMargin {
    margin-bottom: 0;
  }
  span {
    font-size: 17px;
  }
  .btn {
    margin-left: 20px;
  }
  ::v-deep .el-radio-group {
    flex: 1;
    margin-left: 20px;
    .el-radio-button {
      width: 25%;
    }
    .el-radio-button__inner {
      width: 100%;
    }
  }
}

.tag-item {
  margin-right: 10px;
  cursor: pointer;
}

.stepBox {
  margin-bottom: 20px;
  .step-btn {
    color: #1890ff;
    font-size: 20px;
    margin-left: 10px;
    vertical-align: middle;
  }
}
</style>
