<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="left-card">
          <div slot="header" class="clearfix">事件类型</div>
          <el-input class="search" v-model="filterText"  maxlength="20" placeholder="搜索"></el-input>
          <el-tree
            :current-node-key="currentKey"
            :data="treeData"
            :props="defaultProps"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
            ref="tree"
          ></el-tree>
        </el-card>
      </el-col>
      <el-col :span="16">
        <el-card>
          <div slot="header" class="clearfix">事件标签</div>
          <el-tag
            class="tag-item"
            :key="item.id"
            v-for="item in tagList"
            closable
            :disable-transitions="false"
            @close="clearTag(item)"
          >{{ item.label }}</el-tag>
          <div class="tag-add-box">
            <el-input class="tag-add-ipt" maxlength="20" v-model="iptTemp" placeholder="请输入"></el-input>
            <el-button round type="primary" @click="addTag">添加</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
  
<script>
import {
  searchTreeData,
  tagAdd,
  tagDel,
  getLabels
} from "@/api/emergencyPlan/planConfiguration/planLabel/index.js";
export default {
  data() {
    return {
      currentKey: "",
      filterText: "",
      treeData: [],
      defaultProps: {
        children: "children",
        label: "nodeName"
      },
      tagList: [],
      iptTemp: "",
      treeItemId: ""
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getTreeData();
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.nodeName.indexOf(value) !== -1;
    },
    // 添加标签
    addTag() {
      if (this.treeItemId === "") {
        this.$message({
          type: "info",
          message: "请先选择左侧事件类型"
        });
        return;
      }
      if (!this.iptTemp) {
        this.$message({
          type: "info",
          message: "请输入标签内容"
        });
        return;
      }
      tagAdd(this.treeItemId, this.iptTemp).then(res => {
        // 获取tree分支上的标签
        if (res?.code == 200) {
          this.$message({ type: "success", message: "添加成功" });
          this.getLabels();
        }
        this.iptTemp = "";
      });
    },
    getLabels() {
      getLabels(this.treeItemId).then(res => {
        if (res?.code == 200) {
          this.tagList = res.data;
          console.log(this.tagList);
        }
        //  else {
        //   this.$message({
        //     type: "error",
        //     message: res.msg
        //   });
        // }
      });
    },
    // 删除标签
    clearTag(record) {
      this.$modal.confirm("是否确认删除当前标签").then(function() {
        return tagDel(record.id);
      }).then(() => {
        this.getLabels();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleNodeClick(data) {
      this.tagList = [];
      if (data.children && data.children.length > 0) {
        this.treeItemId = "";
        return;
      }
      this.treeItemId = data.id;
      // 获取tree分支上的标签
      this.getLabels();
    },
    getTreeData() {
      searchTreeData().then(res => {
        if (res?.code == 200) {
          this.treeData = res.data;
        } 
        // else {
        //   this.$message({
        //     type: "error",
        //     message: res.msg
        //   });
        // }
      })
    }
  }
};
</script>
<style lang="scss" scoped>
.left-card {
  height: calc(100vh - 124px);
  overflow-y: auto;
}

.search {
  margin: 10px 0;
}

.tag-item {
  margin-right: 15px;
  margin-bottom: 10px;
}

.tag-add-box {
  display: flex;
  align-items: center;
  width: 40%;
  margin-top: 30px;
  .tag-add-ipt {
    margin-right: 15px;
  }
}

::v-deep .el-tree-node.is-current > .el-tree-node__content {
  background: #1890ff;
  color: #ffffff;
}
</style>