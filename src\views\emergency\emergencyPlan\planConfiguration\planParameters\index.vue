<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="left-card">
          <div slot="header" class="clearfix">事件类型</div>
          <!-- <el-input
            class="search"
            v-model="searchIpt"
            @input="searchInput"
            placeholder="搜索"
            maxlength="20"
          ></el-input> -->
          <el-tree
            :highlight-current="true"
            :data="treeData"
            :props="defaultProps"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
            ref="tree"
          ></el-tree>
        </el-card>
      </el-col>
      <el-col :span="16" v-show="treeItemId">
        <el-card class="box-card">
          <div slot="header" class="clearfix">事件参数</div>
          <div class="tag-add-box">
            <span>持续时间（天）</span>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.durationTime1"
              placeholder="下限"
            ></el-input>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.durationTime2"
              placeholder="上限"
            ></el-input>
            <el-button round type="primary" @click="addTag('durationTime')"
              >添加</el-button
            >
          </div>
          <div class="tag-box">
            <el-tag
              class="tag-item"
              :key="index + ''"
              v-for="(item, index) in durationTimeList"
              closable
              :disable-transitions="false"
              @close="clearTag('durationTime', index)"
              >持续时间{{ item }}（天）</el-tag
            >
          </div>
          <div class="tag-add-box">
            <span>死亡人数（人）</span>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.deathNumber1"
              placeholder="下限"
            ></el-input>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.deathNumber2"
              placeholder="上限"
            ></el-input>
            <el-button round type="primary" @click="addTag('deathNumber')"
              >添加</el-button
            >
          </div>
          <div class="tag-box">
            <el-tag
              class="tag-item"
              :key="index + ''"
              v-for="(item, index) in deathNumberList"
              closable
              :disable-transitions="false"
              @close="clearTag('deathNumber', index)"
              >死亡人数{{ item }}（人）</el-tag
            >
          </div>
          <div class="tag-add-box">
            <span>感染人数（人）</span>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.infectNumber1"
              placeholder="下限"
            ></el-input>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.infectNumber2"
              placeholder="上限"
            ></el-input>
            <el-button round type="primary" @click="addTag('infectNumber')"
              >添加</el-button
            >
          </div>
          <div class="tag-box">
            <el-tag
              class="tag-item"
              :key="index + ''"
              v-for="(item, index) in infectNumberList"
              closable
              :disable-transitions="false"
              @close="clearTag('infectNumber', index)"
              >感染人数{{ item }}（人）</el-tag
            >
          </div>
          <div class="tag-add-box">
            <span>失踪人数（人）</span>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.missingNumber1"
              placeholder="下限"
            ></el-input>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.missingNumber2"
              placeholder="上限"
            ></el-input>
            <el-button round type="primary" @click="addTag('missingNumber')"
              >添加</el-button
            >
          </div>
          <div class="tag-box">
            <el-tag
              class="tag-item"
              :key="index + ''"
              v-for="(item, index) in missingNumberList"
              closable
              :disable-transitions="false"
              @close="clearTag('missingNumber', index)"
              >失踪人数{{ item }}（人）</el-tag
            >
          </div>
          <div class="tag-add-box">
            <span>受伤人数（人）</span>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.woundedNumber1"
              placeholder="下限"
            ></el-input>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.woundedNumber2"
              placeholder="上限"
            ></el-input>
            <el-button round type="primary" @click="addTag('woundedNumber')"
              >添加</el-button
            >
          </div>
          <div class="tag-box">
            <el-tag
              class="tag-item"
              :key="index + ''"
              v-for="(item, index) in woundedNumberList"
              closable
              :disable-transitions="false"
              @close="clearTag('woundedNumber', index)"
              >受伤人数{{ item }}（人）</el-tag
            >
          </div>
          <div class="tag-add-box">
            <span>经济损失（万元）</span>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.economicLosses1"
              placeholder="下限"
            ></el-input>
            <el-input
              class="tag-add-ipt"
              maxlength="20"
              type="number"
              :min="0"
              v-model="editParams.economicLosses2"
              placeholder="上限"
            ></el-input>
            <el-button round type="primary" @click="addTag('economicLosses')"
              >添加</el-button
            >
          </div>
          <div class="tag-box">
            <el-tag
              class="tag-item"
              :key="index + ''"
              v-for="(item, index) in economicLossesList"
              closable
              :disable-transitions="false"
              @close="clearTag('economicLosses', index)"
              >经济损失{{ item }}（万元）</el-tag
            >
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { searchTreeData } from "@/api/emergencyPlan/planConfiguration/planLabel/index.js";
import {
  tagAddOrDel,
  searchParameter,
} from "@/api/emergencyPlan/planConfiguration/planParameters/index.js";
export default {
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: "children",
        label: "nodeName",
      },
      durationTimeList: [],
      deathNumberList: [],
      infectNumberList: [],
      missingNumberList: [],
      woundedNumberList: [],
      economicLossesList: [],
      editParams: {
        durationTime1: "",
        durationTime2: "",
        deathNumber1: "",
        deathNumber2: "",
        infectNumber1: "",
        infectNumber2: "",
        missingNumber1: "",
        missingNumber2: "",
        woundedNumber1: "",
        woundedNumber2: "",
        economicLosses1: "",
        economicLosses2: "",
      },
      searchIpt: "",
      eventTypeId: "",
      treeItemId: "",
      timer: "",
      tagId: "",
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getTreeData();
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.nodeName.indexOf(value) !== -1;
    },
    clearTag(type, index) {
      console.log(this[type + "List"]);
      let _this = this;
      this.$modal
        .confirm("是否确认删除当前标签")
        .then(function () {
          _this[type + "List"].splice(index, 1);
          let upObj = {
            id: _this.tagId,
            eventTypeId: _this.treeItemId,
          };
          upObj[type] = _this[type + "List"].join();
          tagAddOrDel(upObj).then((res) => {
            // 获取tree分支上的标签
            _this.getDetail();
            _this.$modal.msgSuccess("删除成功");
          });
        })
        .catch(() => {});
    },
    searchInput(res) {
      // if (!val) return;
      this.timer && clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.timer = null;
        this.recursion(res, this.treeData);
      }, 500);
      console.log();
    },
    // 通过递归返回搜索的数据
    recursion(res, treeData) {
      treeData.forEach((item) => {
        if (item.nodeName.indexOf(res) != -1) {
          console.log(item, "sss");
          this.treeData = [item];
        }
        if (item.children) {
          if (item.nodeName.indexOf(res) != -1) {
            console.log(item, "www");
          }
          return this.recursion(res, item.children);
        } else {
          if (item.nodeName.indexOf(res) != -1) {
            console.log(item, "www");
          }
        }
        //   if (item.children) {
        //     console.log(item.nodeName=='洪水',item,'item');
        //     console.log(item.nodeName.indexOf(res) != -1,'w-w-');
        //     if (item.nodeName.indexOf(res) != -1) {
        //       console.log(item, "sss");
        //     }

        //   } else {
        //     // if (item.nodeName.indexOf(res) != -1) {
        //     //   this.treeData = [item];
        //     // }
        //   }
      });
    },
    // 添加标签
    addTag(type) {
      let a1 = parseInt(this.editParams[type + "1"]);
      let a2 = parseInt(this.editParams[type + "2"]);
      if (!a1 || !a2) {
        this.$message({
          type: "error",
          message: "请输入",
        });
        return;
      }
      if (a1 >= a2 || a1 < 0) {
        this.$message({
          type: "error",
          message: "请确认是否输入正确",
        });
        return;
      }
      let upObj = {
        id: this.tagId || "",
        eventTypeId: this.treeItemId,
      };
      this[type + "List"].push(a1 + "-" + a2);
      upObj[type] = this[type + "List"].join();

      tagAddOrDel(upObj).then((res) => {
        // console.log(res);
        this.getDetail();
      });
    },

    handleNodeClick(data) {
      this.deathNumberList = [];
      this.durationTimeList = [];
      this.economicLossesList = [];
      this.infectNumberList = [];
      this.missingNumberList = [];
      this.woundedNumberList = [];
      if (data.children && data.children.length > 0) {
        this.treeItemId = "";
        return;
      }
      this.treeItemId = data.id;
      // 获取tree分支上的标签
      this.getDetail();
    },
    getDetail() {
      // 获取tree分支上的标签
      searchParameter(this.treeItemId).then((res) => {
        if (res?.code == 200) {
          //console.log(res);
          this.tagId = res.data.id || "";
          this.deathNumberList = res.data.deathNumber || [];
          this.durationTimeList = res.data.durationTime || [];
          this.economicLossesList = res.data.economicLosses || [];
          this.infectNumberList = res.data.infectNumber || [];
          this.missingNumberList = res.data.missingNumber || [];
          this.woundedNumberList = res.data.woundedNumber || [];
          this.editParams = {
            durationTime1: "",
            durationTime2: "",
            deathNumber1: "",
            deathNumber2: "",
            infectNumber1: "",
            infectNumber2: "",
            missingNumber1: "",
            missingNumber2: "",
            woundedNumber1: "",
            woundedNumber2: "",
            economicLosses1: "",
            economicLosses2: "",
          };
        }
      });
    },
    getTreeData() {
      searchTreeData().then((res) => {
        if (res?.code == 200) {
          this.treeData = res.data;
          // console.log(this.treeData);
        } 
        // else {
        //   this.$message({
        //     type: "error",
        //     message: res.msg,
        //   });
        // }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.left-card {
  height: calc(100vh - 124px);
  overflow-y: auto;
}

.search {
  margin: 10px 0;
}

.tag-item {
  margin-right: 15px;
  margin-bottom: 10px;
}

.tag-add-box {
  display: flex;
  align-items: center;
  margin-top: 30px;
  span {
    font-size: 14px;
    width: 150px;
    margin-left: 40px;
  }
  .tag-add-ipt {
    margin-right: 15px;
    width: 10vw;
  }
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background: #1890ff;
  color: #ffffff;
}

.tag-box {
  padding: 5px 5px 5px 40px;
}
</style>
