<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-position="left" style="display: flex; justify-content: space-between">
            <div>
              <el-form-item label="法律法规名称">
                <el-input v-model.number="queryParams.lawName" placeholder="请输入法律法规名称" clearable style="width: 10vw"
                  @keyup.enter.native="handleQuery" maxlength="20"/>
              </el-form-item>
              <el-form-item label="法律法规大类">
                <el-select style="width: 10vw" @change="largeCategoryChange" v-model="queryParams.parentId"
                  placeholder="请选择法律法规大类">
                  <el-option v-for="dict in largeCategoryData" :key="dict.id" :label="dict.nodeName" :value="dict.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="法律法规小类">
                <el-select style="width: 10vw" v-model="queryParams.lawTypeId" placeholder="请选择法律法规小类">
                  <el-option v-for="dict in subclassData" :key="dict.id" :label="dict.className" :value="dict.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button class="resetQueryStyle" type="primary" icon="el-icon-search" size="mini"
                  @click="handleQuery">搜索</el-button>
                <el-button class="resetQueryStyle" icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>法律法规列表</span>
            <el-button type="primary" size="mini" @click="handleAdd" icon="el-icon-plus"
              class="queryBtnT" v-if="!$store.getters.limits">新增法律法规</el-button>
          </div>
          <el-table v-loading="loading" :data="shelter">
            <el-table-column label="序号" align="center">
              <template slot-scope="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size +
                  scope.$index +
                  1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="法律法规名称" align="center" prop="lawName" show-overflow-tooltip />
            <el-table-column label="法律法规大类" align="center">
              <template slot-scope="scope">
                {{ getNameById(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="法律法规小类" align="center" prop="name"></el-table-column>
            <el-table-column label="发布时间" align="center" prop="publishTime" show-overflow-tooltip />
            <el-table-column label="发布单位" align="center" prop="publishingUnit" show-overflow-tooltip />
            <el-table-column label="更新日期" align="center" prop="updateTime" show-overflow-tooltip />
            <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-view" @click="handleLook(scope.row)">查看</el-button>
                <el-button  v-if="!$store.getters.limits" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
                <el-button type="text" icon="el-icon-download"
                  @click="handledownload(scope.row)">下载</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
            @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改预警事件对话框 -->
    <el-dialog :title="title" :visible.sync="abilityOpen" width="960px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form ref="abilityForm" :model="abilityForm" :rules="abilityRules" label-width="150px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="法律法规名称 :" prop="lawName">
                  <el-input v-model="abilityForm.lawName" placeholder="请输入法律法规名称" maxlength="20" :disabled="disabled"
                    style="width: 245px" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发布单位 :" prop="publishingUnit">
                  <el-input v-model="abilityForm.publishingUnit" placeholder="请输入发布单位" maxlength="20" :disabled="disabled"
                    style="width: 245px" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="法律法规大类 :" prop="parentId">
                  <el-select :disabled="disabled" v-model="abilityForm.parentId" style="width: 245px"
                    @change="largeCategoryChange" placeholder="请选择法律法规大类">
                    <el-option v-for="dict in largeCategoryData" :key="dict.id" :label="dict.nodeName" :value="dict.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="法律法规小类 :" prop="lawTypeId">
                  <el-select style="width: 245px" v-model="abilityForm.lawTypeId" placeholder="请选择法律法规小类"
                    :disabled="disabled">
                    <el-option v-for="dict in subclassData" :key="dict.id" :label="dict.className" :value="dict.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="发布时间" prop="publishTime">
                  <el-date-picker v-model="abilityForm.publishTime" type="date" placeholder="选择日期" :disabled="disabled"
                    style="width: 245px" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发布字号 :" prop="documentNumber">
                  <el-input v-model="abilityForm.documentNumber" placeholder="请输入发布字号" maxlength="20" :disabled="disabled"
                    style="width: 245px" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="详细内容 :" prop="details">
                  <el-input v-model="abilityForm.details" placeholder="请输入详细内容" type="textarea" maxlength="200"
                    :disabled="disabled" style="width: 245px">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="附件 :">
                  <el-upload class="upload-demo" :action="uploadImgUrl" :on-success="handleAvatarSuccess"
                    :file-list="fileList" :disabled="disabled" :on-remove="handleRemove" v-model="abilityForm.fileUrl"
                    :limit="1" :headers="headers" :on-exceed="handleExceed" :before-upload="beforeAvatarUpload">
                    <el-button size="small" type="primary" :disabled="disabled">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip"><div slot="tip" class="el-upload__tip">支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB</div></div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form></el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button class="popupButton" type="primary" @click="confirm('abilityForm')" v-if="!disabled">确 定</el-button>
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
import {
  page,
  save,
  update,
  deleteById,
  largeCategory,
  getSubclass,
  handledownload
} from "@/api/emergency/knowledgeBase/lawsAndRegulations/index";
export default {
  name: "EmergencySupplies",
  dicts: ["material_type"],
  data() {
    return {
      container: {},
      lngAndLat: "",
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      shelter: null,
      // 是否显示弹出层
      abilityOpen: false,
      title: "新增法律法规",
      text: undefined,
      imgUrl: `${require("@/assets/images/map.png")}`,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        lawTypeId: undefined,
        parentId: undefined,
        lawName: undefined,
      },
      frequency: 0,
      abilityForm: {},
      disabled: false,
      // 表单校验
      abilityRules: {
        lawName: [
          { required: true, message: "场所名称不能为空", trigger: "blur" },
        ],
        refugeArea: [
          { required: true, message: "场所面积不能为空", trigger: "blur" },
        ],
        parentId: [
          { required: true, message: "法律法规大类不能为空", trigger: "blur" },
        ],
        lawTypeId: [
          { required: true, message: "法律法规小类不能为空", trigger: "blur" },
        ],
      },
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/emergency-v2/file/uploadFile",
      fileList: [],
      largeCategoryData: [],
      subclassData: [],
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  mounted() {
    this.largeCategory();
  },
  methods: {
    /** 查询场所列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams);
      page(this.queryParams).then((response) => {
        console.log(response);
        if (response.data != null) {
          this.shelter = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    beforeAvatarUpload(file) {
      console.log(file);
      let array=["jpeg","jpg","png","gif","bmp","tiff","webp","svg","mp4","avi","mkv","mov","wmv",
      "flv","webm","mpeg","mp3","wav","aac","flac","ogg","wma","pdf","word","excel","txt","doc","docx","xlsx","xls","pptx","ppt"]
      let type=file.name.split('.')
      const isLt2M = file.size / 1024 / 1024 < 100;
      const isType=array.indexOf(type[1])==-1
      console.log(isType);
      if(isType)
       {
        this.$message.error("仅支持 jpeg|jpg|png|gif|bmp|tiff|webp|svg|mp4|avi|mkv|mov|wmv|flv|webm|mpeg|mp3|wav|aac|flac|ogg|wma|pdf|word|excel|txt|doc|docx|xlsx|xls|pptx|ppt| 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传附件大小不能超过 100MB!");
      }
      return !isType && isLt2M;
    },
    handleLook(row) {
      this.reset();
      this.abilityOpen = true;
      this.abilityForm = JSON.parse(JSON.stringify(row));
      this.abilityForm.parentId = this.abilityForm.parentId + "";
      this.abilityForm.parentId = this.abilityForm.parentId + "";
      this.largeCategoryChange(row.parentId);
      this.title = "查看法律法规";
      this.disabled = true;
      this.lngAndLat = row.longitude + "," + row.latitude;
      console.log(this.abilityForm);
      let array = [];
      if (row.fileUrl != "") {
        array = row.fileUrl.split("/");
        this.fileList.push({
          name: array[array.length - 1],
          url: row.fileUrl,
        });
      }
    },
    handledownload(row) {
      const _this=this
      this.$modal
        .confirm("是否确认下载附件")
        .then(function () {
          let arr1 = row.fileUrl.split(",");
          console.log(arr1);
          arr1.map((res) => {
            let arr = res.split("/");
            handledownload(arr).then(async res => {
              _this.handledownloadGet(arr, res)
            })
          });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("下载成功");
        })
        .catch((error) => { });
    },
    aaa(arr) {
      console.log(111);

    },
    handleAdd() {
      this.reset();
      this.abilityOpen = true;
      this.title = "新增法律法规";
      this.disabled = false;
    },

    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => { });
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    /*  确认保存新增*/
    confirm(formName) {
      console.log(this.abilityForm);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.abilityForm.id != undefined) {
            update(this.abilityForm).then((response) => {
              // console.log(response, "编辑");
              if (response.code == 200) {
                this.$modal.msgSuccess("编辑成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          } else {
            save(this.abilityForm).then((response) => {
              // console.log(response, "新增");
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          }
        }
      });
      // console.log(this.evaluateData, "evaluateData");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.abilityForm = {
        id: undefined,
        refugeName: undefined,
        refugeArea: undefined,
        holdsNumber: undefined,
        liabilityUser: undefined,
        phone: undefined,
        remark: undefined,
      };
      this.fileList = [];
      this.lngAndLat = "";
      this.resetForm("abilityForm");
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        current: 1,
        size: 10,
        refugeName: undefined,
        liabilityUser: undefined,
        phone: undefined,
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAvatarSuccess(response, res, file) {
      console.log(response, res, file);
      this.abilityForm.fileUrl = response;
      this.abilityForm.fileName = res.name;
    },
    handleRemove(file, fileList) {
      this.abilityForm.fileUrl = "";
      this.abilityForm.fileName = "";
    },
    handleExceed() {
      this.$modal.msgSuccess("请不要上传多个文件");
    },
    largeCategory() {
      largeCategory({ classType: 1 }).then((res) => {
        if (res.code == 200) {
          console.log(res);
          this.largeCategoryData = res.data;
        }
      });
    },
    largeCategoryChange(res) {
      console.log(res);
      getSubclass({ parentId: res, classType: 1 }).then((res) => {
        console.log(res, "小类");
        if (res.code == 200) {
          this.subclassData = res.data;
        }
      });
    },
    getNameById(res) {
      if (
        res.parentId != undefined &&
        res.parentId != "" &&
        res.parentId != null
      ) {
        return this.largeCategoryData.filter(
          (item) => item.id == res.parentId
        )[0].nodeName;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
//   font-size: 13px;
  float: right;
  margin-right: 10px;
}

.resetQueryStyle {
//   width: 88px;
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}
::v-deep .el-form-item__label{
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>