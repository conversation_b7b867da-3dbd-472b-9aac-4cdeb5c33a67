<template>
  <div class="app-container">
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-position="left"
        style="display: flex; justify-content: space-between"
      >
        <div>
          <el-form-item label="应急类型">
            <el-select
              v-model="queryParams.emergencyType"
              placeholder="请选择应急类型"
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.emergency_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上报人">
            <el-input
              v-model.number="queryParams.reporter"
              placeholder="请输入上报人"
              clearable
              maxlength="20"
              style="width: 190px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </div>
        <div style="min-width: 166px">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </div>
      </el-form>
      <el-row :gutter="10" class="mb8" style="margin-bottom: 20px">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            size="mini"
            @click="handleAdd"
            icon="el-icon-plus"
            v-hasPermi="['system:user:add']"
            >新增复盘</el-button
          >
        </el-col>

        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <el-table :data="tableData" v-loading="loading" style="width: 100%">
        <el-table-column align="center" prop="id" label="ID">
        </el-table-column>
        <el-table-column
          align="center"
          prop="emergencyContent"
          label="应急事件内容"
        >
        </el-table-column>
        <el-table-column align="center" prop="emergencyType" label="应急类型">
        </el-table-column>
        <el-table-column align="center" prop="reporter" label="上报人">
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间">
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              v-hasPermi="['system:user:detail']"
              @click="handleDetail(scope.row)"
              >详情</el-button
            >
            <el-button
              type="text"
              size="mini"
              style="margin-left: 20px"
              v-hasPermi="['system:user:edit']"
              @click="handleUpdate(scope.row)"
              >复盘</el-button
            >
            <el-button
              type="text"
              size="mini"
              style="margin-left: 20px"
              v-hasPermi="['system:user:remove']"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        layout="prev, pager, next, jumper"
        :page.sync="queryParams.current"
        :limit.sync="queryParams.size"
        @pagination="getList"
      />
    </el-row>

    <!-- 应急事件复盘对话框 -->
    <el-dialog :title="title" :visible.sync="abilityOpen" append-to-body>
      <el-form
        ref="emergencyForm"
        :model="emergencyForm"
        :rules="abilityRules"
        label-width="110px"
      >
        <el-row type="flex">
          <el-form-item label="应急类型" prop="queryParams">
            <el-select
              v-model="emergencyForm.emergencyType"
              placeholder="请选择"
            >
              <el-option
                v-for="item in dict.type.emergency_type"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="上报人" prop="reporter">
            <el-input v-model="emergencyForm.reporter"  maxlength="20"/>
          </el-form-item>

          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="emergencyForm.phone"  maxlength="20"/>
          </el-form-item>
        </el-row>

        <el-form-item label="应急内容" prop="emergencyContent">
          <el-input
            v-model="emergencyForm.emergencyContent"
            type="textarea"
            maxlength="200"
            autosize
          />
        </el-form-item>

        <el-form-item label="执行应急预案" prop="planId">
          <el-select v-model="emergencyForm.planId" placeholder="请选择">
            <el-option
              v-for="item in planList"
              :key="item.planId"
              :label="item.planName"
              :value="item.planId"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="复盘结论" prop="reviewConclusion">
          <el-input
            v-model="emergencyForm.reviewConclusion"
            type="textarea"
            maxlength="200"
            autosize
          />
        </el-form-item>

        <el-form-item label="是否需要修订应急预案" label-width="160px">
          <el-radio-group v-model="emergencyForm.isPlan">
            <el-radio :label="1">需要</el-radio>
            <el-radio :label="0">不需要</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="是否通知预案责任人" label-width="160px">
          <el-radio-group v-model="emergencyForm.isNotice">
            <el-radio :label="1">通知</el-radio>
            <el-radio :label="0">不通知</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 上传 -->
        <el-form-item label-width="0px">
          <el-upload
            class="upload-demo"
            :limit="1"
            :on-exceed="handleExceed"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :before-upload="beforeAvatarUpload"
            :action="uploadFileUrl"
            :on-success="handlePreview"
            :headers="headers"
            :file-list="fileList"
          >
            <el-button type="primary" :disabled="disabled" icon="el-icon-plus"
              >上传附件</el-button
            >
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          v-show="!disabled"
          @click="confirm('emergencyForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import {
  page,
  save,
  update,
  deleteById,
  selectById,
  selectEmergencyPlans,
} from "@/api/emergency/naturalResources/emergencyResponse/index";

export default {
  name: "emergencyResponse",
  dicts: ["emergency_type"],
  data() {
    return {
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 上传文件列表
      fileList: [],
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      // 上传的文件服务器地址
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/file/uploadFile",
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 是否显示弹出层
      abilityOpen: false,
      title: "新增应急知识库",
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        emergencyType: undefined,
        reporter: undefined,
      },
      //预案列表
      planList: [],
      emergencyForm: {
        emergencyType: undefined,
        reporter: undefined,
        phone: undefined,
        emergencyContent: undefined,
        planId: undefined,
        reviewConclusion: undefined,
        isPlan: undefined,
        isNotice: undefined,
      },
      disabled: false,
      // 表单校验
      abilityRules: {},
    };
  },
  watch: {},
  created() {
    this.getList();
    this.getselectEmergencyPlans();
  },
  methods: {
    // 预案列表
    getselectEmergencyPlans() {
      selectEmergencyPlans().then((response) => {
        if (response.data != null) {
          this.planList = response.data;
        }
      });
    },
    beforeAvatarUpload(file) {
      console.log(file);
      let array=["jpeg","jpg","png","gif","bmp","tiff","webp","svg","mp4","avi","mkv","mov","wmv",
      "flv","webm","mpeg","mp3","wav","aac","flac","ogg","wma","pdf","word","excel","txt","doc","docx","xlsx","xls","pptx","ppt"]
      let type=file.name.split('.')
      const isLt2M = file.size / 1024 / 1024 < 100;
      const isType=array.indexOf(type[1])==-1
      console.log(isType);
      if(isType)
       {
        this.$message.error("仅支持 jpeg|jpg|png|gif|bmp|tiff|webp|svg|mp4|avi|mkv|mov|wmv|flv|webm|mpeg|mp3|wav|aac|flac|ogg|wma|pdf|word|excel|txt|doc|docx|xlsx|xls|pptx|ppt| 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传附件大小不能超过 100MB!");
      }
      return !isType && isLt2M;
    },
    // 复盘新增
    handleAdd() {
      this.reset();
      this.abilityOpen = true;
      this.title = "新增应急事件复盘";
      this.disabled = false;
    },
    // 文件上传
    handlePreview(res, file) {
      if (res.code === 0) {
        this.$modal.msgSuccess(res.msg);
        this.emergencyForm.fileUrl = res.fileUrl;
      }
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleRemove(file, fileList) {
      this.emergencyForm.fileUrl = "";
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过1个!`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传失败，请重试");
    },
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        if (response.data != null) {
          this.tableData = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    handleUpdate(row) {
      this.abilityOpen = true;
      this.reset();
      this.getselectById(row.eventId);
      this.title = "应急事件复盘";
      this.disabled = false;
    },
    handleDetail(row) {
      this.reset();
      this.getselectById(row.eventId);
      this.abilityOpen = true;
      this.title = "应急事件详情";
      this.disabled = true;
    },
    // 应急事件详情获取
    getselectById(id) {
      selectById({ eventId: id }).then((response) => {
        console.log(response.data);
        if (response.data != null) {
          let data = response.data;
          this.emergencyForm = {
            emergencyType: data.emergencyType,
            reporter: data.reporter,
            phone: data.phone,
            emergencyContent: data.emergencyContent,
            planId: data.planId,
            reviewConclusion: data.reviewConclusion,
            isPlan: data.isPlan,
            isNotice: data.isNotice,
            fileUrl: data.fileUrl,
            eventId: data.eventId,
          };
        }
      });
    },
    handleDelete(row) {
      const eventAbilityId = row.eventId;
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ eventId: eventAbilityId });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    /*  确认保存*/
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // console.log(this.emergencyForm);
          if (this.emergencyForm.eventId) {
            update(this.emergencyForm).then((response) => {
              console.log(response, "复盘");
              if (response.code == 200) {
                this.$modal.msgSuccess(response.msg);
                this.abilityOpen = false;
                this.getList();
              }
            });
          } else {
            save(this.emergencyForm).then((response) => {
              console.log(response, "复盘");
              if (response.code == 200) {
                this.$modal.msgSuccess(response.msg);
                this.abilityOpen = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    // 取消按钮
    // 表单重置
    reset() {
      this.emergencyForm = {
        kemergencytype: undefined,
        people: undefined,
        content: undefined,
        resource: undefined,
      };
      this.fileList = [];
      this.resetForm("emergencyForm");
    },
    /** 重置按钮操作 */
    resetQuery() {
      (this.queryParams = {
        current: 1,
        size: 10,
        liabilityUser: undefined,
        phone: undefined,
      }),
        this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}
::v-deep .el-form-item__label{
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>