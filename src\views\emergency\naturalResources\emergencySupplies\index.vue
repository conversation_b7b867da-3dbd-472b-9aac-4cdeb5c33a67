<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <!--用户数据-->
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-position="left"
            style="display: flex; justify-content: space-between"
          >
            <div>
              <el-form-item label="物资库名称">
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入物资库名称"
                  clearable
                  maxlength="20"
                  style="width: 10vw"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="责任人">
                <el-input
                  v-model="queryParams.liabilityUser"
                  placeholder="请输入责任人"
                  clearable
                  maxlength="20"
                  style="width: 10vw"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button
                  class="resetQueryStyle"
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  class="resetQueryStyle"
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>应急物资库展示列表</span>
            <el-button
              type="primary"
              size="mini"
              @click="handleAdd"
              icon="el-icon-plus"
              class="queryBtnT"
              >新增物资库</el-button
            >
          </div>
          <el-table
            v-loading="loading"
            :data="userList"
            :cell-style="{ padding: '0px' }"
            :row-style="{ height: '48px' }"
          >
            <!-- <el-table-column type="selection" width="50" align="center" /> -->
            <el-table-column label="序号" align="center">
              <template slot-scope="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size +
                  scope.$index +
                  1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="所属单位" align="center">
              <template slot-scope="scope">
                {{ scope.row.firmName == null ? "园区" : scope.row.firmName }}
              </template>
            </el-table-column>
            <el-table-column
              label="物资库名称"
              align="center"
              prop="name"
              show-overflow-tooltip
            />
            <el-table-column
              label="责任人"
              align="center"
              prop="liabilityUser"
              show-overflow-tooltip
            />
            <el-table-column label="联系电话" align="center" prop="phone" />
            <el-table-column
              label="操作"
              align="center"
              width="320"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="handleLook(scope.row)"
                  >物资/装备管理</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-view "
                  @click="handleUpdate(scope.row)"
                  >查看</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.current"
            :limit.sync="queryParams.size"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改应急物资对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="abilityOpen"
      width="960px"
      append-to-body
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form
            ref="abilityForm"
            :model="abilityForm"
            :rules="abilityRules"
            label-width="110px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="物资库名称" prop="name">
                  <el-tooltip
                    v-if="abilityForm.id"
                    class="item"
                    effect="dark"
                    :content="abilityForm.name"
                    placement="top-start"
                  >
                    <el-input
                      v-model.trim="abilityForm.name"
                      style="width: 245px"
                      placeholder="请输物资名称"
                      maxlength="20"
                      :disabled="disabled"
                    />
                  </el-tooltip>
                  <el-input
                    v-else
                    v-model.trim="abilityForm.name"
                    style="width: 245px"
                    placeholder="请输物资名称"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经纬度" prop="lngAndLat">
                  <el-button type="primary" @click="openMap()">{{
                    lngAndLat ? lngAndLat : "点击选择"
                  }}</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="行政区划" prop="administrativeDivision">
                  <el-input
                    v-model.trim="abilityForm.administrativeDivision"
                    style="width: 245px"
                    placeholder="请输入行政区划"
                    maxlength="20"
                    :disabled="true"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="详细地址" prop="detailedAddress">
                  <el-input
                    v-model="abilityForm.detailedAddress"
                    style="width: 245px"
                    placeholder="请输入详细地址"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="物资库级别" prop="depotLevel">
                  <el-select
                  v-model="abilityForm.depotLevel"
                  placeholder="请选择应急类型"
                  style="width: 245px"
                >
                  <el-option
                    v-for="dict in dict.type.depotLevel"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物资库类型" prop="depotType">
                  <el-select
                  v-model="abilityForm.depotType"
                  placeholder="请选择物资库类型"
                  style="width: 245px"
                >
                  <el-option
                    v-for="dict in dict.type.depotType"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="物资库密级" prop="depotSecurityLevel">
                  <el-select
                  v-model="abilityForm.depotSecurityLevel"
                  placeholder="请选择物资库密级"
                  style="width: 245px"
                >
                  <el-option
                    v-for="dict in dict.type.depotSecurityLevel"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="应急通信方式" prop="emergencyCommunication">
                  <el-select
                  v-model="abilityForm.emergencyCommunication"
                  placeholder="请选择应急通信方式"
                  style="width: 245px"
                  multiple
                  collapse-tags
                >
                  <el-option
                    v-for="dict in dict.type.emergencyCommunication"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="受灾形式" prop="disasterType">
                  <el-select
                  v-model="abilityForm.disasterType"
                  placeholder="请选择受灾形式"
                  style="width: 245px"
                  multiple
                  collapse-tags
                >
                  <el-option
                    v-for="dict in dict.type.disasterType"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="防护区域" prop="protectionArea">
                  <el-input
                    v-model.trim="abilityForm.protectionArea"
                    style="width: 245px"
                    placeholder="请输入防护区域"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
              
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="监测方式" prop="monitoringMethod">
                  <el-select
                  v-model="abilityForm.monitoringMethod"
                  placeholder="请选择监测方式"
                  style="width: 245px"
                  multiple
                  collapse-tags
                >
                  <el-option
                    v-for="dict in dict.type.monitoringMethod"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="防护措施" prop="protectionMeasures">
                  <el-select
                  v-model="abilityForm.protectionMeasures"
                  placeholder="请选择防护措施"
                  style="width: 245px"
                  multiple
                  collapse-tags
                >
                  <el-option
                    v-for="dict in dict.type.protectionMeasures"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="投入使用时间" prop="commissioningDate">
                  <el-date-picker
                    v-model="abilityForm.commissioningDate"
                    type="date"
                    placeholder="选择日期"
                    style="width: 245px"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  >
              </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="可容纳人数" prop="capacity">
                  <el-input-number
                    v-model="abilityForm.capacity"
                    style="width: 245px"
                    placeholder="请输入可容纳人数"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="库容" prop="storageCapacity">
                  <el-input
                    v-model.trim="abilityForm.storageCapacity"
                    style="width: 245px"
                    placeholder="请输入库容"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="面积" prop="area">
                  <el-input
                    v-model="abilityForm.area"
                    style="width: 245px"
                    placeholder="请输入面积"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系人" prop="contactPerson">
                  <el-input
                    v-model.trim="abilityForm.contactPerson"
                    style="width: 245px"
                    placeholder="请输入联系人"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="contactPhone">
                  <el-input
                    v-model="abilityForm.contactPhone"
                    style="width: 245px"
                    placeholder="请输入联系电话"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="所属单位" prop="firmName">
                  <el-input
                    v-model.trim="abilityForm.firmName"
                    style="width: 245px"
                    placeholder="请输入所属单位"
                    maxlength="20"
                    :disabled="disabled||isEnterprise"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="存放物资" prop="storedMaterials">
                  <el-input
                    v-model="abilityForm.storedMaterials"
                    style="width: 700px"
                    placeholder="请输入存放物资"
                    type="textarea"
                    :disabled="disabled"
                    maxlength="100"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="基本情况" prop="basicSituation">
                  <el-input
                    v-model="abilityForm.basicSituation"
                    style="width: 700px"
                    placeholder="请输入基本情况"
                    type="textarea"
                    :disabled="disabled"
                    maxlength="100"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="popupButton"
          type="primary"
          @click="confirm('abilityForm')"
          :disabled="disabled"
          v-if="!disabled"
          >确 定</el-button
        >
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 物资管理 -->
    <el-dialog
      title="物资/装备管理"
      :visible.sync="dialogTableVisible"
      width="75%"
      append-to-body
    >
      <div class="topBottom">
        <div class="descriptions">
          <el-descriptions :column="3" :colon="false">
            <el-descriptions-item>
              <div slot="label" class="labelStyle">物资/装备名称：</div>
              <el-input
                v-model="materialsParams.materialName"
                style="width: 10vw"
                placeholder="请输入物资/装备名称"
                maxlength="20"
                clearable
                @change="materialsChange"
              />
            </el-descriptions-item>
            <el-descriptions-item>
              <div slot="label" class="labelStyle">物资/装备类型：</div>
              <el-select
                style="width: 10vw"
                v-model="materialsParams.materialType"
                clearable
                placeholder="请选择"
                @change="materialsChange"
              >
                <el-option
                  v-for="dict in dict.type.materiel_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-descriptions-item>
            <el-descriptions-item>
              <div slot="label" class="labelStyle">操作人：</div>
              <el-input
                v-model="materialsParams.operator"
                style="width: 10vw"
                placeholder="请输入操作人"
                maxlength="20"
                clearable
                @change="materialsChange"
              />
            </el-descriptions-item>
            <el-descriptions-item>
              <div slot="label" class="labelStyle">操作时间：</div>
              <el-date-picker
                style="width: 10vw"
                v-model="dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                @change="materialsChange"
              >
              </el-date-picker>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="tabButton">
          <el-button
            type="primary"
            plain
            size="mini"
            @click="clickMaterialsAdd"
            icon="el-icon-plus"
            class="queryBtnT"
            >新增物资/装备</el-button
          >
        </div>
      </div>
      <el-table
        v-loading="loading"
        :data="materialsList"
        :cell-style="{ padding: '0px' }"
        :row-style="{ height: '48px' }"
      >
        <!-- <el-table-column type="selection" width="50" align="center" /> -->
        <el-table-column label="序号" align="center">
          <template slot-scope="scope">
            <span>{{
              (queryParams.current - 1) * queryParams.size + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="物资/装备名称"
          align="center"
          prop="materialName"
          show-overflow-tooltip
        />
        <el-table-column label="物资/装备类型" align="center">
          <template slot-scope="scope">
            {{ getNameById(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column
          label="最新入库时间"
          align="center"
          prop="storageTime"
          show-overflow-tooltip
        />
        <el-table-column
          label="最新出库时间"
          align="center"
          prop="outboundTime"
          show-overflow-tooltip
        />
        <el-table-column
          label="操作人"
          align="center"
          prop="updateUser"
          show-overflow-tooltip
        />
        <el-table-column
          label="库存量"
          align="center"
          prop="inventory"
          show-overflow-tooltip
        />
        <el-table-column
          label="计量单位"
          align="center"
          prop="unit"
          show-overflow-tooltip
        />
        <el-table-column
          label="操作"
          align="center"
          width="220"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleLookArticle(scope.row)"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleInAndOut(scope.row, 1)"
              >入库</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleInAndOut(scope.row, 2)"
              >出库</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleRecords(scope.row)"
              >记录</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="inAndOutDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="materialsTotal > 0"
        :total="materialsTotal"
        :page.sync="materialsParams.current"
        :limit.sync="materialsParams.size"
        @pagination="getMaterialsList"
      />
    </el-dialog>
    <!-- 应急物资出入库对话框 -->
    <el-dialog
      :title="inAndOutTitle"
      :visible.sync="inAndOutOpen"
      width="960px"
      append-to-body
    >
      <el-form
        ref="inAndOutForm"
        :model="inAndOutForm"
        :rules="inAndOutRules"
        label-width="110px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="当前库存：">
              <span>{{ inAndOutForm.inventory }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="入库数量"
              prop="storageQuantity"
              v-if="inAndOutFlag == 1"
            >
              <el-input
                v-model.trim="inAndOutForm.storageQuantity"
                autocomplete="off"
                style="width: 245px"
                maxlength="20"
              ></el-input>
            </el-form-item>
            <el-form-item label="出库数量" prop="outboundQuantity" v-else>
              <el-input
                v-model.trim="inAndOutForm.outboundQuantity"
                autocomplete="off"
                style="width: 245px"
                maxlength="20"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="备注：">
              <el-input
                v-model="inAndOutForm.remark"
                style="width: 700px"
                placeholder="请输入物资说明"
                maxlength="200"
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="popupButton" @click="inAndOutCancel">取 消</el-button>
        <el-button
          class="popupButton"
          type="primary"
          @click="inAndOutConfirm('inAndOutForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 新增物资 -->
    <el-dialog
      title="新增物资/装备"
      :visible.sync="materialsOpen"
      width="960px"
      append-to-body
      @close="materialsCancel"
    >
      <el-form
        ref="materialsForm"
        :model="materialsForm"
        :rules="materialsRules"
        label-width="110px"
        :disabled="materialsFormdisabled"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属储备库" prop="supplyDepotId">
              <el-input
                style="width: 245px"
                v-model="selectedName"
                placeholder="请输入所属储备库"
                maxlength="20"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属队伍" prop="teamName">
              <el-select
                style="width: 245px"
                v-model="materialsForm.teamName"
                placeholder="请选择所属队伍"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in ranksOptions"
                  :key="dict.id"
                  :label="dict.contingentName"
                  :value="dict.contingentName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物资/装备名称" prop="materialName">
              <el-input
                style="width: 245px"
                v-model="materialsForm.materialName"
                placeholder="请输入物资/装备名称"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="物资/装备类型" prop="materialType">
              <el-select
                style="width: 245px"
                v-model="materialsForm.materialType"
                placeholder="请选择物资/装备类型"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.materiel_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="liabilityUser">
              <el-input
                style="width: 245px"
                v-model="materialsForm.liabilityUser"
                placeholder="请输入负责人"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="办公电话" prop="phoneOffice">
              <el-input
                style="width: 245px"
                v-model="materialsForm.phoneOffice"
                placeholder="请输入办公电话"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="家庭电话" prop="phoneFamily">
              <el-input
                style="width: 245px"
                v-model="materialsForm.phoneFamily"
                placeholder="请输入家庭电话"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="移动电话" prop="phone">
              <el-input
                style="width: 245px"
                v-model="materialsForm.phone"
                placeholder="请输入移动电话"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位传真" prop="fax">
              <el-input
                style="width: 245px"
                v-model="materialsForm.fax"
                placeholder="请输入单位传真"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用途" prop="useFor">
              <el-input
                style="width: 245px"
                v-model="materialsForm.useFor"
                placeholder="请输入用途"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物资/装备来源" prop="comeFrom">
              <el-select
                style="width: 245px"
                v-model="materialsForm.comeFrom"
                placeholder="请选择物资/装备来源"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.come_from"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产类型" prop="assetType">
              <el-select
                style="width: 245px"
                v-model="materialsForm.assetType"
                placeholder="请选择资产类型"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.materiel_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号" prop="model">
              <el-input
                style="width: 245px"
                v-model="materialsForm.model"
                placeholder="请输入规格型号"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产厂家" prop="manufacturer">
              <el-input
                style="width: 245px"
                v-model="materialsForm.manufacturer"
                placeholder="请输入生产厂家"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="inventory">
              <el-input
                style="width: 245px"
                v-model="materialsForm.inventory"
                placeholder="请输入数量"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单价" prop="price">
              <el-input
                style="width: 245px"
                v-model="materialsForm.price"
                placeholder="请输入单价"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计量单位" prop="unit">
              <el-input
                style="width: 245px"
                v-model="materialsForm.unit"
                placeholder="请输入计量单位"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产日期" prop="newDate">
              <el-date-picker
                v-model="materialsForm.newDate"
                type="date"
                placeholder="选择日期"
                style="width: 245px"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用年限" prop="useYear">
              <el-input
                style="width: 245px"
                v-model="materialsForm.useYear"
                placeholder="请输入使用年限"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="限用日期" prop="expiredDate">
              <el-date-picker
                v-model="materialsForm.expiredDate"
                type="date"
                placeholder="选择日期"
                style="width: 245px"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属单位" prop="affiliatedUnit">
              <el-input
                style="width: 245px"
                v-model="materialsForm.affiliatedUnit"
                placeholder="请输入所属单位"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="购买日期" prop="buyDate">
              <el-date-picker
                v-model="materialsForm.buyDate"
                type="date"
                placeholder="选择日期"
                style="width: 245px"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定期保修间隔" prop="repairInterval">
              <el-input
                style="width: 245px"
                placeholder="请输入定期保修间隔"
                maxlength="200"
                v-model="materialsForm.repairInterval"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扩展属性名" prop="extendName">
              <el-input
                style="width: 245px"
                placeholder="请输入扩展属性名"
                maxlength="200"
                v-model="materialsForm.extendName"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扩展属性单位" prop="extendUnit">
              <el-input
                style="width: 245px"
                placeholder="请输入扩展属性单位"
                maxlength="200"
                v-model="materialsForm.extendUnit"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扩展属性值" prop="extendValue">
              <el-input
                style="width: 245px"
                placeholder="请输入扩展属性值"
                maxlength="200"
                v-model="materialsForm.extendValue"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登记类型" prop="registerType">
              <el-select
                style="width: 245px"
                v-model="materialsForm.registerType"
                placeholder="请选择登记类型"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.register_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物资/装备状态" prop="equipmentStatus">
              <el-select
                style="width: 245px"
                v-model="materialsForm.equipmentStatus"
                placeholder="请选择物资/装备状态"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.equipment_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入备注"
                maxlength="200"
                v-model="materialsForm.remark"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="物资/装备图片:" prop="contingentName">
              <el-upload
                class="avatar-uploader"
                :action="uploadImgUrl"
                :headers="headers"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!materialsFormdisabled">
        <el-button class="popupButton" @click="materialsCancel"
          >取 消</el-button
        >
        <el-button
          class="popupButton"
          type="primary"
          @click="materialsConfirm('materialsForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 物资记录对话框 -->
    <el-dialog
      title="物资记录"
      :visible.sync="recordsOpen"
      width="720px"
      append-to-body
    >
      <el-button
        type="primary"
        :plain="plain1"
        size="mini"
        @click="switchRecords(1)"
        >入库记录</el-button
      >
      <el-button
        type="primary"
        :plain="plain2"
        size="mini"
        @click="switchRecords(2)"
        >出库记录</el-button
      >
      <el-timeline style="margin-top: 20px">
        <el-timeline-item
          v-for="(item, index) in recordsList"
          :key="index"
          placement="top"
          :timestamp="plain1?item.outboundTime:item.storageTime"
        >
          <div>
            {{ plain1 ? "出" : "入" }}库数量：{{
              plain1 ? item.outboundQuantity : item.storageQuantity
            }}
          </div>
          <div>备注：{{ item.remark }}</div>
        </el-timeline-item>
      </el-timeline>
      <div slot="footer" class="dialog-footer">
        <el-button class="popupButton" @click="recordsOpen = false"
          >取 消</el-button
        >
        <el-button
          class="popupButton"
          type="primary"
          @click="recordsOpen = false"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 地图展示 -->
    <Map
      @mapConfirm="mapConfirm"
      :ranksForm="abilityForm"
      :mapVisible="mapVisible"
      :disabled="disabled"
      @mapCancellation="mapCancellation"
      ref="mapRef"
      :url="url"
    ></Map>
  </div>
</template>

<script>
import {
  page,
  save,
  update,
  deleteById,
  detail,
  materialsList,
  materialsAdd,
  inAndOutAdd,
  inAndOutDelete,
  records,
} from "@/api/emergency/naturalResources/emergencySupplies/index";
import {
    getTeamListCom
    
} from "@/api/emergency/new/index.js";
import { handledownload } from "@/api/emergency/naturalResources/rescueExperts/index";
import { blobValidate } from "@/utils/ruoyi";
import Map from "../../../map/index.vue";
export default {
  name: "EmergencySupplies",
  dicts: [
    "depotLevel",
    "materiel_type",
    "register_type",
    "equipment_status",
    "asset_type",
    "come_from",
    "depotType",
    "depotSecurityLevel",
    "emergencyCommunication",
    "disasterType",
    "monitoringMethod",
    "protectionMeasures"
  ],
  components: { Map },
  data() {
    // 
    let checkPhone = (rule, value, callback) => {
      console.log(value);
      let reg = /^1[345789]\d{9}$/;
      if (!reg.test(value)) {
        callback(new Error("请输入11位手机号"));
      } else {
        callback();
      }
    };
    const validCode = (rule, value, callback) => {
      if (this.lngAndLat) {
        callback();
      } else {
        callback(new Error("请选择经纬度"));
      }
    };
    const checkInAndOut = (rule, value, callback) => {
      let req = /(^[\-0-9][0-9]*(.[0-9]+)?)$/;
      console.log(this.inAndOutForm.inventory, value, "value");
      if (req.test(value)) {
        if (value > 0) {
          if (this.inAndOutFlag == 1) {
            callback();
          } else {
            if (value < Number(this.inAndOutForm.inventory)) {
              callback();
            } else {
              callback(new Error("物资出库数量不能大于库存量"));
            }
          }
        } else {
          callback(
            new Error(
              `物资${this.inAndOutFlag == 1 ? "入" : "出"}库数量不能小于0`
            )
          );
        }
      } else {
        callback(new Error("请输入正确的物资出入库数量"));
      }
    };
    const checkNumber = (rule, value, callback) => {
      if (!isNaN(value)) {
        callback();
      } else {
        callback(new Error("请输入正确的入库数量"));
      }
    };
    const checkNumber1 = (rule, value, callback) => {
      if (isNaN(value)) {
        callback();
      } else {
        callback(new Error("请输入正确的计量单位"));
      }
    };
      return {
        ranksOptions:[],
      // 物资库id
      supplyDepotId: undefined,
      dialogTableVisible: false,
      // 出入库弹出层
      inAndOutOpen: false,
      inAndOutForm: {},
      inAndOutTitle: "物资入库",
      inAndOut: undefined,
      inAndOutFlag: 1,
      inventory: undefined, //库存量
      // 新增物资弹出层
      materialsOpen: false,
      materialsFormdisabled: false,
      materialsForm: {},
      // 物资出入库记录弹出层
      recordsOpen: false,
      recordsList: [],
      recordsFlag: 1,
      recordsId: undefined,
      plain1: false,
      plain2: false,
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      materialsTotal: 0,
      // 用户表格数据
      userList: null,
      // 物资管理数据
      materialsList: [],
      // 是否显示弹出层
      abilityOpen: false,
      // 地图遮罩层
      mapVisible: false,
      // 地图点标记图标地址
      url: `${require("@/assets/icons/warehouse.png")}`,
      title: "新增事件类型",
      lngAndLat: "", //经纬度
      refugeList: [],
      imgUrl: `${require("@/assets/images/map.png")}`,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        name: undefined,
        liabilityUser: undefined,
      },
      dateRange: [],
      materialsParams: {
        current: 1,
        size: 10,
        materialName: undefined,
        materialType: undefined,
        operator: undefined,
        supplyDepotId: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      frequency: 0,
      placement: null,
      abilityForm: {},
      disabled: false,
      // 表单校验--新增物资库
      abilityRules: {
        name: [
          { required: true, message: "物资名称不能为空", trigger: "blur" },
        ],
        lngAndLat: [{ required: true, validator: validCode, trigger: "blur",message: "请选择经纬度", }],
        commissioningDate: [
          { required: true, message: "请选择投入使用时间", trigger: "blur" },
        ],
        capacity: [
          { required: true, message: "请输入可容纳人数", trigger: "blur" },
        ],
        storageCapacity: [
          { required: true, message: "请输入库容", trigger: "blur" },
        ],
        area: [
          { required: true, message: "请输入面积", trigger: "blur" },
        ],
        contactPerson: [
          { required: true, message: "请输入联系人", trigger: "blur" },
        ],

        contactPhone: [
          {
            type: "number",
            validator: checkPhone,
            message: "请输入正确的手机号",
            trigger: "change",
            required: true,
          },
        ],
        firmName: [
          { required: true, message: "请输入所属单位", trigger: "blur" },
        ],
      },
      materialsRules: {
        materialName: [
          { required: true, message: "请输入装备名称", trigger: "blur" },
        ],
        teamName: [
          { required: true, message: "请输入所属队伍", trigger: "blur" },
        ],
        liabilityUser: [
          { required: true, message: "请输入负责人", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "请输入移动电话", trigger: "blur" },
          {
            pattern:
              /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        inventory: [
          { required: true, trigger: "blur", validator: checkNumber },
        ],
        // unit: [{ required: true, trigger: "change", validator: checkNumber1 }],
      },
      inAndOutRules: {
        storageQuantity: [
          { required: true, trigger: "blur", validator: checkInAndOut },
        ],
        outboundQuantity: [
          { required: true, trigger: "blur", validator: checkInAndOut },
        ],
      },
      selectedId: null,
      selectedName: null,
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadImgUrl:
        process.env.VUE_APP_BASE_API + "/emergency-v2/file/uploadFile",
      imageUrl: "",
      //是否为企业，判断所属单位是否禁用
      isEnterprise: false,
    };
  },
  watch: {},
  created() {
      this.getList();
    this.handleGetTeamList();
    console.log(this.$store.getters.enterprise,'lalallalaalla');
    if (this.$store.getters.enterprise.enterpriseName != null){
        this.queryParams={firmName:this.$store.getters.enterprise.enterpriseName}
        this.isEnterprise=true;
    }
    
  },
    methods: {
    //获取队伍
        handleGetTeamList() {
            getTeamListCom({ firmName: this.$store.getters.enterprise.enterpriseName }).then((res) => {
                this.ranksOptions = res.data
            })
    },
    handleAvatarSuccess(response, res, file) {
      console.log(URL.createObjectURL(res.raw));
      this.imageUrl = URL.createObjectURL(res.raw);
      console.log(response, res, file);
      this.materialsForm.photoUrl = res.response;
    },
    beforeAvatarUpload(file) {
      const isJPG =
        file.type === "image/jpeg" ||
        file.type === "image/png" ||
        file.type === "image/gif" ||
        file.type === "image/bmp" ||
        file.type === "image/psd" ||
        file.type === "image/tiff";
      const isLt2M = file.size / 1024 / 1024 < 500;

      if (!isJPG) {
        this.$message.error("仅支持 JPG/png/gif/bmp/psd/tiff 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传附件大小不能超过 500MB!");
      }
      return isJPG && isLt2M;
    },
    /** 查询物资列表 */
    getList() {
      this.loading = true;
        page({ ...this.queryParams,firmName: this.$store.getters.enterprise.enterpriseName}).then((response) => {
        console.log(response);
        if (response.data != null) {
          this.userList = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    //获取能力详情
        handleLook(row) {
        this.dateRange=[]
      console.log("99999999dhkj", row);
      this.selectedId = row.id;
      this.selectedName = row.name;
      this.reset();
      this.dialogTableVisible = true;
      this.supplyDepotId = row.id;
      this.getMaterialsList();
    },
    handleUpdate(row) {
      this.reset();
      detail({ id: row.id }).then((res) => {
        console.log(res);
        this.abilityForm = JSON.parse(JSON.stringify(res.data));
        this.abilityOpen = true;
        this.title = "查看应急物资库";
        this.lngAndLat = row.longitude + "," + row.latitude;
        this.disabled = true;
      });
    },
    handleAdd() {
      this.reset();
      this.abilityOpen = true;
      this.title = "新增应急物资库";
      this.placement = 1;
      this.abilityForm.placement = 1;
      this.disabled = false;
      },
      resetObject() {
          this.materialsForm = {}
       this.imageUrl = null
    },
    //新增物资
      clickMaterialsAdd() {
        this.resetObject()
      this.materialsFormdisabled = false;
      
      this.materialsOpen = true;
    },
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => {});
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    materialsCancel() {
      (this.materialsOpen = false), this.resetObject();
    },
    /*  确认保存新增物资库*/
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
          console.log(this.abilityForm,'ceshixinzeng-------');
        
          if (valid) {
            // this.$set(this.abilityForm,'firmName',this.$store.getters.enterprise.enterpriseName)
          if (this.abilityForm.materialId != undefined) {
            if (this.placement == 2) {
              this.abilityForm.refugeId = null;
            }
            update(this.abilityForm).then((response) => {
              console.log(response, "编辑");
              if (response.code == 200) {
                this.$modal.msgSuccess("编辑成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          } else {
            save(this.abilityForm).then((response) => {
              console.log(response, "新增");
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          }
        }
      });
      // console.log(this.evaluateData, "evaluateData");
    },
    // 确认物资保存
    materialsConfirm(formName) {
      this.materialsForm.supplyDepotId = this.supplyDepotId;
      this.$refs[formName].validate((valid) => {
        if (valid) {
            materialsAdd({ ...this.materialsForm,firmName: this.$store.getters.enterprise.enterpriseName }).then((response) => {
            console.log(response, "新增");
            if (response.code == 200) {
              this.$modal.msgSuccess("新增成功");
              this.materialsOpen = false;
              this.getMaterialsList();
            }
          });
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    // 表单重置
    reset() {
      this.abilityForm = {
        id: undefined,
        name: undefined,
        phone: undefined,
        refugeId: undefined,
        coordinate: undefined,
        unit: undefined,
      };
      this.materialsForm = {};
      this.inAndOutForm = {};
      this.materialsParams = {
        current: 1,
        size: 10,
        materialName: undefined,
        materialType: undefined,
        operator: undefined,
        supplyDepotId: undefined,
        startTime: undefined,
        endTime: undefined,
      };
      this.lngAndLat = undefined;
      this.resetForm("abilityForm");
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        current: 1,
        size: 10,
        name: undefined,
        liabilityUser: undefined,
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 打开地图按钮
    openMap() {
      this.mapVisible = true;
      this.$nextTick(() => {
        this.$refs.mapRef.initMap();
      });
    },
    // 地图返回经纬度的回调
    mapConfirm(lng, lat,addressInfo) {
      if (lng && lat) {
        this.mapVisible = false;
        this.lngAndLat = lng + "," + lat;
        this.abilityForm.longitude = lng;
        this.abilityForm.latitude = lat;
        this.abilityForm.administrativeDivision = addressInfo;
        console.log('lallalallalalal,',lng,lat,addressInfo);
        
        // 获取到经纬度就取消验证提示
        this.$nextTick(() => {
          this.$refs.abilityForm.clearValidate();
        });
      } else {
        this.$modal.msgSuccess("请选择经纬度");
      }
    },
    // 取消地图的回调
    mapCancellation() {
      this.mapVisible = false;
    },
    // 物资管理数据获取
    getMaterialsList() {
      // console.log(this.dateRange);
      if (this.dateRange) {
        this.materialsParams.startTime = this.dateRange[0];
        this.materialsParams.endTime = this.dateRange[1];
      } else {
        this.materialsParams.startTime = undefined;
        this.materialsParams.endTime = undefined;
      }
      this.materialsParams.supplyDepotId = this.supplyDepotId;
      materialsList(this.materialsParams).then((res) => {
        if (res.code == 200) {
          this.materialsList = res.data.records;
          this.materialsTotal = res.data.total;
        }
        console.log(res, "物资数据");
      });
    },
    // 用于显示列表的物资类型
    getNameById(res) {
      if (
        res.materialType != undefined &&
        res.materialType != "" &&
        res.materialType != null
      ) {
        return this.dict.type.materiel_type.filter(
          (item) => item.value == res.materialType
        )[0].label;
      }
    },
    // 物资名称和物资类型的change
    materialsChange(res) {
      this.getMaterialsList();
    },
    //查看物资详情
      handleLookArticle(val) {
          console.log(val);
        this.resetObject()
      this.imageUrl = val.photoUrl;

      this.materialsFormdisabled = true;
      this.materialsOpen = true;
      this.materialsForm = val;
      if (val.photoUrl != null && val.photoUrl != undefined) {
        let arr = val.photoUrl.split("/");

        handledownload(arr).then(async (res) => {
          const isLogin = await blobValidate(res);
          if (isLogin) {
            this.imageUrl = window.URL.createObjectURL(new Blob([res]));
          }
        });
      }
    },
    // 物资出入库弹出层
    handleInAndOut(res, flag) {
      console.log(this.inAndOutForm, "ssssss");
      if (flag == 1) {
        this.inAndOutTitle = "物资入库";
        this.inAndOutFlag = 1;
      } else {
        this.inAndOutTitle = "物资出库";
        this.inAndOutFlag = 2;
      }
      this.reset();
      this.inAndOutForm.inventory = res.inventory;
      this.inAndOutOpen = true;
      this.inAndOutForm.id = res.id;
    },
    inAndOutCancel() {
      this.inAndOutOpen = false;
      this.reset();
    },
    // 确认物资出入库保存
    inAndOutConfirm(formName) {
      if (this.inAndOutFlag == 1) {
        this.inAndOutForm.outboundQuantity = this.inAndOut;
      } else {
        this.inAndOutForm.storageQuantity = this.inAndOut;
      }
      console.log(this.inAndOutForm, "新增");
      this.$refs[formName].validate((valid) => {
        if (valid) {
          inAndOutAdd(this.inAndOutForm).then((response) => {
            if (response.code == 200) {
              this.$modal.msgSuccess(
                `物资${this.inAndOutFlag == 1 ? "人" : "出"}库成功`
              );
              this.inAndOutOpen = false;
              this.getMaterialsList();
            }
          });
        }
      });
    },
    inAndOutDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return inAndOutDelete({ id: row.id });
        })
        .then(() => {
          this.getMaterialsList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => {});
    },
    handleRecords(res) {
      this.recordsOpen = true;
      this.recordsId = res.id;
      this.records(1);
    },
    switchRecords(res) {
      this.records(res);
      if (res) {
      }
    },
    records(flag) {
      records({ id: this.recordsId }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          if (flag == 1) {
            this.recordsList = res.data.emergencyMaterialStorageRecords;
            this.plain1 = false;
            this.plain2 = true;
          } else {
            this.recordsList = res.data.emergencyMaterialOutboundRecords;
            this.plain1 = true;
            this.plain2 = false;
          }
        }
        console.log(res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
//   height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
//   font-size: 13px;
  float: right;
  margin-right: 10px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.resetQueryStyle {
  width: 88px;
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}
::v-deep .el-form-item__label {
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
.topBottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}

.topBottom .descriptions {
  -webkit-box-flex: 3;
  -ms-flex: 3;
  flex: 3;
}

.topBottom .tabButton {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.topBottom .tabButton button {
  float: right;
  margin: 0 5px;
}
.labelStyle {
  height: 14px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  letter-spacing: 0.04em;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -moz-text-align-last: justify;
  text-align-last: justify;
  margin: auto;
}
.avatar {
  width: 96px;
  height: 96px;
  display: block;
}
</style>
