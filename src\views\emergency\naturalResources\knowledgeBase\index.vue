<template>
  <div class="app-container">
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-col :span="1.5">
        <el-button
          type="primary"
          @click="handleAdd"
          v-hasPermi="['system:user:add']"
          >新增</el-button
        >
      </el-col>
    </el-row>

    <el-table :data="tableData" v-loading="loading" style="width: 100%">
      <el-table-column type="index" label="序号" align="center" width="50">
        <template slot-scope="scope">
          <span>{{
            (queryParams.current - 1) * queryParams.size + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="fileName" label="文件名称">
      </el-table-column>
      <el-table-column align="center" prop="fileType" label="文件类型">
      </el-table-column>
      <el-table-column align="center" prop="uploadTime" label="上传时间">
      </el-table-column>
      <el-table-column align="center" prop="createUser" label="创建人">
      </el-table-column>
      <el-table-column align="center" prop="updateTime" label="更新日期">
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            v-hasPermi="['system:user:edit']"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            size="mini"
            style="margin-left: 20px"
            v-hasPermi="['system:user:remove']"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
          <el-button
            type="text"
            size="mini"
            style="margin-left: 20px"
            v-hasPermi="['system:user:remove']"
            @click="download(scope.row)"
            >下载</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      layout="prev, pager, next, jumper"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改知识库对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="abilityOpen"
      width="680px"
      append-to-body
    >
      <el-form
        ref="knowledgeForm"
        :model="knowledgeForm"
        :rules="abilityRules"
        label-width="110px"
      >
        <el-form-item label="文件名称" prop="fileName">
          <el-input
            v-model="knowledgeForm.fileName"
            placeholder="请输入文件名称"
            maxlength="30"
          />
        </el-form-item>
        <el-form-item label="文件类型" prop="fileType">
          <el-select
            style="width: 100%"
            v-model="knowledgeForm.fileType"
            placeholder="请选择文件类型"
            :disabled="disabled"
          >
            <el-option
              v-for="dict in dict.type.file_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.label"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 上传 -->
        <el-form-item label="附件">
          <el-upload
            class="upload-demo"
            :limit="1"
            :on-exceed="handleExceed"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :action="uploadFileUrl"
            :on-success="handlePreview"
            :headers="headers"
            :file-list="fileList"
          >
            <el-button type="primary" :disabled="disabled">点击上传</el-button>
            <div slot="tip" class="el-upload__tip"><div slot="tip" class="el-upload__tip">支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB</div></div>
          </el-upload>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="knowledgeForm.remark"
            type="textarea"
            autosize
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm('knowledgeForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import {
  page,
  save,
  update,
  deleteById,
  download
} from "@/api/emergency/naturalResources/knowledgeBase/index";

export default {
  name: "knowledgeBase",
  dicts: ["file_type"],
  data() {
    return {
      // 遮罩层d
      loading: false,
      // 上传文件列表
      fileList: [],
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      // 上传的文件服务器地址
      uploadFileUrl: process.env.VUE_APP_BASE_API + "api/file/uploadFile",
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 是否显示弹出层
      abilityOpen: false,
      title: "新增应急知识库",
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
      },
      knowledgeForm: {
        fileName: undefined,
        fileType: undefined,
        remark: undefined,
        fileUrl: undefined,
      },
      disabled: false,
      // 表单校验
      abilityRules: {},
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    // 文件上传
    handlePreview(res, file) {
      if (res.code === 0) {
        this.$modal.msgSuccess(res.msg);
        this.knowledgeForm.fileUrl = res.fileUrl;
      }
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleRemove(file, fileList) {
      this.knowledgeForm.fileUrl = "";
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过1个!`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传失败，请重试");
    },
    /** 查询专家队伍列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        if (response.data != null) {
          console.log(response.data);
          this.tableData = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    handleUpdate(row) {
      this.abilityOpen = true;
      this.reset();
      this.knowledgeForm.fileId = row.fileId;
      this.knowledgeForm.fileName = row.fileName;
      this.knowledgeForm.fileType = row.fileType;
      this.knowledgeForm.remark = row.remark;
      this.title = "编辑应急知识库";
      this.disabled = true;
    },
    handleAdd() {
      this.reset();
      this.abilityOpen = true;
      this.title = "新增应急知识库";
      this.disabled = false;
    },
    handleDelete(row) {
      const eventAbilityId = row.fileId;
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ fileId: eventAbilityId });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    // 下载按钮
    download(row){
      // console.log(row);
      window.open(
          `/api/file/downloadFile?fileId=${row.fileId}`
        );
    },
    /*  确认保存新增*/
    confirm(formName) {
      console.log(this.knowledgeForm);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.knowledgeForm.fileId != undefined) {
            update(this.knowledgeForm).then((response) => {
              console.log(response, "编辑");
              if (response.code == 200) {
                this.$modal.msgSuccess("编辑成功");
                this.abilityOpen = false;
                this.getList();
              }
            });
          } else {
            save(this.knowledgeForm).then((response) => {
              console.log(response, "新增");
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");
                this.abilityOpen = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.knowledgeForm = {
        fileName: "",
        fileType: "",
        remark: "",
        fileUrl: "",
      };
      this.fileList = [];
      this.resetForm("knowledgeForm");
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}
</style>