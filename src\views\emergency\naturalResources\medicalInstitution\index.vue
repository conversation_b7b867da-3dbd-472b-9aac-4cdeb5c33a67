<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-position="left"
            style="display: flex; justify-content: space-between"
          >
            <div>
              <el-form-item label="机构类型 :" prop="hygieneType">
                <el-select
                  style="width: 10vw"
                  v-model="queryParams.hygieneType"
                  placeholder="请选择机构类型"
                >
                  <el-option
                    v-for="dict in dict.type.hygiene_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="机构名称">
                <el-input
                  v-model.number="queryParams.name"
                  placeholder="请输入机构名称"
                  clearable
                  maxlength="20"
                  style="width: 10vw"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <!-- <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 10vw"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item> -->
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button
                  class="resetQueryStyle"
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  class="resetQueryStyle"
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>医疗卫生机构展示列表</span>
            <el-button
              type="primary"
              
              size="mini"
              @click="handleAdd"
              icon="el-icon-plus"
              class="queryBtnT"
              >新增医疗卫生机构</el-button
            >
          </div>
          <el-table
            v-loading="loading"
            :data="shelter"
            :cell-style="{ padding: '0px' }"
            :row-style="{ height: '48px' }"
          >
            <el-table-column label="序号" align="center">
              <template slot-scope="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size +
                  scope.$index +
                  1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="医疗卫生机构名称"
              align="center"
              prop="name"
              show-overflow-tooltip
            />
            <el-table-column label="类型" align="center">
              <template slot-scope="scope">
                {{ getTypeById(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column
              label="能力情况"
              align="center"
              prop="capacityStatus"
              show-overflow-tooltip
            />
            <el-table-column
              label="责任人"
              align="center"
              prop="liabilityUser"
              show-overflow-tooltip
            />
            <el-table-column label="联系方式" align="center" prop="phone" />
            <el-table-column
              label="操作"
              align="center"
              width="220"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  icon="el-icon-view"
                  @click="handleLook(scope.row)"
                  >查看</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.current"
            :limit.sync="queryParams.size"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改避难所信息对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="abilityOpen"
      width="560px"
      append-to-body
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form
            ref="abilityForm"
            :model="abilityForm"
            :rules="abilityRules"
            label-width="110px"
          >
            <el-form-item label="经纬度 :" prop="lngAndLat">
              <el-button type="primary" @click="openMap()">{{
                lngAndLat ? lngAndLat : "点击选择"
              }}</el-button>
            </el-form-item>
            <el-form-item label="机构名称" prop="name" label-width="110px">
              <el-input
                v-model="abilityForm.name"
                placeholder="请输入单位名称"
                maxlength="20"
                :disabled="disabled"
                style="width: 245px"
              />
            </el-form-item>
            <el-form-item label="类型 :" prop="hygieneType" label-width="110px">
              <el-select
                style="width: 245px"
                v-model="abilityForm.hygieneType"
                placeholder="请选择风险类型"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.hygiene_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="能力情况"
              prop="capacityStatus"
              label-width="110px"
            >
              <el-input
                v-model="abilityForm.capacityStatus"
                placeholder="请输入能力情况"
                maxlength="20"
                :disabled="disabled"
                style="width: 245px"
              />
            </el-form-item>
            <el-form-item
              label="负责人"
              prop="liabilityUser"
              label-width="110px"
            >
              <el-input
                v-model="abilityForm.liabilityUser"
                placeholder="请输入负责人"
                maxlength="20"
                :disabled="disabled"
                style="width: 245px"
              />
            </el-form-item>
            <el-form-item label="联系电话" prop="phone" label-width="110px">
              <el-input
                v-model="abilityForm.phone"
                placeholder="请输入联系电话"
                maxlength="20"
                :disabled="disabled"
                style="width: 245px"
              />
            </el-form-item>
            <el-form-item label="备注" prop="remark" label-width="110px">
              <el-input
                v-model="abilityForm.remark"
                placeholder="请输入备注"
                type="textarea"
                maxlength="200"
                :disabled="disabled"
                style="width: 245px"
              ></el-input>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="popupButton"
          type="primary"
          @click="confirm('abilityForm')"
          :disabled="disabled"
          >确 定</el-button
        >
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 地图展示 -->
    <Map
      @mapConfirm="mapConfirm"
      :ranksForm="abilityForm"
      :mapVisible="mapVisible"
      :disabled="disabled"
      @mapCancellation="mapCancellation"
      ref="mapRef"
      :url="url"
    ></Map>
  </div>
</template>
    
<script>
import {
  page,
  save,
  update,
  deleteById,
} from "@/api/emergency/naturalResources/medicalInstitution/index";
import Map from "../../../map/index.vue";
export default {
  name: "EmergencySupplies",
  dicts: ["hygiene_type"],
  components: { Map },
  data() {
    let checkPhone = (rule, value, callback) => {
      let reg = /^1[345789]\d{9}$/;
      if (!reg.test(value)) {
        callback(new Error("请输入11位手机号"));
      } else {
        callback();
      }
    };
    const validCode = (rule, value, callback) => {
      console.log(rule, value, "value");
      if (this.lngAndLat) {
        callback();
      } else {
        callback(new Error("请选择经纬度"));
      }
    };
    return {
      // 地图点标记图标地址
      url: require("../../../../assets/icons/medical.png"),
      // 地图遮罩层
      mapVisible: false,
      lngAndLat: "",
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      shelter: null,
      // 是否显示弹出层
      abilityOpen: false,
      title: "新增风险隐患部位",
      text: undefined,
      imgUrl: `${require("@/assets/images/map.png")}`,
      dateRange: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        name: undefined,
        hygieneType: undefined,
      },
      abilityForm: {},
      disabled: false,
      // 表单校验
      abilityRules: {
        name: [
          { required: true, message: "机构名称不能为空", trigger: "blur" },
        ],
        hygieneType: [
          { required: true, message: "请选择机构类型", trigger: "blur" },
        ],
        liabilityUser: [
          { required: true, message: "负责人不能为空", trigger: "blur" },
        ],
        lngAndLat: [{ required: true, validator: validCode, trigger: "blur" }],
        phone: [
          {
            type: "number",
            validator: checkPhone,
            message: "请输入正确的手机号",
            trigger: "change",
            required: true,
          },
        ],
      },
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    /** 查询场所列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams);
      page(this.queryParams).then((response) => {
        console.log(response);
        if (response.data != null) {
          this.shelter = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    //获取场所详情

    handleLook(row) {
      this.reset();
      this.abilityOpen = true;
      this.abilityForm = JSON.parse(JSON.stringify(row));
      this.title = "查看医疗卫生机构";
      this.disabled = true;
      this.lngAndLat = row.longitude + "," + row.latitude;
      console.log(this.abilityForm);
    },
    handleUpdate(row) {
      this.reset();
      this.abilityOpen = true;
      this.title = "编辑医疗卫生机构";
      this.disabled = false;
      this.lngAndLat = row.longitude + "," + row.latitude;
      this.abilityForm = JSON.parse(JSON.stringify(row));
    },
    handleAdd() {
      this.reset();
      this.abilityOpen = true;
      this.title = "新增医疗卫生机构";
      this.disabled = false;
    },
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          console.log(row.id);
          return deleteById({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => {});
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    /*  确认保存新增*/
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.abilityForm.id != undefined) {
            update(this.abilityForm).then((response) => {
              // console.log(response, "编辑");
              if (response.code == 200) {
                this.$modal.msgSuccess("编辑成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          } else {
            save(this.abilityForm).then((response) => {
              console.log(response, this.abilityForm, "新增");
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          }
        }
      });
      // console.log(this.evaluateData, "evaluateData");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.queryParams.startTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];
      this.getList();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.abilityForm = {};
      this.lngAndLat = "";
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        current: 1,
        size: 10,
        name: undefined,
        hygieneType: undefined,
      };
      this.handleQuery();
    },
    // 打开地图按钮
    openMap() {
      this.mapVisible = true;
      this.$nextTick(() => {
        this.$refs.mapRef.initMap();
      });
    },
    // 地图返回经纬度的回调
    mapConfirm(lng, lat) {
      if (lng && lat) {
        this.mapVisible = false;
        this.lngAndLat = lng + "," + lat;
        this.abilityForm.longitude = lng;
        this.abilityForm.latitude = lat;
        // 获取到经纬度就取消验证提示
        this.$nextTick(() => {
          this.$refs.abilityForm.clearValidate();
        });
      } else {
        this.$modal.msgSuccess("请选择经纬度");
      }
    },
    // 取消地图的回调
    mapCancellation() {
      this.mapVisible = false;
    },
    getTypeById(res) {
      //   console.log(res, this.dict.type.hygiene_type);
      if (
        res.hygieneType != undefined &&
        res.hygieneType != "" &&
        res.hygieneType != null
      ) {
        return this.dict.type.hygiene_type.filter(
          (item) => item.value == res.hygieneType
        )[0].label;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
//   font-size: 13px;
  float: right;
  margin-right: 10px;
}

.resetQueryStyle {
//   width: 88px;
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}
::v-deep .el-form-item__label {
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>