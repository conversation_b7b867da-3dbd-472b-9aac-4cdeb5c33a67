<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <!--用户数据-->
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-position="right"
            style="display: flex; justify-content: space-between"
          >
            <div>
              <el-form-item label="专家姓名 :" label-width="120px">
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入专家姓名"
                  clearable
                  maxlength="20"
                  style="width: 10vw"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="类型 :" prop="expertType" label-width="120px">
                <el-select
                  style="width: 10vw"
                  v-model="queryParams.expertType"
                  placeholder="请选择类型"
                >
                  <el-option
                    v-for="dict in dict.type.emergency_expert_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="擅长事故类型 :" prop="eventType" label-width="120px">
                <el-select
                  style="width: 10vw"
                  v-model="queryParams.eventType"
                  placeholder="请选择擅长事故类型"
                >
                  <el-option
                    v-for="dict in eventTypeData"
                    :key="dict.value"
                    :label="dict.name"
                    :value="dict.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="专家级别 :" prop="expertLevel" label-width="120px">
                <el-select
                  style="width: 10vw"
                  v-model="queryParams.expertLevel"
                  placeholder="请选择专家级别"
                >
                  <el-option
                    v-for="dict in dict.type.emergency_expert_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="专家工作单位 :" label-width="120px">
                <el-input
                  v-model="queryParams.workplace"
                  placeholder="请输入专家工作单位"
                  clearable
                  maxlength="20"
                  style="width: 10vw"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </div>
            <div style="min-width: 200px">
              <el-form-item>
                <el-button
                style="font-size:13px"
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                style="font-size:13px"
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card>
          <div slot="header" class="clearfix">
            <span>救援专家展示列表</span>
            <div class="header-btns">
              <el-button
                type="primary"
                
                size="mini"
                @click="handleAdd"
                icon="el-icon-plus"
                class="queryBtnT"
                >新增救援专家</el-button
              >
              <el-button
                type="primary"
                class="queryBtn"
                size="mini"
                ><template><el-upload
                        class="upload-demo"  
                        :on-error="onError"
                        :on-success="handleImportSuccess"
                        action="/emergency-v2/emergency-expert/import"
                        :headers="headers"
                        :file-list="fileListImport">
                            <div class="select" style="cursor: pointer">导入信息</div>
    
                        </el-upload></template></el-button>
                
              <el-button
                type="primary"
                class="queryBtn"
                size="mini"
                @click="exportReport()"
                >导出</el-button
              >
              <el-button
                type="primary"
                class="queryBtn"
                size="mini"
                @click="stationaryPlaten()"
                >固定模板</el-button
              >
            </div>
          </div>
          <el-table
            v-loading="loading"
            :data="userList"
            :cell-style="{ padding: '0px' }"
            :row-style="{ height: '48px' }"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="序号" type="index" width="70" align="center">
              <template slot-scope="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size + scope.$index + 1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="专家姓名"
              align="center"
              prop="name"
              show-overflow-tooltip
            />
            <el-table-column
              label="性别"
              align="center"
              prop="gender"
              show-overflow-tooltip
            />
            <el-table-column label="类型" align="center">
              <template slot-scope="scope">
                {{ getTypeById(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="擅长事故类型" align="center">
              <template slot-scope="scope">
                {{ getNameById1(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="专家级别" align="center">
              <template slot-scope="scope">
                {{ getNameById(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="审核状态" align="center">
              <template slot-scope="scope">
                {{
                  dict.type.event_state&&dict.type.event_state.find(
                    (ele) => ele.value == scope.row.approveStatus
                  )!=undefined?dict.type.event_state.find(
                    (ele) => ele.value == scope.row.approveStatus
                  ).label:'-'
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="300"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <!-- <el-button size="mini" type="text" icon="el-icon-plus" 
                  @click="handleRankadd(scope.row)">新增人员</el-button> -->
                <el-button
                  type="text"
                  icon="el-icon-view"
                  @click="handleLook(scope.row)"
                  >查看</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
                <!-- <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  >呼叫</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  >短信</el-button
                > -->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.current"
            :limit.sync="queryParams.size"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改队伍配置对话框 -->
    <el-dialog :title="title" :visible.sync="abilityOpen" width="960px" append-to-body>
      <el-form
        ref="ranksForm"
        :model="ranksForm"
        :rules="abilityRules"
        label-width="130px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="头像 :" prop="contingentName">
              <el-upload
                class="avatar-uploader"
                :action="uploadImgUrl"
                :headers="headers"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名 :" prop="name">
              <el-input
                style="width: 245px"
                v-model="ranksForm.name"
                placeholder="请输入姓名"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别 :" prop="gender">
              <el-select
                style="width: 245px"
                v-model="ranksForm.gender"
                placeholder="请选择性别"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in genderData"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="专家编号 :" prop="expertNumber">
              <el-input
                style="width: 245px"
                v-model="ranksForm.expertNumber"
                placeholder="请输入专家编号"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统一标识码 :" prop="unificationCode">
              <el-input
                style="width: 245px"
                v-model="ranksForm.unificationCode"
                placeholder="请输入统一标识码"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="擅长事故类型 :" prop="eventType" label-width="120px">
              <el-select
                style="width: 245px"
                multiple
                v-model="ranksForm.eventType"
                placeholder="请选择擅长事故类型"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in eventTypeData"
                  :key="dict.value"
                  :label="dict.name"
                  :value="dict.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专家级别 :" prop="expertLevel">
              <el-select
                style="width: 245px"
                v-model="ranksForm.expertLevel"
                placeholder="请选择专家级别"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.emergency_expert_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="经纬度 :" prop="lngAndLat">
              <el-button
                type="primary"
                plain
                @click="openMap()"
                >{{ lngAndLat ? lngAndLat : "点击选择" }}</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出生日期 :" prop="bornTime">
              <el-date-picker
                style="width: 245px"
                v-model="ranksForm.bornTime"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="籍贯 :" prop="origin">
              <el-input
                style="width: 245px"
                v-model="ranksForm.origin"
                placeholder="请输入籍贯"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="民族 :" prop="ethnicGroup">
              <el-input
                style="width: 245px"
                v-model="ranksForm.ethnicGroup"
                placeholder="请输入民族"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="职称 :" prop="jobTitle">
              <el-input
                style="width: 245px"
                v-model="ranksForm.jobTitle"
                placeholder="请输入职称"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行政职务 :" prop="administrativePositions">
              <el-input
                style="width: 245px"
                v-model="ranksForm.administrativePositions"
                placeholder="请输入行政职务"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="身份证号码 :" prop="idNumber">
              <el-input
                style="width: 245px"
                v-model="ranksForm.idNumber"
                placeholder="请输入身份证号码"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="政治面貌 :" prop="politicalLandscape">
              <el-input
                style="width: 245px"
                v-model="ranksForm.politicalLandscape"
                placeholder="请输入政治面貌"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="参加工作时间 :" prop="workingHours">
              <el-date-picker
                style="width: 245px"
                v-model="ranksForm.workingHours"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :disabled="disabled"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="健康状况 :" prop="healthStatus">
              <el-input
                style="width: 245px"
                v-model="ranksForm.healthStatus"
                placeholder="请输入健康状况"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="最高学历 :" prop="highestEducation">
              <el-input
                style="width: 245px"
                v-model="ranksForm.highestEducation"
                placeholder="请输入最高学历"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="毕业院校 :" prop="graduateSchool">
              <el-input
                style="width: 245px"
                v-model="ranksForm.graduateSchool"
                placeholder="请输入毕业院校"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="家庭住址 :" prop="homeAddress">
              <el-input
                style="width: 245px"
                v-model="ranksForm.homeAddress"
                placeholder="请输入家庭住址"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作单位 :" prop="workplace">
              <el-input
                style="width: 245px"
                v-model="ranksForm.workplace"
                placeholder="请输入工作单位"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位主管部门 :" prop="unitCompetentDepartment">
              <el-input
                style="width: 245px"
                v-model="ranksForm.unitCompetentDepartment"
                placeholder="请输入单位主管部门"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位邮政编码 :" prop="unitPostalCode">
              <el-input
                style="width: 245px"
                v-model="ranksForm.unitPostalCode"
                placeholder="请输入单位邮政编码"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="通信地址 :" prop="mailingAddress">
              <el-input
                style="width: 245px"
                v-model="ranksForm.mailingAddress"
                placeholder="请输入通信地址"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="户籍所在地 :" prop="placeDomicile">
              <el-input
                style="width: 245px"
                v-model="ranksForm.placeDomicile"
                placeholder="请输入户籍所在地"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="专业类型 :" prop="expertType">
              <el-select
                style="width: 245px"
                v-model="ranksForm.expertType"
                placeholder="请选择专业类型"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.emergency_expert_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专业特长描述 :" prop="professionalExpertiseDescription">
              <el-input
                style="width: 245px"
                v-model="ranksForm.professionalExpertiseDescription"
                placeholder="请输入专业特长描述"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工作简历概述 :" prop="jobResumeDescription">
              <el-input
                style="width: 245px"
                v-model="ranksForm.jobResumeDescription"
                placeholder="请输入工作简历概述"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主要成果 :" prop="mainResults">
              <el-input
                style="width: 245px"
                v-model="ranksForm.mainResults"
                placeholder="请输入主要成果"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="办公电话 :" prop="officePhone">
              <el-input
                style="width: 245px"
                v-model="ranksForm.officePhone"
                placeholder="请输入办公电话"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="移动电话 :" prop="mobilePhone">
              <el-input
                style="width: 245px"
                v-model="ranksForm.mobilePhone"
                placeholder="请输入移动电话"
                maxlength="200"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div v-show="!disabled" slot="footer" class="dialog-footer">
        <el-button class="popupButton" type="primary" @click="confirm('ranksForm')"
          >确 定</el-button
        >
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 地图展示 -->
    <Map
      @mapConfirm="mapConfirm"
      :ranksForm="ranksForm"
      :disabled="disabled"
      :mapVisible="mapVisible"
      @mapCancellation="mapCancellation"
      ref="mapRef"
      :url="url"
    ></Map>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import Map from "../../../map/index.vue";
import {
  page,
  save,
  saveStaff,
  removeStaff,
  update,
  deleteById,
  eventType,
  handledownload,
  exportTemplateExpert,
  exportExpert
} from "@/api/emergency/naturalResources/rescueExperts/index";
import { blobValidate } from "@/utils/ruoyi";
export default {
  name: "Specialist",
  dicts: ["plan_type", "emergency_expert_type", "emergency_expert_level", "event_state"],
  components: { Map },
  data() {
    const validCode = (rule, value, callback) => {
      console.log(rule, value, "value");
      if (this.lngAndLat) {
        callback();
      } else {
        callback(new Error("请选择经纬度"));
      }
    };
    let checkPhone = (rule, value, callback) => {
      let reg = /^1[345789]\d{9}$/;
      if (!reg.test(value)) {
        callback(new Error("请输入11位手机号"));
      } else {
        callback();
      }
    };
    return {
      // 地图点标记图标地址
      url: require("@/assets/icons/name.png"),
      // 地图遮罩层
      mapVisible: false,
      lngAndLat: "",
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      userList: null,
      tableData: null,
      // 是否显示弹出层
      abilityOpen: false,
      ranksOpen: false,
      ranksAdd: false,
      title: "新增救援专家",
      genderData: [
        {
          label: "男",
          value: 1,
        },
        {
          label: "女",
          value: 2,
        },
      ],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        contingentType: undefined,
        contingentName: undefined,
      },

      ranksForm: {},
      rankaddForm: {
        dictKey: undefined,
        contact: undefined,
        phone: undefined,
      },
      disabled: false,
      // 表单校验
      abilityRules: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        gender: [{ required: true, message: "请选择性别", trigger: "blur" }],
        expertNumber: [{ required: true, message: "专家编号不能为空", trigger: "blur" }],
        unificationCode: [
          { required: true, message: "统一标识码不能为空", trigger: "blur" },
        ],
        mobilePhone: [
          {
            type: "number",
            validator: checkPhone,
            message: "请输入正确的手机号",
            trigger: "change",
            required: true,
          },
        ],
        eventType: [{ required: true, message: "请选择擅长事故类型", trigger: "change" }],
        workplace: [{ required: true, message: "工作单位不能为空", trigger: "change" }],
        expertType: [{ required: true, message: "请选择专业类型", trigger: "change" }],
        professionalExpertiseDescription: [
          { required: true, message: "专业特长描述", trigger: "change" },
        ],
        lngAndLat: [{ required: true, validator: validCode, trigger: "blur" }],
      },
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/emergency-v2/file/uploadFile",
      imageUrl: "",
      eventTypeData: [],
      //批量操作id数组
        batchData: [],
        fileListImport:[]
    };
  },
  watch: {},
  created() {
    this.eventType();
    this.getList();
  },
  mounted() {},
  methods: {
    /** 查询专家队伍列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        if (response.data != null) {
          this.userList = response.data.records;
          console.log(this.userList);
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    // 查看人员
    handleLook(row) {
      this.reset();
      this.abilityOpen = true;
      this.imageUrl = row.avatar;
      this.ranksForm = JSON.parse(JSON.stringify(row));
      this.ranksForm.eventType = this.ranksForm.eventTypes;
      this.title = "查看救援专家";
      this.disabled = true;
      this.lngAndLat = row.longitude + "," + row.latitude;
      let arr = row.avatar.split("/");
      handledownload(arr).then(async (res) => {
        const isLogin = await blobValidate(res);
        if (isLogin) {
            this.imageUrl = window.URL.createObjectURL(new Blob([res]));
          console.log(this.imageUrl);
        }
      });
      console.log(this.abilityForm);
    },
    // 编辑人员
    handleUpdate(row) {
      console.log(row, "row");
      this.abilityOpen = true;
      this.reset();
      this.ranksForm = row;
      // this.imageUrl = row.avatar;
      this.disabled = false;
      this.ranksForm.eventType = this.ranksForm.eventTypes;
      this.lngAndLat = row.longitude + "," + row.latitude;
      let arr = row.avatar.split("/");
      handledownload(arr).then(async (res) => {
        const isLogin = await blobValidate(res);
        if (isLogin) {
          this.imageUrl = window.URL.createObjectURL(new Blob([res]));
        }
      });
      // this.ranksForm.id = row.id;
      // this.ranksForm.dictKey = row.id;
      // this.ranksForm.contingentName=row.contingentName
      // this.ranksForm.contingentType=row.contingentType
      // this.ranksForm.phone = row.phone;
      // this.ranksForm.contingentNumber = row.contingentNumber;
      // this.ranksForm.companyName = row.companyName;
      // this.ranksForm.contact = row.contact;
      // this.ranksForm.phone = row.phone;
      this.title = "编辑救援专家";
    },
    handleAdd() {
      this.reset();
      if (this.$refs.ranksForm) {
        this.$refs.ranksForm.clearValidate();
      }
      this.abilityOpen = true;
      this.title = "新增救援专家";
      this.disabled = false;
    },
    handleRankadd(row) {
      this.reset();
      this.rankaddForm.dictKey = row.id;
      this.ranksAdd = true;
    },
    handleDelete(row) {
      const eventAbilityId = row.id;
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ id: eventAbilityId });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    getNameById(res) {
      // console.log(res);
      if (
        res.expertLevel != undefined &&
        res.expertLevel != "" &&
        res.expertLevel != null
      ) {
        return this.dict.type.emergency_expert_level.filter(
          (item) => item.value == res.expertLevel
        )[0].label;
      }
    },
    getNameById1(res) {
      // console.log(res);
      const arr = [];
      if (res.eventTypes != undefined && res.eventTypes != "" && res.eventTypes != null) {
        res.eventTypes.forEach((ele) => {
          arr.push(this.eventTypeData.filter((item) => item.id == ele)[0].name);
        });
      }
      return arr.toString();
    },
    getTypeById(res) {
      if (res.expertType != undefined && res.expertType != "" && res.expertType != null) {
        return this.dict.type.emergency_expert_type.filter(
          (item) => item.value == res.expertType
        )[0].label;
      }
    },
    /*  确认保存新增*/
    confirm(formName) {
      console.log(this.ranksForm);
      if (formName == "ranksForm") {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if (this.ranksForm.id != undefined) {
              this.ranksForm.eventType = this.ranksForm.eventType.toString();
              update(this.ranksForm).then((response) => {
                console.log(response, "编辑");
                if (response.code == 200) {
                  this.$modal.msgSuccess("编辑成功");
                  this.abilityOpen = false;
                  this.getList();
                }
              });
            } else {
              this.ranksForm.eventType = this.ranksForm.eventType.toString();
              save(this.ranksForm).then((response) => {
                console.log(this.ranksForm, "新增");
                if (response.code == 200) {
                  this.$modal.msgSuccess("新增成功");
                  this.abilityOpen = false;
                  this.getList();
                }
              });
            }
          }
        });
      }
    },
    removeStaff(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return removeStaff({ id: row.id });
        })
        .then(() => {
          this.ranksOpen = false;
          this.getList();
          this.$modal.msgSuccess("删除队伍人员成功！");
        })
        .catch(() => {});
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.ranksForm = {};
      this.rankaddForm = {
        dictKey: undefined,
        contact: undefined,
        phone: undefined,
      };
      this.imageUrl = "";
      this.lngAndLat = "";
    },

    /** 重置按钮操作 */
    resetQuery() {
      (this.queryParams = {
        current: 1,
        size: 10,
        liabilityUser: undefined,
        phone: undefined,
      }),
        this.resetForm("queryForm");
      this.handleQuery();
    },
    // 打开地图按钮
    openMap() {
      this.mapVisible = true;
      this.$nextTick(() => {
        this.$refs.mapRef.initMap();
      });
    },
    // 地图返回经纬度的回调
    mapConfirm(lng, lat) {
      if (lng && lat) {
        this.mapVisible = false;
        this.lngAndLat = lng + "," + lat;
        this.ranksForm.longitude = lng;
        this.ranksForm.latitude = lat;
        // 获取到经纬度就取消验证提示
        this.$nextTick(() => {
          this.$refs.ranksForm.clearValidate();
        });
      } else {
        this.$modal.msgSuccess("请选择经纬度");
      }
    },
    // 取消地图的回调
    mapCancellation() {
      this.mapVisible = false;
    },
    handleAvatarSuccess(response, res, file) {
      this.imageUrl = URL.createObjectURL(res.raw);
      console.log(response, res, file);
      this.ranksForm.avatar = res.response;
    },
    beforeAvatarUpload(file) {
      const isJPG =
        file.type === "image/jpeg" ||
        file.type === "image/png" ||
        file.type === "image/gif" ||
        file.type === "image/bmp" ||
        file.type === "image/psd" ||
        file.type === "image/tiff";
      const isLt2M = file.size / 1024 / 1024 < 500;

      if (!isJPG) {
        this.$message.error("仅支持 JPG/png/gif/bmp/psd/tiff 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传附件大小不能超过 500MB!");
      }
      return isJPG && isLt2M;
    },
    eventType() {
      eventType().then((res) => {
        if ((res.code = 200)) {
          this.eventTypeData = res.data;
        }
        console.log(res);
      });
    },
    //导入导出相关代码
      //导出
      download(blob, name) {
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.href = url;
        link.download = name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      },
         //固定模板
      stationaryPlaten() {
        exportTemplateExpert().then((res) => {
          this.download(res, "导入模版.xlsx");
        });
            },
      //导出
      exportReport() {
        //       if (this.batchData.length <= 0) {
        // this.$message.error("至少选择一条数据")
        
        //           } else {
                    console.log(this.batchData,'this.batchData--------');
                    
        exportExpert({idList:this.batchData }).then((res) => {    
            this.download(res,'导出救援专家报表.xlsx')
        })
    // }
        },
        onError(){
          this.$message.error('无法导入！请检查导入数据')
            },
        handleImportSuccess(res) {
            if (res.code != 200) {
        this.$modal.msgError(res.msg);
      } else {
        this.$modal.msgSuccess("导入成功");
      }
      this.resetQuery();
              
        },
        handleSelectionChange(val) {
        console.log(val);
        //可以获得具体行数
        this.multipleSelection = val;
        console.log(this.multipleSelection);
        this.batchData=[];
        if (this.multipleSelection.length > 0) {
          this.batchData = [];
          this.multipleSelection.forEach((item) => {
            this.batchData.push(item.id);
          });
          console.log(this.batchData);
        }
      },
  },
};
</script>
<style lang="scss" scoped>
.header-btns{
    float: right;

  }
  .queryBtn ::v-deep .el-upload-list--text{
    display: none !important;
  }
  .queryBtn ::v-deep .el-upload-list{
    display: none !important;
  }
  .queryBtn ::v-deep .el-upload-list__item .is-uploading{
    display: none !important;
  }
  .uploadList ::v-deep .el-upload-list .el-progress{
display: none !important;
}
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

::v-deep.box-card .topBottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}

::v-deep.box-card .topBottom .descriptions {
  -webkit-box-flex: 6;
  -ms-flex: 6;
  flex: 6;
}

::v-deep.box-card .topBottom .tabButton {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

::v-deep.box-card .topBottom .tabButton button {
  float: right;
  margin: 0 5px;
}

.queryBtnT {
//   height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
//   font-size: 13px;
  // float: right;
  // margin-right: 10px;
}

.resetQueryStyle {
  width: 88px;
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}
::v-deep .el-form-item__label {
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 96px;
  height: 96px;
  line-height: 96px;
  text-align: center;
}

.avatar {
  width: 96px;
  height: 96px;
  display: block;
}
</style>
