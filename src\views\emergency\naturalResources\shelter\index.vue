<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-position="left"
            style="display: flex; justify-content: space-between"
          >
            <div>
              <el-form-item label="避难所名称">
                <el-input
                  v-model.number="queryParams.refugeName"
                  placeholder="请输入避难所名称"
                  clearable
                  style="width: 10vw"
                  maxlength="20"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="联系人">
                <el-input
                  v-model="queryParams.liabilityUser"
                  placeholder="请输入联系人"
                  clearable
                  style="width: 10vw"
                  maxlength="20"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="联系电话">
                <el-input
                  v-model.number="queryParams.phone"
                  placeholder="请输入联系电话"
                  clearable
                  style="width: 10vw"
                  maxlength="20"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button
                  class="resetQueryStyle"
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  class="resetQueryStyle"
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>避难所展示列表</span>
            <el-button
              type="primary"
              
              size="mini"
              @click="handleAdd"
              icon="el-icon-plus"
              class="queryBtnT"
              >新增避难所</el-button
            >
          </div>
          <el-table v-loading="loading" :data="shelter">
            <el-table-column label="序号" align="center">
              <template slot-scope="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size + scope.$index + 1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="避难所名称"
              align="center"
              prop="refugeName"
              show-overflow-tooltip
            />
            <el-table-column
              label="场所面积"
              align="center"
              prop="refugeArea"
              show-overflow-tooltip
            />
            <el-table-column
              label="容纳人数"
              align="center"
              prop="holdsNumber"
              show-overflow-tooltip
            />
            <el-table-column
              label="联系人"
              align="center"
              prop="liabilityUser"
              show-overflow-tooltip
            />
            <el-table-column label="联系方式" align="center" prop="phone" />
            <el-table-column
              label="操作"
              align="center"
              width="220"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  icon="el-icon-view"
                  @click="handleLook(scope.row)"
                  >查看</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.current"
            :limit.sync="queryParams.size"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改避难所信息对话框 -->
    <el-dialog :title="title" :visible.sync="abilityOpen" width="960px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form
            ref="abilityForm"
            :model="abilityForm"
            :rules="abilityRules"
            label-width="110px"
          >
           <el-row>
            <el-col :span="12">
              <el-form-item label="避难所名称" prop="refugeName">
                <el-input
                  v-model="abilityForm.refugeName"
                  placeholder="请输入避难所名称"
                  maxlength="20"
                  :disabled="disabled"
                  style="width: 245px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="经纬度 :" prop="lngAndLat">
                <el-button type="primary" @click="openMap()">{{
                  lngAndLat ? lngAndLat : "点击选择"
                }}</el-button>
              </el-form-item>
            </el-col>
            </el-row>
            <el-row>  
              <el-col :span="12">
                  <el-form-item label="行政区划" prop="administrativeDivision">
                    <el-input
                      v-model.trim="abilityForm.administrativeDivision"
                      style="width: 245px"
                      placeholder="请输入行政区划"
                      maxlength="20"
                      :disabled="true"
                    />
                  </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="详细地址" prop="detailedAddress">
                  <el-input
                    v-model="abilityForm.detailedAddress"
                    style="width: 245px"
                    placeholder="请输入详细地址"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>  
              <el-col :span="12">
                  <el-form-item label="避难所编号" prop="refugeId">
                    <el-input
                      v-model.trim="abilityForm.refugeId"
                      style="width: 245px"
                      placeholder="请输入避难所编号"
                      maxlength="20"
                      :disabled="disabled"
                    />
                  </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="周边交通情况" prop="trafficCondition">
                  <el-input
                    v-model="abilityForm.trafficCondition"
                    style="width: 245px"
                    placeholder="请输入周边交通情况"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="避难所级别" prop="shelterLevel">
                  <el-select
                  v-model="abilityForm.shelterLevel"
                  placeholder="请选择避难所级别"
                  style="width: 245px"
                >
                  <el-option
                    v-for="dict in dict.type.shelterLevel"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="避难所类型" prop="shelterType">
                  <el-select
                  v-model="abilityForm.shelterType"
                  placeholder="请选择避难所类型"
                  style="width: 245px"
                >
                  <el-option
                    v-for="dict in dict.type.shelterType"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="避难所密级" prop="shelterSecurityLevel">
                  <el-select
                  v-model="abilityForm.shelterSecurityLevel"
                  placeholder="请选择避难所密级"
                  style="width: 245px"
                >
                  <el-option
                    v-for="dict in dict.type.shelterSecurityLevel"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="应急通信方式" prop="emergencyCommunication">
                  <el-select
                  v-model="abilityForm.emergencyCommunication"
                  placeholder="请选择应急通信方式"
                  style="width: 245px"
                  multiple
                  collapse-tags
                >
                  <el-option
                    v-for="dict in dict.type.emergencyCommunication"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="抗震设防烈度" prop="seismicIntensity">
                  <el-select
                  v-model="abilityForm.seismicIntensity"
                  placeholder="请选择抗震设防烈度"
                  style="width: 245px"
                  multiple
                  collapse-tags
                >
                  <el-option
                    v-for="dict in dict.type.seismicIntensity"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="防护区域" prop="protectiveUnits">
                  <el-input
                    v-model="abilityForm.protectiveUnits"
                    style="width: 245px"
                    placeholder="请输入防护区域"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
              
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="投入使用时间" prop="commissioningDate">
                  <el-date-picker
                    v-model="abilityForm.commissioningDate"
                    type="date"
                    placeholder="选择日期"
                    style="width: 245px"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  >
              </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="可容纳人数" prop="holdsNumber">
                  <el-input-number
                    v-model="abilityForm.holdsNumber"
                    style="width: 245px"
                    placeholder="请输入可容纳人数"
                    maxlength="20"
                    :disabled="disabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
            <el-form-item label="面积" prop="refugeArea">
              <el-input
                v-model="abilityForm.refugeArea"
                placeholder="请输入面积"
                maxlength="20"
                oninput="!/^(\d+\.?)?\d{0,2}$/.test(this.value)?(this.value=this.value.substring(0, this.value.length-1)): ''"
                :disabled="disabled"
                style="width: 245px"
              />
            </el-form-item>
            </el-col>
              <el-col :span="12">
                <el-form-item label="所属单位" prop="firmName">
                  <el-input
                    v-model.trim="abilityForm.firmName"
                    style="width: 245px"
                    placeholder="请输入所属单位"
                    maxlength="20"
                    :disabled="disabled||isEnterprise"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input
                v-model="abilityForm.contactPerson"
                placeholder="请输入联系人"
                maxlength="20"
                :disabled="disabled"
                style="width: 245px"
              />
            </el-form-item>
            </el-col>
              <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input
                v-model="abilityForm.contactPhone"
                placeholder="请输入联系电话"
                maxlength="20"
                :disabled="disabled"
                style="width: 245px"
              />
            </el-form-item>
            </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
            <el-form-item label="基本情况" prop="basicInformation">
              <el-input
                v-model="abilityForm.basicInformation"
                placeholder="请输入基本情况"
                type="textarea"
                maxlength="200"
                :disabled="disabled"
                style="width: 700px"
              ></el-input></el-form-item>
              </el-col>
            </el-row>
          </el-form
        ></el-col>
        <!-- <el-col
          :span="16"
          style="text-align: right; height: 700px; overflow: hidden"
        >
          <el-button
            size="mini"
            icon="el-icon-delete"
            style="margin-bottom: 10px"
            @click="jtopoDel()"
            type="danger"
            >删除位置点</el-button
          >
          <div
            class="map_canvas"
            style="width: 1000px; height: 700px"
            id="divId"
          ></div>
        </el-col> -->
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button
          class="popupButton"
          type="primary"
          @click="confirm('abilityForm')"
          :disabled="disabled"
          v-if="disabled == false"
          >确 定</el-button
        >
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 地图展示 -->
    <Map
      @mapConfirm="mapConfirm"
      :ranksForm="abilityForm"
      :mapVisible="mapVisible"
      :disabled="disabled"
      @mapCancellation="mapCancellation"
      ref="mapRef"
      :url="url"
    ></Map>
  </div>
</template>

<script>
import {
  page,
  save,
  update,
  deleteById,
} from "@/api/emergency/naturalResources/shelter/index";
import Map from "../../../map/index.vue";
export default {
  name: "EmergencySupplies",
  dicts: ["shelterLevel","shelterType","shelterSecurityLevel","emergencyCommunication","seismicIntensity"],
  components: { Map },
  data() {
    let checkPhone = (rule, value, callback) => {
      let reg = /^1[345789]\d{9}$/;
      if (!reg.test(value)) {
        callback(new Error("请输入11位手机号"));
      } else {
        callback();
      }
    };
    const validCode = (rule, value, callback) => {
      console.log(rule, value, "value");
      if (this.lngAndLat) {
        callback();
      } else {
        callback(new Error("请选择经纬度"));
      }
    };
    return {
      // 地图点标记图标地址
      url: require("../../../../assets/icons/shelter.png"),
      container: {},
      // 地图遮罩层
      mapVisible: false,
      lngAndLat: "",
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      shelter: null,
      // 是否显示弹出层
      abilityOpen: false,
      title: "新增避难场所",
      text: undefined,
      imgUrl: `${require("@/assets/images/map.png")}`,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        refugeName: undefined,
        liabilityUser: undefined,
        phone: undefined,
      },
      frequency: 0,
      abilityForm: {},
      disabled: false,
      // 表单校验--新增避难所
      abilityRules: {
        refugeName: [
          { required: true, message: "避难所名称不能为空", trigger: "blur" },
        ],
        commissioningDate: [
          { required: true, message: "投入使用时间不能为空", trigger: "blur" },
        ],
        refugeArea: [
          { required: true, message: "场所面积不能为空", trigger: "blur" },
        ],
        holdsNumber: [
          { required: true, message: "可容纳人数不能为空", trigger: "blur" },
        ],
        contactPerson: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        lngAndLat: [{ required: true, validator: validCode, trigger: "blur" }],
        contactPhone: [
          {
            type: "number",
            validator: checkPhone,
            message: "请输入正确的手机号",
            trigger: "change",
            required: true,
          },
        ],
      },
      //是否为企业，判断所属单位是否禁用
      isEnterprise: false,
    };
  },
  watch: {},
  created() {
    this.getList();
    if (this.$store.getters.enterprise.enterpriseName != null){
        this.queryParams={firmName:this.$store.getters.enterprise.enterpriseName}
        this.isEnterprise=true;
    }
  },
  methods: {
    /** 查询场所列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        console.log(response);
        if (response.data != null) {
          this.shelter = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    inputUpNumber(value){
    var reg = /^[1-9]{1}\d*/;   // 不能以0开头
    console.log(!reg.test(value));
    if(!reg.test(value)){
       this.abilityForm.holdsNumber=''
    }
},
    //获取场所详情
    handleLook(row) {
      this.reset();
      this.abilityOpen = true;
      this.abilityForm = JSON.parse(JSON.stringify(row));
      this.title = "查看避难场所";
      this.disabled = true;
      this.lngAndLat = row.longitude + "," + row.latitude;
      console.log(this.abilityForm);
      let arr = this.abilityForm.node.split(",");
    },
    handleUpdate(row) {
      this.reset();
      this.abilityOpen = true;
      this.title = "编辑避难场所";
      this.disabled = false;
      this.lngAndLat = row.longitude + "," + row.latitude;
      this.abilityForm = JSON.parse(JSON.stringify(row));
      this.getJtopo();
      let arr = this.abilityForm.node.split(",");
      this.getNode(arr);
    },
    handleAdd() {
      this.reset()
      this.abilityOpen = true;
      this.title = "新增避难场所";
      this.disabled = false;
    },
    handleDelete(row) {
      console.log(row);
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => {});
    },
    jtopoDel() {
      this.abilityForm.node = undefined;
      toNode.remove();
      toNode = undefined;
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    /*  确认保存新增*/
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.abilityForm.id != undefined) {
            update(this.abilityForm).then((response) => {
              // console.log(response, "编辑");
              if (response.code == 200) {
                this.$modal.msgSuccess("编辑成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          } else {
            save(this.abilityForm).then((response) => {
              // console.log(response, "新增");
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");

                this.abilityOpen = false;
                this.getList();
              }
            });
          }
        }
      });
      // console.log(this.evaluateData, "evaluateData");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.abilityForm = {
        id: undefined,
        refugeName: undefined,
        refugeArea: undefined,
        holdsNumber: undefined,
        liabilityUser: undefined,
        phone: undefined,
        remark: undefined,
      };
      this.lngAndLat = "";
      this.resetForm("abilityForm");
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        current: 1,
        size: 10,
        refugeName: undefined,
        liabilityUser: undefined,
        phone: undefined,
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 打开地图按钮
    openMap() {
      this.mapVisible = true;
      this.$nextTick(() => {
        this.$refs.mapRef.initMap();
      });
    },
    // 地图返回经纬度的回调
    mapConfirm(lng, lat,addressInfo) {
      if (lng && lat ) {
        this.mapVisible = false;
        this.lngAndLat = lng + "," + lat;
        this.abilityForm.longitude = lng;
        this.abilityForm.latitude = lat;
        this.abilityForm.administrativeDivision = addressInfo;

        // 获取到经纬度就取消验证提示
        this.$nextTick(() => {
          this.$refs.abilityForm.clearValidate();
        });
      } else {
        this.$modal.msgSuccess("请选择经纬度");
      }
    },
    // 取消地图的回调
    mapCancellation() {
      this.mapVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
//   font-size: 13px;
  float: right;
  margin-right: 10px;
}

.resetQueryStyle {
//   width: 88px;
//   height: 32px;
//   border: 1px solid #cccccc;
//   border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}
::v-deep .el-form-item__label {
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>
