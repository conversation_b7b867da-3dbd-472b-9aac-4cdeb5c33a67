<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <!--用户数据-->
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-position="left"
            style="display: flex; justify-content: space-between"
          >
            <div>
              <el-form-item label="队伍名称">
                <el-input
                  v-model="queryParams.contingentName"
                  placeholder="请输入队伍名称"
                  clearable
                  style="width: 10vw"
                  maxlength="20"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="队伍类型 :" prop="contingentType">
                <el-select
                  style="width: 10vw"
                  v-model="queryParams.contingentType"
                  placeholder="请选择队伍类型"
                >
                  <el-option
                    v-for="dict in dict.type.team_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="所属区域" prop="region">
                <el-cascader
                  clearable
                  v-model="queryParams.region"
                  :props="{ checkStrictly: true, label: 'name', value: 'id' }"
                  :options="areaOptions"
                  @change="handleChangeRegion"
                ></el-cascader>
              </el-form-item>
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button
                style="font-size:13px"
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                style="font-size:13px"
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card>
          <div slot="header" class="clearfix">
            <span>应急队伍展示列表</span>
            <div class="header-btns">
              <el-button
                type="primary"
                class="queryBtn"
                size="mini"
                @click="handleAdd"
                icon="el-icon-plus"
                >新增队伍</el-button
              >
              <el-button type="primary" class="queryBtn" size="mini"
                ><template
                  ><el-upload
                  
                    class="upload-demo"
                    :on-error="onError"
                    :on-success="handleAvatarSuccess"
                    :data="importData"
                    action="/emergency-v2/emergency_expert_contingent/import"
                    :headers="headers"
                    :file-list="fileList"
                  >
                    <div class="select" style="cursor: pointer">导入信息</div>
                  </el-upload></template
                ></el-button
              >

              <el-button type="primary" class="queryBtn" @click="exportReport()" size="mini"
                >导出</el-button
              >
              <el-button
              size="mini"
                type="primary"
                class="queryBtn"
                @click="stationaryPlaten()"
                >固定模板</el-button
              >
            </div>
          </div>
          <el-table
            v-loading="loading"
            :data="userList"
            :cell-style="{ padding: '0px' }"
            :row-style="{ height: '48px' }"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column
              label="序号"
              type="index"
              width="70"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size +
                  scope.$index +
                  1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="所属单位" align="center">
              <template slot-scope="scope">
                {{ scope.row.firmName == null ? "园区" : scope.row.firmName }}
              </template>
            </el-table-column>
            <el-table-column
              label="队伍名称"
              align="center"
              prop="contingentName"
              show-overflow-tooltip
            />
            <el-table-column label="队伍类型" align="center">
              <template slot-scope="scope">
                {{ getNameById(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column
              label="队伍人数"
              align="center"
              prop="headcount"
              show-overflow-tooltip
            />
            <el-table-column
              label="队伍负责人"
              align="center"
              prop="contact"
              show-overflow-tooltip
            />
            <el-table-column label="联系方式" align="center" prop="phone" />
            <el-table-column
              label="操作"
              align="center"
              width="300"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <!-- <el-button size="mini" type="text" icon="el-icon-plus" 
                @click="handleRankadd(scope.row)">新增人员</el-button> -->
                <el-button
                  type="text"
                  icon="el-icon-view"
                  @click="handleLook(scope.row)"
                  >查看</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
                <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                >呼叫</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                >短信</el-button
              > -->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.current"
            :limit.sync="queryParams.size"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改队伍配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="abilityOpen"
      width="960px"
      append-to-body
      @close="cancel"
    >
      <el-form
        ref="ranksForm"
        :model="ranksForm"
        :rules="abilityRules"
        label-width="110px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="队伍名称" prop="contingentName">
              <el-input
                style="width: 245px"
                v-model="ranksForm.contingentName"
                placeholder="请输入队伍名称"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属区域" prop="regionString">
              <el-cascader
                v-model="ranksForm.regionString"
                :props="{ checkStrictly: true, label: 'name', value: 'id' }"
                :options="areaOptions"
                @change="handleChange"
                :disabled="disabled"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="队伍类型" prop="contingentType">
              <el-select
                style="width: 245px"
                v-model="ranksForm.contingentType"
                placeholder="请选择队伍类型"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.team_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资质等级" prop="aptitude">
              <el-select
                style="width: 245px"
                v-model="ranksForm.aptitude"
                placeholder="请选择资质等级"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.aptitude"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-select
                style="width: 245px"
                v-model="ranksForm.taskType"
                placeholder="请选择任务类型"
                :disabled="disabled"
              >
                <el-option
                  v-for="dict in dict.type.team_task_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="救援队地址" prop="address">
              <el-input
                style="width: 245px"
                v-model="ranksForm.address"
                placeholder="请输入救援队地址"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="队伍状态" prop="contingentStatus">
              <el-radio-group
                v-model="ranksForm.contingentStatus"
                :disabled="disabled"
              >
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">使用中</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级主管部门" prop="superiorDepartment">
              <el-input
                style="width: 245px"
                v-model="ranksForm.superiorDepartment"
                placeholder="请输入上级主管部门"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务区域" prop="serviceArea">
              <el-input
                style="width: 245px"
                v-model="ranksForm.serviceArea"
                placeholder="请输入服务区域"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="GIS范围" prop="gisScope">
              <el-input
                style="width: 245px"
                v-model="ranksForm.gisScope"
                placeholder="请输入GIS范围"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主要负责人" prop="contact">
              <el-input
                style="width: 245px"
                v-model="ranksForm.contact"
                placeholder="请输入主要负责人"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="办公电话" prop="officePhone">
              <el-input
                style="width: 245px"
                v-model="ranksForm.officePhone"
                placeholder="请输入办公电话"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="家庭电话" prop="homePhone">
              <el-input
                style="width: 245px"
                v-model="ranksForm.homePhone"
                placeholder="请输入家庭电话"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="移动电话" prop="phone">
              <el-input
                style="width: 245px"
                v-model="ranksForm.phone"
                placeholder="请输入移动电话"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应急值班电话" prop="dutyPhone">
              <el-input
                style="width: 245px"
                v-model="ranksForm.dutyPhone"
                placeholder="请输入应急值班电话"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="传真" prop="fax">
              <el-input
                style="width: 245px"
                v-model="ranksForm.fax"
                placeholder="请输入传真"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总人数" prop="headcount">
              <el-input
                style="width: 245px"
                v-model="ranksForm.headcount"
                type="number"
                min="0"
                placeholder="请输入总人数"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专职人数" prop="fullStaff">
              <el-input
                style="width: 245px"
                v-model="ranksForm.fullStaff"
                type="number"
                min="0"
                placeholder="请输入专职人数"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="兼职人数" prop="partStaff">
              <el-input
                style="width: 245px"
                v-model="ranksForm.partStaff"
                type="number"
                min="0"
                placeholder="请输入兼职人数"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中队人数" prop="squadronsNum">
              <el-input
                style="width: 245px"
                v-model="ranksForm.squadronsNum"
                type="number"
                min="0"
                placeholder="请输入中队人数"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="小队人数" prop="squadsNum">
              <el-input
                style="width: 245px"
                v-model="ranksForm.squadsNum"
                type="number"
                min="0"
                placeholder="请输入小队人数"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="质量标准化等级" prop="qualityGrade">
              <el-input
                style="width: 245px"
                v-model="ranksForm.qualityGrade"
                placeholder="请输入质量标准化等级"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮政代码" prop="postalCode">
              <el-input
                style="width: 245px"
                v-model="ranksForm.postalCode"
                placeholder="请输入邮政代码"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="经纬度 :" prop="lngAndLat">
              <el-button type="primary" plain @click="openMap()">{{
                lngAndLat ? lngAndLat : "点击选择"
              }}</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书编号" prop="certificateNum">
              <el-input
                style="width: 245px"
                v-model="ranksForm.certificateNum"
                placeholder="请输入证书编号"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发证机关" prop="issuingAuthority">
              <el-input
                style="width: 245px"
                v-model="ranksForm.issuingAuthority"
                placeholder="请输入发证机关"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contactWay">
              <el-input
                style="width: 245px"
                v-model="ranksForm.contactWay"
                placeholder="请输入联系方式"
                maxlength="20"
                :disabled="disabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成立日期" prop="foundingTime">
              <el-date-picker
                v-model="ranksForm.foundingTime"
                type="date"
                placeholder="选择日期"
                style="width: 245px"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发证日期" prop="issueDate">
              <el-date-picker
                v-model="ranksForm.issueDate"
                type="date"
                placeholder="选择日期"
                style="width: 245px"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止日期" prop="deadlineDate">
              <el-date-picker
                v-model="ranksForm.deadlineDate"
                type="date"
                placeholder="选择日期"
                style="width: 245px"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="主要装备描述" prop="equipDescription">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入主要装备描述"
                maxlength="200"
                v-model="ranksForm.equipDescription"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="专业描述" prop="professionalDescription">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入专业描述"
                maxlength="200"
                v-model="ranksForm.professionalDescription"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="note">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入备注"
                maxlength="200"
                v-model="ranksForm.note"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!disabled">
        <el-button
          class="popupButton"
          type="primary"
          @click="confirm('ranksForm')"
          >确 定</el-button
        >
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="队伍人员" :visible.sync="ranksOpen" width="560px">
      <el-table
        :data="tableData"
        style="width: 100%"
        :cell-style="{ padding: '0px' }"
        :row-style="{ height: '48px' }"
      >
        <el-table-column align="center" prop="contact" label="姓名">
        </el-table-column>
        <el-table-column align="center" prop="phone" label="联系方式">
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <el-button
              plain
              type="primary"
              size="mini"
              @click="removeStaff(scope.row)"
              >移除人员</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button class="popupButton" type="primary" @click="ranksOpen = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog title="新增人员" :visible.sync="ranksAdd" width="560px">
      <el-form
        ref="rankaddForm"
        :model="rankaddForm"
        :rules="rankaddRules"
        label-width="110px"
      >
        <el-form-item label="队伍类型" prop="dictKey">
          <el-select
            style="width: 245px"
            v-model="rankaddForm.dictKey"
            placeholder="请选择队伍类型"
            disabled
          >
            <el-option
              v-for="dict in dict.type.team_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系人" prop="contact">
          <el-input
            style="width: 245px"
            v-model="rankaddForm.contact"
            placeholder="请输入联系人"
            maxlength="20"
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="phone">
          <el-input
            style="width: 245px"
            v-model="rankaddForm.phone"
            placeholder="请输入联系电话"
            maxlength="20"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="popupButton"
          type="primary"
          @click="confirm('rankaddForm')"
          >确 定</el-button
        >
        <el-button class="popupButton" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 地图展示 -->
    <Map
      @mapConfirm="mapConfirm"
      :ranksForm="ranksForm"
      :mapVisible="mapVisible"
      :disabled="disabled"
      @mapCancellation="mapCancellation"
      ref="mapRef"
      :url="url"
    ></Map>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import Map from "../../../map/index.vue";
import {
  page,
  pageList,
  save,
  ranksDetail,
  saveStaff,
  removeStaff,
  update,
  deleteById,
} from "@/api/emergency/naturalResources/specialist/index";
import {
  emergencyAreaTree,
  exportExpert,
  exportTemplateExpert,
} from "@/api/emergency/new/index.js";
export default {
  name: "Specialist",
  dicts: ["team_type", "aptitude", "team_task_type"],
  components: { Map },
  data() {
    const validCode = (rule, value, callback) => {
      console.log(rule, value, "value");
      if (this.lngAndLat) {
        callback();
      } else {
        callback(new Error("请选择经纬度"));
      }
    };
    let checkPhone = (rule, value, callback) => {
      let reg = /^1[345789]\d{9}$/;
      if (!reg.test(value)) {
        callback(new Error("请输入11位手机号"));
      } else {
        callback();
      }
    };
    return {
      importData: {},
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      fileList: [],
      multipleSelection: [],
      //批量操作id数组
      batchData: [],
      areaOptions: [],
      areaData: [],
      // 地图点标记图标地址
      url: require("@/assets/icons/name.png"),
      // 地图遮罩层
      mapVisible: false,
      lngAndLat: "",
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      userList: null,
      tableData: null,
      // 是否显示弹出层
      abilityOpen: false,
      ranksOpen: false,
      ranksAdd: false,
      title: "新增事件类型",
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        contingentType: undefined,
        contingentName: undefined,
      },

      ranksForm: {},
      rankaddForm: {
        dictKey: undefined,
        contact: undefined,
        phone: undefined,
      },
      disabled: false,
      // 表单校验
      abilityRules: {
        contingentName: [
          { required: true, message: "请输入队伍名称", trigger: "blur" },
        ],
        contingentNumber: [
          { required: true, message: "总人数不能为空", trigger: "blur" },
        ],
        dutyPhone: [
          {
            message: "请输入应急值班电话",
            trigger: "blur",
            required: true,
          },
        ],
        // phone: [
        //   {
        //     type: "number",
        //     validator: checkPhone,
        //     message: "请输入正确的手机号",
        //     trigger: "blur",
        //     required: true,
        //   },
        // ],
        // homePhone: [
        //   {
        //     pattern:
        //       /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        //     message: "请输入正确的手机号码",
        //     trigger: "blur",
        //   },
        // ],
        // officePhone: [
        //   {
        //     pattern:
        //       /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        //     message: "请输入正确的手机号码",
        //     trigger: "blur",
        //   },
        // ],
        contingentType: [
          { required: true, message: "请选择队伍类型", trigger: "blur" },
        ],
        taskType: [
          { required: true, message: "请选择任务类型", trigger: "blur" },
        ],
        address: [
          { required: true, message: "请输入救援队地址", trigger: "blur" },
        ],
        contingentStatus: [
          { required: true, message: "请选择队伍状态", trigger: "blur" },
        ],
        superiorDepartment: [
          { required: true, message: "请输入上级主管部门", trigger: "blur" },
        ],
        serviceArea: [
          { required: true, message: "请输入服务区域", trigger: "blur" },
        ],
        contact: [
          { required: true, message: "请输入主要负责人", trigger: "blur" },
        ],
        lngAndLat: [{ required: true, validator: validCode, trigger: "blur" }],
      },
      rankaddRules: {
        contact: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
        ],
        dictKey: [
          { required: true, message: "请选择队伍类型", trigger: "blur" },
        ],
      },
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  mounted() {
    this.getArea();
    if (this.$store.getters.enterprise.enterpriseName != null) {
      this.importData = {
        firmName: this.$store.getters.enterprise.enterpriseName,
      };
    }
  },
  methods: {
    handleChange(value) {
      console.log(value);
    },
    getArea() {
      emergencyAreaTree().then((res) => {
        this.areaOptions = res.data;
      });
    },
    /** 查询专家队伍列表 */
    getList() {
      this.loading = true;
      pageList({
        ...this.queryParams,
        firmName: this.$store.getters.enterprise.enterpriseName,
      }).then((response) => {
        this.loading = false;

        if (response.data != null) {
          this.userList = response.data.list;
          console.log(response.data.list);
          this.total = response.data.total;
        }
      });
    },
    // 查看人员
    handleLook(row) {
      this.reset();
      this.abilityOpen = true;
      this.ranksForm = JSON.parse(JSON.stringify(row));
      if (
        this.ranksForm.regionString != null &&
        this.ranksForm.regionString != undefined
      ) {
        this.ranksForm.regionString = this.ranksForm.regionString.split(",");
      }
      this.title = "查看应急队伍";
      this.disabled = true;
      this.lngAndLat = row.longitude + "," + row.latitude;
      console.log(this.abilityForm);
    },
    // 编辑人员
    handleUpdate(row) {
      console.log(row, "row");
      this.abilityOpen = true;
      this.reset();
      this.ranksForm = row;
      if (
        this.ranksForm.regionString != null &&
        this.ranksForm.regionString != undefined
      ) {
        this.ranksForm.regionString = this.ranksForm.regionString.split(",");
      }

      this.disabled = false;
      this.lngAndLat = row.longitude + "," + row.latitude;
      // this.ranksForm.id = row.id;
      // this.ranksForm.dictKey = row.id;
      // this.ranksForm.contingentName=row.contingentName
      // this.ranksForm.contingentType=row.contingentType
      // this.ranksForm.phone = row.phone;
      // this.ranksForm.contingentNumber = row.contingentNumber;
      // this.ranksForm.companyName = row.companyName;
      // this.ranksForm.contact = row.contact;
      // this.ranksForm.phone = row.phone;
      this.title = "编辑队伍";
    },
    handleAdd() {
      this.reset();
      this.abilityOpen = true;
      this.title = "新增队伍";
      this.disabled = false;
    },
    handleRankadd(row) {
      this.reset();
      this.rankaddForm.dictKey = row.id;
      this.ranksAdd = true;
    },
    handleDelete(row) {
      const eventAbilityId = row.id;
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ id: eventAbilityId });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 取消按钮
    cancel() {
        this.abilityOpen = false;
        if (this.$refs.ranksForm) {
        this.$refs.ranksForm.resetFields();
      }
      this.reset();
    },
    getNameById(res) {
      // console.log(res, this.dict.type.team_type);
      if (
        res.contingentType != undefined &&
        res.contingentType != "" &&
        res.contingentType != null
      ) {
        return this.dict.type.team_type.filter(
          (item) => item.value == res.contingentType
        )[0].label;
      }
    },
    /*  确认保存新增*/
    confirm(formName) {
      if (formName == "ranksForm") {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if (
              this.ranksForm.regionString &&
              this.ranksForm.regionString.length > 0
            ) {
              this.$set(
                this.ranksForm,
                "region",
                this.ranksForm.regionString[
                  this.ranksForm.regionString.length - 1
                ]
              );
              this.ranksForm.regionString =
                this.ranksForm.regionString.toString();
            }
            this.$set(
              this.ranksForm,
              "firmName",
              this.$store.getters.enterprise.enterpriseName
            );

            if (this.ranksForm.id != undefined) {
              update(this.ranksForm).then((response) => {
                console.log(response, "编辑");
                if (response.code == 200) {
                  this.$modal.msgSuccess("编辑成功");
                  this.abilityOpen = false;
                    this.getList();
                    this.$refs[formName].resetFields();
                }
              });
            } else {
              save(this.ranksForm).then((response) => {
                console.log(this.ranksForm, "新增");
                if (response.code == 200) {
                  this.$modal.msgSuccess("新增成功");
                  this.abilityOpen = false;
                    this.getList();
                    this.$refs[formName].resetFields();
                  
                }
              });
            }
          }
        });
      } else {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            saveStaff(this.rankaddForm).then((response) => {
              console.log(response, "新增");
              if (response.code == 200) {
                this.$modal.msgSuccess("新增成功");
                this.ranksAdd = false;
                this.getList();
              }
            });
          }
        });
      }
    },
    removeStaff(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return removeStaff({ contingentId: row.contingentId });
        })
        .then(() => {
          this.ranksOpen = false;
          this.getList();
          this.$modal.msgSuccess("删除队伍人员成功！");
        })
        .catch(() => {});
    },
    handleChangeRegion() {},
    /** 搜索按钮操作 */
    handleQuery() {
      if (
        this.queryParams.region != null &&
        this.queryParams.region != undefined &&
        this.queryParams.region != []
      ) {
        this.$set(
          this.queryParams,
          "region",
          this.queryParams.region[this.queryParams.region.length - 1]
        );
      }
      this.queryParams.current = 1;
      this.getList();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.ranksForm = {};
      this.rankaddForm = {
        dictKey: undefined,
        contact: undefined,
        phone: undefined,
      };
      this.lngAndLat = "";
    },

    /** 重置按钮操作 */
    resetQuery() {
      (this.queryParams = {
        current: 1,
        size: 10,
        liabilityUser: undefined,
        phone: undefined,
      }),
        this.resetForm("queryForm");
      this.handleQuery();
    },
    // 打开地图按钮
    openMap() {
      this.mapVisible = true;
      this.$nextTick(() => {
        this.$refs.mapRef.initMap();
      });
    },
    // 地图返回经纬度的回调
    mapConfirm(lng, lat) {
      if (lng && lat) {
        this.mapVisible = false;
        this.lngAndLat = lng + "," + lat;
        this.ranksForm.longitude = lng;
        this.ranksForm.latitude = lat;
        // 获取到经纬度就取消验证提示
        this.$nextTick(() => {
          this.$refs.ranksForm.clearValidate();
        });
      } else {
        this.$modal.msgSuccess("请选择经纬度");
      }
    },
    // 取消地图的回调
    mapCancellation() {
      this.mapVisible = false;
    },
    //导出
    download(blob, name) {
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.href = url;
      link.download = name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },
    //固定模板
    stationaryPlaten() {
      exportTemplateExpert().then((res) => {
        this.download(res, "导入模版.xlsx");
      });
    },
    //导出
    exportReport() {
      if (this.batchData.length <= 0) {
        this.$message.error("至少选择一条数据");
      } else {
        exportExpert({ ids: this.batchData }).then((res) => {
          this.download(res, "导出队伍信息报表.xlsx");
        });
      }
    },
    handleSelectionChange(val) {
      console.log(val);
      //可以获得具体行数
      this.multipleSelection = val;
      console.log(this.multipleSelection);
      if (this.multipleSelection.length > 0) {
        this.batchData = [];
        this.multipleSelection.forEach((item) => {
          this.batchData.push(item.id);
        });
        console.log(this.batchData);
      }
    },
    onError() {
      this.$message.error("无法导入！请检查导入数据");
    },
    handleAvatarSuccess(res) {
      if (res.code != 200) {
        this.$modal.msgError(res.msg);
      } else {
        this.$modal.msgSuccess("导入成功");
      }
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.header-btns {
  float: right;
}
.queryBtn ::v-deep .el-upload-list--text {
  display: none !important;
}
.queryBtn ::v-deep .el-upload-list {
  display: none !important;
}
.queryBtn ::v-deep .el-upload-list__item .is-uploading {
  display: none !important;
}
.uploadList ::v-deep .el-upload-list .el-progress {
  display: none !important;
}
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

::v-deep.box-card .topBottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}

::v-deep.box-card .topBottom .descriptions {
  -webkit-box-flex: 6;
  -ms-flex: 6;
  flex: 6;
}

::v-deep.box-card .topBottom .tabButton {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

::v-deep.box-card .topBottom .tabButton button {
  float: right;
  margin: 0 5px;
}

.queryBtnT {
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
  float: right;
  margin-right: 10px;
}

.resetQueryStyle {
  width: 88px;
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
}

.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}
::v-deep .el-form-item__label {
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
</style>