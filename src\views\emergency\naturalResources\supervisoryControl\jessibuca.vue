<template>
  <div id="jessibuca" style="width: auto; height: auto">
    <div id="container" ref="container" style="width: 100%; height: 10rem; background-color: #000" @dblclick="fullscreenSwich">
      <!-- <div class="buttons-box" id="buttonsBox">
        <div class="buttons-box-left">
          <i v-if="!playing" class="iconfont icon-play jessibuca-btn" @click="playBtnClick"></i>
          <i v-if="playing" class="iconfont icon-pause jessibuca-btn" @click="pause"></i>
          <i class="iconfont icon-stop jessibuca-btn" @click="destroy"></i>
          <i v-if="isNotMute" class="iconfont icon-audio-high jessibuca-btn" @click="jessibuca.mute()"></i>
          <i v-if="!isNotMute" class="iconfont icon-audio-mute jessibuca-btn" @click="jessibuca.cancelMute()"></i>
        </div>
        <div class="buttons-box-right">
          <span class="jessibuca-btn">{{kBps}} kb/s</span>
          <i class="iconfont icon-camera1196054easyiconnet jessibuca-btn" @click="jessibuca.screenshot('截图','png',0.5)" style="font-size: 1rem !important"></i>
          <i class="iconfont icon-shuaxin11 jessibuca-btn" @click="playBtnClick"></i>
          <i v-if="!fullscreen" class="iconfont icon-weibiaoti10 jessibuca-btn" @click="fullscreenSwich"></i>
          <i v-if="fullscreen" class="iconfont icon-weibiaoti11 jessibuca-btn" @click="fullscreenSwich"></i>
        </div>
    </div> -->
    </div>
  </div>
</template>

<script>
export default {
    name: 'jessibuca',
    data() {
        return {
          jessibuca: null,
          playing: false,
          isNotMute: false,
          quieting: false,
          fullscreen: false,
          loaded: false, // mute
          speed: 0,
          performance: "", // 工作情况
          kBps: 0,
          btnDom: null,
          videoInfo: null,
          volume: 1,
          rotate: 0,
          vod: true, // 点播
          forceNoOffscreen: true,
        };
    },
    props: ['videoUrl', 'error', 'hasAudio', 'height'],
    mounted () {
      window.onerror = (msg) => {
        // console.error(msg)
      };
      let paramUrl = decodeURIComponent(this.$route.params.url)
       this.$nextTick(() =>{
         let dom = document.getElementById("container");
         dom.style.height = (9/16 ) * dom.clientWidth + "px"
          if (typeof (this.videoUrl) == "undefined") {
            this.videoUrl = paramUrl;
          }
        //  this.btnDom = document.getElementById("buttonsBox");
          console.log("初始化时的地址为: " + this.videoUrl)
         this.play(this.videoUrl)
        })
    },
    created(){
      console.log(this.videoUrl);
    },
    // watch:{
    //     videoUrl:{
    //       handler(newData, oldData){
    //         console.log(newData,'newdata');
    //         this.$nextTick(()=>{
    //         this.play(newData)

    //         })
    //       },
    //       immediate:true
    //     },
    // },
    //     // 暂时使用videoChange实现效果 对接口后可去
    //     // videoChange:{
    //     //   handler(newData, oldData){
    //     //     // console.log(newData);
    //     //     // this.videoUrl = 'http://192.168.30.99:8090/rtp/0614886C.live.flv'
    //     //     // console.log(this.videoUrl);
    //     //     if(this.videoUrl!==''&&this.videoUrl!==null&&this.videoUrl!==undefined){
    //     //       // console.log(this.videoUrl);
    //     //       this.play(this.videoUrl)
    //     //     }
    //     //   },
    //     //   immediate:true
    //     // }
    // },
    methods: {
        create(){
          let options =  {};
          console.log(this.$refs.container)
          console.log("hasAudio  " + this.hasAudio)

         this.jessibuca = new JessibucaPro({
              container: '#container',
              decoder: './decoder-pro.js',
              videoBuffer: 0.2, // 缓存时长
              isResize: false,
              text: "",
              loadingText: "加载中",
              debug: true,
              isMulti: true,
              useMSE: true,
              useSIMD: true,
              useWCS: true,
              hasAudio: false,
              useVideoRender: true,
              controlAutoHide: true,
              showBandwidth: true, // 显示网速
              showPerformance: false,
              operateBtns: {
                fullscreen: true,
                screenshot: true,
                play: true,
                audio: true,
              },
              watermarkConfig: {
                text: {
                  content: '摄像头'
                },
                right: 10,
                top: 10
              },
            },);
            this.jessibuca.on("fullscreen", (flag)=> {
              console.log('is fullscreen', index, flag)
            })
        },
        playBtnClick: function (event){
          this.play(this.videoUrl)
        },
        play: function (url) {
          console.log(url)
            if (this.jessibuca) {
              this.destroy();
            }
            this.create();
            this.jessibuca.on("play", () => {
              this.playing = true;
              this.loaded = true;
              this.quieting = this.jessibuca.quieting;
            });
            if (this.jessibuca.hasLoaded()) {
              this.jessibuca.play(url);
            } else {
              this.jessibuca.on("load", () => {
                console.log("load 播放")
                this.jessibuca.play(url);
              });
            }
        },
        pause: function () {
          if (this.jessibuca) {
            this.jessibuca.pause();
          }
          this.playing = false;
          this.err = "";
          this.performance = "";
        },
        destroy: function () {
          if (this.jessibuca) {
            this.jessibuca.destroy();
          }
          // if (document.getElementById("buttonsBox") == null) {
          //   document.getElementById("container").appendChild(this.btnDom)
          // }
          this.jessibuca = null;
          this.playing = false;
          this.err = "";
          this.performance = "";

        },
        eventcallbacK: function(type, message) {
            console.log("player 事件回调")
            console.log(type)
            console.log(message)
        },
        fullscreenSwich: function (){
            let isFull = this.isFullscreen()
            this.jessibuca.setFullscreen(!isFull)
            this.fullscreen = !isFull;
        },
        isFullscreen: function (){
          return document.fullscreenElement ||
            document.msFullscreenElement  ||
            document.mozFullScreenElement ||
            document.webkitFullscreenElement || false;
        }
    },
    destroyed() {
      if (this.jessibuca) {
        this.jessibuca.destroy();
      }
      this.playing = false;
      this.loaded = false;
      this.performance = "";
    },
}
</script>

<style>
  .buttons-box{
    width: 100%;
    height: 28px;
    background-color: rgba(43, 51, 63, 0.7);
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    left: 0;
    bottom: 0;
    user-select: none;
    z-index: 10;
  }
  .jessibuca-btn{
    width: 20px;
    color: rgb(255, 255, 255);
    line-height: 27px;
    margin: 0px 10px;
    padding: 0px 2px;
    cursor: pointer;
    text-align: center;
    font-size: 0.8rem !important;
  }
  .buttons-box-right {
    position: absolute;
    right: 0;
  }
</style>
