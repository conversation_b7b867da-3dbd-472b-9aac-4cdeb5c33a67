<template>
  <div class="all">
    <div class="topTitle">数据筛选</div>
    <div class="topForm">
      <div class="leftForm">
        <el-descriptions :column="4">
          <el-descriptions-item label="报警类型">
            <el-select v-model="tabFlag" placeholder="请选择">
              <el-option
                v-for="item in typeOptions"
                :key="item.code"
                :label="item.type"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="公司名称">
            <el-input v-model="enterpriseName"></el-input>
          </el-descriptions-item>
          <el-descriptions-item label="报警对象">
            <!-- <el-cascader
              v-model="earevalue"
              :props="propsArea"
              :show-all-levels="false"
              @change="changeArea"
            ></el-cascader> -->
            <el-input v-model="alarmObject"></el-input>
          </el-descriptions-item>
          <el-descriptions-item label="告警时间">
            <el-date-picker
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始"
              end-placeholder="结束"

              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
            <!-- <el-cascader
              v-model="earevalue"
              :props="propsArea"
              :show-all-levels="false"
              @change="changeArea"
            ></el-cascader> -->
          </el-descriptions-item>
        </el-descriptions>
        <div style="margin-left: 25px">
          <el-checkbox-group v-model="processState">
            <el-checkbox label="未处理"></el-checkbox>
            <el-checkbox label="已派单"></el-checkbox>
            <el-checkbox label="已处理"></el-checkbox>
            <el-checkbox label="已关闭"></el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div class="twoButton">
        <el-button type="primary" @click="search" icon="el-icon-search" size="mini"
          >查询</el-button
        >
        <el-button @click="reset" icon="el-icon-refresh" size="mini">重置</el-button>
        
      </div>
    </div>
    <div class="bottomTable">
      <div class="bottom">
        <div>已登记入库数据列表</div>
        <div class="newbutton">
          <el-button type="primary" size="mini" @click="toDeal">处理</el-button>
          <el-button type="primary" size="mini" @click="toClose">关闭</el-button>
        </div>
      </div>
      <div class="tableBottom">
        <el-table
          ref="multipleTable"
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
      :row-key="(row) => row.id"
          :cell-style="{ padding: '0px' }"
          :row-style="{ height: '48px' }"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column
            v-for="item in column"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :align="item.align"
            show-overflow-tooltip
          >
           
          </el-table-column>
<el-table-column label="采集数值"  align="center">
  <template slot-scope="scope" >
            <div>
              {{scope.row.tabFlag=='封闭管理'?'-':scope.row.acquisitionValue}}
            </div>
           </template>
</el-table-column>
          <el-table-column label="操作" width="250" align="center">
            <template slot-scope="scope">
              <el-button
                @click="push(scope.row)"
                type="text"
                icon="el-icon-upload2"
                v-if="scope.row.processState!='已处理'"
                >推送</el-button
              >
              <el-button
                @click="toDetile(scope.row)"
                type="text"
                icon="el-icon-document"
                >详情</el-button
              >
               <el-button
                @click="toEscalation(scope.row)"
                type="text"
                icon="el-icon-arrow-right"
                v-if="scope.row.tabFlag=='智慧消防'&&scope.row.processState=='未处理'"
                >上报</el-button
              >
              <el-button
                @click="toDeal(scope.row)"
                type="text"
                icon="el-icon-arrow-right"
                v-if="scope.row.processState=='未处理'"
                >处理</el-button
              >
              <el-button
                @click="todelete(scope.row)"
                type="text"
                icon="el-icon-delete"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pages">
          <el-pagination
            background
            layout="total,prev, pager, next,sizes,jumper"
            :total="total"
            @size-change="changeSize"
            @current-change="changePage"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog
      title="告警信息"
      :visible.sync="dialogMsg"
      width="800px"
      append-to-body
    >
     <el-descriptions :column="2">
      <el-descriptions-item label="企业名称">{{
        detileForm.enterpriseName
      }}</el-descriptions-item>
      <el-descriptions-item label="报警类型">{{
        detileForm.tabFlag
      }}</el-descriptions-item>
      <el-descriptions-item label="报警对象">{{
        detileForm.alarmObject
      }}</el-descriptions-item>
      <el-descriptions-item label="采集数值">
        <div style="color:red">
          {{detileForm.tabFlag=='封闭管理'?'-':
        detileForm.acquisitionValue
      }}
          </div></el-descriptions-item>
      <el-descriptions-item label="标准范围">{{
        detileForm.standardRange
      }}</el-descriptions-item>
      <el-descriptions-item label="告警设备">{{
        detileForm.id
      }}</el-descriptions-item>
      <el-descriptions-item label="告警时间">{{
        detileForm.alarmTime,

      }}</el-descriptions-item>
       <el-descriptions-item label="告警内容">{{
        detileForm.content

      }}</el-descriptions-item>
      <el-descriptions-item label="告警状态">{{
        detileForm.processState
      }}</el-descriptions-item>
       <el-descriptions-item label="告警次数">{{
        detileForm.count
      }}
       <div
              style="color: #409eff; cursor: pointer;margin-left:10px"
              @click="toFrequencyDetile(detileForm.id)"
            >
              点击查看详情
            </div> 
      </el-descriptions-item>
     
    </el-descriptions>
    <div class="dialogTitle" v-if="detileForm.processState=='已处理'">处理详情</div>
    <el-descriptions :column="1" v-if="detileForm.processState=='已处理'">
      <el-descriptions-item label="处理意见">{{
        detileForm.handleReason
      }}</el-descriptions-item>
      <el-descriptions-item label="上传附件" v-if="detileForm.handleFile">
        <div class="fileName" @click="downFile(detileForm.handleFileObj)" :title="detileForm.handleFileObj?detileForm.handleFileObj.fileName:'暂无附件'">
          {{detileForm.handleFileObj.fileName}}
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <div class="dialogTitle" v-if="detileForm.processState=='已关闭'">关闭详情</div>
    <el-descriptions :column="1" v-if="detileForm.processState=='已关闭'">
      <el-descriptions-item label="关闭原因">{{
        detileForm.closeReason
      }}</el-descriptions-item>
      <el-descriptions-item label="上传附件" v-if="detileForm.closeFile">
        <div class="fileName" @click="downFile(detileForm.closeFileObj)" :title="detileForm.closeFile?detileForm.closeFileObj.fileName:'暂无附件'">
          {{detileForm.closeFileObj.fileName}}
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <div v-if="detileForm.tag=='人员离岗告警' &&detileForm.acquisitionValue">
      <div>
        告警图片
      </div>
      <img :src="detileForm.acquisitionValue" alt="" width="100" height="100">
    </div>
    <div style="
    position: absolute;
    top: 81px;
    right: 40px;
">
      <img src="./img/close.png" alt="" v-if="detileForm.processState=='已关闭'" class="imgStyle1">
      <img src="./img/done.png" alt="" v-if="detileForm.processState=='已处理'" class="imgStyle1">
      <img src="./img/todo.png" alt="" v-if="detileForm.processState=='未处理'" class="imgStyle">
    </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogMsg = false">取 消</el-button>
       
      </span>
    </el-dialog>
    <el-dialog title="告警信息" :visible.sync="dialogPush" width="800px" append-to-body>
      <el-steps simple :active="active">
        <el-step title="未处理" icon="el-icon-document-copy"></el-step>
        <el-step title="已派单" icon="el-icon-position"></el-step>
        <el-step title="已关闭" icon="el-icon-s-release"></el-step>
      </el-steps>
      <div class="step-container">
        <div class="step-container-item-1">
          <div>
            <div>告警描述：{{alarmPushForm.content }}</div>
            <br />
            <div>告警次数：共{{ alarmPushForm.count }}次</div>
            <div
              style="color: #409eff; cursor: pointer"
              @click="toFrequencyDetile(alarmPushForm.id)"
            >
              点击查看详情
            </div> 
          </div>
        </div>
        <div class="step-container-item-2">
           <div v-if="alarmPushForm.orderTime!=null">
            <div class="assigneeName">办理人：{{ alarmPushForm.orderTo }}</div>
            <div class="assigneeName">办结时间：{{ alarmPushForm.orderTime }}</div>
          </div>
        </div>
        <div class="step-container-item-3">
          <div v-if="alarmPushForm.processState=='已关闭'">
            <div class="assigneeName">办理人：{{ alarmPushForm.closeBy }}</div>
            <div class="assigneeName">办结时间：{{ alarmPushForm.closeTime }}</div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogPush = false">取 消</el-button>
        <el-button type="primary" @click="closeAlarm" v-if="active != 2"
          >关闭告警</el-button
        >
        <el-button type="primary" @click="dispatchWorkFlow" v-if="active === 0"
          >派单</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="派单"
      :visible.sync="dialogDispatchWorkFlow"
      width="500px"
      @close="closeDispatchWorkFlow"
      append-to-body
    >
      <el-form
        ref="formWorkflow"
        :model="formWorkflow"
        label-position="right"
        :rules="rulesWork"
        label-width="120px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="接单人：" prop="userIds">
              <el-select v-model="formWorkflow.userIds" multiple placeholder="请选择">
    <el-option
      v-for="item in peopleListCompany"
      :key="item.userId"
      :label="item.realname"
      :value="item.userId">
    </el-option>
  </el-select>
            </el-form-item>
          </el-col>
        
        </el-row>
<el-row>
    <el-col :span="24">
            <el-form-item label="是否短信通知" prop="isMessage" title="是否短信通知">
          <!-- <el-switch v-model="form.message" @change="changeMessage"> -->
             <el-radio-group v-model="formWorkflow.smsMessage">
    <el-radio :label="true">是</el-radio>
    <el-radio :label="false">否</el-radio>
  </el-radio-group>
          </el-switch>
        </el-form-item>
          </el-col>
</el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="处理意见："
              prop="content"
              :rules="[{ max: 300, message: '不能超过300字', trigger: 'blur' }]"
            >
              <el-input
                v-model="formWorkflow.content"
                type="textarea"
                class="form-item-texarea"
                :autosize="{ minRows: 4, maxRows: 6 }"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDispatchWorkFlow = false">取 消</el-button>
        <el-button type="primary" @click="defineDispatch">派单</el-button>
      </span>
    </el-dialog>
    
    <el-dialog
      title="告警处理"
      :visible.sync="dialogDispatch"
      width="650px"
      @close="closeDispatch"
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        label-position="right"
        label-width="80px"
      >
      
          <el-col :span="24">
            <el-form-item
              label="处理意见"
              prop="handleReason"
              :rules="[{
            required: true,
            message: '请输入处理意见',
            trigger: 'blur',
          }]"
            >
              <el-input
                v-model="form.handleReason"
                type="textarea"
                class="form-item-texarea"
                :autosize="{ minRows: 4, maxRows: 6 }"

              />
            </el-form-item>
          </el-col>
          <el-form-item label="上传附件" prop="file" title="上传附件">
          <el-upload
            class="upload-demo"
            action="/safety-v2/file/upload"
            :on-success="successUpload"
            :on-exceed="handleExceed"
            :before-upload="beforeAvatarUpload"
            :on-remove="removeFile"
            :headers="headers"
            :limit="1"
            :file-list="fileList"
            accept=".rar,.zip,.doc,.docx,.pdf,.png,.jpg"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              只能上传.rar,.zip,.doc,.docx,.pdf,.png,.jpg文件，单个文件不能超过20MB
            </div>
          </el-upload>
        </el-form-item>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDispatch = false">取 消</el-button>
        <el-button type="primary" @click="defineDispatchhandle()">确定</el-button>
      </span>
    </el-dialog>
     <el-dialog
      title="关闭告警"
      :visible.sync="dialogCloseAlarm"
      width="650px"
      append-to-body
      @close="closeCloseAlarm"
    >
      <el-form
        ref="formClose"
        :model="formClose"
        label-position="right"
        label-width="80px"
      >
      
          <el-col :span="24">
            <el-form-item
              label="关闭理由"
              prop="closeReason"
              :rules="[{ max: 300, message: '不能超过300字', trigger: 'blur' }, {
            required: true,
            message: '请输入关闭理由',
            trigger: 'blur',
          }]"
            >
              <el-input
                v-model="formClose.closeReason"
                type="textarea"
                class="form-item-texarea"
                :autosize="{ minRows: 4, maxRows: 6 }"
              />
            </el-form-item>
          </el-col>
          <el-form-item label="上传附件" prop="file" title="上传附件">
          <el-upload
            class="upload-demo"
            action="/safety-v2/file/upload"
            :on-success="successUpload1"
            :on-exceed="handleExceed1"
            :before-upload="beforeAvatarUpload1"
            :on-remove="removeFile1"
            :headers="headers"
            :limit="1"
            :file-list="fileList1"
            accept=".rar,.zip,.doc,.docx,.pdf,.png,.jpg"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              只能上传.rar,.zip,.doc,.docx,.pdf,.png,.jpg文件，单个文件不能超过50MB
            </div>
          </el-upload>
        </el-form-item>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCloseAlarm = false">取 消</el-button>
        <el-button type="primary" @click="defineCloseAlarm">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="titleDialog"
      :visible.sync="dialogEdit"
      width="960px"
      class="dialog"
      @close="closeDialog"
      append-to-body
      v-if="dialogEdit"
    >
      <!-- <el-steps :active="active" finish-status="success">
  <el-step title="企业信息"></el-step>
  <el-step title="其他信息"></el-step>
</el-steps> -->
     <el-form
            ref="abilityForm"
            :model="abilityForm"
            :rules="abilityRules"
            label-width="110px"

          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="事件名称" prop="eventName">
                  <el-input
                    v-model="abilityForm.eventName"
                    placeholder="请输入事件名称"
                    maxlength="30"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发生时间" prop="occurrenceTime">
                  <el-date-picker
                    v-model="abilityForm.occurrenceTime"
                    type="datetime"
                    placeholder="选择日期"
                    :disabled="disabled"
                    style="width: 245px"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="上报人" prop="submitPerson">
                  <el-input
                    v-model="abilityForm.submitPerson"
                    placeholder="请输入上报人"
                    maxlength="30"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系方式" prop="contactNumber">
                  <el-input
                    v-model="abilityForm.contactNumber"
                    placeholder="请输入联系方式"
                    maxlength="30"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="事件类型" prop="eventTypeId">
                  <el-select
                    style="width: 245px"
                    placeholder="请选择事件类型"
                    v-model="abilityForm.eventTypeId"
                    :disabled="disabled"
                    @change="changeType"
                  >
                   <el-option
      v-for="item in eventTypeOptions"
      :key="item.id"
      :label="item.nodeName"
      :value="item.id">
    </el-option>
                    <!-- <el-option :value="abilityForm.eventTypeName" class="option">
                      <el-tree
                        :data="AddtreeData"
                        :show-checkbox="true"
                        node-key="id"
                        :props="defaultProps"
                        class="tree"
                        @check="handleNodeClick"
                        ref="Addtree"
                      >
                      </el-tree>
                    </el-option> -->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件等级" prop="eventLevel">
                  <el-select
                    style="width: 245px"
                    v-model="abilityForm.eventLevel"
                    placeholder="请选择事件等级"
                    :disabled="disabled"
                  >
                    <el-option
                      v-for="dict in eventLevelOption"
                      :key="dict.dictKey"
                      :label="dict.dictValue"
                      :value="dict.dictKey"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="经济损失" prop="economicLoss">
                  <el-input
                    v-model="abilityForm.economicLoss"
                    placeholder="请输入经济损失"
                    maxlength="30"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受灾面积" prop="disasterArea">
                  <el-input
                    v-model="abilityForm.disasterArea"
                    placeholder="请输入受灾面积"
                    maxlength="30"
                    :disabled="disabled"
                    style="width: 245px"
                  />公顷
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="死亡人数" prop="deathNumber">
                  <el-input
                    v-model="abilityForm.deathNumber"
                    placeholder="请输入死亡人数"
                    maxlength="30"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受伤人数" prop="injuredNumber">
                  <el-input
                    v-model="abilityForm.injuredNumber"
                    placeholder="请输入受伤人数"
                    maxlength="30"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="失踪人数" prop="missingNumber">
                  <el-input
                    v-model="abilityForm.missingNumber"
                    placeholder="请输入失踪人数"
                    maxlength="30"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="受困人数" prop="trappedNumber">
                  <el-input
                    v-model="abilityForm.trappedNumber"
                    placeholder="请输入受困人数"
                    maxlength="30"
                    :disabled="disabled"
                    style="width: 245px"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="事件标签" prop="eventLabel">
                  <el-select
                    style="width: 245px"
                    v-model="abilityForm.eventLabel"
                    placeholder="请选择事件类型"
                    :disabled="disabled"
                    @change="eventLabelChange"
                  >
                    <el-option
                      v-for="dict in labelData"
                      :key="dict.eventTypeId"
                      :label="dict.label"
                      :value="dict.eventTypeId"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经纬度 :" prop="lngAndLat">
                   <el-input
                    v-model="lngAndLat"
                    placeholder="请输入经纬度"
                    disabled
                    style="width: 245px"
                  ></el-input
                >
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="事件描述" prop="eventDescription">
                  <el-input
                    v-model="abilityForm.eventDescription"
                    placeholder="请输入备注"
                    type="textarea"
                    :disabled="disabled"
                    style="width: 245px"
                  ></el-input
                ></el-form-item>
              </el-col>
              <el-col :span="12" v-if="disabled==false">
                <el-form-item label="附件 :">
                  <el-upload
                    class="upload-demo"
                    :action="uploadImgUrl"
                    :on-success="handleAvatarSuccessFire"
                    :file-list="fileList"
                    :disabled="disabled"
                    :on-remove="handleRemoveFire"
                    v-model="abilityForm.attachmentAddress"
                    :before-upload="beforeAvatarUploadFire"
                    :limit="1"
                    :headers="headers"
                    accept=".doc,.docx,.pdf"
                    :on-exceed="handleExceedFire"
                  >
                    <el-button :disabled="disabled" size="small" type="primary"
                      >点击上传</el-button
                    >
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="disabled==true && abilityForm.attachmentAddress!=undefined" >
                <el-form-item label="附件 :">
                  <div @click="downloadFile(abilityForm.attachmentAddress)" style="cursor:default" title="点击下载">
                    {{abilityForm.attachmentAddress.split('/')[abilityForm.attachmentAddress.split('/').length-1]}}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogEdit = false">取 消</el-button>
        <el-button @click="determineEdit('abilityForm')" type="primary" 
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <alarmRecord ref="alarmRecord" />
    <myTransfer
      ref="myTransfer"
      @define="define"
      :deptListUsed="deptListUsed"
      @cancel="cancel"
    />
  </div>
</template>

<script>
import myTransfer from "../components/myTransfer.vue";
import alarmRecord from "../components/alarmRecord.vue";
import {
  alarmPage,
  typeList,
  levelList,
  handling,
  closeAlarm1,
  labelList,
  eventDetail,
  downloadOneFile,
} from "@/api/fireEngine/index.js";
import {
  getAlarmList,
  getAlarmDetail,
  getDeptList,
  closeEquipmentAlarm,
  sendDispatch,
  getEnumList,
  getFileUrl,
  deleteAlarm,
  alarmDetile,
  handleAlarm,
  closeAlarm,
  getBondUsersInfoByName,
  sendOrder,
  selectPageNew,
  alarmDetileNew,
  closeAlarmNew,
  handleAlarmNew
} from "@/api/alarm/alarm";
export default {
  components: { myTransfer, alarmRecord },
  data() {
    return {
      // 判断是否在推送里关闭
      closeOne:false,
      peopleListCompany: [],
      dialogDispatchWorkFlow: false,
      formPush: {},
      dialogPush: false,
      labelData: [],
      uploadImgUrl: "/firecontrol/file/uploadFile",
      dialogEdit: false,
      alarmId: null,
      titleDialog: null,
      disabled: false,
      lngAndLat: null,
      abilityForm: {},
      abilityRules: {
        eventName: [
          { required: true, message: "事件名称不能为空", trigger: "blur" },
        ],
        submitPerson: [
          { required: true, message: "上报人不能为空", trigger: "change" },
        ],
        eventLevel: [
          { required: true, message: "上报等级不能为空", trigger: "change" },
        ],
        eventTypeId: [
          { required: true, message: "请选择事件类型", trigger: "change" },
        ],
        eventDescription: [
          { required: true, message: "事件描述不能为空", trigger: "blur" },
        ],
        // lngAndLat: [{ required: true, validator: validCode, trigger: "blur" }],
      },
      eventTypeOptions: [],
      eventLevelOption: [],
      tabFlag: null,
      enterpriseName: null,
      alarmObject: null,
      time: [],
      processState: [],
      showRecord: false,
      finishBy: null,
      finishTime: null,
      message: null,
      frequency: null,
      active: 0,
      total: 0,
      transferShow: false,
      dispatchStepsList: [],
      form: {},
      dialogCloseAlarm: false,
      formClose: {},
      fileList1: [],
      rules: {
        userNameList: [
          { required: true, message: "接单人不能为空", trigger: "change" },
        ],
        orderLevel: [
          { required: true, message: "工单级别不能为空", trigger: "change" },
        ],
        influenceScope: [
          { required: true, message: "影响范围不能为空", trigger: "change" },
        ],
      },
      dialogDispatch: false,
      dialogMsg: false,
      typevalue: null,
      groupvalue: null,
      groupName: null,
      earevalue: null,
      typeOptions: [],

      column: [
        {
          label: "企业名称",
          prop: "enterpriseName",
          align: "center",
        },
        {
          label: "报警类型",
          prop: "tabFlag",
          align: "center",
        },
        {
          label: "报警时间",
          prop: "alarmTime",
          align: "center",
        },
        {
          label: "报警对象",
          prop: "alarmObject",
          align: "center",
        },
        {
          label: "告警内容",
          prop: "content",
          align: "center",
        },
        // {
        //   label: "采集数值",
        //   prop: "acquisitionValue",
        //   align: "center",
        // },
        {
          label: "标准范围",
          prop: "standardRange",
          align: "center",
        },
        {
          label: "处理状态",
          prop: "processState",
          align: "center",
        },
      ],
      tableData: [],
      pageSize: 10,
      pageNumber: 1,
      alarmId: null,
      deptList: [],
      deptListUsed: [],
      userIdList: [],
      userList: [],
      ids: [],
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      fileList: [],
      detileForm: {},
      escalationId: null,
      formWorkflow: {
        smsMessage: false,
      },
      rulesWork: {
        userIds: [
          { required: true, message: "接单人不能为空", trigger: "select" },
        ],
      },
      alarmPushForm:{}
    };
  },
  watch: {
    // "form.userNameList"(val) {
    //   if (val) {
    //     this.$refs.form.validateField(["userNameList"]);
    //   }
    // },
  },
  mounted() {
    this.selectAlarmList();
    this.getDept();
    this.getEnum();
    this.getType();
  },
  methods: {
    getdetileAram(id){
       alarmDetileNew(id).then((res)=>{
         this.alarmPushForm=res.data
         if(this.alarmPushForm.processState=='未处理'){
          this.active=0
         }else if(this.alarmPushForm.processState=='已派单'){
          this.active=1
         }else if(this.alarmPushForm.processState=='已关闭'){
          this.active=2
         }
      })
    },
    dispatchWorkFlow() {
      getBondUsersInfoByName({ name: this.alarmPushForm.enterpriseName }).then((res) => {
       
        this.peopleListCompany = res.data;
      this.dialogDispatchWorkFlow = true;
        
      });
    },
    push(e) {
      alarmDetileNew(e.id).then((res)=>{
         this.dialogPush = true;
         this.alarmPushForm=res.data
         if(this.alarmPushForm.processState=='未处理'){
          this.active=0
         }else if(this.alarmPushForm.processState=='已派单'){
          this.active=1
         }else if(this.alarmPushForm.processState=='已关闭'){
          this.active=2
         }
      })
      
    },
    definePushDispatch() {},
    closePushDispatch() {
      this.formPush = {};
    },
    // 解决因为切换叶子节点清空值导致不能选择的问题
    eventLabelChange(res) {
      this.$forceUpdate();
    },
    changeType(e) {
      labelList({ eventTypeId: e }).then((res) => {
        this.labelData = res.data;
      });
    },
    closeDialog(e) {},
    getType() {
      typeList().then((res) => {
        this.eventTypeOptions = res.data;
      });
      levelList().then((res) => {
        this.eventLevelOption = res.data;
      });
    },
    // 上报弹窗
    toEscalation(e) {
      console.log(e);
      this.dialogEdit = true;
      this.alarmId = e.alarmObjectId;
      this.escalationId = e.id;
      this.titleDialog = "上报";
      this.disabled = false;
      if (e.longitude) {
        this.lngAndLat = e.longitude + "," + e.latitude;
      } else {
        this.lngAndLat = "-";
      }
      this.abilityForm = {
        enterpriseName: e.enterpriseName,
        eventTypeId: undefined,
        eventTypeName: undefined,
        eventLabel: undefined,
        eventName: "消防主机告警",
        submitPerson: "消防主机",
        contactNumber: e.phone,
        eventDescription: "消防主机告警",
        eventLevel: "5010901",
        longitude: e.longitude,
        latitude: e.latitude,
      };
    },
    // 下载
    downBlobFile(url, fileName) {
      const x = new window.XMLHttpRequest();
      x.open("GET", url, true);
      x.responseType = "blob";
      x.onload = () => {
        const url = window.URL.createObjectURL(x.response);
        const a = document.createElement("a");
        a.href = url;
        // a.target = "_blank";
        a.download = fileName;
        a.style.display = "none";
        document.body.append(a);
        a.click();
      };
      x.send();
    },
    downFile(e) {
      this.downBlobFile(this.changeUrl(e.viewPath), e.fileName);
    },
    // 处理输入框输入问题
    changeProductName() {
      this.$forceUpdate();
    },
    // 删除一条告警信息
    todelete(e) {
      this.$confirm("是否删除告警信息？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteAlarm(e.id).then((res) => {
          this.selectAlarmList();
          this.$message.success("删除成功");
        });
      });
    },
    // 上传附件 成功
    successUpload(res, b, c) {
      console.log(res, b, c);
      if (res.code == 200) {
        this.$set(this.form, "handleFile", JSON.stringify(res.data));
        // this.$set(this.reportForm.file, "fileId", res.data.fileId);
        // this.$set(this.reportForm.file, "fileName", res.data.fileName);
      } else {
        for (let i = 0; i < c.length; i++) {
          if (b.uid == c[i].uid) {
            c.splice(i, 1);
          }
        }
        this.$message.error(res.msg);
      }

      // this.$set(this.form, "fileName", res.data.fileName);
      // this.$set(this.form, "fileSize", res.data.fileSize);
    },
    // 超出
    handleExceed() {
      this.$message.error("最多上传一个文件");
    },
    // 删除
    removeFile(file, fileList) {
      this.$set(this.form, "handleFile", null);

      // this.$set(this.form, "fileName", null);
      // this.$set(this.form, "fileSize", null);
    },
    // 上传文件
    getFileType(name) {
      let startIndex = name.lastIndexOf(".");
      if (startIndex !== -1) {
        return name.slice(startIndex + 1).toLowerCase();
      } else {
        return "";
      }
    },
    beforeAvatarUpload(file) {
      let suffix = this.getFileType(file.name);
      let suffixArray = ["rar", "zip", "doc", "docx", "pdf", "png", "jpg"];
      const isLt20M = file.size / 1024 / 1024 < 50;
      if (suffixArray.indexOf(suffix) === -1) {
        console.log(this.fileUpList);
        this.$message.error(
          "上传文件只能是 .rar,.zip,.doc,.docx,.pdf,.png,.jpg 格式!"
        );
        // return false;
      }
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过50M");
      }
      return suffixArray.indexOf(suffix) != -1 && isLt20M;
    },
    // 上传附件 成功
    successUpload1(res, b, c) {
      console.log(res, b, c);
      if (res.code == 200) {
        this.$set(this.formClose, "closeFile", JSON.stringify(res.data));
        // this.$set(this.reportForm.file, "fileId", res.data.fileId);
        // this.$set(this.reportForm.file, "fileName", res.data.fileName);
      } else {
        for (let i = 0; i < c.length; i++) {
          if (b.uid == c[i].uid) {
            c.splice(i, 1);
          }
        }
        this.$message.error(res.msg);
      }

      // this.$set(this.form, "fileName", res.data.fileName);
      // this.$set(this.form, "fileSize", res.data.fileSize);
    },
    // 超出
    handleExceed1() {
      this.$message.error("最多上传一个文件");
    },
    // 删除
    removeFile1(file, fileList) {
      this.$set(this.formClose, "closeFile", null);

      // this.$set(this.form, "fileName", null);
      // this.$set(this.form, "fileSize", null);
    },

    beforeAvatarUpload1(file) {
      let suffix = this.getFileType(file.name);
      let suffixArray = ["rar", "zip", "doc", "docx", "pdf", "png", "jpg"];
      const isLt20M = file.size / 1024 / 1024 < 50;
      if (suffixArray.indexOf(suffix) === -1) {
        console.log(this.fileUpList);
        this.$message.error(
          "上传文件只能是 .rar,.zip,.doc,.docx,.pdf,.png,.jpg 格式!"
        );
        // return false;
      }
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过50M");
      }
      return suffixArray.indexOf(suffix) != -1 && isLt20M;
    },

    getEnum() {
      getEnumList().then((res) => {
        this.typeOptions = res.data;
      });
    },
    // 处理方法
    toDeal(e) {
      console.log(e);
      if (e.id) {
        this.$refs.multipleTable.clearSelection();
        this.dialogDispatch = true;
        this.ids = [];
        this.ids.push(e.id);
      } else {
        console.log(this.ids);
        if (this.ids.length == 0) {
          this.$message.error("请选择处理数据");
        } else {
          this.dialogDispatch = true;
        }
      }
    },
    toClose(e) {
      if (e.id) {
        this.dialogCloseAlarm = true;
        this.closeOne=false
      } else {
        console.log(this.ids);
        if (this.ids.length == 0) {
          this.$message.error("请选择关闭数据");
        } else {
          this.dialogCloseAlarm = true;
        this.closeOne=false

        }
      }
    },
    // 确认关闭
    defineCloseAlarm() {
      this.$refs.formClose.validate((valid) => {
        if (valid) {
          if(this.closeOne){
            closeAlarmNew({
            ids: [this.alarmPushForm.id],
            closeReason: this.formClose.closeReason,
            closeFile: this.formClose.closeFile,
          }).then((res) => {
            this.selectAlarmList();
            this.getdetileAram(this.alarmPushForm.id)
             this.dialogCloseAlarm = false;
            this.$message.success("关闭成功");
            
           
          });
          }else{
            closeAlarmNew({
            ids: this.ids,
            closeReason: this.formClose.closeReason,
            closeFile: this.formClose.closeFile,
          }).then((res) => {
            this.selectAlarmList();
            this.dialogCloseAlarm = false;
            this.$message.success("关闭成功");
          });
          }
          
        }
      });
    },
    handleSelectionChange(selection) {
      console.log(selection);
      this.ids = selection.map((item) => item.id);
    },
    // 关闭派单弹窗
    closeDispatch() {
      this.$refs.multipleTable.clearSelection();
      this.ids = [];
      this.form = {};
    },
    // 关闭告警弹窗
    closeCloseAlarm() {
      this.$refs.multipleTable.clearSelection();
      this.ids = [];
      this.formClose = {};
    },
    getDept() {
      getDeptList().then((res) => {
        this.deptList = res.data;
        this.readNodes(this.deptList, this.deptListUsed);
      });
    },
    readNodes(nodes, arr) {
      console.log(nodes, arr);
      for (let item of nodes) {
        arr.push({
          id: item.id,
          name: item.name,
        });
        console.log(arr);
        if (item.children && item.children.length)
          this.readNodes(item.children, arr);
      }
      return arr;
    },
    // 查看次数详情
    toFrequencyDetile(e) {
      this.showRecord = true;
      this.$refs.alarmRecord.recordData = [];
      this.$refs.alarmRecord.show = true;
      this.$refs.alarmRecord.radio = "0";
      this.$refs.alarmRecord.pid = e;
      this.$refs.alarmRecord.pageSize = 10;
      this.$refs.alarmRecord.pageNumber = 1;
      this.$refs.alarmRecord.total = 0;
      this.$refs.alarmRecord.searchList(e);
    },
    selectAlarmList() {
      let data = {
        pageNum: this.pageNumber,
        pageSize: this.pageSize,
        tabFlag: this.tabFlag,
        enterpriseName: this.enterpriseName,
        alarmObject: this.alarmObject,
        processState:
          this.processState.length == 0
            ? null
            : JSON.stringify(this.processState),
      };
      if (this.time.length == 2) {
        this.$set(data, "startTime", this.time[0]);
        this.$set(data, "endTime", this.time[1]);
      } else {
        this.$set(data, "startTime", null);
        this.$set(data, "endTime", null);
      }
      selectPageNew(data).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
        this.tableData.forEach((item) => {
          for (let item1 in item) {
            console.log(item1);
            if (item[item1] == null || item[item1] == "") {
              item[item1] = "-";
            }
          }
        });
      });
    },
    changeSize(pageSize) {
      this.pageSize = pageSize;
      this.selectAlarmList();
    },
    changePage(current) {
      this.pageNumber = current;
      this.selectAlarmList();
    },
    toPop() {
      this.$refs.myTransfer.showTransferDialog = true;
      this.$refs.myTransfer.rightData = [];
      if (this.userList) {
        this.userList.forEach((item) => {
          this.$refs.myTransfer.rightData.push(item);
        });
      }
      this.$nextTick(() => {
        this.$refs.myTransfer.getLeftData();
      });
    },
    closeAlarm() {
      // this.$confirm("是否关闭告警", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // }).then(() => {
      //   closeEquipmentAlarm(this.alarmId).then((res) => {
      //     this.selectAlarmList();
      //     this.dialogMsg = false;
      //     this.$message.success("关闭成功");
      //   });
      // });
      this.dialogCloseAlarm=true
      this.closeOne=true
    },
    closeDispatchWorkFlow(){
      this.formWorkflow={
        smsMessage: false,
      }
    },
    defineDispatch() {
      console.log(this.formWorkflow.userIds);
      this.$refs.formWorkflow.validate((valid) => {
        if (valid) {
          let userIds = [];
          this.peopleListCompany.forEach((item) => {
            this.formWorkflow.userIds.forEach((item1) => {
              if (item1 == item.userId) {
                userIds.push(item);
              }
            });
          });
          this.$set(this.formWorkflow, "userList", userIds);
          this.$set(this.formWorkflow, "tabFlag", this.alarmPushForm.tabFlag);
          this.$set(this.formWorkflow, "alarmTime", this.alarmPushForm.alarmTime);
          this.$set(this.formWorkflow, "id", this.alarmPushForm.id);
          sendOrder(this.formWorkflow).then((res) => {
            this.selectAlarmList();
            this.getdetileAram(this.alarmPushForm.id);
            this.dialogDispatchWorkFlow = false;
            this.$message.success("派单成功");
          });
        }
      });
    },
    defineDispatchhandle() {
      console.log(this.userIdList);
      this.$refs.form.validate((valid) => {
        if (valid) {
          handleAlarmNew({
            ids: this.ids,
            handleFile: this.form.handleFile,
            handleReason: this.form.handleReason,
          }).then((res) => {
            this.selectAlarmList();
            this.dialogDispatch = false;
            this.$message.success("处理成功");
          });
        }
      });
    },
    dispatch() {
      this.dialogDispatch = true;
    },
    toDetile(e) {
      console.log(e);
      alarmDetileNew(e.id).then((res) => {
        this.dialogMsg = true;
        this.detileForm = res.data;
        if (this.detileForm.closeFile) {
          this.$set(
            this.detileForm,
            "closeFileObj",
            JSON.parse(this.detileForm.closeFile)
          );
        }
        if (this.detileForm.handleFile) {
          this.$set(
            this.detileForm,
            "handleFileObj",
            JSON.parse(this.detileForm.handleFile)
          );
        }
        if (this.detileForm.tag == "人员离岗告警") {
          let font = window.location.protocol;
          // let host='*************'
          let host = window.location.hostname;
          this.$set(
            this.detileForm,
            "acquisitionValue",
            font +
              "//" +
              host +
              ":13005/" +
              this.detileForm.acquisitionValue
                .split("/")
                .splice(3, this.detileForm.acquisitionValue.split("/").length)
                .join("/")
          );
        }
      });
    },
    search() {
      this.selectAlarmList();
    },
    selectTypes() {
      // selectAllType().then((res) => {
      //   this.typeOptions = res.data;
      // });
    },
    changeGroup(a) {
      console.log(a);
      this.groupvalue = a[a.length - 1].groupId;
    },
    // 重置
    reset() {
      this.tabFlag = null;
      this.enterpriseName = null;
      this.alarmObject = null;
      this.time = [];
      this.processState = [];
      this.search();
    },
    //
    define(e) {
      this.userList = e;
      this.userIdList = [];
      console.log(e);
      let userNameList = [];
      for (let i = 0; i < e.length; i++) {
        userNameList.push(e[i].realname);
        this.userIdList.push(e[i].userId);
        // userIdList.push(e[i].userId)
      }
      this.form.userNameList = userNameList.toString();
      this.$refs.myTransfer.showTransferDialog = false;
    },
    cancel() {},
    // wenjian
    handleAvatarSuccessFire(response, res, file) {
      console.log(response, res, file);
      this.abilityForm.attachmentAddress = res.response;
      this.abilityForm.fileName = res.name;
    },
    handleRemoveFire(file, fileList) {
      this.abilityForm.attachmentAddress = "";
      this.abilityForm.fileName = "";
    },
    handleExceedFire() {
      this.$modal.msgSuccess("请不要上传多个文件");
    },
    // 上传文件
    getFileTypeFire(name) {
      let startIndex = name.lastIndexOf(".");
      if (startIndex !== -1) {
        return name.slice(startIndex + 1).toLowerCase();
      } else {
        return "";
      }
    },
    beforeAvatarUploadFire(file) {
      let suffix = this.getFileTypeFire(file.name);
      let suffixArray = ["doc", "docx", "pdf"];
      if (suffixArray.indexOf(suffix) === -1) {
        console.log(this.fileUpList);
        this.$message.error("上传文件只能是 .doc,.docx,.pdf 格式!");
        // return false;
      }
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过20M");
      }
      return suffixArray.indexOf(suffix) != -1 && isLt20M;
    },
    // 新增、编辑确定
    determineEdit(formName) {
      console.log(this.abilityForm);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$set(this.abilityForm, "alarmId", this.alarmId);
          this.$set(this.abilityForm, "businessId", this.escalationId);
          // this.$set(this.abilityForm,'id',this.alarmId)
          this.eventTypeOptions.forEach((item) => {
            if (item.id == this.abilityForm.eventTypeId) {
              this.$set(this.abilityForm, "eventTypeName", item.nodeName);
            }
          });

          handling(this.abilityForm).then((res) => {
            this.dialogEdit = false;
            this.selectAlarmList();
          });
        }
      });
    },
  },
};
</script>

<style scoped>
.newbutton {
  /* margin-bottom: 16px; */
  margin-left: auto;
}

.all {
  padding: 16px;
}
.topTitle {
  font-size: 16px;
  height: 56px;
  padding: 16px;
  border: 1px solid #ebebeb;
}
.topForm {
  display: flex;
  background-color: white;
  padding: 16px;
  border-bottom: 1px solid #ebebeb;
  border-left: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}
.leftForm {
  width: 84%;
}
.twoButton {
  margin-left: auto;
}
.twoButton button {
  font-size: 13px;
  border-radius: 2px;
}
/* ::v-deep .el-button--medium {
  width: 88px;
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
} */
.bottom {
  display: flex;
  height: 64px;
  padding: 16px;
  line-height: 34px;
  font-size: 16px;
  border: 1px solid #ebebeb;
}
.bottomTable {
  margin-top: 16px;
}
.tableBottom {
  border-bottom: 1px solid #ebebeb;
  border-left: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
  padding: 16px 0;
}
::v-deep .el-pagination {
  margin-top: 20px;
  float: right;
}
.pages {
  width: 100%;
  height: 50px;
}
.leftForm ::v-deep .el-descriptions-item__label {
  margin-top: 7px;
  width: 102px;
  display: block;
  text-align: right;
}
::v-deep .el-descriptions-item__container {
  margin-right: 20px;
}
::v-deep .el-cascader {
  width: 10vw;
  height: 32px;
}
.leftForm ::v-deep .el-select {
  width: 10vw;
  height: 32px;
}
::v-deep.el-input {
  width: 10vw;
  height: 32px;
}
.step-container {
  display: flex;
  width: 100%;
  height: 200px;
  background-color: #f6f7fa;
  padding: 10px 0;
}
.step-container-item-1 {
  width: 32%;
  padding: 20px 10px;
  display: flex;

  justify-content: center;
}
.step-container-item-2 {
  width: 38%;
  padding: 0 10px;
  border-left: 2px solid #c0c4cc;
}
.step-container-item-3 {
  padding: 0 10px;
  width: 30%;
  border-left: 2px solid #c0c4cc;
}
.activityName {
  font-weight: bold;
  color: rgb(64, 158, 255);
}
.pages ::v-deep button {
  background-color: #fff !important;
  border: 1px solid #cccccc;
  border-radius: 2px;
  color: #666666;
  font-weight: 400 !important;
}
::v-deep .number {
  background-color: #fff !important;
  border: 1px solid #cccccc;
  font-weight: 400 !important;
}
::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  border: 1px solid #1890ff !important ;
  color: #1890ff !important;
  background-color: #fff !important;
  font-weight: 400 !important;
}
::v-deep .el-pagination.is-background .el-pager li {
  background-color: #fff !important;
  font-weight: 400 !important;
}
::v-deep .el-pager li.active + li {
  border-left: 1px solid #cccccc !important;
}
::v-deep .el-table__header .el-table__cell {
  color: #007baf;
  font-weight: 400;
  font-size: 16px;
  height: 24px;
  line-height: 24px;
  background-color: rgba(25, 159, 255, 0.15);
}
::v-deep .el-date-editor {
  width: 10vw;
}
.dialogTitle {
  /* margin-top: 10px; */
  font-size: 16px !important;
  margin-bottom: 10px !important;
  font-weight: 600;
  color: #000;
}
::v-deep .el-textarea__inner {
  position: relative !important;
  z-index: 99999999 !important;
}
.fileName {
  color: #409eff;
  width: 500px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.imgStyle {
  width: 57px;
  height: 50px;
}
.imgStyle1 {
  width: 50px;
  height: 50px;
}
</style>