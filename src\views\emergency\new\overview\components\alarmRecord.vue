<template>
  <div>
    <el-dialog title="告警历史记录" :visible.sync="show" width="960px" append-to-body>
      <!-- <el-radio-group v-model="radio" @input="changeRadio">
        <el-radio-button label="0">今日频次</el-radio-button>
        <el-radio-button label="1">历史频次</el-radio-button>
      </el-radio-group> -->
      <el-table :data="recordData" :cell-style="{padding: '0px'}"
           :row-style="{height: '48px'}">
        <el-table-column
          property="id"
          label="序号"
          width="150"
        >
          <template slot-scope="scope">
          <span>{{(pageNumber - 1) * pageSize + scope.$index + 1}}</span>
        </template>
        </el-table-column>
        <el-table-column
          property="alarmTime"
          label="告警时间"
          width="200"
        ></el-table-column>
        <el-table-column property="content" label="告警内容"></el-table-column>
      </el-table>
      <div class="pages">
        <el-pagination
          background
          layout="total,prev, pager, next,sizes,jumper"
          :total="total"
          @size-change="changeSize"
          @current-change="changePage"
          :pager-count="5"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getAlarmList } from "@/api/alarm/alarm";
export default {
  data() {
    return {
      show:false,
      radio: "0",
      recordData: [],
      total: null,
      pageSize: 10,
      pageNumber: 1,
      pid:null
    };
  },
  mounted(){
    // this.searchList()
  },
  methods: {
    searchList(e){
        console.log(this.$parent.alarmId);

        let data={
            pageNum:this.pageNumber,
            pageSize:this.pageSize,
            pid:this.pid,
        }
        getAlarmList(data).then((res)=>{
            this.recordData=res.data.list
            this.total=res.data.total
            for(let i=0;i<this.recordData.length;i++){
              this.$set(this.recordData[i],"id",i+1)
            }
        })
    },
    changeSize(pageSize) {
      this.pageSize = pageSize;
      this.searchList()

    },
    changePage(current) {
      this.pageNumber = current;
      this.searchList()

    },
    changeRadio(){
      this.searchList()
    }
  },
};
</script>

<style scoped>
::v-deep .el-dialog__body{
    padding: 30px 20px 70px;
}
</style>