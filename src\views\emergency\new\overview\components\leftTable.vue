<template>
  <div class="left">
    <el-cascader :options="options" v-model="deptId" :show-all-levels="false" :props="prop"></el-cascader>

    <el-table ref="leftTable" :data="leftData" style="width: 100%" :cell-style="{padding: '0px'}"
           :row-style="{height: '48px'}">
      <el-table-column
        v-for="item in column"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column label="操作"  align="center">
        <template slot-scope="scope">
          <el-button
            @click="choose(scope.row)"
            type="text"
            size="small"
            icon="el-icon-arrow-right"
            class="detileButton"
            >选择</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        small
        class="pagination"
        :pager-count="5"
        layout="total,prev, pager, next"
        :total="total"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
  </div>
</template>

<script>
import {getDeptList } from "@/api/alarm/alarm";

export default {
    props:{
        leftData:{type:Array}
    },
  data() {
    return {
      prop:{
        value:"id",
        label:"name"
      },
      deptId:null,
      options:null,
      queryParams: {
        pages: 1,
        size: 10,
      },
      total:0,
      column: [
        {
          label: "姓名",
          prop: "realname",
        },
        {
          label: "部门",
          prop: "deptName",
        },
        {
          label: "联系方式",
          prop: "phone",
        },
        {
          label: "邮箱",
          prop: "email",
        },
      ],
    };
  },
  mounted(){
    this.getDept()
  },
  methods:{
     getDept(){
      getDeptList().then((res)=>{
        this.options=res.data
      })
    },
    choose(e){
        this.$emit('choose',e)
    },
    // 搜索人员
    searchPeople(){

    },
    
    handleCurrentChange(val){
      console.log(val);
      this.$set(this.queryParams,"pages",val)
      this.$emit('handleCurrentChange')
    } 
  }
};
</script>

<style scoped>
::v-deep .el-table__body-wrapper {
  max-height: 350px;
  overflow:scroll;
}
.left ::v-deep .el-cascader{
  margin-bottom: 10px;
}
</style>