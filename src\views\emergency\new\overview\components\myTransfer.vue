<template>
  <div>
     <el-dialog
  title="派单"
  :visible.sync="showTransferDialog"
  width="1000px"
  @close="closeTransferDialog"
  >
    <div class="tableAll">
      <div class="letfPart">
        <leftTable :leftData="leftData" @choose="choose" ref="leftTable" @handleCurrentChange="handleCurrentChange"/>
      </div>
      <div class="letfPart">
        <rightTable ref="rightTable" :rightData="rightData" @deleteOne="deleteOne"/>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
    <el-button @click="define">确 定</el-button>
    <el-button @click="cancel">取 消</el-button>
  </span>
     </el-dialog>
     
  </div>
</template>

<script>
//引入组
import leftTable from '../components/leftTable.vue'
import rightTable from '../components/rightTable.vue'
import { listByDept } from "@/api/alarm/alarm";

export default {
  components: {
    leftTable,rightTable
  },
  props:{
    deptListUsed:{
      type:Array
    }

  },
  data() {
    return {
        showTransferDialog:false,
        rightData:[],
     leftData: [
        
      ],
      chooseDeviceList: [],
    }
  },
  mounted(){
    // this.$nextTick(()=>{
    //    this.getLeftData()

    // })
  },
  methods: {
    handleCurrentChange(){
      this.getLeftData()
    },
    getLeftData(){
      console.log(this.$refs.leftTable);
      listByDept({
        pageNum:this.$refs.leftTable.queryParams.pages,
        pageSize:this.$refs.leftTable.queryParams.size,
        deptId:this.$refs.leftTable.deptId
      }).then((res)=>{
        this.leftData=res.data.records
        this.$refs.leftTable.total=res.data.total
        this.leftData.forEach(item=>{
          for(let i=0;i<this.deptListUsed.length;i++){
            if(item.deptId=this.deptListUsed[i].id){
              this.$set(item,'deptName',this.deptListUsed[i].name)
            }
          }
        })
      })
    },
    // 获取最新的右侧数据
    transferChange(data) {
      this.chooseDeviceList = data
    },
    choose(e){
      // console.log(this.rightData);
      if(this.rightData.length===0){
         this.rightData.push(e)
      }else{
        let flag=0
        for(let i=0;i<this.rightData.length;i++){
          if(this.rightData[i].userId===e.userId){
            flag++
          }
        }
        if(flag===0){
          this.rightData.push(e)
        }
      }
    },
    deleteOne(e){
      let flag =this.rightData.indexOf(e);
      this.rightData.splice(flag,1)
      console.log(this.rightData);
    },
    define(){
      this.$emit('define',this.rightData)
    },
cancel(){
  this.showTransferDialog=false
},
closeTransferDialog(){
}
  }
}
</script>
<style scoped>
/* 盒子宽度 */
.box-width {
  width: 800px;
}
.tableAll{
  display: flex;
  height:  400px;
}
.letfPart{
  width: 450px;
  margin-right: 20px;

}

</style>

