<template>
    <div class="all-wrapper">
        <div id="economicDetailsThirdTwo" ref="economicDetailsThirdTwo" v-show="show"></div>
        <el-empty description="暂无数据"  v-show="!show"></el-empty>
    </div>
  </template>
  
  <script>
  import * as echarts from "echarts";
  import {
    overview,
    planManageFirmList,
    emergencyAreaTree,
    savePlanManageFirm,
    getDict,
    view,
    del,
    editPlanManageFirm,
    exportTemplatePlan,
    exportPlan,
    overviewLeft,
overviewRight
    
} from "@/api/emergency/new/parkPlan.js";
  export default {
      name: 'centerTwo',
      data() {
            return {
                  pieData: [],
                  show:false
        }
      },
      methods: {
         //获取图表
         getOverviewLeft() {
              overviewLeft().then((res) => {
                // this.overviewData = res.data
                    if (res.data && res.data.length > 0) {
                        this.show = true
                        this.pieData = res.data.map(x => {
                          return {
                                value: x.number,
                        name:x.name
                          }
                          })
                      console.log(this.pieData);
                      this.drawSpaceResources()
                      } else {
                    this.show = false
                }
                    
                
            })
        },
          drawSpaceResources() {
              var pieChart = this.$echarts.init(document.getElementById("economicDetailsThirdTwo"));
              var option = {
            calculable : true,
        legend: {
            show: true,
            type: 'scroll',
            pageIconColor: '#2f4554', // 翻页按钮的颜色
            pageIconSize:[8,8],
          pageIconInactiveColor: '#aaa', // 翻页按钮不激活时（即翻页到头时）的颜色
          pageTextStyle: { // 图例页信息的文字样式
              color: '#cbcbcb',
            fontSize:12
          },
          layout:'vertical',
        //   x: "right", // 图例水平居中
            y: "bottom", // 图例垂直居上
            itemHeight: 7,                      //修改icon图形大小
            itemWidth: 7,                      //修改icon图形大小
            icon: 'circle',                         //图例前面的图标形状
            textStyle: {                            //图例文字的样式
                color: '#000',               //图例文字颜色
                fontSize:12                    //图例文字大小
            }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}'
        },
        series: [{
          name: '基础饼图',
          roseType : 'radius',
          type:'pie',
            radius : [60, 80],
            center : ['50%', '50%'],
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          labelLine: {
            normal: {
              show: false
            }
          },
          data: this.pieData
        }]
      }
              pieChart.setOption(option);
              window.addEventListener("resize", () => {
                  pieChart.resize();
              });
          },
      },
      mounted() {
    //   this.drawSpaceResources()
    this.getOverviewLeft()
  }
  }
  </script>
  
  <style scoped>
  .all-wrapper{
    width:100%;
      height: 256px;
      text-align: center;
  }
  #economicDetailsThirdTwo{
      width:40vw;
      height: 256px;
  }
  </style>