<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>概览</span>
      </div>
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="grid-content" style="cursor: pointer">
            <div class="grid-wrapper">
              <img
                class="grid-img"
                src="../../../../assets/images/building.svg"
              />
              <div class="grid-text">
                <div class="grid-text-name">预案总数</div>
                <div class="grid-text-num">
                  {{ overviewData.planNumSum }}<span class="unit">个</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6"
          ><div class="grid-content">
            <div class="grid-wrapper">
              <img class="grid-img" src="../../../../assets/images/area.svg" />
              <div class="grid-text">
                <div class="grid-text-name">综合预案</div>
                <div class="grid-text-num">
                  {{ overviewData.synthesisPlanNum
                  }}<span class="unit">个</span>
                </div>
              </div>
            </div>
          </div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content">
            <div class="grid-wrapper">
              <img
                class="grid-img"
                src="../../../../assets/images/investment.svg"
              />
              <div class="grid-text">
                <div class="grid-text-name">专项预案</div>
                <div class="grid-text-num">
                  {{ overviewData.specialPlanNum
                  }}<span class="unit">个</span>
                </div>
              </div>
            </div>
          </div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content">
            <div class="grid-wrapper">
              <img class="grid-img" src="../../../../assets/images/space.svg" />
              <div class="grid-text">
                <div class="grid-text-name">现场处置预案</div>
                <div class="grid-text-num">
                  {{ overviewData.scenePlanNum }}<span class="unit">个</span>
                </div>
              </div>
            </div>
          </div></el-col
        >
      </el-row>
      <el-row :gutter="16"  style="margin-top: 20px">
        <el-col :span="12">
          <div class="two-inner">
            <div class="two-inner-title">预案类型占比</div>
            <economic-details-third-two ref="detailsTwo"></economic-details-third-two>

          </div>
        </el-col>
        <el-col :span="12">
          <div class="two-inner">
            <div class="two-inner-title">灾害类型占比</div>
            <economic-details-third-three  ref="detailsThree"></economic-details-third-three>

          </div>
        </el-col>
       
      </el-row>
    </el-card>
    <el-card class="box-card" style="margin-top: 20px">
      <div slot="header" class="clearfix">
        <span>筛选条件</span>
      </div>
      <el-row>
        <el-col :span="18">
          <el-form label-width="80px">
            <el-row>
              <!-- <el-col :span="6">
                <el-form-item label="企业名称">
                  <el-input
                    v-model="queryParams.firmName"
                    placeholder="请输入企业名称"
                    clearable
                    maxlength="20"
                    style="width: 10vw"
                  />
                </el-form-item>
              </el-col> -->
              <el-col :span="6">
                <el-form-item label="预案名称">
                  <el-input
                    v-model="queryParams.planName"
                    placeholder="请输入预案名称"
                    maxlength="20"
                    clearable
                    style="width: 10vw"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="预案类型">
                  <el-select
                    v-model="queryParams.planType"
                    placeholder="请选择预案类型"
                    style="width: 10vw"
                    clearable
                  >
                  <el-option
                  v-for="dict in dict.type.plan_deduction"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
        <el-col :span="6">
          <el-button
            icon="el-icon-refresh"
            size="mini"
            style="float: right; margin-left: 20px;font-size:13px"
            @click="resetQuery"
            >重置</el-button
          >
          <el-button
          size="mini"
            type="primary"
            icon="el-icon-search"
            style="float: right;font-size:13px"
            @click="handleQuery"
            >搜索</el-button
          >
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" style="margin-top: 20px">
      <div slot="header" class="clearfix">
        <span>展示列表</span>
        <div class="header-btns">
            <el-button
            type="primary"
            icon="el-icon-plus"
            class="queryBtn"
            size="mini"
            @click="handleOperation('add')"
            >新增应急预案</el-button
            >
            <el-button
              type="primary"
              class="queryBtn"
              size="mini"
              ><template><el-upload
                      class="upload-demo"  
                      :on-error="onError"
                      :on-success="handleAvatarSuccess"
                      action="/emergency-v2/emergency-plan-superior-dept/import"
                      :headers="headers"
                      :file-list="fileList">
                          <div class="select" style="cursor: pointer">导入信息</div>
  
                      </el-upload></template></el-button>
              
            <el-button
              type="primary"
              class="queryBtn"
              size="mini"
              @click="exportReport()"
              >导出</el-button
            >
            <el-button
              type="primary"
              class="queryBtn"
              size="mini"
              @click="stationaryPlaten()"
              >固定模板</el-button
            >
        </div>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        :cell-style="{ padding: '0px' }"
        :row-style="{ height: '48px' }"
        @selection-change="handleSelectionChange"

      >
      <el-table-column type="selection" width="55"> </el-table-column>

      <el-table-column label="序号" align="center">
          <template slot-scope="scope">
            <span>{{ (pageInfo.current - 1) * pageInfo.size + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="企业名称" align="center" prop="firmName" /> -->
        <el-table-column label="预案名称" align="center" prop="planName" />
        <el-table-column label="所属区域" align="center" prop="name">
        </el-table-column>
        <el-table-column label="预案编号" align="center" prop="planNum" />
        <el-table-column
          label="预案有效期"
          align="center"
          prop="publishingUnitName"
        >
        <template slot-scope="scope">
            {{
              (scope.row.dateStart==null?'':scope.row.dateStart) +'-'+ (scope.row.dateEnd==null?'':scope.row.dateEnd)
                
            }}
          </template></el-table-column>
        <el-table-column
          label="预案类型"
          align="center"
          prop="planType"
        >
        <template slot-scope="scope">
            {{dict.type.plan_deduction.find(i=>i.value==scope.row.planType)==undefined?'':dict.type.plan_deduction.find(i=>i.value==scope.row.planType).label
            }}
          </template>
        </el-table-column>
        <el-table-column label="灾害类型" align="center" prop="disasterType">
          <template slot-scope="scope">
            {{dict.type.disaster_type.find(i=>i.value==scope.row.disasterType)==undefined?'':dict.type.disaster_type.find(i=>i.value==scope.row.disasterType).label
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="160"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleOperation('look', scope.row)"
              >查看</el-button
            >
            <el-button
              type="text"
              @click="handleOperation('edit', scope.row)"
              >编辑</el-button
            >
            <el-button
              type="text"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="pageInfo.total"
        :page.sync="pageInfo.current"
        :limit.sync="pageInfo.size"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改应急预案对话框 -->
    <el-dialog
      :title="dialogInfo.title"
      :visible.sync="dialogInfo.show"
      v-loading="dialogInfo.loading"
      width="960px"
      append-to-body
    >
      <el-form
        ref="ruleForm"
        :model="formData"
        :rules="formRules"
        :disabled="dialogInfo.disabled"
        label-width="110px"
      >
        <!-- 新增展示、查看展示 -->
        <el-row :gutter="20">
          <!-- <el-col :span="12">
            <el-form-item label="企业名称" prop="firmName">
              <el-input
                v-model="formData.firmName"
                placeholder="请输入企业名称"
                maxlength="20"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="预案名称" prop="planName">
              <el-input
                v-model="formData.planName"
                placeholder="请输入预案名称"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属区域" prop="regionString">
                <el-cascader
                style="width: 100%"
                    v-model="formData.regionString"
                    :props="{ checkStrictly: true,label: 'name', value: 'id'  }"
                    :options="areaOptions"
                    @change="handleChange"></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预案编号" prop="planNum">
              <el-input
                v-model="formData.planNum"
                placeholder="请输入预案编号"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预案类型" prop="planType">
              <el-select
                v-model="formData.planType"
                placeholder="请选择预案类型"
                style="width: 100%"
              >
              <el-option
                  v-for="dict in dict.type.plan_deduction"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="灾害类型" prop="disasterType">
                <el-select
                v-model="formData.disasterType"
                placeholder="请选择灾害类型"
                style="width: 100%"
              >
              <el-option
                  v-for="dict in dict.type.disaster_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预案有效期" prop="dateRange">
                <el-date-picker
                    v-model="formData.dateRange"
                    style="width: 100%"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    type="datetimerange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="预案内容" prop="planContent">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入预案内容"
                v-model="formData.planContent"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="备注" prop="note">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入备注"
                v-model="formData.note"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件 :">
              <el-upload
                class="upload-demo"
                :action="uploadImgUrl"
                :headers="headers"
                :on-success="handleUploadSuccess"
                :on-preview="handledownload"
                :file-list="dialogInfo.uploadList"
                :on-remove="handleUploadRemove"
                :before-upload="beforeAvatarUpload"
                v-model="formData.attachment"
                :limit="1"
              >
                <el-button size="small" type="primary" icon="el-icon-plus"
                  >添加附件</el-button
                >
                <div slot="tip" class="el-upload__tip"><div slot="tip" class="el-upload__tip">支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB</div></div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div v-if="!dialogInfo.disabled" slot="footer" class="dialog-footer">
        <el-button @click="dialogInfo.show = false">取消</el-button>
        <el-button type="primary" @click="saveAdd('ruleForm')">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import { deepClone } from "@/utils/index.js";
import { quillEditor } from "vue-quill-editor";

import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";

import {
  getPlanList,
  planAdd,
  planUpdate,
  planDelete,
  planDetail,
  getStaffList,
  handledownload,
} from "@/api/emergencyPlan/structuredPlan/planManagement/index.js";
import {
  getAllTemplateList,
  templateDetail,
} from "@/api/emergencyPlan/structuredPlan/structuredTemplate/index.js";
import {
    overview,
    planManageFirmList,
    emergencyAreaTree,
    savePlanManageFirm,
    getDict,
    view,
    del,
    editPlanManageFirm,
    exportTemplatePlan,
    exportPlan,
overviewRight
    
} from "@/api/emergency/new/superiorDepartment.js";
import { searchTreeData } from "@/api/emergencyPlan/planConfiguration/planLabel/index.js";
import economicDetailsThirdTwo from './subgroup/economicDetailsThirdTwo.vue'
import economicDetailsThirdThree from './subgroup/economicDetailsThirdThree.vue'
export default {
  components: { quillEditor },
  name: "EmergencySupplies",
  dicts: ["plan_deduction","disaster_type"],
  data() {
        return {
            //预案有效期
            //   dateRange: null,
              overviewData: {
                  planNumSum: 0,
                  synthesisPlanNum: 0,
                  specialPlanNum: 0,
                  scenePlanNum:0
            },
      queryParams: {
      },
      pageInfo: {
        current: 1,
        size: 10,
        total: 0,
      },
      tableLoading: false,
      tableData: [],
      headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadImgUrl: "/emergency-v2/file/uploadFile",
      dialogInfo: {
        // uploadUrl: process.env.VUE_APP_BASE_API + "emergency/file/uploadFile",
        uploadList: [], // 已上传的附件
        eventTree: [], // 事件类型树
        staffOptions: [],
        templateOptions: [], // 预案模板下拉
        templateTree: [], // 左侧预案模板树
        templateTitle: "", //
        title: "",
        show: false,
        loading: false,
        disabled: false,
        steps: [], // 1 2 3
      },
              formData: {
                dateRange:[]
      },
      // 表单校验
      formRules: {
        // firmName: [
        //         { required: true, message: "请输入企业名称", trigger: "blur" },
        //         ],
                planName: [
                { required: true, message: "请输入预案名称", trigger: "blur" },
            ],
            planNum: [
                { required: true, message: "请输入预案编号", trigger: "blur" },
            ],
            regionString: [
                { required: true, message: "请选择所属区域", trigger: "blur" },
                ],
                planType: [
                { required: true, message: "请选择预案类型", trigger: "blur" },
                ],
                dateRange: [
                { required: true, message: "请选择预案有效期", trigger: "blur" },
                ],
                disasterType: [
                { required: true, message: "请选择灾害类型", trigger: "blur" },
            ],
            planContent: [
                {
                    min: 1,
                    max: 255,
                    message: "长度限制在1到255个字符",
                    trigger: "blur",
                },
                ],
                note: [
                {
                    min: 1,
                    max: 255,
                    message: "长度限制在1到255个字符",
                    trigger: "blur",
                },
                ],
              },
              areaData:[],
              areaOptions: [],
              //预案类型
              planTypeData: [],
              //灾害类型
              disasterTypeData: [],
              editId: null,
              fileList: [],
              multipleSelection: [],
        //批量操作id数组
        batchData: [],
    };
      },
      components: {
    economicDetailsThirdTwo,
    economicDetailsThirdThree
  },
  created() {
    // 事件类型树下拉
    searchTreeData().then((res) => {
      const resetData = (data) => {
        data.forEach((item, index) => {
          if (item.children) {
            item.disabled = true;
            return resetData(item.children);
          }
        });
      };
      resetData(res.data || []);
      this.dialogInfo.eventTree = res.data || [];
    });
    // 预案负责人下拉
    getStaffList().then((res) => {
      this.dialogInfo.staffOptions = res.data || [];
    });
    // 预案模板下拉
    getAllTemplateList().then((res) => {
      this.dialogInfo.templateOptions = res.data || [];
    });
    this.getList();
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.pageInfo.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
      };
      this.handleQuery();
    },
    beforeAvatarUpload(file) {
      console.log(file);
      let array = [
        "jpeg",
        "jpg",
        "png",
        "gif",
        "bmp",
        "tiff",
        "webp",
        "svg",
        "mp4",
        "avi",
        "mkv",
        "mov",
        "wmv",
        "flv",
        "webm",
        "mpeg",
        "mp3",
        "wav",
        "aac",
        "flac",
        "ogg",
        "wma",
        "pdf",
        "word",
        "excel",
        "txt",
        "doc",
        "docx",
        "xlsx",
        "xls",
        "pptx",
        "ppt",
      ];
      let type = file.name.split(".");
      const isLt2M = file.size / 1024 / 1024 < 100;
      const isType = array.indexOf(type[1]) == -1;
      console.log(isType);
      if (isType) {
        this.$message.error(
          "仅支持 jpeg|jpg|png|gif|bmp|tiff|webp|svg|mp4|avi|mkv|mov|wmv|flv|webm|mpeg|mp3|wav|aac|flac|ogg|wma|pdf|word|excel|txt|doc|docx|xlsx|xls|pptx|ppt| 格式!"
        );
      }
      if (!isLt2M) {
        this.$message.error("上传附件大小不能超过 100MB!");
      }
      return !isType && isLt2M;
    },
    handledownload(row) {
      console.log(row);
      const _this = this;
      let arr1 = [];
      if (this.dialogInfo.title == "查看企业预案") {
        arr1 = row.name.split(",");
      } else {
        arr1 = row.response.split(",");
      }
      arr1.map((res) => {
        let arr = res.split("/");
        handledownload(arr).then(async (res) => {
          _this.handledownloadGet(arr, res);
        });
      });
    },
    /** 查询列表 */
    getList() {
    
        planManageFirmList({
        ...this.queryParams,
        pageNum: this.pageInfo.current,
        pageSize: this.pageInfo.size,
          }).then((res) => {
        console.log(res.data.records);
            this.tableData = res.data.records
            console.log(this.tableData);
        this.pageInfo.total = res.data.total
            });
          
      
    },
    handleOperation(type, data = {}) {
      this.resetData();
      this.dialogInfo.title =
        { add: "新增", edit: "编辑", look: "查看" }[type] + "企业预案";
      switch (type) {
        case "add":
          this.dialogInfo.show = true;
          this.dialogInfo.steps = [1];
          searchTreeData().then((res) => {
            const resetData = (data) => {
              data.forEach((item, index) => {
                if (item.children) {
                  item.disabled = true;
                  return resetData(item.children);
                }
              });
            };
            resetData(res.data || []);
            this.dialogInfo.eventTree = res.data || [];
            this.$nextTick(() => {
              this.$refs.ruleForm.clearValidate();
            });
          });
          break;
            case "edit":
            this.dialogInfo.show = true;
          this.dialogInfo.disabled = type === "look";
                view(data.id).then((res) => {
            this.editId = data.id
            console.log(res.data);
                      this.formData = res.data
                      if (this.formData.regionString != null) {
                      this.formData.regionString = this.formData.regionString.split(',')
                        
                      }
                      let middle = []
                      if (res.data.dateStart != null && res.data.dateStart != undefined&&res.data.dateEnd != null && res.data.dateEnd != undefined) {
                        middle.push(res.data.dateStart)
                            middle.push(res.data.dateEnd)
                      }
                      this.$set(this.formData,'dateRange',middle)
                
                console.log(this.formData);
            if (res.data.attachment) {
              this.dialogInfo.uploadList = [{ name: res.data.attachment, url: res.data.attachment }];
            }

            // templateType && this.handleChangeTemplate(templateType, false);
            console.log(this.formData);
          });
          break
        case "look":
          this.dialogInfo.show = true;
          this.dialogInfo.disabled = type === "look";
          view(data.id).then((res) => {
            console.log(res.data);
                this.formData = res.data
                if (this.formData.regionString != null) {
                      this.formData.regionString = this.formData.regionString.split(',')
                        
                      }
                let middle = []
                      if (res.data.dateStart != null && res.data.dateStart != undefined&&res.data.dateEnd != null && res.data.dateEnd != undefined) {
                        middle.push(res.data.dateStart)
                            middle.push(res.data.dateEnd)
                      }
                      this.$set(this.formData,'dateRange',middle)
                console.log(this.formData);
                if (res.data.attachment) {
              this.dialogInfo.uploadList = [{ name: res.data.attachment, url: res.data.attachment }];
            }

            // templateType && this.handleChangeTemplate(templateType, false);
            console.log(this.formData);
          });
          break;
        case "abandon":
        case "restart":
          planUpdate({
            id: data.id,
            status: { abandon: "5010703", restart: "5010701" }[type],
          }).then((res) => {
            if (res.code === 200) {
              this.$modal.msgSuccess("操作成功");
              this.getList();
            }
          });
          break;
        // case 'delete':
        // this.$modal.confirm("是否确认删除当前数据").then(() => {
        //   return planDelete({ id: data.id });
        // }).then((res) => {
        //   if (res.code === 200) {
        //     this.getList();
        //     this.$modal.msgSuccess("删除成功");
        //   }
        // }).catch((error) => {})
        // break;
      }
        },
        handleDelete(val) {
            this.$confirm("确认删除?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(() => {
                del(val.id).then((res) => {
                  if (res.code == 200) {
                    this.$message.success("删除成功");
                        this.getList();
                        this.getOverview()
            this.$refs.detailsTwo.getOverviewLeft()
            this.$refs.detailsThree.getOverviewRight()
                  }
                });
              })
              .catch(() => {
                this.$message({
                  type: "info",
                  message: "已取消删除",
                });
              });
        },
    handleNodeClick(data, res) {
      if (!res) {
        this.formData.eventTypeId = undefined;
        this.formData.eventTypeName = undefined;
      } else {
        this.$refs.eventTree.setCheckedNodes([data]);
        this.$set(this.formData, "eventType", data.id);
        this.$set(this.formData, "eventTypeName", data.nodeName);
      }
    },
    handleUploadSuccess(response, res, file) {
      console.log(response, res, file, "response, res, file");
      this.formData.attachment = response;
    },
    handleUploadRemove(file, fileList) {
      this.formData.attachment = "";
    },
    // 选择模板
    handleChangeTemplate(id, reset = true) {
      templateDetail({ structuredTemplateId: id }).then((res) => {
        const { code, data } = res;
        if (code === 200) {
          delete data.emergencyStructuredTemplateDetailVO.id;
          const { children = [], title } =
            data.emergencyStructuredTemplateDetailVO || {};
          this.dialogInfo.templateTree = children || []; // 左侧树
          this.dialogInfo.templateTitle = title;
          const formatterData = (data) => {
            (data || []).map((item) => {
              delete item.id;
              delete item.parentId;
              if (item.children && item.children.length) {
                formatterData(item.children);
              }
              return item;
            });
          };
          if (!reset) {
            const { emergencyPlanTemplateDetails } = this.formData;
            const title = emergencyPlanTemplateDetails[0].title;
            const content = emergencyPlanTemplateDetails[0].content;
            emergencyPlanTemplateDetails.splice(0, 1);
            formatterData(emergencyPlanTemplateDetails);
            this.$set(
              this.formData,
              "emergencyPlanTemplateDetails",
              deepClone({
                parentId: 0,
                title,
                content,
                children: emergencyPlanTemplateDetails,
              })
            );
          } else {
            formatterData(this.dialogInfo.templateTree);
            this.$set(
              this.formData,
              "emergencyPlanTemplateDetails",
              deepClone(data.emergencyStructuredTemplateDetailVO)
            );
          }
        }
      });
    },
    /*  确认保存新增*/
        saveAdd(formName) {
              console.log(this.dateRange);
            
              console.log(this.formData, "(this.formData");
              if (this.formData.regionString&&this.formData.regionString.length>0) {
                    this.$set(this.formData, 'region', this.formData.regionString[this.formData.regionString.length - 1])
                this.formData.regionString=this.formData.regionString.toString()
                    
              }
      this.$refs[formName].validate((valid) => {
            if (valid) {
                if (this.formData.dateRange != null && this.formData.dateRange != []) {
            this.$set(this.formData,'dateStart',this.formData.dateRange[0])
            this.$set(this.formData,'dateEnd',this.formData.dateRange[1])
        }
                  
                        if (this.editId) {
                              editPlanManageFirm({ id: this.editId, ...this.formData }).then((res) => {
                                this.$message.success("保存成功");
                                this.dialogInfo.show = false;
                                            this.getList();
                                    this.dialogInfo.show = false
                                this.editId = null
                              })
                          } else {
                            savePlanManageFirm(this.formData).then((res) => {
                                this.$message.success("保存成功");
                                this.dialogInfo.show = false;
                                  this.getList();
                                  this.getOverview()
            this.$refs.detailsTwo.getOverviewLeft()
            this.$refs.detailsThree.getOverviewRight()
                                this.dialogInfo.show = false
                                });
                            }
  
                    
          } else {
            return false;
          }
        });

        },
   
    // 重置
    resetData() {
      this.formData = {};
      this.dialogInfo.show = false;
      this.dialogInfo.loading = false;
      this.dialogInfo.disabled = false;
      this.dialogInfo.templateTree = [];
      this.dialogInfo.templateTitle = "";
      this.dialogInfo.uploadList = [];
      this.dialogInfo.steps = [];
        },
        //获取概览
        getOverview() {
              overview().then((res) => {
                this.overviewData = res.data
            })
        },
        
       
       
        //区域树
        getArea() {
                        emergencyAreaTree().then((res) => {
                    this.areaOptions = res.data
                })
        },
        handleChange(value) {
        console.log(value);
        },
        getPlanTypeDict() {
              getDict({ type: 'planType' }).then((res) => {
                this.planTypeData = res.data
            })
        },
        getDisasterTypeDict() {
              getDict({ type: 'disasterType' }).then((res) => {
                this.disasterTypeData = res.data
            })
        },
         //导出
      download(blob, name) {
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.href = url;
        link.download = name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      },
         //固定模板
      stationaryPlaten() {
        exportTemplatePlan().then((res) => {
          this.download(res, "导入模版.xlsx");
        });
            },
      //导出
            exportReport() {
                    if (this.batchData.length <= 0) {
              this.$message.error("至少选择一条数据")
              
                        } else {
                            exportPlan({ids:this.batchData }).then((res) => {
                 this.download(res,'导出预案报表.xlsx')
              })
          }
        },
        onError(){
          this.$message.error('无法导入！请检查导入数据')
            },
        handleAvatarSuccess(res) {
            if (res.code != 200) {
        this.$modal.msgError(res.msg);
      } else {
        this.$modal.msgSuccess("导入成功");
      }

              this.getList()
              this.getOverview()
            this.$refs.detailsTwo.getOverviewLeft()
            this.$refs.detailsThree.getOverviewRight()
        },
        handleSelectionChange(val) {
        console.log(val);
        //可以获得具体行数
        this.multipleSelection = val;
        console.log(this.multipleSelection);
        if (this.multipleSelection.length > 0) {
          this.batchData = [];
          this.multipleSelection.forEach((item) => {
            this.batchData.push(item.id);
          });
          console.log(this.batchData);
        }
      },
      },
      mounted() {
        //     this.getPlanTypeDict()
        // this.getDisasterTypeDict()
            this.getOverview()
    this.getArea()
  }
};
</script>
  <style lang="scss" scoped>
  .header-btns{
    float: right;

  }
  .queryBtn ::v-deep .el-upload-list--text{
    display: none !important;
  }
  .queryBtn ::v-deep .el-upload-list{
    display: none !important;
  }
  .queryBtn ::v-deep .el-upload-list__item .is-uploading{
    display: none !important;
  }
  .uploadList ::v-deep .el-upload-list .el-progress{
display: none !important;
}
// .queryBtn {
//   float: right;
// }

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.treeOption {
  height: auto;
  margin: 0;
  background-color: #fff;
}

::v-deep .el-collapse {
  border: none;
  .el-collapse-item__header {
    font-size: 18px;
  }
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}

::v-deep .right-tree .el-tree-node__content {
  height: auto;
  margin: 2px 0 30px;
  background-color: #fff;
}

.custom-tree-node {
  width: 100%;
}

.ql-editor {
  padding: 0;
}

::v-deep .el-dialog__body {
  max-height: 80vh;
  overflow-y: auto;
}

// 给文本内容加高度，滚动条
::v-deep.quill-editor .ql-container {
  min-height: 220px;
}

.ql-container {
  min-height: 230px;
}

::v-deep {
  .ql-snow .ql-tooltip [data-mode="link"]::before {
    content: "请输入链接地址:";
    left: 0;
  }

  .ql-snow .ql-tooltip.ql-editing {
    left: 0 !important;
  }

  .ql-snow .ql-tooltip {
    left: 0 !important;
  }

  .ql-snow .ql-editor {
    max-height: 200px;
  }

  .ql-snow .ql-tooltip.ql-editing a.ql-action::after {
    border-right: 0px;
    content: "保存";
    padding-right: 0px;
  }

  .ql-snow .ql-tooltip[data-mode="video"]::before {
    content: "请输入视频地址:";
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item::before {
    content: "14px" !important;
    font-size: 14px;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before {
    content: "10px" !important;
    font-size: 10px;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
    content: "12px" !important;
    font-size: 12px;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
    content: "16px" !important;
    font-size: 16px;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
    content: "18px" !important;
    font-size: 18px;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
    content: "20px" !important;
    font-size: 20px;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="30px"]::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="30px"]::before {
    content: "30px" !important;
    font-size: 30px;
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
    content: "32px" !important;
    font-size: 32px;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item::before {
    content: "文本" !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
    content: "标题1" !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
    content: "标题2" !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
    content: "标题3" !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
    content: "标题4" !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
    content: "标题5" !important;
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
    content: "标题6" !important;
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item::before {
    content: "标准字体" !important;
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
    content: "衬线字体" !important;
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
    content: "等宽字体" !important;
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimSun"]::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimSun"]::before {
    content: "宋体" !important;
    font-family: "SimSun";
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="SimHei"]::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="SimHei"]::before {
    content: "黑体" !important;
    font-family: "SimHei";
  }

  .ql-snow
    .ql-picker.ql-font
    .ql-picker-label[data-value="Microsoft-YaHei"]::before,
  .ql-snow
    .ql-picker.ql-font
    .ql-picker-item[data-value="Microsoft-YaHei"]::before {
    content: "微软雅黑" !important;
    font-family: "Microsoft YaHei";
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="KaiTi"]::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="KaiTi"]::before {
    content: "楷体" !important;
    font-family: "KaiTi";
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="FangSong"]::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="FangSong"]::before {
    content: "仿宋" !important;
    font-family: "FangSong";
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Arial"]::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Arial"]::before {
    content: "Arial" !important;
    font-family: "Arial";
  }

  .ql-snow
    .ql-picker.ql-font
    .ql-picker-label[data-value="Times-New-Roman"]::before,
  .ql-snow
    .ql-picker.ql-font
    .ql-picker-item[data-value="Times-New-Roman"]::before {
    content: "Times New Roman" !important;
    font-family: "Times New Roman";
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="sans-serif"]::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="sans-serif"]::before {
    content: "sans-serif" !important;
    font-family: "sans-serif";
  }

  .ql-font-SimSun {
    font-family: "SimSun";
  }

  .ql-font-SimHei {
    font-family: "SimHei";
  }

  .ql-font-Microsoft-YaHei {
    font-family: "Microsoft YaHei";
  }

  .ql-font-KaiTi {
    font-family: "KaiTi";
  }

  .ql-font-FangSong {
    font-family: "FangSong";
  }

  .ql-font-Arial {
    font-family: "Arial";
  }

  .ql-font-Times-New-Roman {
    font-family: "Times New Roman";
  }

  .ql-font-sans-serif {
    font-family: "sans-serif";
  }

  .ql-snow.ql-toolbar .ql-formats .ql-revoke {
    width: 20px;
    height: 20px;
    filter: grayscale(100%);
    opacity: 1;
  }

  .ql-snow.ql-toolbar .ql-formats .ql-revoke:hover {
    width: 20px;
    height: 20px;
    filter: none;
    opacity: 0.9;
  }

  /*加上height和滚动属性就可以，滚动条样式是系统默认样式，可能不同*/
  .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
    border-color: #ccc;
    height: 125px;
    overflow: auto;
  }
}
::v-deep .el-form-item__label {
  width: 100px;
  height: 32px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 32px;
  text-align: right;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: auto;
}
.grid-content {
  height: 104px;
  border: 1px solid #ebebeb;
  padding: 23px, 24px, 23px, 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.grid-wrapper {
  height: 58px;
  margin: auto 0;
  margin-left: 24px;
  display: flex;
  gap: 8px;
}
.grid-img {
  width: 56px;
  height: 56px;
  border-radius: 8px;
}
.grid-text {
  font-family: Noto Sans SC;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0em;
  text-align: left;
  color: #999999;
}
.grid-text-num {
  font-family: DINPro;
  font-size: 24px;
  font-weight: 700;
  line-height: 40px;
  letter-spacing: 0em;
  text-align: left;
  color: #333333;
}
.grid-text-unit {
  font-family: Noto Sans SC;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0em;
  text-align: left;
  color: #333333;
}
.two-inner {
    border-radius: 4px;
    border: 1px solid #EBEBEB;
    background: #FFF;
    height: 320px;

    
  }
  .two-inner-title {
      display: flex;
      height: 56px;
      padding: 0px 16px;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      gap: 10px;
      align-self: stretch;
      border-bottom: 1px solid #EBEBEB;
      background: #FFF;
      color: #333;
      font-family: Noto Sans SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 26px; /* 144.444% */
    }
</style>
  