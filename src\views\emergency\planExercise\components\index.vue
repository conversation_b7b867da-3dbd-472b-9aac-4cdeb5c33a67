<template>
  <div class="app-container" ref="container1">
    <div class="inputDeep" style="height: 60px">
      <el-input
        v-model="topTitle"
        maxlength="20"
        style="
          font-weight: bold;
          color: rgba(128, 128, 128, 1);
          font-size: 20px;
          width: auto;
          min-width: 200px;
        "
      ></el-input
      ><i class="el-icon-edit" style="color: #2a82e4"></i>
    </div>
    <div v-if="type != 'view'" style="height: 50px">
      <el-button type="primary" size="mini" @click="handleAdd(true)"
        >增加节点</el-button
      >
      <!-- <el-button type="primary"
                 size="mini">保存该预案推演</el-button> -->
    </div>
    <!--  -->
    <div style="height: 680px; overflow: hidden">
      <div
        class="map_canvas"
        :style="{ height: height + 'px' }"
        id="divId"
      ></div>
    </div>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="abilityOpen"
      width="680px"
      append-to-body
    >
      <div class="inputDeep">
        <el-input
        maxlength="20"
          v-model="abilityForm.nodeName"
          style="
            font-weight: bold;
            color: rgba(128, 128, 128, 1);
            font-size: 16px;
            width: auto;
            min-width: 200px;
          "
          placeholder="请输入节点名称"
        ></el-input>
        <el-button type="primary" size="mini" v-if="sumbitBtn" @click="sumbit"
          >确 定</el-button
        >
        <el-button type="warning" size="mini" v-if="sumbitBtn" @click="reset"
          >清 空</el-button
        >
        <!-- <el-button type="danger"
                   size="mini">删 除</el-button> -->
      </div>
      <!--  -->
      <p>事件类型</p>
      <el-select
        v-model="abilityForm.eventType"
        placeholder="请选择"
        style="width: 100%"
      >
        <el-option
          v-for="item in dict.type.plan_type"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <!--  -->
      <p>应急专家</p>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-input
            v-model="deptName"
            placeholder="请输入专家名称"
            clearable
            size="small"
            maxlength="20"
            prefix-icon="el-icon-search"
            style="margin-bottom: 10px" />
          <el-tree
            ref="tree"
            :data="deptOptions"
            :props="defaultProps"
            node-key="name"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            @check="currentChecked"
            show-checkbox
        /></el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple"></div>
        </el-col>
      </el-row>
      <!--  -->
      <p>应急行动</p>
      <el-input
        v-model="abilityForm.action"
        placeholder="请输入应急行动"
        type="textarea"
        maxlength="200"
        :rows="3"
      />
      <!--  -->
      <p>避难场所</p>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-input
            v-model="shelter"
            placeholder="请输入避难场所"
            clearable
            size="small"
            maxlength="20"
            prefix-icon="el-icon-search"
            style="margin-bottom: 10px" />
          <el-tree
            ref="shelterTree"
            :data="refugeOptions"
            :props="defaultProps"
            node-key="name"
            :expand-on-click-node="false"
            :filter-node-method="filterShelter"
            highlight-current
            @check="shelterChecked"
            show-checkbox
        /></el-col>
        <el-col :span="12">
          <div class="grid-content bg-purple"></div>
        </el-col>
      </el-row>
      <p>
        应急资源
        <el-button
          type="primary"
          @click="tableDataAdd"
          v-if="sumbitBtn"
          size="mini"
          >新 增</el-button
        >
        <!-- <el-button type="primary"
                   @click="tableDataRemove"
                   size="mini">删 除</el-button> -->
      </p>
      <el-table
        :data="tableDataList"
        @select="tableDataSelect"
        height="250"
        border
        style="width: 100%"
      >
        <!-- <el-table-column type="selection"
                         width="55">
        </el-table-column> -->
        <el-table-column prop="materialType" align="center" label="资源类型">
        </el-table-column>
        <el-table-column prop="materialName" align="center" label="资源名称">
        </el-table-column>
        <el-table-column prop="inventory" align="center" label="储存量">
        </el-table-column>
        <el-table-column prop="demandNumber" align="center" label="需求量">
        </el-table-column>
      </el-table>
      <!-- 应急资源新增 -->
      <el-dialog
        width="30%"
        title="应急资源新增"
        :visible.sync="innerShow"
        append-to-body
      >
        <el-form
          ref="innerForm"
          :model="innerForm"
          :rules="innerRules"
          label-width="110px"
        >
          <el-form-item label="资源名称" prop="materialId">
            <el-select
              v-model="innerForm.materialId"
              placeholder="请选择资源名称"
              @change="eventTypeChange"
              style="width: 100%"
            >
              <el-option
                v-for="item in this.tableData"
                :key="item.materialId"
                :label="item.materialName"
                :value="item.materialId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资源类型" prop="materialType">
            <!-- <el-input v-model="innerForm.materialType" :disabled="true" /> -->
            <el-select
              v-model="innerForm.materialType"
              placeholder="请选择资源类型"
              style="width: 100%"
              :disabled="true"
            >
              <el-option
                v-for="item in dict.type.material_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="储存量" prop="inventory">
            <el-input v-model="innerForm.inventory" :disabled="true"  maxlength="20"/>
          </el-form-item>
          <el-form-item label="需求量" prop="demandNumber">
            <el-input-number
              v-model="innerForm.demandNumber"
              placeholder="请填写需求量"
              maxlength="20"
              :disabled="disabled"
              :max="innerForm.inventory"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="confirm('innerForm')"
            :disabled="disabled"
            >确 定</el-button
          >
          <el-button @click="innerFormcancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import {
  Stage,
  Layer,
  Node,
  Editor,
  Link,
  ArrowNode,
  IconsPanel,
} from "../jtopo/jtopo-1.4.5_trial-esm-min.js";
import { getToken } from "@/utils/auth";
import {
  getTree,
  getshelter,
  selectEmergencyMaterialByPage,
  selectById,
  nodeSave,
} from "@/api/emergency/planExercise/index";
var stage;
var layer;
var editor;
var toNode = undefined;
export default {
  name: "PlanExerciseEdit",
  dicts: ["plan_type", "material_type"],
  data() {
    return {
      sumbitBtn: true,
      type: "",
      planId: "", //预案id
      planDetails: {}, //预案详情
      height: null,
      width: null,
      // 遮罩层d
      loading: false,
      // 是否显示弹出层
      abilityOpen: false,
      innerShow: false,
      title: "新增节点",
      abilityForm: {
        eventType: "", // 事件类型
        deptOptions: [], //专家
        refugeOptions: [], //场所
        action: "",
        nodeName: "",
      },
      innerForm: {
        id: null,
        materialName: "",
        materialType: "",
        inventory: "",
        demandNumber: "",
      },
      nodeForm: {}, //节点数据
      disabled: false,
      flag: false,
      topTitle: "",
      options: [],
      deptOptions: [],
      refugeOptions: [],
      deptName: "", //专家搜索
      shelter: "", //避难场所
      // 查询参数
      queryParams: {
        current: 1,
        size: 1000,
      },
      //应急资源列表展示数据
      tableDataList: [],
      //
      defaultProps: {
        children: "children",
        label: "name",
      },
      tableData: [],
      imgUrl:
        `${require("@/assets/images/map.png")}`,
      imgcs: require("@/assets/images/profile.jpg"),
      // 表单校验
      innerRules: {
        materialId: [
          {
            required: true,
            message: "请选择资源名称",
            type: "string",
            trigger: "change",
          },
        ],
        demandNumber: [
          { required: true, message: "请输入需求量", trigger: "blur" },
        ],
      },
      //
      container: null,
      nodeObj: undefined,
      // jtopo图例
      leftPanelConfig: {
        items: [
          {
            name: "文字",
            className: "Node",
            iconHtml: `
              <img width="100%" style="padding:2px;" src='./img/point.png'/>
          `,
            properties: {
              text: "文字",
              image: "./img/point.png",
              sizeToImage: true,
              css: {
                textPosition: "cb",
                borderWidth: 0,
              },
            },
          },
        ],
      },
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
    shelter(val) {
      this.$refs.shelterTree.filter(val);
    },
  },
  created() {
    this.topTitle = this.$route.query.name;
    this.planId = this.$route.query.planId;
    this.type = this.$route.query.type;
    this.details(this.$route.query.planId);
    this.getdeptOptions();
    this.getrefugeOptions();
    this.getEmergencyMaterial();
  },
  mounted() {
    let height = this.$refs.container1.offsetHeight;
    let width = this.$refs.container1.offsetWidth;

    this.height = height - 145;
    this.width = width - 200;
    setTimeout(() => {
      // 根据一个DIV的id创建顶层对象：Stage
      // 注：'divId' 需换成你页面实际的divd的id或者dom对象
      stage = new Stage("divId");
      // 一个Stage下面可以有多个'层',Layer
      // 多个Layer可以配合，比如有背景层，有动画层，有交互、展示层等
      layer = new Layer();
      // 一般步骤，将Layer放入到Stage中
      stage.addChild(layer);
      stage.hideToolbar();

      // // 最后一步：显示
      stage.show();
      editor = new Editor(stage);
      this.addImg();
    }, 1000);
  },
  methods: {
    // 详情查看
    details(id) {
      let that = this;
      selectById({ planId: id }).then((response) => {
        if (response.data != null) {
          this.planDetails = response.data;
          // console.log(this.planDetails);
          // 节点 数据渲染
          this.getJtopo();
          this.planDetails.emergencyPlanNodes.map((item, index) => {
            let arr = item.node.split(",");
            //节点渲染
            setTimeout(() => {
              let node;
              node = new Node(
                item.nodeName,
                Number(arr[0]),
                Number(arr[1]),
                40,
                40
              );
              node.css({
                width: 40,
                height: "40px",
                background: "url('./img/point.png') no-repeat",
                backgroundSize: "40px 40px",
                backgroundPosition: "center center",
                font: "bold 11px arial",
                color: "#000",
                zIndex: 2,
              });
              //将节点信息添加到自定义属性中
              node.userData = { ...item };

              layer.addChild(node);
              node.on("mousedragend", (event) => {
                this.abilityForm.node = `${event.details.x - 20},${
                  event.details.y - 20
                }`;
              });
              // 鼠标点击
              node.on("click", function (event) {
                // console.log('click', node);
                that.lookNode(node);
              });
              // 绘制连线
              if (index > 0) {
                let lastArr =
                  this.planDetails.emergencyPlanNodes[index - 1].node.split(
                    ","
                  );
                let lastArrName =
                  this.planDetails.emergencyPlanNodes[index - 1].nodeName;
                // 连线 new Link('文本',开始节点,结束节点);
                let link = new Link("");
                link.setBegin(
                  new Node(
                    lastArrName,
                    Number(lastArr[0]),
                    Number(lastArr[1]),
                    40,
                    40
                  ),
                  "lm"
                );
                link.setEnd(
                  new Node(
                    item.nodeName,
                    Number(arr[0]),
                    Number(arr[1]),
                    40,
                    40
                  ),
                  "rm"
                );
                link.css({
                  border: "solid 2px D43030",
                });
                // 起止偏移
                link.beginOffset = {
                  x: 20,
                  y: 5,
                };
                link.endOffset = {
                  x: -15,
                  y: 5,
                };
                layer.addChild(link);
              }
            }, 1500);
          });
        }
      });
    },
    getJtopo() {
      if (toNode != undefined) {
        toNode.remove();
        toNode = undefined;
      }
      setTimeout(() => {
        if (this.frequency == 0) {
          this.frequency++;
          // 根据一个DIV的id创建顶层对象：Stage
          // 注：'divId' 需换成你页面实际的divd的id或者dom对象
          stage = new Stage("divId");
          // 一个Stage下面可以有多个'层',Layer
          // 多个Layer可以配合，比如有背景层，有动画层，有交互、展示层等
          layer = new Layer();
          // 一般步骤，将Layer放入到Stage中
          stage.addChild(layer);
          stage.hideToolbar();
          // // 最后一步：显示
          stage.show();

          this.addImg();
        }
      }, 500);
    },
    addImg() {
      let that = this;
      that.container = new Node("xxxx广场", 100, 0, that.width, that.height);
      // 设置一张图片
      that.container.setImage(that.imgUrl);
      that.container.zIndex = 1;
      // 将创建好的图元对象放入layer中
      layer.addChild(that.container);
      // =========

      editor = new Editor(stage);
      if (this.type == "view") {
        stage.setMode("view");
      } else {
        stage.setMode("edit");
      }

      // 左侧图元面板配置
      const leftPanelConfig = this.leftPanelConfig;

      // 创建左侧的图标面板，并设置图标数据
      let iconPanel = new IconsPanel(stage);
      iconPanel.setConfig(leftPanelConfig).show();

      // 左侧拖拽开始
      iconPanel.on("dragstart", (event) => {
        this.nodeObj = undefined;
        const config = event.config;
        event.dataTransfer.setData("drop_data", JSON.stringify(config));
      });

      // 画布接收到拖拽结束
      editor.on("drop", function (event) {
        const json = event.dataTransfer.getData("drop_data");
        that.nodeObj = JSON.parse(json);
        // 画布上生成的实例
        that.handleAdd(false);

        // editor.record("添加");
      });
      // 鼠标点中的对象在右侧显示属性面板
      // stage.on("mousedown", function (e) {
      //   const pickedObject = stage.pickedObject;
      //   console.log(pickedObject);
      // });
      // 打开最后一次保存
      // editor.openLasted();
    },
    lookNode(node) {
      console.log(node.userData);
      this.title = "节点详情";
      let nodeInfo = JSON.parse(JSON.stringify(node.userData));
      this.sumbitBtn = false;
      this.abilityForm = {
        nodeName: nodeInfo.nodeName,
        eventType: nodeInfo.eventType,
        action: nodeInfo.emergencyActions,
      };
      // 专家
      let contacts = [];
      nodeInfo.emergencyExpertDeductions.map((item) => {
        contacts.push(item.contact);
      });
      //避难场所
      let refuges = [];
      nodeInfo.emergencyRefugeDeductions.map((item) => {
        refuges.push(item.refugeName);
      });
      // 物资
      this.tableDataList = nodeInfo.emergencyMaterialDeductions;
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys(contacts);
        this.$refs.shelterTree.setCheckedKeys(refuges);
      });
      this.abilityOpen = true;
    },
    // 节点新增确认
    sumbit() {
      // 判断是否已拖拽节点
      if (this.flag) {
        this.abilityForm.node = "173,60"; //默认位置
        console.log(this.abilityForm.node);
        let arr = this.abilityForm.node.split(",");
        this.getNode(arr, this.abilityForm.nodeName);
      } else {
        editor.record("添加");
        var nodeOrLink = editor.create(this.nodeObj);
        nodeOrLink.text = this.abilityForm.nodeName;
        this.abilityForm.node = `${nodeOrLink.x},${nodeOrLink.y}`;
        // 鼠标拖拽结束
        nodeOrLink.on("mousedragend", function (event) {
          this.abilityForm.node = `${event.details.x - 20},${
            event.details.y - 20
          }`;
        });
        // 鼠标点击
        nodeOrLink.on("click", function (event) {
          console.log("click");
        });
        editor.recordEnd("添加");
        // 节点大于2时添加连线
        if (this.planDetails.emergencyPlanNodes.length) {
          let nodeSky =
            this.planDetails.emergencyPlanNodes[
              this.planDetails.emergencyPlanNodes.length - 1
            ];
          let nodeArr = nodeSky.node.split(",");
          // 连线 new Link('文本',开始节点,结束节点);
          let link = new Link("");
          link.setBegin(
            new Node(
              nodeOrLink.text,
              nodeOrLink.x + 10,
              nodeOrLink.y + 10,
              40,
              40
            ),
            "lm"
          );
          link.setEnd(
            new Node(
              nodeSky.nodeName,
              Number(nodeArr[0]),
              Number(nodeArr[1]),
              40,
              40
            ),
            "rm"
          );
          link.css({
            border: "solid 2px D43030",
          });
          // 起止偏移
          link.beginOffset = {
            x: 20,
            y: 5,
          };
          link.endOffset = {
            x: -15,
            y: 5,
          };
          layer.addChild(link);
        }
      }
      this.nodeForm = {
        //预案id
        planId: this.planId,
        // 节点名称
        nodeName: this.abilityForm.nodeName,
        // 节点位置
        node: this.abilityForm.node,
        //事件类型
        eventType: this.abilityForm.eventType,
        // 应急专家
        emergencyExpertDeductions: this.abilityForm.emergencyExpertDeductions,
        // 应急行动
        emergencyActions: this.abilityForm.action,
        // 避难所
        emergencyRefugeDeductions: this.abilityForm.emergencyRefugeDeductions,
        // 应急物资
        emergencyMaterialDeductions: this.tableDataList,
        // 节点排序
        sort: this.planDetails.emergencyPlanNodes.length + 1,
      };
      console.log(this.nodeForm);
      nodeSave(this.nodeForm).then((response) => {
        if (response.code == 200) {
          this.$modal.msgSuccess("新增节点成功");
          this.abilityOpen = false;
          // this.details(this.planId);
        }
      });
    },
    // 节点渲染
    getNode(arr, name) {
      setTimeout(() => {
        toNode = new Node(name, Number(arr[0]), Number(arr[1]), 40, 40);
        toNode.css({
          width: 40,
          height: "40px",
          background: "url('./img/point.png') no-repeat",
          backgroundSize: "40px 40px",
          backgroundPosition: "center center",
          font: "bold 11px arial",
          color: "#000",
          zIndex: 2,
        });
        layer.addChild(toNode);
        toNode.on("mousedragend", (event) => {
          this.abilityForm.node = `${event.details.x - 20},${
            event.details.y - 20
          }`;
        });
        // 鼠标点击
        toNode.on("click", function (event) {
          console.log("click");
        });
      }, 1500);
    },
    handleUpdate(row) {
      let params = {
        abilityId: row.id,
      };
      this.reset();
      this.title = "编辑节点";
    },
    handleAdd(flag) {
      this.flag = flag;
      this.sumbitBtn = true;
      this.reset();
      this.abilityOpen = true;
      this.title = "新增节点";
    },
    // 专家选中
    currentChecked(node, nodeobj) {
      this.abilityForm.emergencyExpertDeductions = [];
      let obj = JSON.parse(JSON.stringify(nodeobj.checkedNodes));
      obj.map((item) => {
        if (item.children == undefined)
          this.abilityForm.emergencyExpertDeductions.push({
            contact: item.name,
          });
      });
    },
    // 场所选中
    shelterChecked(node, nodeobj) {
      this.abilityForm.emergencyRefugeDeductions = [];
      let obj = JSON.parse(JSON.stringify(nodeobj.checkedNodes));
      obj.map((item) => {
        this.abilityForm.emergencyRefugeDeductions.push({
          refugeName: item.name,
        });
      });
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    innerFormcancel() {
      this.innerShow = false;
      this.resetForm("innerForm");
    },

    // 表单重置
    reset() {
      this.abilityForm = {
        nodeName: "",
        eventType: "",
        action: "",
      };
      this.resetForm("abilityForm");
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([]);
        this.$refs.shelterTree.setCheckedKeys([]);
      });
      this.tableDataList = [];
    },
    // 获取避难场所
    getrefugeOptions() {
      getshelter().then((response) => {
        if (response.data != null) {
          this.refugeOptions = response.data;
          this.refugeOptions.map((item) => {
            item.name = item.refugeName;
          });
        }
      });
    },
    // 获取专家列表
    getdeptOptions() {
      getTree().then((response) => {
        if (response.data != null) {
          this.deptOptions = response.data;
          console.log(response.data);
          this.getTreeData(this.deptOptions);
        }
      });
    },
    //递归处理数据
    getTreeData(data) {
      data.map((item) => {
        if (item.contacts.length >= 1) {
          item.name = item.contingentType;
          item.contacts.map((info) => {
            info.name = info.contact;
          });
          item.children = item.contacts;
        }
      });
      return data;
    },
    // 筛选专家
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    //筛选场所
    filterShelter(value, data) {
      console.log(value, data);
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 预案推演资源下拉待选
    getEmergencyMaterial() {
      selectEmergencyMaterialByPage(this.queryParams).then((response) => {
        if (response.data != null) {
          this.tableData = response.data.records;
        }
      });
    },
    // 应急资源新增
    tableDataAdd() {
      this.$set(this.innerForm, "materialId", null);
      this.$set(this.innerForm, "materialType", null);
      this.$set(this.innerForm, "materialName", null);
      this.$set(this.innerForm, "inventory", null);
      this.$set(this.innerForm, "demandNumber", null);
      this.innerShow = true;
    },
    // 应急物资新增确定
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let form = {
            demandNumber: this.innerForm.demandNumber,
            inventory: this.innerForm.inventory,
            materialName: this.innerForm.materialName,
            materialType: this.innerForm.materialType,
          };
          this.tableDataList.push(JSON.parse(JSON.stringify(form)));
          this.innerShow = false;
        }
      });
    },
    //下拉应急物资选中 后 数据填充
    eventTypeChange(data) {
      let eventTypeForm = JSON.parse(
        JSON.stringify(
          this.tableData.filter(({ materialId }) => materialId == data)
        )
      );
      this.$nextTick(() => {
        this.$set(
          this.innerForm,
          "materialType",
          eventTypeForm[0].materialType
        );
        this.$set(
          this.innerForm,
          "materialName",
          eventTypeForm[0].materialName
        );
        this.$set(
          this.innerForm,
          "inventory",
          eventTypeForm[0].storageNumber || "暂无数据"
        );
        this.$set(this.innerForm, "id", eventTypeForm[0].materialId);
        this.$set(this.innerForm, "demandNumber", null);
      });
    },
    // 应急资源表格选中
    tableDataSelect(selection, row) {
      console.log(selection, row);
    },
    //应急物资删除选择项
    tableDataRemove() {
      this.tableData.splice();
    },
  },
};
</script>
<style scoped>
.app-container {
  height: 100%;
  position: absolute;
  width: 100%;
}
/* 利用穿透，设置input边框隐藏 */
.inputDeep >>> .el-input__inner {
  border: 0 !important;
  padding: 0;
}
.map_canvas {
  width: 100%;
  height: auto;
  background: #efefef;
}
</style>