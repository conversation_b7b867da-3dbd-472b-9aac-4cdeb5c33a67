<template>
  <div class="app-container" ref="container1">
    <div class="inputDeep" style="height: 60px">
      <el-input
        v-model="topTitle"
        style="
          font-weight: bold;
          color: rgba(128, 128, 128, 1);
          font-size: 20px;
          width: auto;
          min-width: 200px;
        "
      ></el-input
      ><i class="el-icon-edit" style="color: #2a82e4"></i>
    </div>
    <div style="height: 50px">
      <el-button type="primary" size="mini" @click="handleAdd"
        >增加节点</el-button
      >
      <el-button type="primary" size="mini">保存该预案推演</el-button>
    </div>
    <!--  -->
    <div class="map_canvas" :style="{ height: height + 'px' }" id="divId"></div>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="abilityOpen"
      width="680px"
      append-to-body
    >
      <div class="inputDeep">
        <el-input
          v-model="abilityForm.aa"
          maxlength="20"
          style="
            font-weight: bold;
            color: rgba(128, 128, 128, 1);
            font-size: 16px;
            width: auto;
            min-width: 200px;
          "
          placeholder="请输入节点名称"
        ></el-input>
        <el-button type="primary" size="mini">确 定</el-button>
        <el-button type="warning" size="mini">清 空</el-button>
        <el-button type="danger" size="mini">删 除</el-button>
      </div>
      <!--  -->
      <p>事件类型</p>
      <el-select
        v-model="abilityForm.aa"
        multiple
        placeholder="请选择"
        style="width: 100%"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <!--  -->
      <p>应急专家</p>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-input
            v-model="deptName"
            placeholder="请输入专家名称"
            clearable
            maxlength="20"
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 10px" />
          <el-tree
            ref="tree"
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            @check="currentChecked"
            show-checkbox
        /></el-col>
        <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
      </el-row>
      <!--  -->
      <p>应急行动</p>
      <el-input
        v-model="abilityForm.aa"
        placeholder="请输入应急行动"
        type="textarea"
        :rows="3"
        maxlength="200"
      />
      <!--  -->
      <p>应急资源 <el-button type="primary" size="mini">新 增</el-button></p>
      <el-table :data="tableData" height="250" border style="width: 100%">
        <el-table-column prop="date" label="资源类型"> </el-table-column>
        <el-table-column prop="name" label="资源名称"> </el-table-column>
        <el-table-column prop="address" label="储存量"> </el-table-column>
        <el-table-column prop="address" label="需求量"> </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  Stage,
  Layer,
  Editor,
  Node,
  ArrowNode,
  Link,
  IconsPanel,
  PropertiesPanel,
} from "../jtopo/jtopo-1.4.5_trial-esm-min.js";
import { getToken } from "@/utils/auth";
var stage;
var layer;
var editor;
export default {
  name: "PlanExerciseAdd",
  dicts: [],
  data() {
    return {
      height: null,

      width: null,

      // 遮罩层d
      loading: false,
      // 是否显示弹出层
      abilityOpen: false,
      title: "新增节点",
      abilityForm: {
        aa: "",
      },
      disabled: false,
      topTitle: "",
      options: [],
      deptOptions: [],
      deptName: "",
      defaultProps: {
        children: "children",
        label: "name",
      },
      tableData: [],
      imgUrl:
        `${require("@/assets/images/map.png")}`,
      imgcs: require("@/assets/images/profile.jpg"),
      // 表单校验
      abilityRules: {
        abilityName: [
          { required: true, message: "方案名称不能为空", trigger: "blur" },
        ],
      },
      //
      container: null,
      leftPanelConfig: {
        items: [
          {
            name: "直线",
            className: "Link",
            iconHtml: `
            <svg width="100%" height="100%">
            <line x1="5" y1="21" x2="35" y2="21" stroke="black" stroke-width="1"/>
            </svg>`,
            properties: {
              text: "一条直线",
              css: {
                borderWidth: theme.borderWidth,
                borderColor: theme.lineColor,
                color: theme.lineFontColor,
              },
            },
          },
          {
            name: "折线",
            className: "AutoFoldLink",
            iconHtml: `
            <svg width="100%" height="100%">
            <line x1="5" y1="10" x2="30" y2="10" stroke="black" stroke-width="1"/>
            <line x1="30" y1="10" x2="30" y2="35" stroke="black" stroke-width="1"/>
            </svg>`,
            properties: {
              text: "折线",
              css: {
                borderWidth: theme.borderWidth,
                borderColor: theme.lineColor,
                color: theme.lineFontColor,
              },
            },
          },
          {
            name: "矩形",
            className: "Node",
            iconHtml: `
            <svg width="100%" height="100%">
            <rect width="30" height="22" x="6" y = "9"
            stroke="black" stroke-width="1" fill="white"/>
            </svg>
        `,
            properties: {
              width: 100,
              height: 50,
              text: "文字",
              css: {
                background: "#FFFFFF",
                borderWidth: 1,
                borderColor: "#000000",
                color: theme.fontColor,
                textPosition: "center",
                textAlign: "center",
                textBaseline: "middle",
                font: "bold 14px arial",
              },
            },
          },

          {
            name: "圆形",
            className: "CircleNode",
            iconHtml: `
            <svg width="100%" height="100%">
            <circle cx="20" cy="20" r="15" 
                stroke="black" stroke-width="1" fill="white"/>
            </svg>
        `,
            properties: {
              text: "圆形",
              radius: 12,
              css: {
                borderWidth: 1,
                borderColor: "#000000",
                textPosition: "center",
                textAlign: "center",
                textBaseline: "middle",
                font: "bold 12px",
                background: "#FFFFFF",
                color: theme.fontColor,
              },
            },
          },

          {
            name: "多边形",
            className: "PolygonNode",
            iconHtml: `
        <svg width="100%" height="100%">
        <polygon points="21,5, 36,21, 21,36, 5,21"
        stroke="black" stroke-width="1" fill="white"/>
        </svg>
        `,
            properties: {
              text: "多边形",
              edges: 3,
              css: {
                borderWidth: 1,
                borderColor: "#000000",
                textPosition: "center",
                textAlign: "center",
                textBaseline: "middle",
                background: "#FFFFFF",
                color: theme.fontColor,
              },
            },
          },
          {
            name: "文字",
            className: "TextNode",
            iconHtml: `
            <div width="100%" height="100%" style="font-size:12px;padding-left:7px;padding-top:10px;font-weight:bold;">
                文字
            </div>
        `,
            properties: {
              text: "文本文字",
              width: 100,
              height: 50,
              autoSize: false,
              autoDirection: false,
              css: {
                borderColor: "#FFFFFF",
                textPosition: "center",
                textAlign: "center",
                textBaseline: "middle",
                font: "bold 14px arial",
                color: theme.textNodeColor,
              },
            },
          },
          // {
          //   name: "云",
          //   className: "Node",
          //   iconHtml: `
          //     <img width="100%" style="padding:2px;" src="./demo/img/cloud.png"/>
          // `,
          //   properties: {
          //     text: "终端",
          //     image: "./demo/img/cloud.png",
          //     padding: 1,
          //     ratio: 0.78,
          //     css: {
          //       borderWidth: 1,
          //       color: theme.fontColor,
          //     },
          //   },
          // },
          // {
          //   name: "终端",
          //   className: "Node",
          //   iconHtml: `
          //     <img width="100%" style="padding:2px;" src="./demo/img/pstn/terminal.png"/>
          //  `,
          //   properties: {
          //     text: "终端",
          //     image: "./demo/img/pstn/terminal.png",
          //     sizeToImage: true,
          //     css: {
          //       textPosition: "cb",
          //       borderWidth: 0,
          //       color: theme.fontColor,
          //     },
          //   },
          // },
        ],
      },
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.topTitle = this.$route.query.name;
  },
  mounted() {
    let height = this.$refs.container1.offsetHeight;
    let width = this.$refs.container1.offsetWidth;

    this.height = height - 145;
    this.width = width - 200;
    // 主题
    let theme = {
      backgroud: `url(${this.imgUrl}) no-repeat`,
    };

    stage = new Stage("divId");
    layer = new Layer();
    stage.addChild(layer);
    stage.show();

    editor = new Editor(stage);
    stage.setMode("edit");
    // 左侧图元面板配置
    const leftPanelConfig = this.leftPanelConfig;

    // 创建左侧的图标面板，并设置图标数据
    let iconPanel = new IconsPanel(stage);
    iconPanel.setConfig(leftPanelConfig).show();

    // 左侧拖拽开始
    iconPanel.on("dragstart", function (event) {
      const config = event.config;
      event.dataTransfer.setData("drop_data", JSON.stringify(config));
    });

    // 画布接收到拖拽结束
    editor.on("drop", function (event) {
      const json = event.dataTransfer.getData("drop_data");
      const config = JSON.parse(json);

      // 画布上生成的实例
      editor.record("添加");

      const nodeOrLink = editor.create(config);

      if (nodeOrLink instanceof Node) {
        nodeOrLink.text = "节点-" + nodeOrLink.id;
      } else {
        nodeOrLink.label.text = "连线-" + nodeOrLink.id;

        nodeOrLink.css({
          lineJoin: "round",
        });
        nodeOrLink.label.css({
          textBaseline: "bottom",
          color: theme.lineFontColor,
        });

        let arrowNode = new ArrowNode();
        arrowNode.resizeTo(10, 6);
        nodeOrLink.setEndArrow(arrowNode);
      }
      editor.recordEnd("添加");
    });

    // 打开最后一次保存
    editor.openLasted();

    layer.css({
      background: theme.backgroud,
    });
  },
  methods: {
    addImg() {
      let that = this;
      that.container = new Node("xxxx广场", 0, 0, that.width, that.height);
      // 设置一张图片
      that.container.setImage(that.imgUrl);
      that.container.zIndex = 1;
      // 鼠标点击
      that.container.on("click", (event) => {
        console.log(event);
        that.addNode(event);
      });
      // 增加一个限制：不允许拖拽出父节点
      // that.container.on("mousedrag", function (e) {
      //   console.log(e.config);
      //   // e.target.dragable = false; //将拖拽设置为false
      // });
      // 左侧拖拽开始
      that.container.on("mousedrag", function (event) {
        var config = event;
        console.log(config);
        // event.dataTransfer.setData("drop_data", JSON.stringify(config));
      });
      that.container.on("mousedragend", function (event) {
        var config = event;
        console.log(config);
        that.dx = event.details.dx;
        that.dy = event.details.dy;
        // event.dataTransfer.setData("drop_data", JSON.stringify(config));
      });
      // 将创建好的图元对象放入layer中
      layer.addChild(that.container);
    },
    addNode(evt) {
      var cantOutNode = new Node(
        "xxxx",
        evt.details.x - this.dx,
        evt.details.y - this.dy,
        20,
        20
      );
      cantOutNode.setImage("./img/decline.png");
      cantOutNode.zIndex = 2;
      // 增加一个限制：不允许拖拽出父节点
      cantOutNode.on("mousedrag", function (e) {
        e.target.dragable = false; //将拖拽设置为false
      });
      this.container.addChild(cantOutNode);
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    handleUpdate(row) {
      let params = {
        abilityId: row.id,
      };
      this.reset();
      this.title = "编辑节点";
    },
    handleAdd() {
      this.reset();
      this.abilityOpen = true;
      this.title = "新增节点";
    },
    currentChecked() {},
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.abilityForm = {
        abilityName: "",
      };
      this.resetForm("abilityForm");
    },
  },
};
</script>
<style scoped>
.app-container {
  height: 100%;
  position: absolute;
  width: 100%;
}
/* 利用穿透，设置input边框隐藏 */
.inputDeep >>> .el-input__inner {
  border: 0 !important;
  padding: 0;
}
.map_canvas {
  width: 100%;
  height: auto;
  background: #efefef;
}
</style>