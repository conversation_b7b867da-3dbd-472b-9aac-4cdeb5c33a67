<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-position="left"
          style="display: flex; justify-content: space-between"
        >
          <div>
            <el-form-item label="联系人" prop="contact">
              <el-input
                v-model="queryParams.contact"
                placeholder="请输入联系人"
                clearable
                style="width: 190px"
                maxlength="20"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model.number="queryParams.phone"
                placeholder="请输入联系电话"
                clearable
                maxlength="20"
                style="width: 190px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="dateRange"
                style="width: 300px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </div>
          <div style="min-width: 166px">
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </div>
        </el-form>
        <el-row :gutter="10" class="mb8" style="margin-bottom: 20px">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              size="mini"
              @click="handleAdd"
              icon="el-icon-plus"
              v-hasPermi="['system:user:add']"
              >新增预案推演方案</el-button
            >
          </el-col>

          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
        <el-table v-loading="loading" :data="userList">
          <!-- <el-table-column type="selection" width="50" align="center" /> -->
          <!-- <el-table-column type="index"
                           width="50">
            <template slot-scope="scope">
              <span>{{
                (queryParams.current - 1) * queryParams.size + scope.$index + 1
              }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="ID" align="center" prop="id" />
          <el-table-column label="预案名称" align="center" prop="planName" />
          <!-- <el-table-column label="应急专家"
                           align="center"
                           prop="contact" />
          <el-table-column label="位置节点"
                           align="center"
                           prop="node" /> -->
          <el-table-column label="责任人" align="center" prop="liabilityUser" />
          <!-- <el-table-column label="应急物资"
                           align="center"
                           prop="ID" /> -->
          <el-table-column label="应急类型" align="center" prop="dictValue" />
          <el-table-column
            label="操作"
            align="center"
            width="160"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="edit(scope.row)"
                v-hasPermi="['system:user:edit']"
                >详情</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:user:edit']"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:user:remove']"
                >删除</el-button
              >
              <!-- <el-button size="mini"
                         type="text"
                         v-hasPermi="['system:user:edit']">下载</el-button> -->
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.current"
          :limit.sync="queryParams.size"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <!--  -->
    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="abilityOpen"
      :close-on-click-modal="false"
      width="680px"
      append-to-body
    >
      <el-form
        ref="abilityForm"
        :model="abilityForm"
        :rules="abilityRules"
        label-width="110px"
      >
        <el-form-item label="方案名称" prop="abilityName">
          <el-input
            v-model="abilityForm.abilityName"
            placeholder="请输入方案名称"
            maxlength="20"
            :disabled="disabled"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="confirm('abilityForm')"
          :disabled="disabled"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import {
  page,
  save,
  update,
  deleteById,
} from "@/api/emergency/planExercise/index";

export default {
  name: "PlanExercise",
  dicts: [],
  data() {
    return {
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 是否显示弹出层
      abilityOpen: false,
      title: "新增预案推演方案",
      row: "",
      dateRange: [],
      // 查询参数
      queryParams: {
        contact: undefined, //联系人
        phone: undefined, //联系电话
        current: 1,
        size: 10,
        startTime: undefined,
        endTime: undefined,
      },

      abilityForm: {},
      disabled: false,
      // 表单校验
      abilityRules: {
        abilityName: [
          { required: true, message: "方案名称不能为空", trigger: "blur" },
        ],
      },
    };
  },
  watch: {},
  created() {
    // this.getDeptTree();
    // this.getAreaTree();
    this.getList();
  },
  methods: {
    /** 查询部门下拉树结构 */
    getDeptTree() {
      treeList().then((response) => {
        if (response.data != null && response.data.length > 0) {
          this.deptArr = response.data;
        }
      });
    },
    getAreaTree() {
      areaList().then((response) => {
        if (response.data != null && response.data.length > 0) {
          this.areaArr = response.data;
        }
      });
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        if (response.data != null) {
          this.userList = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },

    handleLook(row) {
      this.row = row;
      let params = {
        abilityId: row.id,
      };
      this.reset();
      this.title = "新增预案推演方案";
      this.disabled = true;
    },
    handleUpdate(row) {
      this.row = row;
      this.reset();
      this.abilityForm.abilityId = row.id;
      this.abilityForm.planId = row.planId;
      this.abilityForm.abilityName = row.planName;
      this.title = "编辑预案推演方案";
      this.disabled = false;
      this.abilityOpen = true;
    },
    handleAdd() {
      this.reset();
      this.abilityOpen = true;
      this.title = "新增预案推演方案";
      this.disabled = false;
    },
    handleDelete(row) {
      const deleteId = row.planId;
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return deleteById({ planId: deleteId });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 取消按钮
    cancel() {
      this.abilityOpen = false;
      this.reset();
    },
    /*  确认保存新增*/
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.abilityOpen = false;
          if (this.abilityForm.abilityId) {
            //编辑
            console.log(this.abilityForm);
            update({
              planId: this.abilityForm.planId,
              planName: this.abilityForm.abilityName,
            }).then((response) => {
              if (response.code == 200) {
                // this.$modal.msgSuccess(response.msg);
                this.getList();
                this.abilityForm.abilityId = null;
              }
              this.$router.push({
                path: "/portal/vue-emergency/emergency/planExerciseEdit",
                query: { name: this.row.planName, planId: this.row.planId },
              });
            });
          } else {
            // 新增保存
            save({ planName: this.abilityForm.abilityName }).then(
              (response) => {
                if (response.code == 200) {
                  this.$modal.msgSuccess(response.msg);
                  this.getList();
                }
                this.$router.push({
                  path: "/portal/vue-emergency/emergency/planExerciseEdit",
                  query: { name: this.row.planName, planId: this.row.planId },
                });
              }
            );
          }
        }
      });
    },
    //点击详情跳转
    edit(row) {
      // console.log(row);
      this.row = row;
      this.$router.push({
        path: "/portal/vue-emergency/emergency/planExerciseEdit",
        query: { name: row.planName, planId: row.planId, type: "view" },
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.queryParams.startTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];

      this.getList();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.abilityForm = {
        abilityName: "",
      };
      this.resetForm("abilityForm");
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];

      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}
</style>