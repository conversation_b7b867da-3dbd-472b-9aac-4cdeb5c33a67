<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--用户数据-->
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="100px" style="display: flex; justify-content: space-between">
            <div>
              <el-form-item label="ID :" prop="deviceId">
                <el-input v-model="queryParams.deviceId" placeholder="请输入ID" clearable style="width: 10vw"
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="设备名称 :" prop="deviceName">
                <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable style="width: 10vw"
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="告警状态 :" prop="alarmStatus">
                <el-select v-model="queryParams.alarmStatus" placeholder="告警状态" clearable style="width: 10vw">
                  <el-option v-for="dict in dict.type.firecontrol_alarm_status" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="告警时间 :">
                <el-date-picker v-model="dateRange" style="width: 10vw" format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss" type="daterange" range-separator="-" start-placeholder="开始日期"
                  end-placeholder="结束日期"></el-date-picker>
              </el-form-item>
              <el-form-item label="告警等级 :" prop="alarmLevel">
                <el-select v-model="queryParams.alarmLevel" placeholder="告警等级" clearable style="width: 10vw">
                  <el-option v-for="dict in dict.type.firecontrol_alarm_level" :key="dict.value" :label="dict.label"
                    :value="dict.value" />
                </el-select>
              </el-form-item>
            </div>
            <div style="min-width: 200px">
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>设备告警列表</span>
          </div>
          <el-table v-loading="loading" :data="userList">
            <el-table-column type="index" width="50" label="序号">
              <template slot-scope="scope">
                <span>{{
                  (queryParams.current - 1) * queryParams.size + scope.$index + 1
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="设备ID" align="center" key="deviceId" prop="deviceId" />
            <el-table-column label="设备名称" align="center" key="deviceName" prop="deviceName"
              :show-overflow-tooltip="true" />
            <el-table-column label="安装位置" align="center" key="location" prop="location" :show-overflow-tooltip="true" />
            <el-table-column label="告警类型" align="center">
              <template slot-scope="scope">
                {{ getNameById1(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="告警状态" align="center">
              <template slot-scope="scope">
                <span :class="getstatusByClass(scope.row)">{{ getNameById(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="告警时间" align="center" key="alarmTime" prop="alarmTime">
            </el-table-column>
            <el-table-column label="告警等级" align="center">
              <template slot-scope="scope">
                {{ getNameById2(scope.row) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleDetail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
            @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
    <!-- 详情 -->
    <el-dialog :title="titleDetail" :visible.sync="openDetail" width="1200px" append-to-body>
      <div class="diaTil">
        <p>处理记录</p>
      </div>
      <el-table :data="tableData" height="200" border style="width: 100%">
        <el-table-column type="index" width="50" label="序号">
          <template slot-scope="scope">
            <span>{{
              (queryParams.current - 1) * queryParams.size + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="approvalUser" label="处理人" align="center">
        </el-table-column>
        <el-table-column prop="result" label="审批状态" align="center">
          <template slot-scope="scope">
            {{ getNameById3(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column prop="approvalTime" label="时间" align="center">
        </el-table-column>
      </el-table>
      <!-- <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      /> -->
      <div class="diaTil" style="padding-top: 10px">
        <p>设备信息</p>
      </div>
      <el-card shadow="hover"><el-row :gutter="20">
          <!--部门数据-->
          <el-col :span="24" :xs="14">
            <div class="diadevice_top" style="margin-bottom: 30px">
              <div>ID：{{ deptOptions.id }}</div>
              <div>设备名称：{{ deptOptions.deviceName }}</div>
              <div>所属部门：{{ deptOptions.departmentName }}</div>
            </div>
            <div class="diadevice_top" style="margin-bottom: 30px">
              <div>当前状态：{{ deptOptions.deviceStatus }}</div>
              <div>安装位置：{{ deptOptions.location }}</div>
              <div>责任人：{{ deptOptions.principal }}</div>
            </div>
            <div class="diadevice_top">
              <div>联系电话：{{ deptOptions.phone }}</div>
            </div>
          </el-col>
          <!-- <el-col :span="10" :xs="10">
            <div style="
                width: 100%;
                height: 120px;
                text-align: center;
                line-height: 120px;
              ">
              <jessibuca-player id="videoElement1" ref="videoPlayer" :videoUrl="videoUrl" :error="videoError"
                :message="videoError" :height="false" :hasAudio="hasAudio" fluent autoplay live>
              </jessibuca-player>
            </div>
          </el-col> -->
        </el-row>
      </el-card>
      <div class="diaTil" style="padding-top: 10px">
        <p>告警信息</p>
      </div>
      <el-card shadow="hover"><el-row :gutter="20">
          <!--部门数据-->
          <el-col :span="8" :xs="8">
            <div>告警类型：{{ getNameById1(deptOptions) }}</div>
          </el-col>
          <el-col :span="8" :xs="8">
            <div>告警值：{{ deptOptions.alarmValue }}</div>
          </el-col>
          <el-col :span="8" :xs="8">
            <div>告警时间：{{ deptOptions.alarmTime }}</div>
          </el-col>
        </el-row>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDetail">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  detail,
  getVideoStreaming
} from "@/api/fireManagement/alarmCenter/alarmLog/index";
// import jessibucaPlayer from "./jessibuca.vue";
export default {
  name: "AlarmLog",
  components: {
    // jessibucaPlayer,
  },
  dicts: [
    "firecontrol_alarm_status",
    "firecontrol_alarm_level",
    'firecontrol_device_type',
    'firecontrol_approve_status',

  ],
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: {},
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,

      // 日期范围
      dateRange: [],

      // 表单参数
      form: {},
      activeNames: ["1"],
      defaultProps: {
        children: "children",
        label: "label",
      },

      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
      },
      tableData: [],
      // 表单校验
      rules: {},
      titleDetail: "设备详情",
      openDetail: false,
      formDetail: {},
      videoUrl: '',
      hasAudio: false,
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getList();
    // this.getDeptTree();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams);
      page(this.queryParams).then(
        (res) => {
          if (res.code == 200) {
            this.userList = res.data.records;
            this.total = res.data.total;
          }
          console.log(res);
          this.loading = false;
        }
      );
      this.userList = [{}];
    },
    /** 查询详情 */
    getDetail(id) {
      console.log(id);
      detail({ id: id }).then((res) => {
        console.log(res);
        this.titleDetail = `告警详情-${this.getNameById(res.data)}`;
        // this.getvideoFlv(res.data.monitor)
        // this.getVideoStreaming(res.data.)
        this.deptOptions = res.data;
        this.tableData = res.data.workTickets
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    getstatusByClass(res) {
      switch (res.alarmStatus) {
        case "4012501":
          return 'blue'
        case "4012503":
          return 'green'
        default:
          break;
      }
    },
    getNameById(res) {
      // console.log(res, this.dict.type.risk_level);
      if (
        res.alarmStatus != undefined &&
        res.alarmStatus != "" &&
        res.alarmStatus != null
      ) {
        return this.dict.type.firecontrol_alarm_status.filter(
          (item) => item.value == res.alarmStatus
        )[0].label;
      }
    },
    getNameById1(res) {
      // console.log(res, this.dict.type.risk_level);
      if (
        res.alarmType != undefined &&
        res.alarmType != "" &&
        res.alarmType != null
      ) {
        return this.dict.type.firecontrol_device_type.filter(
          (item) => item.value == res.alarmType
        )[0].label;
      }
    },
    getNameById2(res) {
      // console.log(res, this.dict.type.risk_level);
      if (
        res.alarmLevel != undefined &&
        res.alarmLevel != "" &&
        res.alarmLevel != null
      ) {
        return this.dict.type.firecontrol_alarm_level.filter(
          (item) => item.value == res.alarmLevel
        )[0].label;
      }
    },
    getNameById3(res) {
      // console.log(res, this.dict.type.risk_level);
      if (
        res.result != undefined &&
        res.result != "" &&
        res.result != null
      ) {
        return this.dict.type.firecontrol_approve_status.filter(
          (item) => item.value == res.result
        )[0].label;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.queryParams.size = 10
      this.queryParams.startTime = this.dateRange[0];
      this.queryParams.endTime = this.dateRange[1];
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams = {};
      this.handleQuery();
    },
    handleDetail(row) {
      this.openDetail = true;
      this.getDetail(row.id)
    },
    cancelDetail() {
      this.openDetail = false;
      this.resetDetail();
    },
    resetDetail() {
      this.formDetail = {};
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    /** 提交按钮 */
    submitForm: function () {
      //   this.$refs["form"].validate(valid => {
      //     if (valid) {
      //       if (this.form.userId != undefined) {
      //         updateUser(this.form).then(response => {
      //           this.$modal.msgSuccess("修改成功");
      //           this.open = false;
      //           this.getList();
      //         });
      //       } else {
      //         addUser(this.form).then(response => {
      //           this.$modal.msgSuccess("新增成功");
      //           this.open = false;
      //           this.getList();
      //         });
      //       }
      //     }
      //   });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal
        .confirm('是否确认删除用户编号为"' + userIds + '"的数据项？')
        .then(function () {
          //   return delUser(userIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    // 视频流播放相关
    videoError: function (e) {
      console.log("播放器错误：" + JSON.stringify(e));
    },
    // 视频流获取
    getvideoFlv(item) {
      console.log(item);
      this.dialogVisible = true;
      getVideoStreaming({
        equipmentIdList: [item],
      }).then((res) => {
        console.log(res);
        this.$nextTick(() => {
          this.videoUrl = res.data[0].flvAddress;
          this.$refs.videoPlayer.play(this.videoUrl);
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;

  p {
    font-size: 20px;
    font-weight: bold;
  }
}

.diadevice_top {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
  float: right;
  margin-right: 10px;
}

.blue {
  color: #1890FF;
}

.green {
  color: green;
}
</style>