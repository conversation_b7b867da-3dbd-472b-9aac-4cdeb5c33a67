<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>数据筛选</span>
      </div>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
        style="display: flex; justify-content: space-between">
        <div>
          <el-form-item label="隐患ID" prop="id">
            <el-input v-model="queryParams.id" placeholder="请输入隐患ID" clearable style="width: 10vw"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <!-- <el-form-item label="关键字" prop="description">
            <el-input v-model="queryParams.description" placeholder="请输入关键字" clearable style="width: 10vw"
              @keyup.enter.native="handleQuery" />
          </el-form-item> -->
          <el-form-item label="修复时间">
            <el-date-picker v-model="dateRange" style="width: 10vw" value-format="yyyy-MM-dd" type="daterange"
              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
          <el-form-item label="上报人" prop="createUser">
            <el-input v-model="queryParams.createUser" placeholder="请输入上报人" clearable style="width: 10vw"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="隐患类型" prop="dangerType">
            <el-select v-model="queryParams.dangerType" placeholder="隐患类型" clearable style="width: 10vw">
              <el-option v-for="dict in dict.type.fault_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
        </div>
        <div style="min-width: 200px">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>隐患配置列表</span>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          class="queryBtnT">上报隐患</el-button>
      </div>
      <el-table v-loading="loading" :data="roleList">
        <el-table-column label="隐患ID" :show-overflow-tooltip="true" prop="id" align="center" />
        <el-table-column label="隐患简述" prop="description" align="center" />
        <el-table-column label="隐患类型" prop="dangerType" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.fault_type" :value="scope.row.dangerType" :type="1" />
          </template>
        </el-table-column>
        <el-table-column label="所属区域" prop="griddingId" align="center">
          <template slot-scope="scope">
            {{ scope.row.griddingId | getfullName(originArr) }}
          </template>
        </el-table-column>
        <el-table-column label="上报人员" prop="createUser" align="center" />
        <el-table-column label="具体位置" prop="location" align="center" />
        <el-table-column label="上报时间" prop="createTime" align="center" />
        <el-table-column label="修复时间" prop="restoreTime" align="center" />
        <el-table-column label="状态" prop="handleType" align="center">
          <template slot-scope="scope">
            {{
              getNameById(scope.row)
            }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
          <template slot-scope="scope">
            <el-button size="mini" type="text" v-hasPermi="['system:role:edit']"
              @click="handleUpdate(scope.row, 'look')">详情</el-button>
            <!-- <el-button
            size="mini"
            type="text"
            v-hasPermi="['system:role:edit']"
            @click="handleUpdate(scope.row, 'edit')"
            >处理</el-button
          > -->
            <el-button size="mini" type="text" @click="handleDelete(scope.row)"
              v-hasPermi="['system:role:edit']">删除</el-button>
            <el-button size="mini" type="text" @click="handledownloadFile(scope.row)"
              v-hasPermi="['system:role:edit']">下载附件</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
        @pagination="getList" />
    </el-card>
    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="750px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="隐患类型" prop="dangerType">
              <el-select v-model="form.dangerType" placeholder="隐患类型" clearable :disabled="disabled">
                <el-option v-for="dict in dict.type.fault_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" /> </el-select></el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="区域名称" prop="griddingId">
              <el-select v-model="form.griddingId" placeholder="隐患类型" clearable :disabled="disabled">
                <el-option v-for="dict in originArr" :key="dict.griddingId" :label="dict.griddingName"
                  :value="dict.griddingId" />
              </el-select> </el-form-item></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="具体位置" prop="location">
              <el-input v-model="form.location" :disabled="disabled" placeholder="请输入具体位置" /> </el-form-item></el-col>
        </el-row>
        <!-- <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="处理方式" prop="rcmc">
              <el-radio-group v-model="form.handleType" :disabled="disabled">
                <el-radio v-for="(item, i) in dict.type.handle_type" :label="item.value" :key="i">{{ item.label
                }}</el-radio>
              </el-radio-group></el-form-item></el-col>
        </el-row> -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="隐患描述" prop="description">
              <el-input :disabled="disabled" v-model="form.description" type="textarea"
                placeholder="请输入"></el-input></el-form-item></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="上传附件" prop="attachment">
              <el-upload :disabled="disabled" ref="my-upload"
              :headers="headers"
               :action="uploadFile"
               :on-remove="handleRemove" 
                :on-success="handlePreview" :file-list="fileList">
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip"><div slot="tip" class="el-upload__tip">支持格式:.xls.xlsx.doc.docx.pdf,单个文件不能超过100MB</div></div>
              </el-upload></el-form-item></el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="disabled">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  list,
  save,
  update,
  del,
} from "@/api/fireManagement/alarmCenter/hiddenDangerRecord/index";
import crypto from "@/utils/crypto";
export default {
  name: "HiddenDangerRecord",
  dicts: ["fault_type", "firecontrol_alarm_status", "emergency_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        startTime: undefined,
        endTime: undefined,
        id: undefined,
        description: undefined,
        createUser: undefined,
        dangerType: undefined,
        },
        headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadFile: process.env.VUE_APP_BASE_API + "api/file/uploadFile",
      fileList: [],
      // 表单参数
      form: {},
      dateRange: [],
      originArr: [],
      // 表单校验
      rules: {
        dangerType: [{ required: true, message: "隐患类型", trigger: "blur" }],
        griddingId: [{ required: true, message: "区域名称", trigger: "blur" }],
        location: [
          { required: true, message: "请输入具体位置", trigger: "blur" },
        ],
        handleType: [{ required: true, message: "处理方式", trigger: "blur" }],
      },
      disabled: false,
    };
  },
  created() {
    this.getList();
    this.getListpage();
  },
  filters: {
    getfullName(val, arr) {
      let name = "";
      arr.map((res) => {
        if (val == res.griddingId) {
          name = res.griddingName;
        }
      });
      return name;
    },
  },
  methods: {
    /** 查询角色列表 */
    getListpage() {
      list().then((res) => {
        this.originArr = res.data;
      });
    },
    getList() {
      this.loading = true;
      console.log(this.queryParams);
      page(this.queryParams).then((response) => {
        console.log(response);
        this.roleList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        dangerType: undefined,
        griddingId: undefined,
        location: undefined,
        description: undefined,
        attachment: undefined,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.startTime = undefined;
      this.queryParams.endTime = undefined;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handledownloadFile(row) {
      this.$modal
        .confirm("是否确认下载附件")
        .then(function () {
          let arr1 = row.attachment.split(",");
          console.log(arr1);
          arr1.map((res) => {
            let arr = res.split("/");
            const link = document.createElement("a"); //自己创建的a标签
            link.href = `/api/file/downloadFile?bucket=${arr[3]}&path=${arr[4]}&fileName=${arr[5]}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            // window.URL.revokeObjectURL(`/api/file/downloadFile?bucket=${arr[3]}&path=${arr[4]}&fileName=${arr[5]}`);
          });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("下载成功");
        })
        .catch((error) => { });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      let arr = [];
      fileList.map((res) => {
        arr.push(res.url);
      });
      this.form.attachment = arr.join(",");
    },
    handlePreview(response, file, fileList) {
      console.log(response, file, fileList);
      if (file.size == 0) {
        this.$modal.msgWarning("当前文件大小不符合规范");

        return true;
      }
      let arr = [];
      fileList.map((res) => {
        if (res.response) {
          arr.push(JSON.parse(crypto.decryptAES(res.response, crypto.aesKey)));
        } else {
          arr.push(res.url);
        }
      });
      this.form.attachment = arr.join(",");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.disabled = false;
      this.reset();
      this.open = true;
      this.title = "上报隐患";
      this.fileList = [];
    },
    /** 修改按钮操作 */
    handleUpdate(row, type) {
      if (type == "look") {
        this.disabled = true;
      } else {
        this.disabled = false;
      }
      this.reset();
      this.open = true;
      this.title = "修改隐患";
      this.form = JSON.parse(JSON.stringify(row));
      console.log(this.form);
      this.fileList = [];
      this.form.amount = null;
      if (row.attachment != null) {
        let arr = [];
        arr = JSON.parse(JSON.stringify(this.form.attachment.split(",")));
        arr.map((res, i) => {
          let arr1 = res.split("/");
          this.fileList.push({
            name:arr1[arr1.length - 1],
            url: res,
          });
        });
      }
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          console.log(this.form);
          if (this.form.id != undefined) {
            update(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            save(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    getNameById(res) {
      console.log(res, this.dict.type.firecontrol_alarm_status,'Name');
      if (
        res.dangerStatus != undefined &&
        res.dangerStatus != "" &&
        res.dangerStatus != null
      ) {
        return this.dict.type.firecontrol_alarm_status.filter(
          (item) => item.value == res.dangerStatus
        )[0].label;
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return del({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;

  p {
    font-size: 20px;
    font-weight: bold;
  }
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.queryBtnT {
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
  float: right;
  margin-right: 10px;
}
</style>