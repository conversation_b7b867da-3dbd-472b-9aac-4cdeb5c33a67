<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="4">
                <el-tree :props="props" accordion :data="treeData" @node-click="handleCheckChange">
                </el-tree>
            </el-col>
            <el-col :span="20" :xs="24">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>告警配置列表</span>
                        <el-button type="primary" size="mini" @click="handleAdd" icon="el-icon-plus"
                            class="queryBtnT">
                            新增告警配置
                        </el-button>
                    </div>
                    <el-table v-loading="loading" :data="shelter">
                        <el-table-column label="序号" align="center">
                            <template slot-scope="scope">
                                <span>{{
                                    (queryParams.current - 1) * queryParams.size + scope.$index + 1
                                }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="告警条件" align="center">
                            <template slot-scope="scope">
                                {{ getNameById(scope.row) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="告警值" align="center" prop="alarmValue" />
                        <el-table-column label="告警等级" align="center">
                            <template slot-scope="scope">
                                {{ getTypeById(scope.row) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
                            <template slot-scope="scope">
                                <el-button size="mini" type="text" icon="el-icon-view"
                                    @click="handleLook(scope.row)">查看</el-button>
                                <el-button size="mini" type="text" icon="el-icon-edit"
                                    @click="handleUpdate(scope.row)">编辑</el-button>
                                <el-button size="mini" type="text" icon="el-icon-delete"
                                    @click="handleDelete(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current"
                        :limit.sync="queryParams.size" @pagination="getList" />
                </el-card>
            </el-col>
        </el-row>
        <!--  -->
        <!-- 添加或修改避难所信息对话框 -->
        <el-dialog :title="title" :visible.sync="abilityOpen" width="560px" append-to-body>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form ref="abilityForm" :model="abilityForm" :rules="abilityRules" label-width="140px">
                        <el-form-item label="告警类型 :" prop="lngAndLat">
                            <el-tag type="info">{{ alarmTypeName }}</el-tag>
                        </el-form-item>
                        <el-form-item label="告警参数 :" prop="lngAndLat">
                            <el-tag type="info">{{ alarmParamName }}</el-tag>
                        </el-form-item>
                        <el-form-item label="告警等级 :" prop="alarmLevel" label-width="140px">
                            <el-select style="width: 245px;" v-model="abilityForm.alarmLevel" placeholder="请选择告警等级"
                                :disabled="disabled">
                                <el-option v-for="dict in dict.type.firecontrol_alarm_level" :key="dict.value"
                                    :label="dict.label" :value="dict.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="告警条件 :" prop="alarmTrigger" label-width="140px">
                            <el-select style="width: 245px;" v-model="abilityForm.alarmTrigger" placeholder="请选择告警条件"
                                :disabled="disabled">
                                <el-option v-for="dict in dict.type.firecontrol_compare" :key="dict.value"
                                    :label="dict.label" :value="dict.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="告警值 :" prop="alarmValue" label-width="140px">
                            <el-input v-model="abilityForm.alarmValue" placeholder="请输入主要危险因素" maxlength="30"
                                :disabled="disabled" style="width: 245px;" />
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="confirm('abilityForm')" :disabled="disabled">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
    
<script>
import {
    page,
    save,
    update,
    del,
    list
} from "@/api/fireManagement/basicConfiguration/index";
export default {
    name: "EmergencySupplies",
    dicts: ["firecontrol_alarm_level", 'firecontrol_compare'],
    components: { Map },
    data() {
        let checkPhone = (rule, value, callback) => {
            let reg = /^1[345789]\d{9}$/;
            if (!reg.test(value)) {
                callback(new Error("请输入11位手机号"));
            } else {
                callback();
            }
        };
        const validCode = (rule, value, callback) => {
            console.log(rule, value, 'value');
            if (this.lngAndLat) {
                callback()
            } else {
                callback(new Error('请选择经纬度'))
            }
        }
        return {
            deviceTypeList: [{
                name: '',
                id: '',
            }],
            // 遮罩层d
            loading: false,
            activeNames: ['1'],
            props: {
                label: 'dictValue',
                children: 'dicts'
            },
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户表格数据
            shelter: null,
            // 是否显示弹出层
            abilityOpen: false,
            title: "新增告警配置",
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                alarmType: undefined,
                alarmParam: undefined,
            },
            abilityForm: {},
            disabled: false,
            // 表单校验
            abilityRules: {

                // lngAndLat: [
                //     { required: true, validator: validCode, trigger: "blur" },
                // ],
                // phone: [
                //     {
                //         type: "number",
                //         validator: checkPhone,
                //         message: "请输入正确的手机号",
                //         trigger: "change",
                //         required: true,
                //     },
                // ],
            },
            nodeObj: undefined,
            treeData: [],
            alarmType: undefined,
            alarmParam: undefined,
            alarmParamName: undefined,
            alarmTypeName: undefined,
            treeFlag:false
        };
    },
    watch: {},
    created() {
        list({ code: 'firecontrol_device_type' }).then(res => {
            if (res.code == 200) {
                console.log(res);
                this.treeData = res.data
                this.recursion(res.data)
            }
        })
    },
    methods: {
        /** 查询场所列表 */
        getList() {
            this.loading = true;
            this.queryParams.alarmType = this.alarmType
            this.queryParams.alarmParam = this.alarmParam
            page(this.queryParams).then((response) => {
                console.log(response);
                if (response.data != null) {
                    this.shelter = response.data.records;
                    this.total = response.data.total;
                }
                this.loading = false;
            });
        },
        //获取场所详情

        handleLook(row) {
            this.reset();
            this.abilityOpen = true;
            this.abilityForm = JSON.parse(JSON.stringify(row));
            this.title = "查看告警配置";
            this.disabled = true;

            this.lngAndLat = row.longitude + ',' + row.latitude
            console.log(this.abilityForm);
        },
        handleUpdate(row) {
            this.reset();
            this.abilityOpen = true;
            this.title = "编辑告警配置";
            this.disabled = false;
            this.lngAndLat = row.longitude + ',' + row.latitude
            this.abilityForm = JSON.parse(JSON.stringify(row));
        },
        handleAdd() {
            console.log(this.treeFlag);
            if (!this.treeFlag) {
                this.$modal.msgSuccess("请选择根节点的告警类型");
            } else {
                this.reset();
                this.abilityOpen = true;
                this.title = "新增告警配置";
                this.disabled = false;
            }
        },
        handleDelete(row) {
            this.$modal
                .confirm("是否确认删除当前数据")
                .then(function () {
                    console.log(row.id);
                    return del({ id: row.id });
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess("删除成功");
                })
                .catch((error) => { });
        },
        // 取消按钮
        cancel() {
            this.abilityOpen = false;
            this.reset();
        },
        /*  确认保存新增*/
        confirm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    console.log(this.abilityForm);
                    this.abilityForm.alarmType = this.alarmType
                    this.abilityForm.alarmParam = this.alarmParam
                    if (this.abilityForm.id != undefined) {
                        update(this.abilityForm).then((response) => {
                            // console.log(response, "编辑");
                            if (response.code == 200) {
                                this.$modal.msgSuccess("编辑成功");
                                this.abilityOpen = false;
                                this.getList();
                            }
                        });
                    } else {
                        save(this.abilityForm).then((response) => {
                            // console.log(response, "新增");
                            if (response.code == 200) {
                                this.$modal.msgSuccess("新增成功");
                                this.abilityOpen = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
            // console.log(this.evaluateData, "evaluateData");
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.current = 1;
            this.getList();
        },

        // 取消按钮
        // 表单重置
        reset() {
            this.abilityForm = {
                id: undefined,
                refugeName: undefined,
                refugeArea: undefined,
                holdsNumber: undefined,
                liabilityUser: undefined,
                phone: undefined,
                remark: undefined,
            };
            this.lngAndLat = ''
            this.resetForm("abilityForm");
        },

        /** 重置按钮操作 */
        resetQuery() {
            this.queryParams = {
                current: 1,
                size: 10,
                refugeName: undefined,
                liabilityUser: undefined,
                phone: undefined,
            };
            this.resetForm("queryForm");
            this.handleQuery();
        },
        getTypeById(res) {
            console.log(res, this.dict.type.firecontrol_alarm_level);
            if (res.alarmLevel != undefined && res.alarmLevel != '' && res.alarmLevel != null) {
                return this.dict.type.firecontrol_alarm_level.filter(item => item.value == res.alarmLevel)[0].label
            }
        },
        getNameById(res) {
            console.log(res, this.dict.type.firecontrol_compare);
            if (res.alarmTrigger != undefined && res.alarmTrigger != '' && res.alarmTrigger != null) {
                return this.dict.type.firecontrol_compare.filter(item => item.value == res.alarmTrigger)[0].label
            }
        },
        handleCheckChange(res) {
            if (!res.dicts) {
                this.treeFlag=true
                this.alarmType = res.parentId
                this.alarmTypeName = this.treeData.filter(item => res.parentId == item.id)[0].dictValue
                this.alarmParam = res.dictKey
                this.alarmParamName = res.dictValue
                this.getList();
            }else{
                this.treeFlag=false
            }
            console.log(res, this.abilityForm);
        },
        // 通过递归给叶子节点添加只读属性
        recursion(data) {
            data.forEach((item, index) => {
                if (item.dicts) {
                    item.disabled = true;
                    return this.recursion(item.dicts);
                } else {
                    item.disabled = false;
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.left_title {
    color: rgba(56, 56, 56, 1);
    font-size: 24px;
    font-weight: bold;
    padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
    background: rgba(25, 159, 255, 0.15);
    font-family: Noto Sans SC;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #007baf;
}

.clearfix:after,
.clearfix:before {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both;
}

.box-card-bottom {
    margin: 20px;
}

.box-card {
    margin-bottom: 20px;
    z-index: 2;
}

.queryBtnT {
    height: 32px;
    border: 1px solid #cccccc;
    border-radius: 2px;
    font-size: 13px;
    float: right;
    margin-right: 10px;
}

.btnType {
    min-width: 150px;
    margin-bottom: 20px;
}
</style>