<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <!-- ========== card-->
        <div class="card_title">消防总览</div>
        <el-row :gutter="20">
          <el-col :span="24" :xs="24">
            <div class="card_top">
              <el-card v-for="(item, i) in cardTopNum" :key="i" class="box-card" shadow="hover" :body-style="{
                padding: '20px 0',
              }">
                <div class="title">
                  {{ item.title }}
                </div>
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                  ">
                  <p class="num">{{ item.num }}</p>
                  <p class="dan">{{ item.dan }}</p>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
        <!-- ========== card-->
        <div class="card_title">当前值班人员</div>
        <el-row :gutter="20">
          <el-col :span="10">
            <el-carousel :interval="4000" height="200px">
              <el-carousel-item v-for="(item,index) in getDutyData" :key="index">
                <el-card>
                  <div class="card_top">
                    <div>
                      <p>姓名：{{ item.name }}</p>

                      <p>工号：{{ item.workNumber }}</p>

                      <p>电话：{{ item.phone }}</p>
                    </div>
                    <div style="text-align: center">
                      <p class="card_num">{{ item.lncumbency }}</p>
                      <p style="margin: 0">在岗率</p>
                    </div>
                  </div>
                </el-card>
              </el-carousel-item>
            </el-carousel>
          </el-col>
          <el-col :span="14">
            <div style="margin-bottom: 20px;">
              <span>请选择消控室</span>
              <el-select v-model="value" placeholder="请选择" @change="fireControlChange">
                <el-option v-for="item in fireControlList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </div>
            <el-carousel :interval="4000" height="200px">
              <el-carousel-item v-for="(item,index) in FlvData" :key="index">
                <jessibuca-player id="videoElement1" ref="videoPlayer" :videoUrl="videoUrl" :error="videoError"
                  :message="videoError" :height="false" :hasAudio="hasAudio" fluent autoplay live>
                </jessibuca-player>
              </el-carousel-item>
            </el-carousel>
          </el-col>
        </el-row>
        <!-- ========== card-->
        <div class="card_title">消防告警</div>
        <el-card shadow="hover" :body-style="{
          padding: '0px 20px',
        }">
          <el-row :gutter="20" style="display: flex; align-items: center; padding-top: 20px">
            <el-col :span="10" :xs="10">
              <el-date-picker v-model="year" style="width: 20%" value-format="yyyy" format="yyyy" type="year"
                placeholder="选择年" :picker-options="pickerOptions" :clearable="false" @change="dateChange" />
            </el-col>
          </el-row>
          <div style="display: flex; margin-top: 10px">
            <div id="cardTwo" style="width: 70%; height: auto; min-height: 260px" />
            <div>
              <el-table :data="tableData" border style="width: 100%" height="250px">
                <el-table-column prop="alarmTime" label="时间" width="180" />
                <el-table-column prop="location" label="区域" width="120" />
                <el-table-column prop="alarmStatus" label="类型" width="120">
                  <template slot-scope="scope">
                    {{ getTypeById(scope.row) }}
                  </template>
                </el-table-column>
                <el-table-column prop="alarmStatus" label="状态" width="120">
                  <template slot-scope="scope">
                    {{ getNameById(scope.row) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
        <!-- ========== card-->
        <div class="card_title">消防耗材（灭火药剂）</div>
        <el-card shadow="hover" :body-style="{
          padding: '0px 20px',
        }">
          <el-row :gutter="20" style="display: flex; align-items: center; padding-top: 20px">
            <el-col :span="8" :xs="6">
              <!-- <el-radio-group v-model="radioNumber" @change="radioChange">
                <el-radio :label="3">灭火药剂</el-radio>
                <el-radio :label="6">水带</el-radio>
                <el-radio :label="9">耗材3</el-radio>
                <el-radio :label="12">耗材4</el-radio>
              </el-radio-group> -->
              <el-select v-model="type" style="width: 230px" placeholder="请选择灭火剂种类" clearable @change="changeType">
                <el-option v-for="item in dict.type.resource_type" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-col>
          </el-row>
          <div id="cardThree" style="width: 100%; height: auto; min-height: 260px" />
        </el-card>
        <!-- ========== card-->
        <div class="card_title">消防设备</div>
        <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true">
          <el-form-item label="设备类型">
            <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable style="width: 240px">
              <el-option v-for="dict in dict.type.fire_device_type" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="安装位置">
            <el-cascader v-model="queryParams.areaId" :options="options" :props="{
              checkStrictly: true,
              label: 'name',
              value: 'id',
              emitPath: false,
            }" clearable />
          </el-form-item>
          <el-form-item label="设备状态">
            <el-select v-model="queryParams.status" placeholder="请选择设备状态" clearable style="width: 240px">
              <el-option v-for="dict in dict.type.fire_device_status" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="deviceList">
          <el-table-column label="设备类型" :show-overflow-tooltip="true" prop="deviceType" align="center">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.fire_device_type" :value="scope.row.deviceType" :type="1" />
            </template>
          </el-table-column>
          <el-table-column label="设备名称" :show-overflow-tooltip="true" prop="deviceName" align="center" />
          <el-table-column label="所属部门" :show-overflow-tooltip="true" prop="organizationId" align="center">
            <!-- <template slot-scope="scope">
              <dict-tag
                :options="dict.type.whether"
                :value="scope.row.isChecked"
                :type="1"
              />
            </template> --><!-- <template slot-scope="scope">
              <dict-tag
                :options="dict.type.whether"
                :value="scope.row.isChecked"
                :type="1"
              />
            </template> -->
          </el-table-column>
          <el-table-column label="安装位置" :show-overflow-tooltip="true" prop="location" align="center" />
          <el-table-column label="责任人" :show-overflow-tooltip="true" prop="principal" align="center" />
          <el-table-column label="状态" :show-overflow-tooltip="true" prop="status" align="center">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.fire_device_status" :value="scope.row.status" :type="1" />
            </template>
          </el-table-column>
          <el-table-column label="关联摄像头" :show-overflow-tooltip="true" prop="camera" align="center" />
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
          @pagination="getList" />
        <div class="card_title">在岗率</div>
        <el-row :gutter="20" style="margin-top: 30px">
          <el-col :span="6">
            <div id="ecahrts_pie" />
          </el-col>
          <el-col :span="18">
            <el-form ref="queryForm" :model="cahrtsFoem" size="small" :inline="true" style="margin-left: 3%">
              <el-form-item label="值班人员" prop="qy">
                <el-select v-model="cahrtsFoem.inspectedPostUserId" multiple :multiple-limit="2" placeholder="请选择"
                  @change="staffChange">
                  <el-option v-for="item in staffOptions" :key="item.workNumber" :label="item.staffName"
                    :value="item.workNumber" />
                </el-select>
              </el-form-item>
              <el-form-item label="日期" prop="qy">
                <el-radio-group v-model="cahrtsFoem.dateType" @change="typeChange">
                  <el-radio :label="'currentWeek'">本周</el-radio>
                  <el-radio :label="'currentMonth'">本月</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <div id="ecahrts_line" />
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import {
  informationCount,
  getFireAlarm,
  firecontrolResourceCount,
  devicePage,
  getDutyPerson,
  getDutyTime,
  getOnDutyRate,
  tree,
  areaList,
  getDuty,
  fireControl,
  relatedMonitor,
  getVideoStreaming
} from '@/api/fireManagement/dataBoard/index'
import echarts from 'echarts'
import jessibucaPlayer from "./jessibuca.vue";
require('echarts/theme/macarons') // echarts theme
export default {
  name: 'EnergyOver',
  components: {
    jessibucaPlayer,
  },
  dicts: [
    'fire_device_type',
    'firecontrol_device_type',
    'firecontrol_alarm_status',
    'resource_type'
  ],
  data() {
    return {
      hasAudio: false,
      // 表格数据
      tableData: [],
      // 月份事件图
      monthCount: [],
      deviceList: [],
      // 总条数
      total: 0,
      value: '',
      fireControlList: [],
      videoUrl: '',
      cahrtsFoem: {
        inspectedPostUserId: undefined,
        dateType: 'currentWeek',
        dutyMonth: []
      },
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        deviceType: undefined,
        areaId: undefined,
        status: undefined
      },
      options: [],
      staffOptions: [],
      organization: [],
      // 遮罩层
      loading: false,
      // 禁用时间段
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() >= new Date().getTime()
        }
      },
      // 消防总览数据
      cardTopNum: [
        {
          title: '人员配备',
          num: '0',
          num1: '0',
          dan: '位'
        },
        {
          title: '设备正常',
          num: '0',
          num1: '0',
          dan: '台'
        },
        {
          title: '设备离线',
          num: '0',
          num1: '0',
          dan: '台'
        },
        {
          title: '设备故障',
          num: '0',
          num1: '0',
          dan: '台'
        },
        {
          title: '消防耗材',
          num: '0',
          num1: '0',
          dan: '个'
        }
      ],
      // 消防告警查询
      year: new Date().getFullYear().toString(),
      // 灭火剂类型
      type: '',
      // 事件完成情况
      eventCompletion: {
        year: new Date().getFullYear().toString(),
        eventSceneId: '',
        eventTypeId: '',
        handleType: '',
        totalCountList: [],
        completedCountList: []
      },
      // 事件评价
      eventJudge: {
        dateRange: [
          `${this.getDate(new Date().getTime() - 3600 * 1000 * 24 * 30) +
          ' 00:00:00'
          }`,
          `${this.getDate(new Date().getTime()) + ' 00:00:00'}`
        ],
        eventDescribe: [],
        evaluateLevel: [],
        pointData: [
          {
            name: '未评价',
            value: 20,
            percent: '20'
          },
          {
            name: '1星',
            value: 20,
            percent: '20'
          },
          {
            name: '2星',
            value: 20,
            percent: '20'
          },
          {
            name: '3星',
            value: 20,
            percent: '20'
          },
          {
            name: '4星',
            value: 20,
            percent: '20'
          },
          {
            name: '5星',
            value: 20,
            percent: '20'
          }
        ]
      },
      radioNumber: 3,
      radioNumber1: 3,
      radioNumber2: 3,
      radioNumber3: 3,
      valueNumber: [],
      dateNumber: [],
      getDutyData: [],
      FlvData: []
    }
  },
  created() {
    // 消防告警信息初载
    getFireAlarm({ year: this.year }).then((res) => {
      console.log(res);
      this.tableData = res.data.page.records
      // this.monthCount = res.data.map
      this.monthCount[0] = res.data.map[0].one
      this.monthCount[1] = res.data.map[0].too
      this.monthCount[2] = res.data.map[0].three
      this.monthCount[3] = res.data.map[0].four
      this.monthCount[4] = res.data.map[0].five
      this.monthCount[5] = res.data.map[0].six
      this.monthCount[6] = res.data.map[0].seven
      this.monthCount[7] = res.data.map[0].eight
      this.monthCount[8] = res.data.map[0].nine
      this.monthCount[9] = res.data.map[0].ten
      this.monthCount[10] = res.data.map[0].eleven
      this.monthCount[11] = res.data.map[0].twelve
      console.log(this.monthCount, this.tableData, 'ssssss');
      this.rigtCardTwo()
    })
    this.fireControl()
    // this.getDuty()
    // 消防耗材信息初载
    firecontrolResourceCount({ isDelete: 0 }).then((res) => {
      console.log(res, 'www');
      const x = []
      const remain = []
      const amount = []
      res.data.forEach((item) => {
        x.push(item.griddingName)
        remain.push(item.remain)
        amount.push(item.amount)
      })
      this.rigtCardThree(x, remain, amount)
    })
    // 安装位置加载
    areaList().then((res) => {
      this.options = res.data
    })
    // 消防总览
    informationCount().then((res) => {
      console.log(res);
      this.cardTopNum[0].num = res.data.humanCount
      this.cardTopNum[1].num = res.data.normalDeviceCount
      this.cardTopNum[2].num = res.data.offlineDeviceCount
      this.cardTopNum[3].num = res.data.breakdownDeviceCount
      this.cardTopNum[4].num = res.data.resourceCount
    })
    // 消防设备
    this.getList()
  },
  mounted() {
    this.startTime = this.$moment().weekday(1).format('YYYY-MM-DD') //本周一
    this.endTime = this.$moment().weekday(7).format('YYYY-MM-DD') //本周日
    console.log(this.startTime, this.endTime);
    this.getDutyPerson()
  },
  methods: {
    // 值班人员数据获取
    getDuty() {
      getDuty().then(res => {
        if (res.code == 200) {
          this.getDutyData = res.data
        }
        console.log(res, '值班人员');
      })
    },
    // 查询消控室list
    fireControl() {
      fireControl(null).then(res => {
        if (res.code == 200) {
          this.fireControlList = res.data
          this.value = res.data[0].id
        }
        console.log(res, '消防控制');
        this.fireControlChange(this.value)
      })
    },
    getDutyPerson() {
      this.staffOptions = []
      console.log(this.startTime, this.endTime, '值班');
      getDutyPerson({
        startTime: this.startTime,
        endTime: this.endTime,
        isDelete: 0
      }).then((res) => {
        this.cahrtsFoem.inspectedPostUserId = []
        this.cahrtsFoem.staffName = []
        this.staffOptions = res.data
        console.log(res.data, '值班人员');
        res.data.forEach(item => {
          this.cahrtsFoem.inspectedPostUserId.push(item.workNumber)
          this.cahrtsFoem.staffName.push(item.staffName)
        })
        // this.cahrtsFoem.inspectedPostUserId = [
        //   res.data[0].workNumber,
        //   // res.data[1].workNumber
        //   console.log(res.data[0].workNumber,'sss')
        // ]
        // this.cahrtsFoem.staffName = [
        //   res.data[0].staffName,
        //   // res.data[1].staffName
        // ]
        const data = []
        const data1 = []
        this.cahrtsFoem.inspectedPostUserId.forEach((item) => {
        if (this.cahrtsFoem.dateType == 'currentWeek') {
          data.push({ inspectedPostUserId: item, currentWeek: '1' })
          data1.push({ inspectedPostUserId: item, currentWeek: '1' })
        } else {
          data.push({ inspectedPostUserId: item, currentMonth: '1' })
          data1.push({ inspectedPostUserId: item, currentMonth: '1' })
        }
      })
        console.log(data, 'data1');
        getDutyTime(data).then((res) => {
          this.cahrtsFoem.dutyTime = res.data
          const arr = []
          if (res.data[0]) {
            for (let index = 0; index < res.data[0].length; index++) {
              arr.push(parseInt(index + 1) + '号')
            }
            this.cahrtsFoem.dutyMonth = arr
          }
          this.rightEcharts()
        })
        getOnDutyRate(data1).then((res) => {
          this.leftEcharts(res.data)
        })
      })
    },
    fireControlChange(id) {
      console.log(id);
      relatedMonitor({ id: id }).then(res => {
        
        this.FlvData = res.data
        console.log(res,this.FlvData,'测试1');
        res.data.forEach(item => {
          this.getvideoFlv(item.deviceId)
        })
      })
    },
    getvideoFlv(item) {
      getVideoStreaming({
        equipmentIdList: [item],
      }).then((res) => {
        console.log(res.data[0].flvAddress);
        this.$nextTick(() => {
          this.videoUrl = res.data[0].flvAddress;
          this.$refs.videoPlayer.play(this.videoUrl);
        });
      });
    },
    /** 查询消防设备 */
    getList() {
      this.loading = true
      devicePage(this.queryParams).then((response) => {
        // 部门树
        tree().then((res) => {
          this.organization = res.data
          response.data.records.forEach((item) => {
            item.organizationId = this.findName(
              this.organization,
              item.organizationId
            )
          })
          this.deviceList = response.data.records
          this.total = response.data.total
          this.loading = false
          console.log(response.data.records, res, this.loading)
        })
      })
    },
    getNameById(res) {
      console.log(res, this.dict.type.firecontrol_alarm_status, 'Name');
      if (
        res.alarmStatus != undefined &&
        res.alarmStatus != "" &&
        res.alarmStatus != null
      ) {
        return this.dict.type.firecontrol_alarm_status.filter(
          (item) => item.value == res.alarmStatus
        )[0].label;
      }
    },
    getTypeById(res) {
      console.log(res, this.dict.type.firecontrol_device_type, 'Type');
      if (
        res.alarmType != undefined &&
        res.alarmType != "" &&
        res.alarmType != null
      ) {
        return this.dict.type.firecontrol_device_type.filter(
          (item) => item.value == res.alarmType
        )[0].label;
      }
    },
    /** 递归查询部门名称*/
    findName(arr, id) {
      let temp = ''
      for (let index = 0; index < arr.length; index++) {
        if (arr[index].id == id) {
          temp = arr[index].name
          break
        } else {
          if (arr[index].children) { temp = this.findName(arr[index].children, id) }
        }
      }
      return temp
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      (this.queryParams = {
        current: 1,
        size: 10,
        deviceTyp: undefined,
        areaId: undefined,
        status: undefined
      }),
        this.resetForm('queryForm')
      this.handleQuery()
    },
    // 灭火剂类型变更
    changeType(value) {
      if (value) {
        firecontrolResourceCount({ type: value }).then((res) => {
          const x = []
          const remain = []
          const amount = []
          res.data.forEach((item) => {
            x.push(item.griddingName)
            remain.push(item.remain)
            amount.push(item.amount)
          })
          this.rigtCardThree(x, remain, amount)
        })
      } else {
        firecontrolResourceCount({ isDelete: 0 }).then((res) => {
          const x = []
          const remain = []
          const amount = []
          res.data.forEach((item) => {
            x.push(item.griddingName)
            remain.push(item.remain)
            amount.push(item.amount)
          })
          this.rigtCardThree(x, remain, amount)
        })
      }
    },
    radioChange(value) {
      this.eventTimesCount = {
        year: new Date().getFullYear().toString(),
        eventSceneId: [],
        eventTypeId: [],
        handleType: [],
        countByYear1: [],
        countByYear2: []
      }
    },
    dateChange(value) {
      getFireAlarm({ year: value }).then((res) => {
        console.log(res);
        this.tableData = res.data.page.records
        // this.monthCount = res.data.map
        this.monthCount[0] = res.data.map[0].one
        this.monthCount[1] = res.data.map[0].too
        this.monthCount[2] = res.data.map[0].three
        this.monthCount[3] = res.data.map[0].four
        this.monthCount[4] = res.data.map[0].five
        this.monthCount[5] = res.data.map[0].six
        this.monthCount[6] = res.data.map[0].seven
        this.monthCount[7] = res.data.map[0].eight
        this.monthCount[8] = res.data.map[0].nine
        this.monthCount[9] = res.data.map[0].ten
        this.monthCount[10] = res.data.map[0].eleven
        this.monthCount[11] = res.data.map[0].twelve
        console.log(this.monthCount, this.tableData, 'ssssss');
        this.rigtCardTwo()
      })
    },
    // 视频流播放相关
    videoError: function (e) {
      console.log("播放器错误：" + JSON.stringify(e));
    },
    // 初始化时间
    getDate(date) {
      // date是传过来的时间戳，注意需为13位，10位需*1000
      // 也可以不传,获取的就是当前时间
      var time = new Date(date)
      var year = time.getFullYear() // 年
      var month = ('0' + (time.getMonth() + 1)).slice(-2) // 月
      var day = ('0' + time.getDate()).slice(-2) // 日
      var mydate = year + '-' + month + '-' + day
      return mydate
    },
    /** 人员变更 */
    staffChange(value) {
      this.cahrtsFoem.staffName = []
      value.forEach((item) => {
        this.staffOptions.forEach((element) => {
          if (element.workNumber == item) {
            this.cahrtsFoem.staffName.push(element.staffName)
          }
        })
      })
      const data = []
      const data1 = []
      value.forEach((item) => {
        if (this.cahrtsFoem.dateType == 'currentWeek') {
          data.push({ inspectedPostUserId: item, currentWeek: '1' })
          data1.push({ inspectedPostUserId: item })
        } else {
          data.push({ inspectedPostUserId: item, currentMonth: '1' })
          data1.push({ inspectedPostUserId: item })
        }
      })
      getDutyTime(data).then((res) => {
        this.cahrtsFoem.dutyTime = res.data
        this.rightEcharts()
      })
      getOnDutyRate(data1).then((res) => {
        this.leftEcharts(res.data)
      })
    },
    /** 日期维度变更*/
    typeChange(value) {
      this.cahrtsFoem.dateType = value
      if (value == 'currentWeek') {
        this.startTime = this.$moment().weekday(1).format('YYYY-MM-DD') //本周一
        this.endTime = this.$moment().weekday(7).format('YYYY-MM-DD') //本周日
      } else {
        this.startTime = this.$moment().startOf('month').format('YYYY-MM-DD');
        this.endTime = this.$moment().endOf('month').format('YYYY-MM-DD');
      }
      // console.log(data, 'data');
      this.getDutyPerson()
    },
    rigtCardTwo() {
      var chartDom = document.getElementById('cardTwo')
      var myChart = echarts.init(chartDom)
      var xData = (function () {
        var data = []
        for (var i = 1; i < 13; i++) {
          data.push(i + '月')
        }
        return data
      })()

      var option = {
        backgroundColor: '#fff',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            textStyle: {
              color: '#fff'
            }
          }
        },
        grid: {
          borderWidth: 0,
          top: 20,
          bottom: 60,
          right: 20,
          left: 50,
          textStyle: {
            color: '#fff'
          }
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            axisLine: {
              lineStyle: {
                color: '#000'
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            data: xData
          }
        ],

        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: '#efefef'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#000'
              }
            },
            minInterval: 1
          }
        ],
        series: [
          {
            name: '件数',
            type: 'line',
            symbolSize: 10,
            symbol: 'circle',
            smooth: true,
            itemStyle: {
              color: '#6f7de3'
            },

            data: this.monthCount
          }
        ]
      }
      myChart.setOption(option)
    },
    rigtCardThree(x, remain, amount) {
      var chartDom = document.getElementById('cardThree')
      var myChart = echarts.init(chartDom)
      var xData = x
      var option = {
        backgroundColor: '#fff',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            textStyle: {
              color: '#fff'
            }
          }
        },
        grid: {
          borderWidth: 0,
          top: 20,
          bottom: 60,
          right: 20,
          left: 50,
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          x: 'center',
          top: '90%',
          textStyle: {
            color: '#000'
          },
          data: ['剩余', '总量']
        },

        calculable: true,
        xAxis: [
          {
            type: 'category',
            axisLine: {
              lineStyle: {
                color: '#000'
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            data: xData
          }
        ],

        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: '#efefef'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#000'
              }
            }
          }
        ],
        series: [
          {
            name: '剩余',
            type: 'bar',
            itemStyle: {
              color: '#6f7de3'
            },
            barWidth: 30, // 柱图宽度
            data: remain
          },
          {
            name: '总量',
            type: 'bar',
            barWidth: 30, // 柱图宽度
            itemStyle: {
              color: 'rgba(17, 122, 242, 1)'
            },

            data: amount
          }
        ]
      }
      myChart.setOption(option)
    },
    cardRakingTwo() {
      var chartDom = document.getElementById('card_raking_two')
      var myChart = echarts.init(chartDom)
      const arr3 = [19, 29, 39, 81, 29, 39]
      var option = {
        backgroundColor: '#fff',
        barWidth: 15,
        grid: {
          borderWidth: 0,
          top: 20,
          bottom: 30,
          right: 55,
          left: 180
        },
        xAxis: {
          type: 'value',
          splitLine: {
            lineStyle: {
              color: '#efefef',
              type: 'dashed'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D9D9D9'
            }
          },
          axisTick: {
            show: false
          },

          axisLabel: {
            //  改变x轴字体颜色和大小
            textStyle: {
              color: '#000',
              fontSize: 12
            }
          }
        },
        yAxis: [
          {
            type: 'category',
            data: this.eventSolveTime.eventDescribe,
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#D9D9D9'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#000',
                fontSize: 12
              }
            }
          },
          {
            type: 'category',
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            show: true,

            data: arr3
          }
        ],
        series: [
          {
            type: 'bar',
            name: '',
            itemStyle: {
              normal: {
                color: 'rgba(60, 144, 247, 1)'
              }
            },
            data: this.eventSolveTime.solveTime,
            label: {
              normal: {
                show: true,
                position: 'right'
              },
              formatter: '{@value}'
            }
          }
        ]
      }
      myChart.setOption(option)
    },
    appraiseOne() {
      var chartDom = document.getElementById('appraise_one')
      var myChart = echarts.init(chartDom)
      var data = this.eventJudge.pointData

      function array2obj(array, key) {
        var resObj = {}
        for (var i = 0; i < array.length; i++) {
          resObj[array[i][key]] = array[i]
        }
        return resObj
      }
      const objData = array2obj(data, 'name')

      var option = {
        color: [
          'rgba(60, 144, 247, 1)',
          'rgba(85, 191, 192, 1)',
          'rgba(94, 190, 103, 1)',
          'rgba(244, 205, 73, 1)',
          'rgba(224, 86, 103, 1)',
          'rgba(124, 75, 216, 1)'
        ],
        backgroundColor: '#fff',
        grid: {
          bottom: 10,
          left: 10,
          right: '10%'
        },
        legend: {
          orient: 'vertical',
          top: 'middle',
          left: '50%',
          textStyle: {
            color: '#000',
            fontSize: 12
          },
          icon: 'circle',
          data: data,
          formatter: function (name, index) {
            return (
              name +
              '   ' +
              objData[name].percent +
              '%   ' +
              objData[name].value
            )
          }
        },
        series: [
          // 主要展示层的
          {
            radius: '80%',
            center: ['29%', '50%'],
            type: 'pie',

            name: '',
            data: data,
            itemStyle: {
              normal: {
                label: {
                  show: false
                },
                borderColor: '#fff',
                borderWidth: 4,
                labelLine: { show: false }
              }
            }
          }
        ]
      }
      myChart.setOption(option)
    },
    appraiseTwo() {
      var chartDom = document.getElementById('appraise_two')
      var myChart = echarts.init(chartDom)

      var option = {
        backgroundColor: '#fff',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            textStyle: {
              color: '#fff'
            }
          }
        },
        grid: {
          borderWidth: 0,
          top: 20,
          bottom: 60,
          right: 20,
          left: 50,
          textStyle: {
            color: '#fff'
          }
        },

        calculable: true,
        xAxis: {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#000'
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          data: this.eventJudge.eventDescribe,
          axisLabel: {
            interval: 0
            // rotate: -40,
          }
        },

        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#efefef'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#000'
            }
          }
        },
        series: [
          {
            name: '总数',
            type: 'bar',
            itemStyle: {
              color: '#6f7de3'
            },
            barWidth: 30, // 柱图宽度
            data: this.eventJudge.evaluateLevel
          }
        ]
      }
      myChart.setOption(option)
    },
    leftEcharts(data) {
      var myChart = echarts.init(document.getElementById('ecahrts_pie'))
      var dataArr = data
      var option = {
        tooltip: {
          formatter: '{a} <br/>{b} : {c}%'
        },
        title: {
          show: true,
          x: 'center',
          y: '68%',
          text: '在岗率', // 幸运值取代码置于值于此处
          // subtext: '幸运指数',
          textStyle: {
            fontSize: 14,
            fontWeight: 'bolder',
            fontStyle: 'normal',
            color: '#31F3FF'
          }
        },
        series: [
          {
            name: '外部线',
            type: 'gauge',
            radius: '95%', // 动态
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                color: [
                  [1, '#31F3FF'] // 动态
                ],
                width: 1
              }
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            title: {
              // 标题
              show: false
            }
          },
          {
            name: '外部刻度',
            type: 'gauge',
            radius: '99%',
            min: 0, // 最小刻度
            max: 100, // 最大刻度
            splitNumber: 10, // 刻度数量
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              show: false,
              lineStyle: {
                color: [[1, 'rgba(0,0,0,0)']]
              }
            }, // 仪表盘轴线
            axisLabel: {
              show: true,
              color: '#31F3FF',
              fontSize: 10, // 动态
              distance: -20 // 动态
            }, // 刻度标签。
            axisTick: {
              show: false
            }, // 刻度样式
            splitLine: {
              show: false
            }
          },
          {
            name: '内部宽线条',
            type: 'gauge',
            radius: '73%',
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                color: [[1, '#122B3C']],
                width: 20
              }
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            title: {
              show: false
            }
          },
          {
            name: '内部细线条',
            type: 'gauge',
            radius: '70%',
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                color: [[1, '#122B3C']],
                width: 3
              }
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            title: {
              show: false
            }
          },
          {
            name: '间隔条形',
            type: 'gauge',
            radius: '73%',
            z: 4,
            splitNumber: 35,
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                opacity: 0
              }
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: true,
              length: 20,
              splitNumber: 1,
              lineStyle: {
                color: '#122B3C',
                width: 1
              }
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            title: {
              show: false
            }
          },
          {
            name: '数据',
            type: 'gauge',
            radius: '72.5%',
            z: 3,
            startAngle: 225,
            max: 100,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                color: [
                  [dataArr / 100, '#31F3FF'], // 动态
                  [1, '#185363']
                ],
                width: 20
              }
            },
            tooltip: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              formatter: function (params) {
                return dataArr + '%'
              },
              show: true,
              fontWeight: 'bold',
              fontSize: 20
            },
            pointer: {
              show: true,
              width: 3
            },
            data: [
              {
                name: '',
                value: dataArr
              }
            ]
          },
          // 内圆
          {
            name: '内圆环',
            type: 'pie',
            radius: ['4%', '2%'],
            hoverAnimation: false,
            tooltip: {
              show: false
            },
            cursor: 'default',
            labelLine: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              color: '#122B3C'
            },
            animation: false,
            data: [1]
          },
          // 内圆
          {
            name: '内圆环2',
            type: 'pie',
            radius: '2%',
            hoverAnimation: false,
            cursor: 'default',
            tooltip: {
              show: false
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              color: '#31F3FF'
            },
            animation: false,
            data: [1]
          }
        ]
      }
      myChart.setOption(option)
    },
    rightEcharts() {
      var myChart = echarts.init(document.getElementById('ecahrts_line'))
      var option = {
        grid: {
          top: '5%',
          left: '3%',
          right: '4%',
          bottom: '13%',
          containLabel: true
        },
        backgroundColor: '#fff', // 画布背景

        legend: {
          icon: 'rect',
          bottom: 10,
          textStyle: {
            color: '#000',
            data: ['张珊珊', '李师师']
          },

          itemWidth: 15, // 设置宽度
          itemHeight: 15 // 设置高度
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          // x轴
          type: 'category',
          boundaryGap: false, // 坐标轴两边留白策略
          data:
            this.cahrtsFoem.dateType == 'currentWeek'
              ? [
                '星期一',
                '星期二',
                '星期三',
                '星期四',
                '星期五',
                '星期六',
                '星期日'
              ]
              : this.cahrtsFoem.dutyMonth,
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: '#000'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#000'
            }
          }
        },
        yAxis: {
          // y轴
          min: 0,
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: '#000'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#fff'
            }
          }
        },
        series: [
          {
            name: this.cahrtsFoem.staffName[0],
            data: this.cahrtsFoem.dutyTime[0],
            type: 'line',
            smooth: false, // 折线是否平滑
            areaStyle: {
              opacity: 0
            },
            itemStyle: {
              normal: {
                color: '#197CD8', // 小圆点的颜色
                lineStyle: {
                  color: '#197CD8' // 折线的颜色
                }
              }
            },
            lineStyle:{
              normal:{
                width:3
              }
            }
          },
          {
            name: this.cahrtsFoem.staffName[1],
            data: this.cahrtsFoem.dutyTime[1],
            type: 'line',
            smooth: false, // 是否平滑
            areaStyle: {
              opacity: 0
            },
            itemStyle: {
              normal: {
                color: '#2B9F50', // 小圆点的颜色
                lineStyle: {
                  color: '#2B9F50' // 折线的颜色
                }
              }
            },
            lineStyle:{
              normal:{
                width:3
              }
            }
          }
        ]
      }
      myChart.setOption(option)
    }
  }
}
</script>
<style lang="scss" scoped>
.card_top {
  display: flex;
  justify-content: space-between;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}

.card_num {
  color: rgba(42, 130, 228, 1);
  font-size: 30px;
  font-weight: bold;
  margin: 0;
  padding-left: 10px;
}

.box-card {
  width: 18%;

  .title {
    width: 100%;
    text-align: left;
    font-size: 18px;
    font-weight: bold;
    color: rgba(128, 128, 128, 1);
    padding-left: 20px;
  }

  .num {
    text-align: center;
    color: rgba(42, 130, 228, 1);
    font-size: 36px;
    font-weight: bold;
    margin: 0;
  }

  .dan {
    color: rgba(128, 128, 128, 1);
    font-size: 18px;
    font-weight: bold;
    margin: 0;
    padding: 0px 20px 0 10px;
  }

  .card_bom {
    text-align: right;
    font-size: 12px;
    color: rgba(80, 80, 80, 1);
    padding-right: 20px;

    p {
      margin: 0;
      padding-top: 10px;
    }
  }
}

.card_title {
  margin: 20px 0 20px 0px;
  padding: 3px 0 3px 8px;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  border-left: 8px solid rgba(42, 130, 228, 1);
  font-weight: bold;
}

.el-row {
  position: inherit !important;
}

.card_con_title {
  color: rgba(128, 128, 128, 1);
  font-size: 16px;
  font-weight: bold;
}

.focus {
  color: rgba(42, 130, 228, 1);
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
}

#ecahrts_pie {
  width: 100%;
  height: 240px;
  padding-top: 35px;
}

#ecahrts_line {
  width: 100%;
  height: 220px;
}


.el-carousel__item:nth-child(2n) {
  background-color: #ffffff;
}

::v-deep .el-carousel__button {
  background-color: gray !important;
}

::v-deep .jessibuca-container {
  height: 280px !important;
}
</style>
<style></style>
