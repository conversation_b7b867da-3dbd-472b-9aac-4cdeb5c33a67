<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:role:add']"
          >新增消防器材</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="roleList">
      <el-table-column type="index" width="50">
        <template slot-scope="scope">
          <span>{{
            (queryParams.current - 1) * queryParams.size + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="器材类型"
        :show-overflow-tooltip="true"
        prop="equipmentType"
        align="center"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.equipment_type"
            :value="scope.row.equipmentType"
            :type="1"
          />
        </template>
      </el-table-column>
      <el-table-column label="规格" prop="equipmentUnit" align="center">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.equipment_unit"
            :value="scope.row.equipmentUnit"
            :type="1"
          />
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="amount" align="center" />
      <el-table-column label="创建人" prop="createUser" align="center" />
      <el-table-column label="创建日期" prop="createTime" align="center" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:role:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:role:edit']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="器材类型" prop="equipmentType">
          <el-select
            v-model="form.equipmentType"
            placeholder="器材类型"
            clearable
          >
            <el-option
              v-for="dict in dict.type.equipment_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="规格" prop="equipmentUnit">
          <el-select v-model="form.equipmentUnit" placeholder="规格" clearable>
            <el-option
              v-for="dict in dict.type.equipment_unit"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            /> </el-select
        ></el-form-item>

        <el-form-item label="数量" prop="amount">
          <el-input
            v-model.number="form.amount"
            placeholder="请输入数量"
            style="width: 300px"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  pageEquipment,
  saveEquipment,
  updateEquipment,
} from "@/api/fireManagement/resourceManagement/fireStation/index";
export default {
  name: "Equipment",
  dicts: ["equipment_type", "equipment_unit"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      roleList: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        stationId: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        equipmentType: [
          { required: true, message: "请选择器材类型", trigger: "change" },
        ],
        equipmentUnit: [
          { required: true, message: "请选择规格", trigger: "change" },
        ],
        amount: [{ required: true, message: "请输入数量", trigger: "blur" }],
      },
      stationId: undefined,
    };
  },
  created() {
    this.getList();
    this.queryParams.stationId = this.$route.query.deviceId;
    this.stationId = this.$route.query.deviceId;
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      pageEquipment(this.queryParams).then((response) => {
        this.roleList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        sex: null,
        phone: undefined,
        stationId: this.stationId,
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增消防器材";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "修改消防器材";
      this.form = row;
      console.log(this.form);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            updateEquipment(this.form).then((response) => {
              console.log(response);
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            saveEquipment(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return updateEquipment({ id: row.id, isDeleted: 1 });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.dia_one {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  margin-bottom: 20px;
}
</style>