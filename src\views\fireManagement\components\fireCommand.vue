<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>消防指挥</span>
      </div>
      <div class="topBottom">
        <el-card
          v-for="(item, index) in btnArr"
          :key="index"
          class="box-card titleCard"
          shadow="hover"
          @click.native="cardClick(item.code)"
        >
          <p style="font-weight: 600">{{ item.title }}</p>
          <div>{{ item.content }}</div>
        </el-card>
      </div>
    </el-card>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>消防指挥操作记录</span>
      </div>
      <el-table
        :cell-style="{ padding: '0px' }"
        :row-style="{ height: '48px' }"
        v-loading="loading"
        :data="tableData"
      >
        <el-table-column type="index" width="50">
          <template slot-scope="scope">
            <span>{{
              (queryParams.current - 1) * queryParams.size + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="执行操作"
          align="center"
          prop="executeOperation"
        >
          <template slot-scope="scope">
            <div>
              {{
                dict.type.firecontrol_execute_operation.find(
                  (ele) => ele.value == scope.row.executeOperation
                ).label || ""
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="执行人" align="center" prop="executePerson" />
        <el-table-column label="执行时间" align="center" prop="executeTime" />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.current"
        :limit.sync="queryParams.size"
        @pagination="getList"
      />
    </el-card>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="720px">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="活动名称" prop="name">
          <el-input v-model="ruleForm.name"></el-input>
        </el-form-item>
        <el-form-item label="活动区域" prop="region">
          <el-select v-model="ruleForm.region" placeholder="请选择活动区域">
            <el-option label="区域一" value="shanghai"></el-option>
            <el-option label="区域二" value="beijing"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="活动时间" required>
          <el-col :span="11">
            <el-form-item prop="date1">
              <el-date-picker
                type="date"
                placeholder="选择日期"
                v-model="ruleForm.date1"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="2">-</el-col>
          <el-col :span="11">
            <el-form-item prop="date2">
              <el-time-picker
                placeholder="选择时间"
                v-model="ruleForm.date2"
                style="width: 100%"
              ></el-time-picker>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="活动形式" prop="desc">
          <el-input type="textarea" v-model="ruleForm.desc"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
} from "@/api/fireManagement/resourceManagement/fireCommand/index";
export default {
  name: "policyConfiguration",
  dicts: ["firecontrol_execute_operation"],
  data() {
    return {
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableData: null,
      // 是否显示弹出层
      open: false,
      status: true,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
      },
      btnArr: [
        {
          title: "门禁一键控制",
          content: "一键打开全部逃生门禁,并关闭电梯进口门禁",
          code: "4013701",
        },
        {
          title: "消防发布告警",
          content: "一键进行园区广播告警",
          code: "4013702",
        },
        {
          title: "拨打消防119",
          content: "一键进行119消防报警",
          code: "4013703",
        },
        {
          title: "着火视频推送",
          content: "一键将着火区域的视频推送到安保人员的移动端",
          code: "4013704",
        },
        {
          title: "关闭区域电源",
          content: "手动关闭区域电源",
          code: "4013705",
        },
      ],
      // 发布告警弹框
      title: "",
      dialogVisible: false,
      ruleForm: {},
      rules: {},
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  computed: {},
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        console.log(response, "response");
        if (response.data != null) {
          this.tableData = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    /** 一键操作按钮 */
    cardClick(code) {
      var message = this.dict.type.firecontrol_execute_operation.find(
        (ele) => ele.value == code
      ).label;
      this.$modal
        .confirm('是否确认执行"' + message + '"的操作？')
        .then(function () {
          return save({ executeOperation: code });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("操作成功！");
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.open_close {
  font-size: 14px;
  color: #448ef7;
  cursor: pointer;
}
.title_rule {
  font-size: 17px;
  font-weight: 400;
  color: #000;
  margin: 10px 0 10px 0;
}
.body {
  width: 100%;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

::v-deep.box-card .topBottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}

.card_wrap {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c_item {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 300px;
  margin: 0 0 0 0;
  width: calc(20% - 2px);
  min-width: calc(20% - 2px);
  max-width: calc(20% - 2px);
}

.c_item :nth-child(5n) {
  margin-right: 0;
}

.el-card.is-always-shadow {
  -webkit-box-shadow: inset 0 -1px 0 #ebebeb;
  box-shadow: inset 0 -1px 0 #ebebeb;
}

.el-pagination.is-background .el-pager li:not(.disabled) {
  border: 1px solid #ccc;
  border-radius: 2px;
  background-color: #fff;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  border: 1px solid #188cff;
  border-radius: 2px;
  background-color: #fff;
  color: #188cff;
}

.unregisteredText {
  font-size: 14px;
}

.el-radio-button__inner {
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  letter-spacing: 0.04em;
  color: #666;
}

::v-deep.el-table .el-table__fixed-header-wrapper th,
::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.el-table--medium .el-table__cell {
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #666;
}

.buttonAdd {
  width: 88px;
  height: 32px;
  background: #1a8cff;
  border-radius: 2px;
  float: right;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 5px 8px;
  gap: 10px;
}

::v-deep .el-card__header {
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 26px;
  color: #333;
}

.labelStyle {
  width: 60px;
  height: 14px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  letter-spacing: 0.04em;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -moz-text-align-last: justify;
  text-align-last: justify;
  margin: auto;
}

.button {
  width: 88px;
  height: 32px;
  border-radius: 2px;
}

.el-input--medium .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.titleCard {
  width: 18%;
  margin-top: 10px;
  cursor: pointer;
}
</style>