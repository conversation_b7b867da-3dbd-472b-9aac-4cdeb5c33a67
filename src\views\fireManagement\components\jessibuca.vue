<template>
  <div id="jessibuca" style="width: auto; height: 300px">
    <div
      :id="id"
      :ref="id"
      style="width: 100%; height: 300px; background-color: #000"
      @dblclick="fullscreenSwich"
    ></div>
  </div>
</template>

<script>
export default {
  name: "jessibuca",
  data() {
    return {
      jessibuca: null,
      playing: false,
      isNotMute: false,
      quieting: false,
      fullscreen: false,
      loaded: false, // mute
      speed: 0,
      performance: "", // 工作情况
      kBps: 0,
      btnDom: null,
      videoInfo: null,
      volume: 1,
      rotate: 0,
      vod: true, // 点播
      forceNoOffscreen: true,
    };
  },
  props: ["videoUrl", "error", "hasAudio", "height", "id"],
  mounted() {
    window.onerror = (msg) => {
      // console.error(msg)
    };
    let paramUrl = decodeURIComponent(this.$route.params.url);
    this.$nextTick(() => {
      let dom = document.getElementById(this.id);
      dom.style.height = (9 / 16) * dom.clientWidth + "px";
      if (typeof this.videoUrl == "undefined") {
        this.videoUrl = paramUrl;
      }
      //  this.btnDom = document.getElementById("buttonsBox");
      console.log("初始化时的地址为: " + this.videoUrl);
      this.play(this.videoUrl);
    });
  },
  created() {
    console.log(this.videoUrl);
  },
  methods: {
    create() {
      let options = {};

      this.jessibuca = new JessibucaPro({
        container: "#"+this.id,
        decoder: "./decoder-pro.js",
        videoBuffer: 0.2, // 缓存时长
        isResize: false,
        text: "",
        loadingText: "加载中",
        debug: true,
        isMulti: true,
        useMSE: true,
        useSIMD: true,
        useWCS: true,
        hasAudio: false,
        useVideoRender: true,
        controlAutoHide: true,
        showBandwidth: true, // 显示网速
        showPerformance: false,
        operateBtns: {
          fullscreen: true,
          screenshot: true,
          play: true,
          audio: true,
        },
        watermarkConfig: {
          text: {
            content: "摄像头",
          },
          right: 10,
          top: 10,
        },
      });
      this.jessibuca.on("fullscreen", (flag) => {
        console.log("is fullscreen", index, flag);
      });
    },
    playBtnClick: function (event) {
      this.play(this.videoUrl);
    },
    play: function (url) {
      console.log(url);
      if (this.jessibuca) {
        this.destroy();
      }
      this.create();
      this.jessibuca.on("play", () => {
        this.playing = true;
        this.loaded = true;
        this.quieting = this.jessibuca.quieting;
      });
      if (this.jessibuca.hasLoaded()) {
        this.jessibuca.play(url);
      } else {
        this.jessibuca.on("load", () => {
          console.log("load 播放");
          this.jessibuca.play(url);
        });
      }
    },
    pause: function () {
      if (this.jessibuca) {
        this.jessibuca.pause();
      }
      this.playing = false;
      this.err = "";
      this.performance = "";
    },
    destroy: function () {
      if (this.jessibuca) {
        this.jessibuca.destroy();
      }
      // if (document.getElementById("buttonsBox") == null) {
      //   document.getElementById("container").appendChild(this.btnDom)
      // }
      this.jessibuca = null;
      this.playing = false;
      this.err = "";
      this.performance = "";
    },
    eventcallbacK: function (type, message) {
      console.log("player 事件回调");
      console.log(type);
      console.log(message);
    },
    fullscreenSwich: function () {
      let isFull = this.isFullscreen();
      this.jessibuca.setFullscreen(!isFull);
      this.fullscreen = !isFull;
    },
    isFullscreen: function () {
      return (
        document.fullscreenElement ||
        document.msFullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        false
      );
    },
  },
  destroyed() {
    if (this.jessibuca) {
      this.jessibuca.destroy();
    }
    this.playing = false;
    this.loaded = false;
    this.performance = "";
  },
};
</script>

<style>
.buttons-box {
  width: 100%;
  height: 28px;
  background-color: rgba(43, 51, 63, 0.7);
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  left: 0;
  bottom: 0;
  user-select: none;
  z-index: 10;
}
.jessibuca-btn {
  width: 20px;
  color: rgb(255, 255, 255);
  line-height: 27px;
  margin: 0px 10px;
  padding: 0px 2px;
  cursor: pointer;
  text-align: center;
  font-size: 0.8rem !important;
}
.buttons-box-right {
  position: absolute;
  right: 0;
}

</style>