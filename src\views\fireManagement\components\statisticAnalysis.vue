<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="24" :xs="24">
                <!-- ========== card-->
                <el-card style="width: 100%; text-align: right;margin-bottom: 20px;">
                    <el-button type="primary" :plain="plain1" @click="timeSwitch('aWeek')">最近一周</el-button>
                    <el-button type="primary" :plain="plain2" @click="timeSwitch('oneMonth')">最近30天</el-button>
                    <el-button type="primary" :plain="plain3" @click="timeSwitch('sixMonth')">最近半年</el-button>
                    <el-button type="primary" :plain="plain4" @click="timeSwitch('aYear')">最近一年</el-button>
                </el-card>
                <el-row :gutter="20">
                    <el-col :span="24" :xs="24">
                        <div class="card_top">
                            <el-card v-for="(item, i) in cardTopNum" :key="i" :class="['box-card', item.color]"
                                shadow="hover" :body-style="{
                                    padding: '20px 0',
                                }">
                                <div style="display: flex; justify-content: space-evenly; align-items: center;">
                                    <img width="72px" height="72px" :src="item.url" alt="">
                                    <div>
                                        <div class="title">
                                            {{ item.title }}
                                        </div>
                                        <div style="">
                                            <p class="num">{{ item.num }}</p>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </el-col>
                </el-row>
                <!-- ========== card-->
                <el-card shadow="hover" :body-style="{
                    padding: '0px 20px',
                }">
                    <el-row :gutter="20" style="display: flex; align-items: center; padding-top: 20px">
                        <el-col :span="12" :xs="10">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <span>报警趋势</span>
                                </div>
                                <div>
                                    <el-button type="primary" :plain="plain5" @click="policeTime('aYear')">年</el-button>
                                    <el-button type="primary" :plain="plain6" @click="policeTime('oneMonth')">月</el-button>
                                </div>
                            </div>
                            <div id="cardTwo" style="width: 100%; height: auto; min-height: 260px" />
                        </el-col>
                        <el-col :span="12">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <span>区域报警数量</span>
                                </div>
                                <div>
                                    <el-button type="primary" :plain="plain7" @click="regionTime('aYear')">年</el-button>
                                    <el-button type="primary" :plain="plain8" @click="regionTime('oneMonth')">月</el-button>
                                </div>
                            </div>
                            <div id="areaAlarm" style="width: 100%; height: auto; min-height: 260px" />
                        </el-col>
                    </el-row>
                </el-card>
                <div class="card_bottom">
                    <div class="card_bottom_title">设备统计</div>
                    <div style="display: flex; justify-content: space-evenly; align-items: center;">
                        <el-card v-for="(item, i) in cardBottomNum" :key="i" class="'box-card'" shadow="hover" :body-style="{
                            padding: '20px 0',
                        }">
                            <div style="text-align: center;margin: 20px;">
                                <svg-icon :icon-class="item.url" class-name="card-panel-icon" />
                                <div>
                                    <div class="title">
                                        {{ item.title }}
                                    </div>
                                    <div style="">
                                        <p class="num">{{ item.num }}</p>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<script>
import {
    getStatistics,
} from '@/api/fireManagement/statisticAnalysis/index'
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
export default {
    name: 'EnergyOver',
    dicts: [
        'fire_device_type',
        'firecontrol_device_type',
        'firecontrol_alarm_status',
        'resource_type'
    ],
    data() {
        return {
            // 月份事件图
            monthCount: [],
            // 查询参数
            timeFlag: 'aWeek',
            monthGraphTimeFlag: 'oneMonth',
            areaGraphTimeFlag: 'oneMonth',
            regionLabelData: [],
            regionData: [],
            plain1: false,
            plain2: true,
            plain3: true,
            plain4: true,
            plain5: true,
            plain6: false,
            plain7: true,
            plain8: false,
            monthData: [],
            deviceList: [],
            // 遮罩层
            loading: false,
            // 禁用时间段
            pickerOptions: {
                disabledDate(v) {
                    return v.getTime() >= new Date().getTime()
                }
            },
            // 消防总览数据
            cardTopNum: [
                {
                    title: '报警总数',
                    num: '0',
                    color: 'blue',
                    url: require('../../../assets/images/policeTotal.png')
                },
                {
                    title: '隐患总数',
                    num: '0',
                    color: 'red',
                    url: require('../../../assets/images/hiddenDangerTotal.png')
                },
                {
                    title: '报警处理率',
                    num: '0',
                    color: 'purple',
                    url: require('../../../assets/images/policePercentage.png')
                },
                {
                    title: '隐患修复率',
                    num: '0',
                    color: 'yellow',
                    url: require('../../../assets/images/hiddenDangerPercentage.png')
                }
            ],
            cardBottomNum: [
                {
                    title: '设备总数',
                    num: '0',
                    url: "deviceTotal"
                },
                {
                    title: '在线总数',
                    num: '0',
                    url: "onLineTotal"
                },
                {
                    title: '离线总数',
                    num: '0',
                    url: "offlineTotal"
                },
                {
                    title: '硬件正常总数',
                    num: '0',
                    url: "normalTotal"
                },
                {
                    title: '硬件故障总数',
                    num: '0',
                    url: "faultTotal"
                },
                {
                    title: '屏蔽总数',
                    num: '0',
                    url: "parcloseTotal"
                }
            ],
            // 消防告警查询
            year: new Date().getFullYear().toString(),
        }
    },
    created() {
        this.getStatistics()
    },
    mounted() { },
    methods: {
        timeSwitch(res) {
            this.switchStats(res)
            this.timeFlag = res
            this.getStatistics()
        },
        switchStats(res) {
            switch (res) {
                case 'aWeek':
                    this.plain1 = false
                    this.plain2 = true
                    this.plain3 = true
                    this.plain4 = true
                    break;
                case 'oneMonth':
                    this.plain1 = true
                    this.plain2 = false
                    this.plain3 = true
                    this.plain4 = true
                    break;
                case 'sixMonth':
                    this.plain1 = true
                    this.plain2 = true
                    this.plain3 = false
                    this.plain4 = true
                    break;
                case 'aYear':
                    this.plain1 = true
                    this.plain2 = true
                    this.plain3 = true
                    this.plain4 = false
                    break;
                default:
                    break;
            }
        },
        policeTime(res) {
            this.monthGraphTimeFlag = res
            if (res == 'oneMonth') {
                this.plain5 = true
                this.plain6 = false
            } else {
                this.plain5 = false
                this.plain6 = true
            }
            this.getStatistics()
        },
        regionTime(res) {
            this.areaGraphTimeFlag = res
            if (res == 'oneMonth') {
                this.plain7 = true
                this.plain8 = false
            } else {
                this.plain7 = false
                this.plain8 = true
            }
            this.getStatistics()
        },
        getStatistics(value) {
            getStatistics(
                {
                    timeFlag: this.timeFlag,
                    monthGraphTimeFlag: this.monthGraphTimeFlag,
                    areaGraphTimeFlag: this.areaGraphTimeFlag
                }).then((res) => {
                    if (res.code == 200) {
                        console.log(res);
                        this.monthData = []
                        this.monthCount = []
                        this.regionLabelData = []
                        this.regionData = []
                        this.cardTopNum[0].num = res.data.alarmCount
                        this.cardTopNum[1].num = res.data.hiddenDangerCount
                        this.cardTopNum[2].num = res.data.alarmHandleRate
                        this.cardTopNum[3].num = res.data.hiddenDangerHandleRate
                        // 设备统计
                        this.cardBottomNum[0].num = res.data.deviceCount
                        this.cardBottomNum[1].num = res.data.onlineCount
                        this.cardBottomNum[2].num = res.data.offlineCount
                        this.cardBottomNum[3].num = res.data.normalCount
                        this.cardBottomNum[4].num = res.data.faultCount
                        this.cardBottomNum[5].num = res.data.shieldCount
                        res.data.alarmTrendMapList.forEach(item => {
                            if (this.monthGraphTimeFlag == 'oneMonth') {
                                this.monthData.push(item.day)
                            } else {
                                this.monthData.push(item.month)
                            }
                            this.monthCount.push(item.total)
                        });
                        res.data.areaAlarmCountMapList.forEach(item => {
                            this.regionLabelData.push(item.areaName)
                            this.regionData.push(item.total)
                        });
                        this.rigtCardTwo()
                        this.areaAlarm()
                    }
                })
        },
        // 初始化时间
        getDate(date) {
            // date是传过来的时间戳，注意需为13位，10位需*1000
            // 也可以不传,获取的就是当前时间
            var time = new Date(date)
            var year = time.getFullYear() // 年
            var month = ('0' + (time.getMonth() + 1)).slice(-2) // 月
            var day = ('0' + time.getDate()).slice(-2) // 日
            var mydate = year + '-' + month + '-' + day
            return mydate
        },
        rigtCardTwo() {
            var chartDom = document.getElementById('cardTwo')
            var myChart = echarts.init(chartDom)
            var option = {
                backgroundColor: '#fff',
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                        textStyle: {
                            color: '#fff'
                        }
                    }
                },
                grid: {
                    borderWidth: 0,
                    top: 20,
                    bottom: 60,
                    right: 20,
                    left: 50,
                    textStyle: {
                        color: '#fff'
                    }
                },
                calculable: true,
                xAxis: [
                    {
                        type: 'category',
                        axisLine: {
                            lineStyle: {
                                color: '#000'
                            }
                        },
                        splitLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        data: this.monthData
                    }
                ],

                yAxis: [
                    {
                        type: 'value',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#efefef'
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#000'
                            }
                        },
                        minInterval: 1
                    }
                ],
                series: [
                    {
                        name: '条数',
                        type: 'line',
                        symbolSize: 10,
                        symbol: 'circle',
                        smooth: true,
                        itemStyle: {
                            color: '#6f7de3'
                        },

                        data: this.monthCount
                    }
                ]
            }
            myChart.setOption(option)
        },
        areaAlarm() {
            var chartDom = document.getElementById('areaAlarm')
            var myChart = echarts.init(chartDom)
            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {},
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01]
                },
                yAxis: {
                    type: 'category',
                    data: this.regionLabelData
                },
                series: [
                    {
                        name: '数量',
                        type: 'bar',
                        data: this.regionData,
                        itemStyle: {
                            color: '#6f7de3'
                        },
                    }
                ]
            };
            myChart.setOption(option)
        },
    }
}
</script>
<style lang="scss" scoped>
.card_top {
    display: flex;
    justify-content: space-between;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    border: 1px solid #EBEEF5;
    margin-bottom: 20px;
}

.card_bottom {
    border: 1px solid #EBEEF5;
    margin-top: 20px;

    .card_bottom_title {
        font-size: 20px;
        font-family: 600;
        margin: 20px 0px 0px 30px;
    }
}

.card_num {
    color: rgba(42, 130, 228, 1);
    font-size: 30px;
    font-weight: bold;
    margin: 0;
    padding-left: 10px;
}

.box-card {
    width: 18%;
    margin: 20px;

    .title {
        width: 100%;
        text-align: left;
        font-size: 18px;
        font-weight: bold;
        color: rgba(128, 128, 128, 1);
        padding-left: 20px;
    }

    .num {
        text-align: center;
        color: rgba(42, 130, 228, 1);
        font-size: 36px;
        font-weight: bold;
        margin: 0;
    }

    .dan {
        color: rgba(128, 128, 128, 1);
        font-size: 18px;
        font-weight: bold;
        margin: 0;
        padding: 0px 20px 0 10px;
    }

    .card_bom {
        text-align: right;
        font-size: 12px;
        color: rgba(80, 80, 80, 1);
        padding-right: 20px;

        p {
            margin: 0;
            padding-top: 10px;
        }
    }
}

.el-row {
    position: inherit !important;
}

.card_con_title {
    color: rgba(128, 128, 128, 1);
    font-size: 16px;
    font-weight: bold;
}

.focus {
    color: rgba(42, 130, 228, 1);
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
}

#ecahrts_pie {
    width: 100%;
    height: 240px;
    padding-top: 35px;
}

#ecahrts_line {
    width: 100%;
    height: 220px;
}

.box-card-bottom {
    margin: 20px;
}

.clearfix {
    width: 100%;

}

.blue {
    border-left: 5px solid rgba(5, 169, 252, 1);
}

.red {
    border-left: 5px solid rgba(252, 98, 54, 1);
}

.purple {
    border-left: 5px solid rgba(164, 100, 244, 1);
}

.yellow {
    border-left: 5px solid rgba(252, 148, 4, 1);
}
</style>
  