<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="left-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 18px; line-height: 36px">监控目录</span>
            <el-button type="primary" style="float: right" @click="addGroup"
              >添加分组</el-button
            >
          </div>
          <el-tree
            :data="treeData"
            :props="defaultProps"
            :expand-on-click-node="false"
            @node-click="handleClick"
            ref="tree"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
              <span>
                <el-button
                  v-show="data.monitoringVos"
                  icon="el-icon-plus"
                  type="text"
                  size="mini"
                  @click.stop="() => groupAdd(data)"
                ></el-button>
                <el-button
                  v-show="data.monitoringVos"
                  icon="el-icon-edit"
                  type="text"
                  size="mini"
                  @click.stop="() => editGroup(data)"
                >
                </el-button>
                <el-button
                  icon="el-icon-delete"
                  type="text"
                  size="mini"
                  @click.stop="() => removeGroup(data)"
                >
                </el-button>
              </span>
            </span>
          </el-tree>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="right-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 18px">实时视频</span>
          </div>
          <div class="videoBox">
            <el-card
              v-for="(item, index) in videoBox"
              :key="index"
              class="videoCard"
            >
              <div slot="header" class="clearfix">
                <span>{{ item.deviceName }}</span>
                <el-button
                  style="float: right; padding: 3px 0"
                  type="text"
                  icon="el-icon-close"
                  @click="closeCard(index)"
                ></el-button>
              </div>
              <div id="videoElement" style="width: 100%; height: 100%">
                <jessibuca-player
                  :ref="'videoPlayer' + index"
                  :id="'container' + index"
                  :videoUrl="videoUrl[index]"
                  :error="videoError"
                  :message="videoError"
                  :height="false"
                  :hasAudio="hasAudio"
                  fluent
                  autoplay
                  live
                >
                </jessibuca-player>
              </div>
            </el-card>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog :title="groupTitle" :visible.sync="groupDialog" width="560px">
      <el-form
        :model="groupForm"
        :rules="rules"
        ref="groupForm"
        label-width="100px"
      >
        <el-form-item label="分组名称" prop="directoryName">
          <el-input
            v-model="groupForm.directoryName"
            placeholder="请输入分组名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">确 定</el-button>
        <el-button @click="groupDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="添加摄像头" :visible.sync="videoDialog" width="960px">
      <el-form ref="form" label-width="88px" inline>
        <el-form-item label="设备ID">
          <el-input
            v-model="query.deviceId"
            placeholder="请输入设备ID"
            @change="getVideoList"
          ></el-input>
        </el-form-item>
        <el-form-item label="摄像头名称">
          <el-input
            v-model="query.deviceName"
            placeholder="请输入摄像头名称"
            @change="getVideoList"
          ></el-input>
        </el-form-item>
        <el-form-item label="所属部门">
          <el-select
            v-model="query.organizationName"
            placeholder="请选择所属部门"
            clearable
            @change="getVideoList"
          >
            <el-option :value="query.organizationId" class="option">
              <el-tree
                :data="organizationTree"
                :show-checkbox="true"
                node-key="id"
                :props="organizationProps"
                class="tree"
                @check="handleNodeClick"
                ref="tree"
              >
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="安装位置">
          <el-input
            v-model="query.placementLocation"
            placeholder="请输入安装位置"
            @change="getVideoList"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-table :data="videoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          v-for="column in tableColumn"
          :key="column.prop"
          :label="column.label"
          :prop="column.prop"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <span v-if="column.prop === 'expertLevel'">{{
              (
                dict.type.emergency_expert_level.find(
                  (t) => t.value == row.expertLevel
                ) || {}
              ).label
            }}</span>
            <span v-else>{{ row[column.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="query.current"
        :limit.sync="query.size"
        @pagination="getVideoList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitVideo">确 定</el-button>
        <el-button @click="videoDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
  
<script>
import {
  tree,
  save,
  update,
  remove,
  monitorList,
  organizationTree,
  getVideoStreaming,
  removeVideo,
  saveVideo,
} from "@/api/fireManagement/videoMonitor/index.js";
import jessibucaPlayer from "./jessibuca.vue";
export default {
  components: {
    jessibucaPlayer,
  },
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: "monitoringVos",
        label: "directoryName",
      },
      organizationProps: {
        children: "children",
        label: "name",
      },
      // 右侧视频容器
      videoBox: [],
      hasAudio: false,
      videoUrl: [],
      // 分组弹框数据
      groupTitle: "",
      groupDialog: false,
      groupForm: {
        directoryName: "",
      },
      rules: {
        directoryName: [
          { required: true, message: "请输入分组名称", trigger: "blur" },
        ],
      },
      // 监控列表弹框
      videoDialog: false,
      organizationTree: [],
      multipleSelection: [],
      query: {
        current: 1,
        size: 10,
      },
      total: 0,
      directoryId: "",
      videoList: [],
      tableColumn: [
        {
          label: "ID",
          prop: "deviceId",
        },
        {
          label: "摄像头名称",
          prop: "deviceName",
        },
        {
          label: "所属部门",
          prop: "organizationName",
        },
        {
          label: "安装位置",
          prop: "placementLocation",
        },
      ],
    };
  },
  watch: {},
  created() {
    this.getTreeData();
    this.getOrganizationTree();
  },
  methods: {
    // 获取树结构
    getTreeData() {
      tree().then((res) => {
        if (res?.code == 200) {
          // console.log(res.data);
          res.data.forEach((item) => {
            item.stringId = "group" + item.id;
            if (item.monitoringVos.length > 0) {
              item.monitoringVos.forEach((ele) => {
                ele.directoryName = ele.deviceName;
                ele.stringId = "device" + ele.id;
              });
            }
          });
          this.treeData = res.data;
        } 
        // else {
        //   this.$message({
        //     type: "error",
        //     message: res.msg,
        //   });
        // }
      });
    },
    // 左侧树节点点击
    handleClick(data) {
      this.videoBox = [];
      // 节点包含摄像头元素展示前四项
      if (data.monitoringVos) {
        if (data.monitoringVos.length >= 4) {
          this.videoBox = JSON.parse(JSON.stringify(data.monitoringVos));
          this.videoBox.length = 4;
          const arr = [];
          data.monitoringVos.forEach((element) => {
            arr.push(element.deviceId);
          });
          // this.getvideoFlv(arr);
        } else {
          this.videoBox = JSON.parse(JSON.stringify(data.monitoringVos));
          const arr = [];
          data.monitoringVos.forEach((element) => {
            arr.push(element.deviceId);
          });
          // this.getvideoFlv(arr);
        }
      }
      // 不包含摄像头展示唯一点击项
      else {
        this.videoBox.push(data);
        this.getvideoFlv([data.deviceId]);
      }
    },
    // 获取部门树结构
    getOrganizationTree() {
      organizationTree().then((res) => {
        // console.log(res.data);
        this.organizationTree = res.data;
      });
    },
    // 部门树复选框选中事件
    handleNodeClick(data, res, item) {
      if (!res) {
        this.query.organizationId = undefined;
        this.query.organizationName = undefined;
      } else {
        this.$refs.tree.setCheckedNodes([data]);
        this.query.organizationId = data.id;
        this.query.organizationName = data.name;
      }
    },
    // 添加分组
    addGroup() {
      if (this.$refs.groupForm) {
        this.$refs.groupForm.resetFields();
      }
      this.groupTitle = "添加监控目录分组";
      this.groupDialog = true;
    },
    // 编辑分组
    editGroup(data) {
      this.groupTitle = "编辑监控目录分组";
      this.groupDialog = true;
      this.groupForm.id = data.id;
      this.groupForm.directoryName = data.directoryName;
    },
    // 删除分组
    removeGroup(data) {
      if (data.monitoringVos) {
        this.$modal
          .confirm("是否确认删除当前分组")
          .then(function () {
            return remove({ id: data.id });
          })
          .then(() => {
            this.$modal.msgSuccess("删除成功");
            this.getTreeData();
          })
          .catch(() => {});
      } else {
        this.$modal
          .confirm("是否确认删除当前分组下该设备")
          .then(function () {
            return removeVideo({ id: data.id });
          })
          .then(() => {
            this.$modal.msgSuccess("删除成功");
            this.getTreeData();
          })
          .catch(() => {});
      }
    },
    // 表单提交
    submit() {
      this.$refs["groupForm"].validate((valid) => {
        if (valid) {
          if (this.groupForm.id) {
            update(this.groupForm).then((res) => {
              if (res?.code == 200) {
                this.$message({ type: "success", message: "编辑成功" });
                this.groupDialog = false;
                this.getTreeData();
              }
            });
          } else {
            save(this.groupForm).then((res) => {
              if (res?.code == 200) {
                this.$message({ type: "success", message: "添加成功" });
                this.groupDialog = false;
                this.getTreeData();
              }
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 分组下添加摄像头
    groupAdd(data) {
      this.videoDialog = true;
      this.query.directoryId = data.id;
      this.getVideoList();
    },
    // 获取摄像头列表
    getVideoList() {
      monitorList(this.query).then((res) => {
        this.videoList = res.data.records || [];
        this.total = res.data.total;
        console.log(res.data.records);
      });
    },
    // 摄像头表格勾选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 添加摄像头到分组下
    submitVideo() {
      if (this.multipleSelection.length > 0) {
        const arr = [];
        this.multipleSelection.forEach((element) => {
          arr.push({
            monitorId: element.id,
            directoryId: this.query.directoryId,
          });
        });
        saveVideo(arr).then((res) => {
          if (res.code == 200) {
            this.$message({ type: "success", message: "摄像头添加成功" });
            this.videoDialog = false;
            this.getTreeData();
          }
        });
      } else {
        this.$message({ type: "warning", message: "请选择需要添加的摄像头！" });
      }
    },
    // 视频流播放相关
    closeCard(index) {
      this.videoBox.splice(index, 1);
    },
    videoError: function (e) {
      console.log("播放器错误：" + JSON.stringify(e));
    },
    getvideoFlv(item) {
      this.dialogVisible = true;
      getVideoStreaming({
        equipmentIdList: [item],
      }).then((res) => {
        console.log(res.data);
        this.$nextTick(() => {
          this.videoUrl = res.data[0].flvAddress;
          this.$refs.videoPlayer.play(this.videoUrl);
        });
      });
    },
    closeVideo() {
      this.$refs.videoPlayer.destroy();
    },
  },
};
</script>
<style lang="scss" scoped>
.left-card {
  height: calc(100vh - 124px);
  overflow-y: auto;
}

.right-card {
  height: calc(100vh - 124px);
  overflow-y: auto;
}

.search {
  margin: 10px 0;
}

.tag-item {
  margin-right: 15px;
  margin-bottom: 10px;
}

.tag-add-box {
  display: flex;
  align-items: center;
  width: 40%;
  margin-top: 30px;
  .tag-add-ipt {
    margin-right: 15px;
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.option {
  height: auto;
  line-height: 1;
  padding: 0;
  background-color: #fff;
}

.tree {
  padding: 4px 20px;
  font-weight: 400;
}

.videoBox {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .videoCard {
    width: 48%;
    margin-bottom: 20px;
  }
}
</style>