<template>
  <div class="app-container">
    <div class="titleDiv">当前值班人员</div>
    <el-row :gutter="20">
      <el-col :span="10">
        <el-carousel :interval="4000" height="200px">
          <el-carousel-item v-for="(item, index) in getDutyData" :key="index">
            <el-card>
              <div class="card_top">
                <div>
                  <p>姓名：{{ item.name }}</p>

                  <p>工号：{{ item.workNumber }}</p>

                  <p>电话：{{ item.phone }}</p>
                </div>
                <div style="text-align: center">
                  <p class="card_num">{{ item.lncumbency }}</p>
                  <p style="margin: 0">在岗率</p>
                </div>
              </div>
            </el-card>
          </el-carousel-item>
        </el-carousel>
      </el-col>
      <el-col :span="14">
        <div style="margin-bottom: 20px;">
          <span>请选择消控室</span>
          <el-select v-model="value" placeholder="请选择" @change="fireControlChange">
            <el-option v-for="item in fireControlList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <el-carousel :interval="4000" height="200px">
          <el-carousel-item v-for="(item, index) in FlvData" :key="index">
            <jessibuca-player id="videoElement1" ref="videoPlayer" :videoUrl="videoUrl" :error="videoError"
              :message="videoError" :height="false" :hasAudio="hasAudio" fluent autoplay live>
            </jessibuca-player>
          </el-carousel-item>
        </el-carousel>
      </el-col>
    </el-row>
    <div class="titleDiv">查岗记录</div>
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true">
      <el-form-item label="姓名">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable style="width: 230px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="应岗">
        <el-select v-model="queryParams.checkStatus" placeholder="应岗" clearable style="width: 240px">
          <el-option v-for="dict in dict.type.check_post_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="查岗时间" prop="lxr">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:role:add']" type="primary" icon="el-icon-plus" size="mini"
          @click="handleAdd">一键查岗</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="postList">
      <el-table-column label="姓名" :show-overflow-tooltip="true" prop="inspectedPostUserName" align="center" />
      <el-table-column label="工号" :show-overflow-tooltip="true" prop="inspectedPostWorkNumber" align="center" />
      <el-table-column label="应岗" :show-overflow-tooltip="true" prop="checkStatus" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.check_post_status" :value="scope.row.checkStatus" :type="1" />
        </template>
      </el-table-column>
      <el-table-column label="查岗时间" :show-overflow-tooltip="true" prop="checkTime" align="center" />
      <el-table-column label="创建人" :show-overflow-tooltip="true" prop="createUser" align="center" />
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
      @pagination="getList" />

    <!-- echarts -->
    <el-row :gutter="20" style="margin-top: 30px">
      <el-col :span="6">
        <div id="ecahrts_pie" />
      </el-col>
      <el-col :span="18">
        <el-form ref="queryForm" :model="cahrtsFoem" size="small" :inline="true" style="margin-left: 3%">
          <el-form-item label="值班人员" prop="qy">
            <el-select v-model="cahrtsFoem.inspectedPostUserId" multiple :multiple-limit="2" placeholder="请选择"
              @change="staffChange">
              <el-option v-for="item in staffOptions" :key="item.workNumber" :label="item.staffName"
                :value="item.workNumber" />
            </el-select>
          </el-form-item>
          <el-form-item label="日期" prop="qy">
            <el-radio-group v-model="cahrtsFoem.dateType" @change="typeChange">
              <el-radio :label="'currentWeek'">本周</el-radio>
              <el-radio :label="'currentMonth'">本月</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div id="ecahrts_line" />
      </el-col>
    </el-row>
    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <!-- <el-button type="primary" icon="el-icon-plus" size="mini" style="height: 30px; margin-left: 10px"
          @click="addRound">增加查岗次数</el-button> -->
      <el-row style="margin-bottom: 10px">
        <span>选择消控室：</span>
        <el-select v-model="roomId" placeholder="请选择">
          <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-row>
      <!-- <el-row v-for="(item, i) in diaArr" :key="i" :gutter="20" style="margin-bottom: 10px">
        <el-col :span="12">
          <el-date-picker v-model="item.checkDate" type="date" style="width: 100%" placeholder="选择日期" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" :picker-options="pickerOptions" />
        </el-col>
        <el-col :span="12">
          <el-time-picker v-model="item.checkTime" style="width: 90%; margin-right: 10px" placeholder="任意时间点"
            format="HH:mm" value-format="HH:mm" :picker-options="pickerOptions1" /><i v-if="i != 0" class="el-icon-error"
            @click="removeRound(i)" /></el-col>
      </el-row> -->

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 一键应岗页面 还没搭建  全局的 所有页面都可以弹出
import * as echarts from 'echarts'
import {
  page,
  roomList,
  save,
  getDutyPerson,
  getDutyTime,
  getOnDutyRate,
  getDuty,
  relatedMonitor,
  getVideoStreaming,
  fireControl
} from '@/api/fireManagement/fireControlRoom/fireControl/index'
import jessibucaPlayer from "./jessibuca.vue";
export default {
  name: 'FireControl',
  dicts: ['check_post_status'],
  components: {
    jessibucaPlayer,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      getDutyData: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      value: "",
      fireControlList: [],
      // 角色表格数据
      postList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        name: undefined,
        isChecked: undefined,
        startTime: undefined,
        endTime: undefined
      },
      startTime: '',
      endTime: '',
      // 表单参数
      form: {},
      dateRange: [],
      // 表单校验
      rules: {},
      hasAudio: false,
      roomId: '',
      options: [],
      FlvData: [],
      videoUrl: '',
      staffOptions: [],
      diaArr: [{ checkDate: '', checkTime: '' }],
      cahrtsFoem: {
        inspectedPostUserId: [],
        dateType: 'currentWeek',
        dutyMonth: [],
        staffName: []
      },
      pickerOptions: {
        disabledDate(date) {
          // 如果没有后面的-8.64e7就是不可以选择今天的
          return date.getTime() < Date.now() - 8.64e7
        }
      },
      pickerOptions1: {
        // selectableRange: (() => {
        //   let data = new Date();
        //   let hour = data.getHours();
        //   let minute = data.getMinutes();
        //   let second = data.getSeconds();
        //   return [` ${hour}:${minute}:${second} - 23:59:59 `]; //设置只可选择此刻之前的时间，此刻之后的时间禁选
        // })(),
      }
    }
  },
  created() {
    roomList().then((res) => {
      this.options = res.data
    })
    this.getDuty()
    this.fireControl()
    this.getList()
  },
  mounted() {
    this.startTime = this.$moment().weekday(1).format('YYYY-MM-DD') //本周一
    this.endTime = this.$moment().weekday(7).format('YYYY-MM-DD') //本周日
    console.log(this.startTime, this.endTime);
    this.getDutyPerson()
  },
  methods: {
    /** 查询查岗列表 */
    getList() {
      this.loading = true
      page(this.queryParams).then((response) => {
        console.log(response);
        this.postList = response.data.records
        this.total = response.data.total
        this.loading = false
      })
    },
    getDutyPerson() {
      this.staffOptions = []
      getDutyPerson({
        startTime: this.startTime,
        endTime: this.endTime,
        isDelete: 0
      }).then((res) => {
        this.staffOptions = res.data
        console.log(this.staffOptions, 'staffOptions');
        this.cahrtsFoem.inspectedPostUserId = []
        this.cahrtsFoem.staffName = []
        res.data.forEach(item => {
          this.cahrtsFoem.inspectedPostUserId.push(item.workNumber)
          this.cahrtsFoem.staffName.push(item.staffName)
        })
        // this.cahrtsFoem.inspectedPostUserId = [
        //   res.data[0].workNumber,
        //   // res.data[1].workNumber
        //   console.log(res.data[0].workNumber,'sss')
        // ]
        // this.cahrtsFoem.staffName = [
        //   res.data[0].staffName,
        //   // res.data[1].staffName
        // ]
        const data = []
        const data1 = []
        this.cahrtsFoem.inspectedPostUserId.forEach((item) => {
          if (this.cahrtsFoem.dateType == 'currentWeek') {
            data.push({ inspectedPostUserId: item, currentWeek: '1' })
            data1.push({ inspectedPostUserId: item, currentWeek: '1' })
          } else {
            data.push({ inspectedPostUserId: item, currentMonth: '1' })
            data1.push({ inspectedPostUserId: item, currentMonth: '1' })
          }
        })
        // console.log(data, 'data');
        getDutyTime(data).then((res) => {
          this.cahrtsFoem.dutyTime = res.data
          console.log(res,'图表数据');
          const arr = []
          if (res.data[0]) {
            for (let index = 0; index < res.data[0].length; index++) {
              arr.push(parseInt(index + 1) + '号')
            }
            this.cahrtsFoem.dutyMonth = arr
          }
          this.rightEcharts()
        })
        getOnDutyRate(data1).then((res) => {
          this.leftEcharts(res.data)
        })
      })
    },
    fireControlChange(id) {
      relatedMonitor({ id: id }).then(res => {
        console.log(res, 'sss');
        this.FlvData = res.data
        res.data.forEach(item => {
          console.log(item, 'w-w-');
          this.getvideoFlv(item.deviceId)
        })
      })
    },
    getvideoFlv(item) {
      getVideoStreaming({
        equipmentIdList: [item],
      }).then((res) => {
        console.log(res.data[0].flvAddress, 'ssssss');
        this.$nextTick(() => {
          this.videoUrl = res.data[0].flvAddress;
          this.$refs.videoPlayer.play(this.videoUrl);
        });
      });
    },
    // 查询消控室list
    fireControl() {

      fireControl(null).then(res => {
        if (res.code == 200) {
          this.fireControlList = res.data
          console.log(this.$route.query, res);
          if (this.$route.query.id) {
            this.value = this.$route.query.id
          } else {
            this.value = res.data[0].id
          }
        }
        this.fireControlChange(this.value)
      })
    },
    // 视频流播放相关
    videoError: function (e) {
      console.log("播放器错误：" + JSON.stringify(e));
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.diaArr = [{ checkDate: '', checkTime: '' }]
      // this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.endTime = this.dateRange[1]
      }
      this.queryParams.current = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '一键查岗'
    },
    /** 新增查岗次数 */
    addRound() {
      this.diaArr.push({ checkDate: '', checkTime: '' })
    },
    /** 删除查岗次数 */
    removeRound(i) {
      this.diaArr.splice(i, 1)
    },
    /** 人员变更 */
    staffChange(value) {
      this.cahrtsFoem.staffName = []
      value.forEach((item) => {
        this.staffOptions.forEach((element) => {
          if (element.workNumber == item) {
            this.cahrtsFoem.staffName.push(element.staffName)
          }
        })
      })
      const data = []
      const data1 = []
      value.forEach((item) => {
        if (this.cahrtsFoem.dateType == 'currentWeek') {
          data.push({ inspectedPostUserId: item, currentWeek: '1' })
          data1.push({ inspectedPostUserId: item })
        } else {
          data.push({ inspectedPostUserId: item, currentMonth: '1' })
          data1.push({ inspectedPostUserId: item })
        }
      })
      getDutyTime(data).then((res) => {
        this.cahrtsFoem.dutyTime = res.data
        this.rightEcharts()
      })
      getOnDutyRate(data1).then((res) => {
        this.leftEcharts(res.data)
      })
    },
    /** 日期维度变更*/
    typeChange(value) {
      this.cahrtsFoem.dateType = value
      if (value == 'currentWeek') {
        this.startTime = this.$moment().weekday(1).format('YYYY-MM-DD') //本周一
        this.endTime = this.$moment().weekday(7).format('YYYY-MM-DD') //本周日
      } else {
        this.startTime = this.$moment().startOf('month').format('YYYY-MM-DD');
        this.endTime = this.$moment().endOf('month').format('YYYY-MM-DD');
      }
      this.getDutyPerson()
    },
    leftEcharts(data) {
      var myChart = echarts.init(document.getElementById('ecahrts_pie'))
      var dataArr = data
      var option = {
        tooltip: {
          formatter: '{a} <br/>{b} : {c}%'
        },
        title: {
          show: true,
          x: 'center',
          y: '68%',
          text: '在岗率', // 幸运值取代码置于值于此处
          // subtext: '幸运指数',
          textStyle: {
            fontSize: 14,
            fontWeight: 'bolder',
            fontStyle: 'normal',
            color: '#31F3FF'
          }
        },
        series: [
          {
            name: '外部线',
            type: 'gauge',
            radius: '95%', // 动态
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                color: [
                  [1, '#31F3FF'] // 动态
                ],
                width: 1
              }
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            title: {
              // 标题
              show: false
            }
          },
          {
            name: '外部刻度',
            type: 'gauge',
            radius: '99%',
            min: 0, // 最小刻度
            max: 100, // 最大刻度
            splitNumber: 10, // 刻度数量
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              show: false,
              lineStyle: {
                color: [[1, 'rgba(0,0,0,0)']]
              }
            }, // 仪表盘轴线
            axisLabel: {
              show: true,
              color: '#31F3FF',
              fontSize: 10, // 动态
              distance: -20 // 动态
            }, // 刻度标签。
            axisTick: {
              show: false
            }, // 刻度样式
            splitLine: {
              show: false
            }
          },
          {
            name: '内部宽线条',
            type: 'gauge',
            radius: '73%',
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                color: [[1, '#122B3C']],
                width: 20
              }
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            title: {
              show: false
            }
          },
          {
            name: '内部细线条',
            type: 'gauge',
            radius: '70%',
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                color: [[1, '#122B3C']],
                width: 3
              }
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            title: {
              show: false
            }
          },
          {
            name: '间隔条形',
            type: 'gauge',
            radius: '73%',
            z: 4,
            splitNumber: 35,
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                opacity: 0
              }
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: true,
              length: 20,
              splitNumber: 1,
              lineStyle: {
                color: '#122B3C',
                width: 1
              }
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            title: {
              show: false
            }
          },
          {
            name: '数据',
            type: 'gauge',
            radius: '72.5%',
            z: 3,
            startAngle: 225,
            max: 100,
            endAngle: -45,
            axisLine: {
              lineStyle: {
                color: [
                  [dataArr / 100, '#31F3FF'], // 动态
                  [1, '#185363']
                ],
                width: 20
              }
            },
            tooltip: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              formatter: function (params) {
                return dataArr + '%'
              },
              show: true,
              fontWeight: 'bold',
              fontSize: 20
            },
            pointer: {
              show: true,
              width: 3
            },
            data: [
              {
                name: '',
                value: dataArr
              }
            ]
          },
          // 内圆
          {
            name: '内圆环',
            type: 'pie',
            radius: ['4%', '2%'],
            hoverAnimation: false,
            tooltip: {
              show: false
            },
            cursor: 'default',
            labelLine: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              color: '#122B3C'
            },
            animation: false,
            data: [1]
          },
          // 内圆
          {
            name: '内圆环2',
            type: 'pie',
            radius: '2%',
            hoverAnimation: false,
            cursor: 'default',
            tooltip: {
              show: false
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            itemStyle: {
              color: '#31F3FF'
            },
            animation: false,
            data: [1]
          }
        ]
      }
      myChart.setOption(option)
    },
    rightEcharts() {
      var myChart = echarts.init(document.getElementById('ecahrts_line'))
      var option = {
        grid: {
          top: '5%',
          left: '3%',
          right: '4%',
          bottom: '13%',
          containLabel: true
        },
        backgroundColor: '#fff', // 画布背景

        legend: {
          icon: 'rect',
          bottom: 10,
          textStyle: {
            color: '#000'
          },
          data: this.cahrtsFoem.staffName,
          itemWidth: 15, // 设置宽度
          itemHeight: 15 // 设置高度
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          // x轴
          type: 'category',
          boundaryGap: false, // 坐标轴两边留白策略
          data:
            this.cahrtsFoem.dateType == 'currentWeek'
              ? [
                '星期一',
                '星期二',
                '星期三',
                '星期四',
                '星期五',
                '星期六',
                '星期日'
              ]
              : this.cahrtsFoem.dutyMonth,
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: '#000'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#000'
            }
          }
        },
        yAxis: {
          // y轴
          min: 0,
          type: 'value',
          minInterval: 1,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLabel: {
            textStyle: {
              fontSize: 12,
              color: '#000'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#fff'
            }
          }
        },
        series: [
          {
            name: this.cahrtsFoem.staffName[0],
            data: this.cahrtsFoem.dutyTime[0],
            type: 'line',
            smooth: false, // 折线是否平滑
            areaStyle: {
              opacity: 0
            },
            itemStyle: {
              normal: {
                color: '#197CD8', // 小圆点的颜色

                lineStyle: {
                  color: '#197CD8', // 折线的颜色
                  
                }
              }
            },
            lineStyle:{
              normal:{
                width:3
              }
            }
          },
          {
            name: this.cahrtsFoem.staffName[1],
            data: this.cahrtsFoem.dutyTime[1],
            type: 'line',
            smooth: false, // 是否平滑
            areaStyle: {
              opacity: 0
            },
            itemStyle: {
              normal: {
                color: '#2B9F50', // 小圆点的颜色
                lineStyle: {
                  color: '#2B9F50' // 折线的颜色
                }
              }
            },
            lineStyle:{
              normal:{
                width:3
              }
            }
          }
        ]
      }
      myChart.setOption(option)
    },
    // 值班人员数据获取
    getDuty() {
      getDuty().then(res => {
        if (res.code == 200) {
          this.getDutyData = res.data
        }
        console.log(res, '值班人员');
      })
    },
    /** 提交按钮 */
    submit() {
      if (this.roomId == '') {
        this.$modal.msgError('请选择安排查岗的消控室！')
        return
      }
      let timeArry = this.$moment().format('YYYY-MM-DD HH:mm:ss').split(':');
      const time = [timeArry[0], Number(timeArry[1]) + 1 + '', timeArry[2]]
      if (this.getDutyData) {
        const data = []
        this.getDutyData.forEach((item) => {
          console.log(item, 'ssssssssssss');
          data.push({
            checkStatus: '4010801',
            firecontrolRoomId: this.roomId,
            checkTime: time.join(':'),
            inspectedPostUserId: item.id,
            inspectedPostWorkNumber: item.workNumber,
            inspectedPostUserName: item.name,
          })
        })
        console.log(data);
        save(data).then((res) => {
          this.$modal.msgSuccess('新增成功')
          this.getList()
          this.open = false
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;

  p {
    font-size: 20px;
    font-weight: bold;
  }
}

.titleDiv {
  color: rgba(80, 80, 80, 1);
  font-size: 17px;
  font-weight: bold;
  margin: 20px 0;
}

.card_top {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}

.card_num {
  color: rgba(42, 130, 228, 1);
  font-size: 30px;
  font-weight: bold;
  margin: 0;
  padding-left: 10px;
}

#ecahrts_pie {
  width: 100%;
  height: 240px;
  padding-top: 35px;
}

#ecahrts_line {
  width: 100%;
  height: 220px;
}

::v-deep .jessibuca-container {
  height: 280px !important;
}
</style>
