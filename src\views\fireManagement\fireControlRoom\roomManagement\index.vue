<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="消控室名称" prop="qy">
        <el-input v-model="queryParams.name" placeholder="请输入消控室名称" clearable style="width: 230px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="关联区域">
        <el-cascader :options="options" v-model="queryParams.areaId" :props="{
          checkStrictly: true,
          label: 'name',
          value: 'id',
          emitPath: false,
        }" clearable></el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:role:add']">新增消控室</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roomList">
      <el-table-column label="消控室名称" prop="name" align="center" />
      <el-table-column label="关联区域" prop="areaName" align="center"/>
      <el-table-column label="责任人" prop="principal" align="center" />
      <el-table-column label="联系电话" prop="phone" align="center" />
      <el-table-column label="创建人" prop="principal" align="center" />
      <el-table-column label="创建日期" prop="createTime" align="center" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:role:edit']">编辑</el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)"
            v-hasPermi="['system:role:edit']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
      @pagination="getList" />

    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="消控室名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入消控室名称" /> </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="关联区域" prop="areaId">
              <el-cascader :options="options" v-model="form.areaId" :props="{
                checkStrictly: true,
                label: 'name',
                value: 'id',
                emitPath: false,
              }" clearable @change="handleChange"></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="责任人" prop="principal">
              <el-input v-model="form.principal" placeholder="请输入责任人" /> </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" /> </el-form-item></el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-button icon="el-icon-plus" size="mini" type="primary" @click="device">关联监控设备</el-button>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 关联设备弹窗 -->
    <el-dialog title="关联设备" :visible.sync="dialogTableVisible" append-to-body width="1400px">
      <el-row>
        <el-col :span="11">
          <el-form :model="deviceQueryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
            <div>
              <!-- <el-form-item label="设备id :" prop="deviceId" label-width="90px">
                <el-input v-model="deviceQueryParams.deviceId" placeholder="请输入消控室名称" clearable style="width: 10vw"
                  @keyup.enter.native="handleQuery" />
              </el-form-item> -->
              <el-form-item label="设备名称 :" prop="deviceName" label-width="90px">
                <el-input v-model="deviceQueryParams.deviceName" placeholder="请输入消控室名称" clearable style="width: 10vw"
                  @keyup.enter.native="handleDeviceQuery" />
              </el-form-item>
              <!-- <el-form-item label="所属部门 :" label-width="90px">
                <el-select style="width: 10vw; " placeholder="请选择所属部门" v-model="deviceQueryParams.organizationName">
                  <el-option :value="deviceQueryParams.organizationName" class="option">
                    <el-tree :data="treeData" :show-checkbox="true" node-key="id" :props="defaultProps" class="tree"
                      @check="handleNodeClick" ref="tree">
                    </el-tree>
                  </el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item label="安装位置 :" label-width="90px">
                <el-input v-model="deviceQueryParams.placementLocation" placeholder="请输入消控室名称" clearable
                  style="width: 10vw" @keyup.enter.native="handleDeviceQuery" />
              </el-form-item>
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button class="resetQueryStyle" type="primary" icon="el-icon-search" size="mini"
                  @click="handleDeviceQuery">搜索</el-button>
                <el-button class="resetQueryStyle" icon="el-icon-refresh" size="mini" @click="resetQuery1">重置</el-button>
              </el-form-item>
            </div>
          </el-form>
          <el-table v-loading="deviceLoading" :data="deviceData" @row-click="handleSelectionChange">
            <el-table-column label="ID" prop="deviceId" align="center" show-overflow-tooltip />
            <el-table-column label="摄像头名称" prop="deviceName" align="center" show-overflow-tooltip />
            <el-table-column label="所属部门" prop="organizationName" align="center" show-overflow-tooltip />
            <el-table-column label="安装位置" prop="placementLocation" align="center" show-overflow-tooltip />
          </el-table>
          <pagination v-show="deviceTotal > 0" :total="deviceTotal" :page.sync="deviceQueryParams.current"
            :limit.sync="deviceQueryParams.size" @pagination="devicePage" />
        </el-col>
        <el-col :span="2" style="min-height: 100px;"></el-col>
        <el-col :span="11">
          <el-table v-loading="deviceLoading" :data="deviceData1">
            <el-table-column label="ID" prop="deviceId" align="center" show-overflow-tooltip />
            <el-table-column label="摄像头名称" prop="deviceName" align="center" show-overflow-tooltip />
            <el-table-column label="所属部门" prop="organizationName" align="center" show-overflow-tooltip />
            <el-table-column label="安装位置" prop="placementLocation" align="center" show-overflow-tooltip />
            <el-table-column label="操作" align="center" width="50">
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-plus" @click="handleDelete1(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="devicesubmitForm">确认添加</el-button>
        <el-button @click="devicecancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
  devicePage,
  update,
  remove,
  areaList,
  department
} from "@/api/fireManagement/fireControlRoom/roomManagement/index";
export default {
  name: "FireStation",
  data() {
    var checkPhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入绑定的手机号码"));
      } else if (
        !/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(
          value
        )
      ) {
        callback(new Error("请输入正确的手机号码"));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层
      dialogTableVisible: false,
      deviceLoading: false,
      deviceData: [],
      deviceData1: [],
      deviceQueryParams: {
        current: 1,
        size: 10,
        organizationId: undefined,
        organizationName: undefined,
      },
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      deviceTotal: 0,
      // 区域选项
      options: [],
      // 消控室表格数据
      roomList: [{}],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        name: undefined,
        areaId: undefined,
      },
      // 表单参数
      form: {
        name: undefined,
        areaId: undefined,
        principal: undefined,
        phone: undefined,
        monitorIdList: [],
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      flag: false,
      // 表单校验
      rules: {
        name: [
          { required: true, message: "请输入消控室名称", trigger: "blur" },
        ],
        areaId: [
          { required: true, message: "请选择关联区域", trigger: "blur" },
        ],
        principal: [
          { required: true, message: "请输入责任人", trigger: "blur" },
        ],
        phone: [
          {
            type: "number",
            validator: checkPhone,
            message: "请输入正确的手机号",
            trigger: "change",
            required: true,
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    devicePage() {
      devicePage(this.deviceQueryParams).then(res => {
        if (res.code == 200) {
          this.deviceData = res.data.records
          this.deviceTotal = res.data.total
        }
        console.log(res);
      })
    },
    devicesubmitForm() {
      this.dialogTableVisible = false;
      this.deviceData1.forEach(item => {
        this.form.monitorIdList.push(item.id)
      })
    },
    handleSelectionChange(res) {
      this.flag = false
      console.log(res, this.deviceData1)
      if (this.deviceData1.length != 0) {
        this.deviceData1.forEach(item => {
          if (item.id == res.id) {
            this.flag = true
            this.$modal.msgSuccess("请勿重复添加！");
          }
        })
        if (!this.flag) {
          this.deviceData1.push(res)
        }
      } else {
        this.deviceData1.push(res)
      }
    },
    handleChange(res) {
      this.findName(this.options, res)
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        areaList().then((res) => {
          res.data.forEach((item) => {
            item.areaName = item.name;
          });
          this.addName(res.data);
          this.options = res.data;
          this.roomList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        });
      });
    },
    handleNodeClick(data, res, item) {
      console.log(data, res, item, '树结构');
      if (!res) {
        this.deviceQueryParams.organizationId = undefined
        this.deviceQueryParams.organizationName = undefined
      } else {
        console.log(111);
        this.$refs.tree.setCheckedNodes([data]);
        this.deviceQueryParams.organizationId = data.id
        this.deviceQueryParams.organizationName = data.name
      }
    },
    // 区域树递归添加展示名称
    addName(data) {
      data.forEach((item) => {
        if (item.children) {
          item.children.forEach((element) => {
            element.areaName = item.areaName + "-" + element.name;
          });
          this.addName(item.children);
        }
      });
    },
    device() {
      this.getEventType()
      this.devicePage()
      this.dialogTableVisible = true
      this.resetQuery1()
    },
    // 根据id查询区域名称
    findName(data, res) {
      data.forEach(item => {
        if (item.areaId == res) {
          console.log(1111);
          this.form.areaName = item.areaName
        } else {
          if (item.children) {
            this.findName(item.children, res)
          }
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    devicecancel() {
      this.resetQuery1()
      this.dialogTableVisible = false;
    },
    // 表单重置
    reset() {
      this.form = {
        name: undefined,
        areaId: undefined,
        principal: undefined,
        phone: undefined,
        monitorIdList: []
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 搜索按钮操作 */
    handleDeviceQuery() {
      this.deviceQueryParams.current = 1;
      this.devicePage();
    },
    /** 重置按钮操作 */
    resetQuery() {
      (this.queryParams = {
        current: 1,
        size: 10,
        name: undefined,
        areaId: undefined,
      }),
        this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 重置按钮操作 */
    resetQuery1() {
      this.deviceQueryParams = {
        current: 1,
        size: 10,
        organizationId: undefined,
        organizationName: undefined,
      },
        this.deviceData1 = []
      this.handleDeviceQuery();
    },
    handleDelete1(res) {
      this.deviceData1 = this.deviceData1.filter(item => item.id != res.id)
      console.log(res);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增消防站";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      row.areaId = row.areaId.toString()
      this.open = true;
      this.title = "修改消防站";
      console.log(row);
      this.form = row;
    },
    /** 提交按钮 */
    submitForm: function () {
      console.log(this.form, 'this.form');
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // this.form.areaId = this.form.areaId[this.form.areaId.length - 1];
          if (this.form.id != undefined) {
            update(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            save(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return remove({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    getEventType() {
      department().then(res => {
        console.log(res, '事件类型');
        if (res.code == 200) {
          this.treeData = res.data
        }
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;

  p {
    font-size: 20px;
    font-weight: bold;
  }
}

.option {
  height: auto;
  line-height: 1;
  padding: 0;
  background-color: #fff;
}

.tree {
  padding: 4px 20px;
  font-weight: 400;
}
</style>