<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="物料种类" prop="materialType">
        <el-select
          v-model="queryParams.materialType"
          placeholder="物料种类"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.material_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入设备名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属区域" prop="griddingId">
        <el-select
          v-model="queryParams.griddingId"
          placeholder="安装区域"
          style="width: 100%"
        >
          <el-option
            v-for="dict in areaArr"
            :key="dict.griddingId"
            :label="dict.griddingName"
            :value="dict.griddingId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="详细位置" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入详细位置"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:role:add']"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <el-table-column
        label="ID"
        :show-overflow-tooltip="true"
        prop="griddingId"
        align="center"
      />
      <el-table-column label="所属区域" prop="griddingName" align="center" />
      <el-table-column
        label="详细位置"
        :show-overflow-tooltip="true"
        prop="location"
        align="center"
      />
      <el-table-column label="物种科类" prop="materialType" align="center">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.material_type"
            :value="scope.row.materialType"
            :type="1"
          />
        </template>
      </el-table-column>
      <el-table-column label="储量" prop="amount" align="center" />
      <el-table-column label="剩余量" prop="remain" align="center" />
      <el-table-column label="联系电话" prop="phone" align="center" />
      <el-table-column label="责任人" prop="principal" align="center" />
      <el-table-column label="更新日期" prop="createTime" align="center" />
      <el-table-column
        label="操作"
        align="center"
        width="160"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-hasPermi="['system:user:edit']"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            v-hasPermi="['system:user:remove']"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <!--  -->
    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="所属区域" prop="griddingId">
          <el-select
            v-model="form.griddingId"
            placeholder="请选择所属区域"
            ref="selectDept"
            @change="getAreaName()"
            style="width: 100%"
          >
            <el-option
              v-for="dict in areaArr"
              :key="dict.griddingId"
              :label="dict.griddingName"
              :value="dict.griddingId"
            /> </el-select
        ></el-form-item>
        <el-form-item label="详细位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入详细位置" />
        </el-form-item>

        <el-form-item label="物种科类" prop="materialType">
          <el-select
            v-model="form.materialType"
            placeholder="物料种类"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.material_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="储量" prop="amount">
          <el-input v-model.number="form.amount" placeholder="请输入储量" />
        </el-form-item>
        <el-form-item label="剩余量" prop="remain">
          <el-input v-model.number="form.remain" placeholder="请输入剩余量" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" />
        </el-form-item>

        <el-form-item label="责任人" prop="principal">
          <el-input
            v-model.number="form.principal"
            placeholder="请输入责任人"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  areaPage,
  save,
  update,
} from "@/api/fireManagement/fireProtection/materialData/index";
export default {
  name: "MaterialData",
  dicts: ["material_type"],
  data() {
    var checkPhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入绑定的手机号码"));
      } else if (
        !/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(
          value
        )
      ) {
        callback(new Error("请输入正确的手机号码"));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],

      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        materialType: undefined,
        materialName: undefined,
        griddingId: undefined,
        location: undefined,
        startTime: undefined,
        endTime: undefined,
      },

      dateRange: [],
      areaArr: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        griddingId: [
          { required: true, message: "请选择所属区域", trigger: "change" },
        ],
        principal: [
          { required: true, message: "请选择联系人", trigger: "blur" },
        ],
        phone: [{ validator: checkPhone, required: true, trigger: "blur" }],
        location: [
          { required: true, message: "请输入详细位置", trigger: "blur" },
        ],
        materialType: [
          { required: true, message: "请选择物种科类", trigger: "change" },
        ],
        amount: [{ required: true, message: "请输入储量", trigger: "blur" }],
        remain: [{ required: true, message: "请输入剩余量", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
    this.getAreaPage();
  },
  methods: {
    getAreaPage() {
      areaPage({ current: 1, size: 1000 }).then((response) => {
        this.areaArr = response.data.records;
      });
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        this.roleList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        griddingId: undefined,
        griddingName: undefined,
        principal: undefined,
        materialType: undefined,
        amount: undefined,
        remain: undefined,
        phone: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      this.queryParams.current = 1;
      this.getList();
    },
    getAreaName() {
      let obj = this.areaArr.find(
        (item) => item.griddingId == this.form.griddingId
      );

      this.form.griddingName = obj.griddingName;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增物料数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "修改物料数据";
      this.form = row;
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            update(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            save(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return update({ id: row.id, isDeleted: 1 });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;
  p {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>