<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="设备编号" prop="deviceId">
        <el-input
          v-model="queryParams.deviceId"
          placeholder="请输入设备编号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="数据类型" prop="deviceType" v-if="btnDia == '2'">
        <el-select v-model="queryParams.deviceType" placeholder="数据类型" clearable style="width: 240px">
          <el-option v-for="dict in dict.type.firecontrol_device_type" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备状态" v-if="btnDia == '1'">
        <!-- <el-radio-group v-model="queryParams.status">
          <el-radio label="4010501">在线</el-radio>
          <el-radio label="4010504">离线</el-radio>
        </el-radio-group> -->
        <el-select v-model="queryParams.status" placeholder="设备状态" clearable style="width: 240px">
          <el-option v-for="dict in dict.type.fire_device_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>

      <!-- <el-form-item label="参数值" v-show="btnDia == '2'">
        <el-select
          v-model="queryParams.param"
          placeholder="比较类型"
          clearable
          style="margin-left: 10px; width: 240px"
        >
          <el-option
            v-for="dict in dict.type.symbol"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <el-input
          v-model="queryParams.value"
          placeholder="请输入"
          clearable
          style="margin-left: 10px; width: 240px"
        />
      </el-form-item> -->
      <el-form-item label="报警类型" v-show="btnDia == '3'">
        <el-select v-model="queryParams.emergencyType" placeholder="报警类型" clearable style="width: 200px">
          <el-option v-for="dict in dict.type.emergency_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" v-show="btnDia == '3'">
        <el-select v-model="queryParams.emergencyStatus" placeholder="处理状态" clearable style="width: 200px">
          <el-option v-for="dict in dict.type.emergency_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button-group>
          <el-button :type="btnDia == '1' ? 'primary' : ''" @click="diaDeatilBtn('1')" size="mini">设备状态</el-button>
          <el-button :type="btnDia == '2' ? 'primary' : ''" size="mini" @click="diaDeatilBtn('2')">运行数据</el-button>
          <!-- <el-button
            :type="btnDia == '3' ? 'primary' : ''"
            size="mini"
            @click="diaDeatilBtn('3')"
            >告警数据</el-button
          > -->
        </el-button-group>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList1" v-show="btnDia == '1'">

      <el-table-column label="设备名称" prop="deviceName" align="center" />
      <el-table-column label="时间" prop="createTime" align="center" />

      <el-table-column prop="status" label="状态" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.fire_device_status" :value="scope.row.status" :type="1" />
        </template>
      </el-table-column>
    </el-table>
    <el-table v-loading="loading" :data="roleList2" v-show="btnDia == '2'">
      <el-table-column label="序号" type="index" width="70" align="center">
        <template slot-scope="scope">
          <span>{{
            (queryParams.current - 1) * queryParams.size +
            scope.$index +
            1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据类型" type="index" width="100" align="center">
        <template slot-scope="scope">
          {{ getNameById(queryParams.deviceType) }}
        </template>
      </el-table-column>
      <el-table-column label="设备名称" prop="deviceName" align="center" />
      <el-table-column label="时间" prop="collectTime" align="center" />
      <el-table-column label="参数" prop="dataType" align="center">
      </el-table-column>
      <el-table-column label="参数值(mg/m³)" prop="dataValue" align="center" />
      <el-table-column label="操作" align="center" width="300" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-plus" 
                  @click="handleRankadd(scope.row)">新增人员</el-button> -->
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size" @pagination="getList" />
    <!-- 详情 -->
    <el-dialog title="历史数据详情" :visible.sync="detailOpen" width="1000px" append-to-body>
      <el-form :model="diaFrom" ref="diaFrom" size="small" :inline="true" label-width="68px" style="margin-top: 10px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备标签">
              <el-tag type="info">{{diaFrom.deviceName}}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日期">
          <el-date-picker @change="diaChange" v-model="diaFrom.dateRange" style="width: 240px"
            value-format="yyyy-MM-dd HH:mm:ss" type="daterange" range-separator="-" start-placeholder="开始日期"
            end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table :data="tableData" height="350" border style="width: 100%" v-loading="loadingDia">
        <el-table-column prop="collectTime" label="时间" align="center" v-if="btnDia == '2'">
        </el-table-column>
        <el-table-column prop="dataType" label="参数" align="center" v-if="btnDia == '2'">
        </el-table-column>
        <el-table-column prop="dataValue" label="参数值" align="center" v-if="btnDia == '2'">
        </el-table-column>
      </el-table>
      <pagination :total="totalDia" :page.sync="diaFrom.current" :limit.sync="diaFrom.size"
        @pagination="historyPageDevice" />
    </el-dialog>
  </div>
</template>

<script>
import {
  pageDevice,
  pageStatus,
  pageEmergency,
  historyPageDevice
} from "@/api/fireManagement/fireProtection/operationData/index";
export default {
  name: "OperationData",
  dicts: [
    "firecontrol_device_type",
    "emergency_status",
    "symbol",
    "fire_device_status",
    "emergency_type",
    "param_type",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      totalDia: 0,
      loadingDia: false,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList1: [],
      roleList2: [],
      //   详情
      detailOpen: false,
      dateRange1: [],
      tableData: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        deviceId: undefined,
        deviceName: undefined,
        deviceType: "4011201",
        emergencyStatus: undefined,
        startTime: undefined,
        endTime: undefined,
        emergencyType: undefined,
        status: undefined,
      },
      diaFrom: {},
      dateRange: [],
      btnDia: "2",
    };
  },
  created() {

  },
  mounted() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {

      if (this.btnDia == "1") {
        this.pageStatus();
      } else if (this.btnDia == "2") {
        this.pageDevice();
      }
    },
    handleDetail(row) {
      console.log(row);
      this.detailOpen = true;
      this.diaFrom.deviceName=row.deviceName
      this.diaFrom.deviceId = row.deviceId
      this.historyPageDevice();
    },
    //
    pageDevice() {
      this.loading = true;
      this.roleList2 = [];
      console.log(this.queryParams.deviceType);
      pageDevice(this.queryParams).then((response) => {
        console.log(response);

        this.roleList2 = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    historyPageDevice() {
      this.loadingDia = true;
      this.tableData = [];
      console.log(this.diaFrom);
      historyPageDevice(this.diaFrom).then((response) => {
        console.log(response);
        this.tableData = response.data.records;
        this.totalDia = response.data.total;
        this.loadingDia = false;
      });
    },
    diaChange() {
      if (this.diaFrom.dateRange.length > 0) {
        this.diaFrom.startTime = this.diaFrom.dateRange[0];
        this.diaFrom.endTime = this.diaFrom.dateRange[1];
      }
      console.log(this.diaFrom);
      this.historyPageDevice()
    },
    getNameById(res) {
      // console.log(res);
      if (
        res != undefined &&
        res != "" &&
        res != null
      ) {
        return this.dict.type.firecontrol_device_type.filter(
          (item) => item.value == res
        )[0].label;
      }
    },
    //

    //
    pageStatus() {
      this.loading = true;
      this.roleList1 = [];
      pageStatus(this.queryParams).then((response) => {
        console.log(response);
        this.roleList1 = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    diaDeatilBtn(ind) {
      this.btnDia = ind;
      this.resetQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      if (this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.startTime = undefined;
      this.queryParams.endTime = undefined;
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;

  p {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>