<template>
    <div class="app-container">
        <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>数据筛选</span>
            </div>
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                style="display: flex; justify-content: space-between">
                <div>
                    <el-form-item label="危险源名称" prop="name">
                        <el-input v-model="queryParams.name" placeholder="请输入危险源名称" clearable style="width: 230px"
                            @keyup.enter.native="handleQuery" />
                    </el-form-item>
                    <el-form-item label="所属企业" prop="enterpriseName">
                        <el-input v-model="queryParams.enterpriseName" placeholder="请输入所属企业" clearable style="width: 230px"
                            @keyup.enter.native="handleQuery" />
                    </el-form-item>
                </div>
                <div style="min-width: 200px">
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </div>
            </el-form>
        </el-card>
        <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>危险源信息列表</span>
                <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
                    class="queryBtnT">新增危险源</el-button>
            </div>
            <el-table v-loading="loading" :data="roleList">
                <el-table-column label="序号" type="index" width="70" align="center">
                    <template slot-scope="scope">
                        <span>{{
                            (queryParams.current - 1) * queryParams.size +
                            scope.$index +
                            1
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="危险源名称" :show-overflow-tooltip="true" prop="name" align="center" />
                <el-table-column label="所属企业" prop="enterpriseName" align="center" />
                <el-table-column label="企业联系人" prop="contact" align="center" />
                <el-table-column label="联系方式" prop="phone" align="center" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" @click="handleUpdate(scope.row, 'look')">查看</el-button>
                        <el-button size="mini" type="text" @click="handleUpdate(scope.row, 'Update')">编辑</el-button>
                        <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
                @pagination="getList" />
        </el-card>
        <!-- -->
        <el-dialog :title="title" :visible.sync="open" width="960px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="150px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="危险源名称 :" prop="name">
                            <el-input v-model="form.name" :disabled="disabled" placeholder="请输入危险源名称"
                                style="width: 245px;" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="对应的MSDS库信息 :" prop="msds">
                            <el-select v-model="form.msds" filterable placeholder="对应的MSDS库信息" clearable :disabled="disabled"
                                style="width: 245px;">
                                <el-option v-for="dict in originArr" :key="dict.id" :label="dict.name"
                                    :value="dict.id" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="企业联系人 :" prop="contact">
                            <el-input v-model="form.contact" :disabled="disabled" placeholder="请输入企业联系人"
                                style="width: 245px;" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话 :" prop="phone">
                            <el-input v-model="form.phone" :disabled="disabled" placeholder="请输入联系电话"
                                style="width: 245px;" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="所属企业名称 :" prop="enterpriseName">
                            <el-input :disabled="disabled" v-model="form.enterpriseName" placeholder="请输入所属企业名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="危险源详细描述 :" prop="description">
                            <el-input :disabled="disabled" v-model="form.description" type="textarea"
                                placeholder="请输入危险源详细描述"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="应对措施 :" prop="countermeasures">
                            <el-input :disabled="disabled" v-model="form.countermeasures" type="textarea"
                                placeholder="请输入应对措施"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm" :disabled="disabled">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
  
<script>
import {
    page,
    msds,
    save,
    update,
    dele,
} from "@/api/fireManagement/keyParts/hazard/index";
import crypto from "@/utils/crypto";
export default {
    name: "HiddenDangerRecord",
    dicts: ["fault_type", "handle_type", "emergency_status"],
    data() {
        let checkPhone = (rule, value, callback) => {
            let reg = /^1[345789]\d{9}$/;
            if (!reg.test(value)) {
                callback(new Error("请输入11位手机号"));
            } else {
                callback();
            }
        };
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 角色表格数据
            roleList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                current: 1,
                size: 10,
                startTime: undefined,
                endTime: undefined,
                id: undefined,
                description: undefined,
                createUser: undefined,
                dangerType: undefined,
            },
            uploadFile: process.env.VUE_APP_BASE_API + "api/file/uploadFile",
            fileList: [],
            // 表单参数
            form: {},
            dateRange: [],
            originArr: [],
            // 表单校验
            rules: {
                name: [{ required: true, message: "请输入危险源名称", trigger: "blur" }],
                enterpriseName: [{ required: true, message: "请输入所属企业名称", trigger: "blur" }],
                description: [
                    { required: true, message: "请输入危险源详细描述", trigger: "blur" },
                ],
                contact: [{ required: true, message: "请输入企业联系人", trigger: "blur" }],
                phone: [
                    {
                        type: "number",
                        validator: checkPhone,
                        message: "请输入正确的手机号",
                        trigger: "change",
                        required: true,
                    },
                ],
            },
            disabled: false,
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询msds */
        getListpage() {
            msds().then((res) => {
                if (res.code == 200) {
                    console.log(res);
                    this.originArr = res.data;
                }
            });
        },
        getList() {
            this.loading = true;
            console.log(this.queryParams);
            page(this.queryParams).then((response) => {
                this.roleList = response.data.records;
                this.total = response.data.total;
                this.loading = false;
                console.log(response);
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },

        // 表单重置
        reset() {
            this.form = {
                id: null,
                dangerType: undefined,
                griddingId: undefined,
                location: undefined,
                description: undefined,
                attachment: undefined,
            };
            this.resetForm("form");
        },

        /** 搜索按钮操作 */
        handleQuery() {
            if (this.dateRange.length > 0) {
                this.queryParams.startTime = this.dateRange[0];
                this.queryParams.endTime = this.dateRange[1];
            }
            this.queryParams.current = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.dateRange = [];
            this.queryParams.startTime = undefined;
            this.queryParams.endTime = undefined;
            this.resetForm("queryForm");
            this.handleQuery();
        },

        handleRemove(file, fileList) {
            console.log(file, fileList);
            let arr = [];
            fileList.map((res) => {
                arr.push(res.url);
            });
            this.form.attachment = arr.join(",");
        },
        handlePreview(response, file, fileList) {
            console.log(response, file, fileList);
            if (file.size == 0) {
                this.$modal.msgWarning("当前文件大小不符合规范");

                return true;
            }
            let arr = [];
            fileList.map((res) => {
                if (res.response) {
                    arr.push(JSON.parse(crypto.decryptAES(res.response, crypto.aesKey)));
                } else {
                    arr.push(res.url);
                }
            });
            this.form.attachment = arr.join(",");
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.disabled = false;
            this.reset();
            this.open = true;
            this.title = "新增危险源";
            this.fileList = [];
            this.getListpage();
        },
        /** 修改按钮操作 */
        handleUpdate(row, type) {
            if (type == "look") {
                this.disabled = true;
                this.title = "查看危险源";
            } else {
                this.disabled = false;
                this.title = "编辑危险源";
            }
            this.reset();
            this.open = true;
            this.form = JSON.parse(JSON.stringify(row));
            this.fileList = [];
            this.form.amount = null;
            if (row.attachment != null) {
                let arr = [];
                arr = JSON.parse(JSON.stringify(this.form.attachment.split(",")));
                arr.map((res, i) => {
                    let arr1 = res.split("/");
                    this.fileList.push({
                        name: Date.now() + "_" + arr1[arr1.length - 1],
                        url: res,
                    });
                });
            }
        },

        /** 提交按钮 */
        submitForm: function () {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    console.log(this.form);
                    if (this.form.id != undefined) {
                        update(this.form).then((response) => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        save(this.form).then((response) => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },

        /** 删除按钮操作 */
        handleDelete(row) {
            this.$modal
                .confirm("是否确认删除当前数据")
                .then(function () {
                    return dele({ id: row.id });
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess("删除成功");
                })
                .catch(() => { });
        },
    },
};
</script>
<style lang="scss" scoped>
.diaTil {
    display: flex;
    align-items: center;

    p {
        font-size: 20px;
        font-weight: bold;
    }
}

.box-card-bottom {
    margin: 20px;
}

.box-card {
    margin-bottom: 20px;
    z-index: 2;
}

.queryBtnT {
    height: 32px;
    border: 1px solid #cccccc;
    border-radius: 2px;
    font-size: 13px;
    float: right;
    margin-right: 10px;
}
</style>