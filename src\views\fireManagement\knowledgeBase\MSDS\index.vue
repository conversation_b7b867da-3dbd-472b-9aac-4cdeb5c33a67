<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据筛选</span>
          </div>
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-position="left"
            style="display: flex; justify-content: space-between">
            <div>
              <el-form-item label="危化品中文名称">
                <el-input v-model.number="queryParams.name" placeholder="请输入危化品名称" style="width: 10vw"
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
            </div>
            <div style="min-width: 166px">
              <el-form-item>
                <el-button class="resetQueryStyle" type="primary" icon="el-icon-search"
                  @click="handleQuery">搜索</el-button>
                <el-button class="resetQueryStyle" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </div>
          </el-form>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>危化品展示列表</span>
          </div>
          <el-table v-loading="loading" :data="dataList">
            <el-table-column label="危化品中文名称" align="center" prop="name" show-overflow-tooltip />
            <el-table-column label="危化品俗名" align="center" prop="trivialName" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.trivialName">{{
                  scope.row.trivialName
                }}</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="技术说明书编码" align="center" prop="msdsId" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.msdsId">{{ scope.row.msdsId }}</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="CAS号" align="center" prop="casId" show-overflow-tooltip><template slot-scope="scope">
                <span v-if="scope.row.casId">{{ scope.row.casId }}</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="分子量" align="center" prop="molecularWeight" show-overflow-tooltip><template
                slot-scope="scope">
                <span v-if="scope.row.molecularWeight">{{
                  scope.row.molecularWeight
                }}</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="危险性类别" align="center" prop="clasn" show-overflow-tooltip>
              <template slot-scope="scope">
                <span v-if="scope.row.clasn">{{
                  scope.row.clasn
                }}</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-view" @click="handleLook(scope.row)">查看</el-button>
                <!-- <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-delete"
                  @click="handleDownload(scope.row)"
                  >下载</el-button
                > -->
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
            @pagination="getList" />
        </el-card>
      </el-col>
    </el-row>
    <!--  -->
    <!-- 查看详情 -->
    <el-dialog :title="title" :visible.sync="abilityOpen" width="560px" append-to-body fullscreen>
      <div>
        <el-row>
          <el-col :span="3">
            <el-button class="navigation" style="margin-left:10px;" @click="navigation('1')">危险品名称</el-button>
            <el-button class="navigation" @click="navigation('2')">成分/组成信息</el-button>
            <el-button class="navigation" @click="navigation('3')">危险性概述</el-button>
            <el-button class="navigation" @click="navigation('4')">急救措施</el-button>
            <el-button class="navigation" @click="navigation('5')">消防措施</el-button>
            <el-button class="navigation" @click="navigation('6')">泄露应急处理</el-button>
            <el-button class="navigation" @click="navigation('7')">操作处理与储存</el-button>
            <el-button class="navigation" @click="navigation('8')">接触控制/个体防护</el-button>
            <el-button class="navigation" @click="navigation('9')">理化特性</el-button>
            <el-button class="navigation" @click="navigation('10')">稳定性和反应活性</el-button>
            <el-button class="navigation" @click="navigation('11')">毒理学资料</el-button>
            <el-button class="navigation" @click="navigation('12')">生态学资料</el-button>
            <el-button class="navigation" @click="navigation('13')">废弃处置</el-button>
            <el-button class="navigation" @click="navigation('14')">运输信息</el-button>
            <el-button class="navigation" @click="navigation('15')">法规信息</el-button>
            <el-button class="navigation" @click="navigation('16')">安全标签信息</el-button>
          </el-col>
          <el-col :span="21">
            <el-collapse v-model="activeNames" @change="handleChange">
              <el-collapse-item title="化学品名称" name="1">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="危化品中文名称" prop="name">
                              <el-input v-model="detailInfo.name" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="危化品英文名称" prop="engName">
                              <el-input v-model="detailInfo.engName" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="危化品俗名" prop="trivialName">
                              <el-input v-model="detailInfo.trivialName" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="英文名称" prop="trivialEngName">
                              <el-input v-model="detailInfo.trivialEngName" :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="技术说明书编号" prop="msdsId">
                              <el-input v-model="detailInfo.msdsId" :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="CAS号" prop="casId">
                              <el-input v-model="detailInfo.casId" :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="分子式" prop="molecularFormula">
                              <el-input v-model="detailInfo.molecularFormula" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="分子量" prop="molecularWeight">
                              <el-input v-model="detailInfo.molecularWeight" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item title="成分/组成信息" name="2">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="纯品或混合物" prop="pureOrMix">
                              <el-input v-model="detailInfo.pureOrMix" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="有害物成分1" prop="ingredientI">
                              <el-input v-model="detailInfo.ingredientI" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="含量(%)" prop="ingredientContentI">
                              <el-input v-model="detailInfo.ingredientContentIi" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="有害物cas号" prop="ingredientCasI">
                              <el-input v-model="detailInfo.ingredientCasI" :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="有害物成分2" prop="ingredientIi">
                              <el-input v-model="detailInfo.ingredientIi" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="含量(%)" prop="ingredientContentIi">
                              <el-input v-model="detailInfo.ingredientContentIi" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="有害物cas号" prop="ingredientCasIi">
                              <el-input v-model="detailInfo.ingredientCasIi" :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="有害物成分3" prop="ingredientIii">
                              <el-input v-model="detailInfo.ingredientIii" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="含量(%)" prop="ingredientContentIii">
                              <el-input v-model="detailInfo.ingredientContentIii" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="有害物cas号" prop="ingredientCasIii">
                              <el-input v-model="detailInfo.ingredientCasIii" :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="有害物成分4" prop="ingredientIv">
                              <el-input v-model="detailInfo.ingredientIv" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="含量(%)" prop="ingredientContentIv">
                              <el-input v-model="detailInfo.ingredientContentIv" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="有害物cas号" prop="ingredientCasIv">
                              <el-input v-model="detailInfo.ingredientCasIv" :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item title="危险性概述" name="3">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="危险性类别" prop="healthHazard">
                              <el-input v-model="detailInfo.healthHazard" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="侵入途径" prop="invasionPath">
                              <el-input v-model="detailInfo.invasionPath" :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="健康危害" prop="healthHazard">
                              <el-input v-model="detailInfo.healthHazard" :disabled="true" type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="爆燃危害" prop="deflagrationHazard">
                              <el-input v-model="detailInfo.deflagrationHazard" :disabled="true" type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="环境危害" prop="environmentHazard">
                              <el-input v-model="detailInfo.environmentHazard" :disabled="true" type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item title="急救措施" name="4">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="皮肤接触" prop="skinContact">
                              <el-input v-model="detailInfo.skinContact" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="眼睛接触" prop="eyesContact">
                              <el-input v-model="detailInfo.eyesContact" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="吸入" prop="suction">
                              <el-input v-model="detailInfo.suction" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="食入" prop="ingestion">
                              <el-input v-model="detailInfo.ingestion" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsSafeinfos" title="消防措施" name="5">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="危险特性" prop="forbiddenCompound">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.forbiddenCompound" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="有害燃烧产物" prop="harmfulCombustionProduct">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.harmfulCombustionProduct"
                                :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="灭火方法" prop="outfireMethod">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.outfireMethod" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsSafeinfos" title="泄露应急处理" name="6">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="应急处理" prop="leakEmergency">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.leakEmergency" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsSafeinfos" title="操作处理与储存" name="7">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="操作注意事项" prop="operateMatter">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.operateMatter" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="存储注意事项" prop="storeMatter">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.storeMatter" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsSafeinfos" title="接触控制/个体防护" name="8">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="中国MAC(mg/m)" prop="chinaMac">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.chinaMac" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="前苏联mac(mg/m)" prop="fsuMac">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.fsuMac" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="TLVIN(ppm,mg/m³)" prop="tlvtn">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.tlvtn" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="TLVWN(ppm,mg/m³)" prop="tlvwn">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.tlvwn" :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="监测方法" prop="avoidCondition">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.avoidCondition" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="工程控制" prop="engineeringControl">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.engineeringControl" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="呼吸系统防护" prop="respiratoryProtection">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.respiratoryProtection" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="眼睛防护" prop="eyesProtection">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.eyesProtection" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="应急处理" prop="leakEmergency">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.leakEmergency" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="身体防护" prop="bodyProtection">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.bodyProtection" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="手防护" prop="handProtection">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.handProtection" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="其他防护" prop="otherProtection">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.otherProtection" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsSafeinfos" title="理化特性" name="9">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="外观与形状" prop="appearanceShape">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.appearanceShape" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="溶解性" prop="solubility">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.solubility" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="主要用途" prop="used">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.used" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="pH值" prop="ph">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.ph" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="熔点(℃)" prop="fusingPoint">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.fusingPoint"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="相对密度(水=1)" prop="relativeDensity">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.relativeDensity" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="沸点(℃)" prop="boilingPoint">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.boilingPoint"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="相对密度(空气=1)" prop="relativeDensity">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.relativeDensity" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="闪点(℃)" prop="flashPoint">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.flashPoint"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="辛醇/水分配系数" prop="distributionCoefficient">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.distributionCoefficient"
                                :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="引燃温度(℃)" prop="ignitionTemperature">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.ignitionTemperature"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="爆炸下限[%(V/V)]" prop="explosiveLowLimit">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.explosiveLowLimit" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="临界温度(℃)" prop="explosiveUpLimitcriticalTemperature">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.explosiveUpLimitcriticalTemperature"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="爆炸上限[%(V/V)]" prop="explosiveUpLimit">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.explosiveUpLimit" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="临界压力(MPa)" prop="criticalPressure">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.criticalPressure"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="饱和蒸气压(kPa)" prop="saturatedVaporPressure">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.saturatedVaporPressure"
                                :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="其他理化性质" prop="otherProperty">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.otherProperty"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsSafeinfos" title="稳定性和反应活性" name="10">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="稳定性" prop="stability">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.stability" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="禁配物" prop="forbiddenCompound">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.forbiddenCompound" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="禁配物" prop="forbiddenCompound">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.forbiddenCompound" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="避免接触条件" prop="avoidCondition">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.avoidCondition" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="聚合危害" prop="polHazard">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.polHazard" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="燃烧分解产物" prop="combustionProduct">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.combustionProduct"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsTransportinfos" title="毒理学资料" name="11">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="LD50" prop="ld50">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.ld50" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="lc50" prop="lc50">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.lc50" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="LD50" prop="ld50">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.materialName" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="亚急性和慢性毒性" prop="subacute">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.subacute" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="刺激性" prop="thrill">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.thrill" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="致敏性" prop="anaphylaxis">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.anaphylaxis" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="致突变性" prop="mutability">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.mutability"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="致畸性" prop="teratogenicity">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.teratogenicity"
                                :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="致癌性" prop="carcinogenicity">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.carcinogenicity"
                                :disabled="true"></el-input>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="其他有害作用" prop="toxiOther">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.toxiOther" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsTransportinfos" title="生态学资料" name="12">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="生态毒性" prop="ecotoxicity">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.ecotoxicity" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="生物降解性" prop="degradability">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.degradability" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="非生物降解性" prop="nondegradability">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.nondegradability"
                                :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="生物富集或生物积累性" prop="fswjjx">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.fswjjx" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="其它有害作用" prop="otherHarm">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.otherHarm" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsTransportinfos" title="废弃处置" name="13">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="废弃物性质" prop="wasteProperty">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.materialName" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="废弃处置方法" prop="wasteDisposal">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.wasteDisposal" :disabled="true"
                                type="textarea" />
                              <!-- <div>isiisis:</div>
                          <div>{{ detailInfo.name || "--" }}</div> -->
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="废弃注意事项" prop="wasteMatter">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.wasteMatter" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsTransportinfos" title="运输信息" name="14">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="危险物质编号" prop="dangerousGoodsId">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.dangerousGoodsId" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="UN编号" prop="unId">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.unId" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="包装标志" prop="packingSign">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.packingSign" :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="包装类别" prop="packingType">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.packingType" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="包装方法" prop="packingMethod">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.packingMethod" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="运输注意事项" prop="tranMatter">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.tranMatter" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsTransportinfos" title="法规信息" name="15">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="法规信息" prop="lawInfo">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.lawInfo" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
              <el-collapse-item v-if="detailInfo.emergencyMsdsTransportinfos" title="安全标签信息" name="16">
                <div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form ref="abilityForm" :model="detailInfo" label-width="110px">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="分子式" prop="molecularFormula">
                              <el-input v-model="detailInfo.emergencyMsdsTransportinfos.molecularFormula"
                                :disabled="true" />
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="危害程度" prop="hazardousProperty">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.hazardousProperty" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="危险性概述" prop="materialType">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.materialName" :disabled="true" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="安全措施" prop="materialName">
                              <el-input v-model="abilityForm.materialName" :disabled="true" type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="灭火方法" prop="outfireMethod">
                              <el-input v-model="detailInfo.emergencyMsdsSafeinfos.outfireMethod" :disabled="true"
                                type="textarea" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>
      
<script>
import { page, pageDetail } from "@/api/fireManagement/knowledgeBase/MSDS/index";
export default {
  data() {
    return {
      loading: false,
      dataList: [],
      detailInfo: {
        emergencyMsdsSafeinfos: {},
        emergencyMsdsTransportinfos: {}
      },
      activeNames: ["1"],
      //查看危险品详情
      popoverVisible: false,
      // 总条数
      total: 0,
      // 是否显示弹出层
      abilityOpen: false,
      title: "危险品详情",
      text: undefined,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        name: undefined,
      },
      frequency: 0,
      abilityForm: {},
      disabled: false,
      // 表单校验
      // abilityRules: {
      //   refugeName: [
      //     {
      //       required: true,
      //       message: "危化品中文名称不能为空",
      //       trigger: "blur",
      //     },
      //   ],
      // },
      nodeObj: undefined,
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    handleChange(val) {
      console.log(val);
    },
    showPopover() {
      this.popoverVisible = !this.popoverVisible;
    },
    /** 查询场所列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        console.log(response);
        if (response.data != null) {
          this.dataList = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    //获取场所详情

    handleLook(row) {
      pageDetail({ id: row.id }).then((response) => {
        console.log(response);
        if (response.data != null) {
          this.detailInfo = response.data;
          this.abilityOpen = true;
        }
      });
    },

    // jtopoDel() {
    //   this.abilityForm.node = undefined;
    //   toNode.remove();
    //   toNode = undefined;
    // },
    // 取消按钮
    // cancel() {
    //   this.abilityOpen = false;
    //   this.reset();
    // },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },

    // 取消按钮
    // 表单重置
    reset() {
      this.abilityForm = {
        id: undefined,
        refugeName: undefined,
        refugeArea: undefined,
        holdsNumber: undefined,
        liabilityUser: undefined,
        phone: undefined,
        remark: undefined,
      };
      this.lngAndLat = "";
      this.resetForm("abilityForm");
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        current: 1,
        size: 10,
        name: undefined,
      };
      this.resetForm("queryForm");
      this.handleQuery();
    },
    navigation(id) {
      this.activeNames = [id]
    }
  },
};
</script>
<style lang="scss" scoped>
.left_title {
  color: rgba(56, 56, 56, 1);
  font-size: 24px;
  font-weight: bold;
  padding-bottom: 14px;
}

::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

.resetQueryStyle {
  width: 88px;
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
}
.navigation{
  display: block;
  width: 150px;
  height: 32px;
  border: 1px solid #cccccc;
  border-radius: 2px;
  font-size: 13px;
  margin-bottom: 15px;
}
.popupButton {
  width: 96px;
  height: 40px;
  border-radius: 2px;
}
</style>