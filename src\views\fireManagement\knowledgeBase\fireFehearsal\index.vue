<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:role:add']"
          >新增消防预演</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <el-table-column type="index" width="50" />
      <el-table-column
        label="预演名称"
        :show-overflow-tooltip="true"
        prop="name"
        align="center"
      />
      <el-table-column label="预演描述" prop="description" align="center" />
      <el-table-column label="制定时间" prop="ruleTime" align="center" />
      <el-table-column label="创建人" prop="createUser" align="center" />
      <el-table-column label="预演次数" prop="amount" align="center" />
      <el-table-column label="创建日期" prop="createTime" align="center" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:role:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:role:edit']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="消防预演名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入消防预演名称" />
        </el-form-item>

        <el-form-item label="制定日期" prop="ruleTime">
          <el-date-picker
            v-model="form.ruleTime"
            type="datetime"
            placeholder="选择日期时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <el-upload
            ref="my-upload"
            :headers="headers"

            :action="uploadImgUrl"
            :on-remove="handleRemove"
            :on-success="handlePreview"
            :file-list="fileList"
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="演练记录" v-show="form.id != undefined">
          <el-button type="primary" @click="addTabel">新增</el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="tableData"
        height="250"
        v-loading="loadingtable"
        border
        style="width: 100%"
        v-show="form.id != undefined"
      >
        <el-table-column
          prop="participant"
          label="参与人员"
          :show-overflow-tooltip="true"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="rehearsalTime"
          label="演练日期"
          :show-overflow-tooltip="true"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="result"
          label="演练结果"
          :show-overflow-tooltip="true"
          align="center"
        >
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              v-hasPermi="['system:role:edit']"
              @click="updateTabel(scope.row)"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              v-hasPermi="['system:role:edit']"
              @click="delTabel(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        width="600px"
        :title="titleVisible"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-form
          ref="formVisible"
          :model="formVisible"
          :rules="rulesVisible"
          label-width="120px"
        >
          <el-form-item label="参与人员" prop="participant">
            <el-input
              v-model="formVisible.participant"
              placeholder="请输入参与人员"
            />
          </el-form-item>

          <el-form-item label="演练时间" prop="rehearsalTime">
            <el-date-picker
              v-model="formVisible.rehearsalTime"
              type="datetime"
              placeholder="选择演练时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="演练结果" prop="result">
            <el-input
              v-model="formVisible.result"
              placeholder="请输入演练结果"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFormVisible">确 定</el-button>
          <el-button @click="cancelVisible">取 消</el-button>
        </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
  update,
  pageRecord,
  saveRecord,
  updateRecord,
  dele,
} from "@/api/fireManagement/knowledgeBase/fireFehearsal/index";
import crypto from "@/utils/crypto";
export default {
  name: "FireFehearsal",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [{}],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
      },
      // 表单参数
      form: {},
      dateRange: [],
      // 表单校验
      rules: {
        name: [
          { required: true, message: "请输入消防预演名称", trigger: "blur" },
        ],
        ruleTime: [
          { required: true, message: "选择日期时间", trigger: "change" },
        ],
      },
      tableData: [],
      loadingtable: false,
      innerVisible: false,
      formVisible: {},
      titleVisible: "",
        fileList: [],
        headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadImgUrl: "/emergency-v2/file/uploadFile",
      uploadFile: process.env.VUE_APP_BASE_API + "api/file/uploadFile",

      rulesVisible: {
        participant: [
          { required: true, message: "请输入参与人员", trigger: "blur" },
        ],
        rehearsalTime: [
          { required: true, message: "选择演练时间", trigger: "change" },
        ],
        result: [
          { required: true, message: "请输入演练结果", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        this.roleList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
      this.getList();
    },

    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        ruleTime: undefined,
        description: undefined,
        attachment: undefined,
        amount: null,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handledownloadFile(row) {
      let arr1 = row.attachment.split(",");
      console.log(arr1);
      arr1.map((res) => {
        let arr = res.split("/");
        // downloadFile(`${arr[3]}`, `/${arr[4]}/${arr[5]}`, `${arr[6]}`).then(
        //   (res) => {
        //     window.url(res);
        //   }
        // );
        console.log(arr);
        window.open(
          `/api/file/downloadFile?bucket=${arr[3]}&path=${arr[4]}/${arr[5]}&fileName=${arr[6]}`
        );
      });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      let arr = [];
      fileList.map((res) => {
        arr.push(res.url);
      });
      this.form.attachment = arr.join(",");
    },
    handlePreview(response, file, fileList) {
      console.log(response, file, fileList);
      if (file.size == 0) {
        this.$modal.msgWarning("当前文件大小不符合规范");

        return true;
      }
      let arr = [];
      fileList.map((res) => {
        if (res.response) {
          arr.push(JSON.parse(crypto.decryptAES(res.response, crypto.aesKey)));
        } else {
          arr.push(res.url);
        }
      });
      this.form.attachment = arr.join(",");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增消防预演";
      this.form.amount = 0;
      this.fileList = [];
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "修改消防预演";
      this.form = JSON.parse(JSON.stringify(row));
      this.fileList = [];
      this.form.amount = null;
      if (row.attachment != null) {
        let arr = [];
        arr = JSON.parse(JSON.stringify(this.form.attachment.split(",")));
        arr.map((res, i) => {
          let arr1 = res.split("/");
          this.fileList.push({
            name: Date.now() + "_" + arr1[arr1.length - 1],
            url: res,
          });
        });
      }

      this.getPageRecord();
    },
    getPageRecord() {
      this.loadingtable = true;
      pageRecord({ rehearsalId: this.form.id }).then((response) => {
        console.log(response);
        this.tableData = response.data;
        this.loadingtable = false;
      });
    },
    resetVisible() {
      this.formVisible = {
        id: undefined,
        participant: undefined,
        rehearsalTime: undefined,
        result: undefined,
        rehearsalId: this.form.id,
      };
      this.resetForm("formVisible");
    },
    addTabel() {
      this.resetVisible();
      this.innerVisible = true;
      this.titleVisible = "新增演练记录";
    },
    updateTabel(row) {
      this.resetVisible();
      this.innerVisible = true;
      this.formVisible = JSON.parse(JSON.stringify(row));
      this.titleVisible = "编辑演练记录";
    },
    delTabel(row) {
      let that = this;
      that.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return dele({ id: row.id, rehearsalId: that.form.id });
        })
        .then(() => {
          that.getPageRecord();
          that.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    submitFormVisible() {
      this.$refs["formVisible"].validate((valid) => {
        if (valid) {
          if (this.formVisible.id != undefined) {
            updateRecord(this.formVisible).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.innerVisible = false;
              this.getPageRecord();
              this.getList();
            });
          } else {
            saveRecord(this.formVisible).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.innerVisible = false;
              this.getPageRecord();
              this.getList();
            });
          }
        }
      });
    },
    cancelVisible() {
      this.innerVisible = false;
      this.resetVisible();
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          console.log(this.form);
          if (this.form.id != undefined) {
            update(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            save(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return update({ id: row.id, isDeleted: 1 });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;
  p {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>