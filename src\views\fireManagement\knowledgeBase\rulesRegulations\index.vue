<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:role:add']"
          >新增规章制度</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <el-table-column type="index" width="50">
        <template slot-scope="scope">
          <span>{{
            (queryParams.current - 1) * queryParams.size + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="制度名称"
        :show-overflow-tooltip="true"
        prop="ruleName"
        align="center"
      />
      <el-table-column label="制度类型" prop="ruleType" align="center">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulations_type"
            :value="scope.row.ruleType"
            :type="1"
          />
        </template>
      </el-table-column>
      <el-table-column label="制定时间" prop="ruleTime" align="center" />
      <el-table-column label="创建人" prop="createUser" align="center" />
      <el-table-column label="创建日期" prop="createTime" align="center" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:role:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:role:edit']"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handledownloadFile(scope.row)"
            v-hasPermi="['system:role:edit']"
            >下载</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="制度名称" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="请输入制度名称" />
        </el-form-item>
        <el-form-item label="制度类型" prop="ruleType">
          <el-select
            v-model="form.ruleType"
            placeholder="请选择制度类型"
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.regulations_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="制定日期" prop="ruleTime">
          <el-date-picker
            v-model="form.ruleTime"
            type="datetime"
            placeholder="选择日期时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <el-upload
            ref="my-upload"
            :headers="headers"
            :action="uploadImgUrl"
            :on-remove="handleRemove"
            :on-success="handlePreview"
            :file-list="fileList"
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
  update,
  downloadFile,
} from "@/api/fireManagement/knowledgeBase/rulesRegulations/index";
import crypto from "@/utils/crypto";
export default {
  name: "RulesRegulations",
  dicts: ["rule_type", "regulations_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
      },
      // 表单参数
      form: {},
      dateRange: [],
      fileList: [],
      // 表单校验
      rules: {
        ruleType: [
          { required: true, message: "请选择制度类型", trigger: "change" },
        ],
        ruleName: [
          { required: true, message: "请输入制度名称", trigger: "blur" },
        ],
        ruleTime: [
          { required: true, message: "选择日期时间", trigger: "change" },
        ],
      },
        uploadFile: process.env.VUE_APP_BASE_API + "api/file/uploadFile",
        headers: {
        Authorization: localStorage.getItem("token"),
      },
      uploadImgUrl: "/emergency-v2/file/uploadFile",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        this.roleList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        ruleName: undefined,
        ruleType: undefined,
        ruleTime: undefined,
        remark: undefined,
        attachment: undefined,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();

      this.open = true;
      this.fileList = [];
      this.title = "新增规章制度";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.fileList = [];
      this.title = "修改规章制度";
      this.form = JSON.parse(JSON.stringify(row));
      this.fileList = [];
      if (row.attachment != null) {
        let arr = [];
        arr = JSON.parse(JSON.stringify(this.form.attachment.split(",")));
        arr.map((res, i) => {
          let arr1 = res.split("/");
          console.log(arr1);
          this.fileList.push({
            name: Date.now() + "_" + arr1[arr1.length - 1],
            url: res,
          });
        });
      }
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      let arr = [];
      fileList.map((res) => {
        arr.push(res.url);
      });
      this.form.attachment = arr.join(",");
    },
    handledownloadFile(row) {
      let arr1 = row.attachment.split(",");
      console.log(arr1);
      arr1.map((res) => {
        let arr = res.split("/");
        // downloadFile(`${arr[3]}`, `/${arr[4]}/${arr[5]}`, `${arr[6]}`).then(
        //   (res) => {
        //     window.url(res);
        //   }
        // );
        console.log(arr);
        window.open(
          `/api/file/downloadFile?bucket=${arr[3]}&path=${arr[4]}/${arr[5]}&fileName=${arr[6]}`
        );
      });
    },
    handlePreview(response, file, fileList) {
      console.log(response, file, fileList);
      if (file.size == 0) {
        this.$modal.msgWarning("当前文件大小不符合规范");

        return true;
      }
      let arr = [];
      fileList.map((res) => {
        if (res.response) {
          arr.push(JSON.parse(crypto.decryptAES(res.response, crypto.aesKey)));
        } else {
          arr.push(res.url);
        }
      });
      this.form.attachment = arr.join(",");
    },
    /** 提交按钮 */
    submitForm: function () {
      console.log(this.form);
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            update(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.$refs["my-upload"].clearFiles();
              this.open = false;
              this.getList();
            });
          } else {
            save(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.$refs["my-upload"].clearFiles();
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return update({ id: row.id, isDeleted: 1 });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;
  p {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>