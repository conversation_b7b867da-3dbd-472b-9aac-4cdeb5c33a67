<template>
  <div class="app-container">
    <div class="top_device">
      <div class="top_card">
        <el-card shadow="hover">
          <div class="top_title">全部设备</div>
          <div class="top_num">{{ deviceObj.total }}</div>
        </el-card>
      </div>
      <div>
        <el-card shadow="hover">
          <div class="top_title">正常</div>
          <div class="top_num">{{ deviceObj.normal }}</div>
        </el-card>
      </div>
      <div>
        <el-card shadow="hover">
          <div class="top_title">报警</div>
          <div class="top_num">{{ deviceObj.emergency }}</div>
        </el-card>
      </div>
      <div>
        <el-card shadow="hover">
          <div class="top_title">故障</div>
          <div class="top_num">{{ deviceObj.breakdown }}</div>
        </el-card>
      </div>
      <div>
        <el-card shadow="hover">
          <div class="top_title">离线</div>
          <div class="top_num">{{ deviceObj.offline }}</div>
        </el-card>
      </div>
    </div>
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input v-model="deptName" placeholder="请输入设备名称" clearable size="small" prefix-icon="el-icon-search"
            style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="dict.type.fire_device_type" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current
            @node-click="handleNodeClick" />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="设备编号" prop="deviceId">
            <el-input v-model="queryParams.deviceId" placeholder="请输入设备编号" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="设备状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="设备状态" clearable style="width: 240px">
              <el-option v-for="dict in dict.type.fire_device_status" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:user:add']"
              >新增设备</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="multiple"
              @click="statMory"
              v-hasPermi="['system:user:edit']"
              >批量启用</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="clockMory"
              v-hasPermi="['system:user:remove']"
              >批量禁用</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-upload2"
              size="mini"
              :disabled="multiple"
              v-hasPermi="['system:user:import']"
              >批量导入</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-download"
              size="mini"
              :disabled="multiple"
              @click="deleMory"
              v-hasPermi="['system:user:export']"
              >批量删除</el-button
            >
          </el-col> -->
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="设备编号" align="center" prop="deviceId" />
          <el-table-column label="设备名称" align="center" prop="deviceName" />
          <el-table-column label="所属部门" align="center" prop="organizationId" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              {{ scope.row.organizationId | getfullName(deptArr) }}
            </template>
          </el-table-column>
          <el-table-column label="安装位置" align="center" prop="location" :show-overflow-tooltip="true" />
          <el-table-column label="责任人" align="center" prop="principal" :show-overflow-tooltip="true" />

          <el-table-column label="状态" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.fire_device_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <!-- <el-table-column label="关联摄像头" align="center" prop="monitor">
          </el-table-column> -->
          <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" v-hasPermi="['system:user:edit']"
                @click="handleDetail(scope.row)">详情</el-button>
              <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['system:user:edit']">关联摄像头</el-button> -->
              <el-button size="mini" type="text" @click="hanldClock(scope.row)"
                v-hasPermi="['system:user:edit']">禁用</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['system:user:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
          @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" label-position="left">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="1">
            <template slot="title">
              <div class="diaTil">
                <p>基础信息</p>
              </div>
            </template>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备编号" prop="deviceId">
                  <el-input v-model="form.deviceId" placeholder="请输入设备编号" :disabled="disabled" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备名称" prop="deviceName">
                  <el-input v-model="form.deviceName" placeholder="请输入设备名称" :disabled="disabled" />
                </el-form-item></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备型号" prop="deviceModel">
                  <el-input v-model="form.deviceModel" placeholder="请输入设备型号" :disabled="disabled" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备类型" prop="deviceType">
                  <el-select v-model="form.deviceType" placeholder="请选择设备类型" :disabled="disabled" style="width: 100%">
                    <el-option v-for="dict in dict.type.fire_device_type" :key="dict.value" :label="dict.label"
                      :value="dict.value"></el-option> </el-select></el-form-item></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属部门" prop="organizationId">
                  <el-select v-model="form.organizationId" placeholder="所属部门" clearable @click="deptChange()"
                    style="width: 100%" :disabled="disabled">
                    <el-option v-for="dict in deptArr" :key="dict.organizationId" :label="dict.name"
                      :value="dict.organizationId" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="关联摄像头" prop="monitor">
                  <el-select v-model="form.monitor" placeholder="请选择关联摄像头" style="width: 100%">
                    <!-- <el-option
                      v-for="dict in dict.type.fire_device_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option> -->
                  </el-select>
                </el-form-item></el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="2">
            <template slot="title">
              <div class="diaTil">
                <p>责任人</p>
                <!-- <el-button
                  type="primary"
                  plain
                  size="mini"
                  style="height: 30px; margin-left: 10px"
                  >新增责任人</el-button
                > -->
              </div>
            </template>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="责任人" prop="principal">
                  <el-input v-model="form.principal" placeholder="请输入责任人" :disabled="disabled" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="form.phone" :disabled="disabled" placeholder="请输入联系电话" /> </el-form-item></el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="3">
            <template slot="title">
              <div class="diaTil">
                <p>安装位置</p>
              </div>
            </template>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="安装区域" prop="griddingId">
                  <el-select v-model="form.griddingId" :disabled="disabled" placeholder="安装区域" style="width: 100%">
                    <el-option v-for="dict in areaArr" :key="dict.griddingId" :label="dict.griddingName"
                      :value="dict.griddingId" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <el-row>
              <el-col :span="24">
                <el-form-item label="详细地址" prop="location">
                  <el-input
                    v-model="form.location"
                    :disabled="disabled"
                    placeholder="请输入详细地址"
                  />
                  <div
                    style="
                      width: 100%;
                      height: 140px;
                      background: #333;
                      margin-top: 10px;
                      color: #fff;
                      text-align: center;
                      line-height: 140px;
                    "
                  >
                    暂无地图
                  </div> 
                </el-form-item>
              </el-col>
            </el-row>  -->
          </el-collapse-item>
          <!-- <el-collapse-item name="4">
            <template slot="title">
              <div class="diaTil">
                <p>报警设置</p>
              </div>
            </template>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="报警类型" prop="emergencyType">
                  <el-select
                    v-model="form.emergencyType"
                    :disabled="disabled"
                    placeholder="请选择报警类型"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="dict in dict.type.emergency_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="报警阈值" prop="threshold">
                  <el-input
                    placeholder="请输入报警阈值"
                    :disabled="disabled"
                    v-model="form.threshold"
                  >
                    <template slot="append">%</template>
                  </el-input></el-form-item
                ></el-col
              >
            </el-row>
          </el-collapse-item> -->
          <el-collapse-item name="5">
            <template slot="title">
              <div class="diaTil">
                <p>设备照片</p>
              </div>
            </template>
            <el-upload :disabled="disabled" :action="uploadPic" list-type="picture-card" :on-remove="handleRemove"
              :on-preview="handlePreview">
              <i class="el-icon-camera-solid"></i>
            </el-upload>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog title="设备详情" :visible.sync="detailOpen" width="1000px" append-to-body>
      <el-card shadow="hover"><el-row :gutter="20">
          <!--部门数据-->
          <el-col :span="24" :xs="14">
            <div class="diadevice_top" style="margin-bottom: 40px">
              <div>设备编号：{{ diaDeviceObj.deviceId }}</div>
              <div>设备名称：{{ diaDeviceObj.deviceName }}</div>
              <div>
                所属部门：{{
                  diaDeviceObj.organizationId | getfullName(deptArr)
                }}
              </div>
            </div>
            <div class="diadevice_top">
              <div style="display: flex">
                当前状态：
                <dict-tag :options="dict.type.fire_device_status" :value="diaDeviceObj.status" :type="1" />
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-button-group style="margin-top: 10px">
        <el-button :type="btnDia == '1' ? 'primary' : ''" @click="diaDeatilBtn('1')" size="mini">设备状态</el-button>
        <el-button :type="btnDia == '2' ? 'primary' : ''" size="mini" @click="diaDeatilBtn('2')">运行数据</el-button>
        <!-- <el-button
          :type="btnDia == '3' ? 'primary' : ''"
          size="mini"
          @click="diaDeatilBtn('3')"
          >告警数据</el-button
        > -->
      </el-button-group>
      <el-form :model="diaFrom" ref="diaFrom" size="small" :inline="true" label-width="68px" style="margin-top: 10px">
        <el-form-item label="">
          <el-date-picker v-model="diaFrom.dateRange" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="参数值" v-show="btnDia == '2'">
          <el-select
            v-model="diaFrom.param"
            placeholder="参数值"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dict in dict.type.symbol"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="" v-show="btnDia == '2'">
          <el-input
            v-model="diaFrom.value"
            placeholder="请输入"
            clearable
            style="width: 200px"
          />
        </el-form-item> -->
        <el-form-item label="" v-show="btnDia == '1'">
          <!-- <el-radio-group v-model="diaFrom.status">
            <el-radio label="undefined">全部</el-radio>
            <el-radio label="4010501">在线</el-radio>
            <el-radio label="4010504">离线</el-radio>
          </el-radio-group> -->
          <el-select v-model="diaFrom.status" placeholder="设备状态" clearable style="width: 240px">
            <el-option v-for="dict in dict.type.fire_device_status" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="报警类型" v-show="btnDia == '3'">
          <el-select v-model="diaFrom.emergencyType" placeholder="报警类型" clearable style="width: 200px">
            <el-option v-for="dict in dict.type.emergency_type" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态" v-show="btnDia == '3'">
          <el-select v-model="diaFrom.emergencyStatus" placeholder="处理状态" clearable style="width: 200px">
            <el-option v-for="dict in dict.type.emergency_status" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="diaSerch()">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetTbableDia()">重置</el-button>
        </el-form-item>
      </el-form>
      <div style="width: 100%; height: 400px">
        <el-table :data="tableData" height="350" border style="width: 100%" v-loading="loadingDia"
          v-if="tableData.length != 0">
          <el-table-column type="index" width="50" align="center">
          </el-table-column>
          <el-table-column prop="emergencyType" label="告警类型" align="center" v-if="btnDia == '3'">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.emergency_type" :value="scope.row.emergencyType" :type="1" />
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="时间" align="center" v-if="btnDia == '1'">
          </el-table-column>
          <el-table-column prop="collectTime" label="时间" align="center" v-if="btnDia == '2'">
          </el-table-column>
          <el-table-column prop="dataType" label="参数" align="center" v-if="btnDia == '2'">
          </el-table-column>
          <el-table-column prop="dataValue" label="参数值" align="center" v-if="btnDia == '2'">
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center" v-if="btnDia == '1'">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.fire_device_status" :value="scope.row.status" :type="1" />
            </template>
          </el-table-column>
          <el-table-column prop="emergencyStatus" label="状态" align="center" v-if="btnDia == '3'">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.emergency_status" :value="scope.row.emergencyStatus" :type="1" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width"
            v-if="btnDia == '3'">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="getdiaDetail(scope.row)">详情</el-button>
              <el-button size="mini" type="text" @click="getchukli(scope.row)">处理</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-if="tableData.length != 0" :total="totalDia" :page.sync="diaFrom.current" :limit.sync="diaFrom.size"
          @pagination="diaGitList" />
      </div>
      <!-- 详情 -->
      <el-dialog title="告警详情" :visible.sync="openDetail" width="1000px" append-to-body>
        <div class="diaTil">
          <p>处理记录</p>
        </div>
        <el-table :data="tableDataDetail" height="200" border style="width: 100%">
          <el-table-column type="index" width="50" align="center">
          </el-table-column>
          <el-table-column prop="createTime" label="时间" align="center">
          </el-table-column>
          <el-table-column prop="date" label="状态" align="center">
          </el-table-column>
          <el-table-column prop="description" label="内容" align="center">
          </el-table-column>
        </el-table>

        <div class="diaTil" style="padding-top: 10px">
          <p>设备信息</p>
        </div>
        <el-card shadow="hover"><el-row :gutter="20">
            <!--部门数据-->
            <el-col :span="14" :xs="14">
              <div class="diadevice_top" style="margin-bottom: 30px">
                <div>设备编号：{{ detailDiadeviceObj.deviceId }}</div>
                <div>设备名称：{{ detailDiadeviceObj.deviceName }}</div>
                <div>所属部门：{{ detailDiadeviceObj.organizationName }}</div>
              </div>
              <div class="diadevice_top" style="margin-bottom: 30px">
                <div style="display: flex">
                  当前状态：
                  <dict-tag :options="dict.type.fire_device_status" :value="detailDiadeviceObj.status" :type="1" />
                </div>
                <div>安装位置：{{ detailDiadeviceObj.location }}</div>
                <div>关联摄像头：{{ detailDiadeviceObj.monitor }}</div>
              </div>
              <div class="diadevice_top">
                <div>责任人：{{ detailDiadeviceObj.principal }}</div>
                <div>联系电话：{{ detailDiadeviceObj.phone }}</div>
              </div>
            </el-col>
            <el-col :span="10" :xs="10">
              <div style="
                  width: 100%;
                  height: 120px;
                  background: #333;
                  color: #fff;
                  text-align: center;
                  line-height: 120px;
                ">
                暂无视频
              </div>
            </el-col>
          </el-row>
        </el-card>
        <div class="diaTil" style="padding-top: 10px">
          <p>告警信息</p>
        </div>
        <el-card shadow="hover"><el-row :gutter="20">
            <!--部门数据-->
            <el-col :span="8" :xs="8">
              <div style="display: flex">
                告警类型：<dict-tag :options="dict.type.emergency_type" :value="detailDiaemergencyObj.emergencyType"
                  :type="1" />
              </div>
            </el-col>
            <el-col :span="8" :xs="8">
              <div>告警值：{{ detailDiaemergencyObj.threshold }}%</div>
            </el-col>
            <el-col :span="8" :xs="8">
              <div>告警时间：{{ detailDiaemergencyObj.createTime }}</div>
            </el-col>
          </el-row>
        </el-card>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelDetail">关 闭</el-button>
        </div>
      </el-dialog>
      <!-- 处理 -->
      <el-dialog title="告警处理" :visible.sync="opengaojin" width="900px" append-to-body>
        <el-form ref="formgaojin" :model="formgaojin" :rules="rulesgaojin" label-width="100px" label-position="left">
          <el-card shadow="hover"><el-row :gutter="20">
              <!--部门数据-->
              <el-col :span="14" :xs="14">
                <div class="diadevice_top" style="margin-bottom: 40px">
                  <div>ID：{{ detailDiadeviceObj.deviceId }}</div>
                  <div>设备名称：{{ detailDiadeviceObj.deviceName }}</div>
                  <div>所属部门：{{ detailDiadeviceObj.organizationName }}</div>
                </div>
                <div class="diadevice_top">
                  <div style="display: flex">
                    当前状态：
                    <dict-tag :options="dict.type.fire_device_status" :value="detailDiadeviceObj.status" :type="1" />
                  </div>
                  <div>安装位置：{{ detailDiadeviceObj.location }}</div>
                  <div>关联摄像头：{{ detailDiadeviceObj.monitor }}</div>
                </div>
              </el-col>
              <el-col :span="10" :xs="10">
                <div style="
                    width: 100%;
                    height: 120px;
                    background: #333;
                    color: #fff;
                    text-align: center;
                    line-height: 120px;
                  ">
                  暂无视频
                </div>
              </el-col>
            </el-row>
          </el-card>
          <el-row :gutter="20" style="padding-top: 20px">
            <!--部门数据-->
            <el-col :span="12" :xs="12"><el-form-item label="告警类型" prop="emergencyType">
                <el-select v-model="formgaojin.emergencyType" placeholder="告警类型" clearable>
                  <el-option v-for="dict in dict.type.emergency_type" :key="dict.value" :label="dict.label"
                    :value="dict.value" /> </el-select></el-form-item>
            </el-col>
            <el-col :span="12" :xs="12">
              <el-form-item label="处理方式" prop="handleType">
                <el-radio-group v-model="formgaojin.handleType">
                  <el-radio :label="4010701">线下处理</el-radio>
                  <el-radio :label="4010602">转工单</el-radio>
                </el-radio-group></el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="处理描述" prop="description">
                <el-input v-model="formgaojin.description" type="textarea"
                  placeholder="处理描述"></el-input></el-form-item></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="上传照片" prop="photo">
                <el-upload :action="uploadPic" list-type="picture-card" :on-remove="handleRemovegaojin"
                  :on-preview="handlePreviewgaojin">
                  <i class="el-icon-camera-solid"></i> </el-upload></el-form-item></el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFormgaojin">完成 处理</el-button>
          <el-button @click="cancelgaojin">取 消</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import {
  page,
  areaPage,
  treeList,
  save,
  update,
  pageDevice,
  pageStatus,
  pageEmergency,
  deviceData,
  diaDetail,
  saveDevice,
} from "@/api/fireManagement/resourceManagement/deviceManagement/index";

export default {
  name: "DeviceManagement",
  dicts: [
    "fire_device_type",
    "fire_device_status",
    "emergency_type",
    "symbol",
    "emergency_status",
    "param_type",
  ],
  data() {
    var checkPhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入绑定的手机号码"));
      } else if (
        !/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(
          value
        )
      ) {
        callback(new Error("请输入正确的手机号码"));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层
      loading: false,
      loadingDia: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      totalDia: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 日期范围
      dateRange: [],
      areaArr: [],
      tableData: [],
      // 表单参数
      form: {},
      activeNames: ["1"],
      defaultProps: {
        children: "children",
        label: "label",
      },
      uploadPic: process.env.VUE_APP_BASE_API + "api/file/uploadFilePic",
      deptArr: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "api/file/uploadFilePic",
      },
      // 查询参数
      queryParams: {
        deviceId: undefined,
        deviceName: undefined,
        status: undefined,
        deviceType: undefined,
        current: 1,
        size: 10,
      },

      // 表单校验
      rules: {
        deviceId: [
          { required: true, message: "请输入设备编号", trigger: "blur" },
        ],
        deviceName: [
          { required: true, message: "请输入设备名称", trigger: "blur" },
        ],
        deviceModel: [
          { required: true, message: "请输入设备型号", trigger: "blur" },
        ],
        deviceType: [
          { required: true, message: "请选择设备类型", trigger: "blur" },
        ],
        organizationId: [
          { required: true, message: "请选择所属部门", trigger: "blur" },
        ],
        monitor: [
          { required: true, message: "请选择关联摄像头", trigger: "blur" },
        ],
        principal: [
          { required: true, message: "请输入责任人", trigger: "blur" },
        ],
        location: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ],
        threshold: [
          { required: true, message: "请输入报警阈值", trigger: "blur" },
        ],
        griddingId: [
          { required: true, message: "请选择安装区域", trigger: "blur" },
        ],
        emergencyType: [
          { required: true, message: "请选择报警类型", trigger: "blur" },
        ],
        phone: [{ validator: checkPhone, required: true, trigger: "blur" }],
      },
      //   详情
      detailOpen: false,
      btnDia: "2",
      // 弹窗搜索
      diaFrom: {},
      diaId: undefined,
      deviceObj: {}, //顶部设备数据
      diaDeviceObj: {}, //弹窗数据
      openDetail: false,
      tableDataDetail: [],
      detailDiadeviceObj: {},
      detailDiaemergencyObj: {},
      // 告警处理
      opengaojin: false,
      formgaojin: {},
      rulesgaojin: {
        handleType: [{ required: true, message: "处理方式", trigger: "blur" }],
        emergencyType: [
          { required: true, message: "告警类型", trigger: "blur" },
        ],
        description: [{ required: true, message: "处理描述", trigger: "blur" }],
      },
      disabled: false,
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  filters: {
    getfullName(val, arr) {
      let name = "";
      arr.map((res) => {
        if (val == res.id) {
          name = res.name;
        }
      });
      return name;
    },
  },
  created() {
    this.getTreeList();
    this.getList();
    this.getAreaPage();

    this.getdeviceData();
  },
  methods: {
    getAreaPage() {
      areaPage({ current: 1, size: 1000 }).then((response) => {
        this.areaArr = response.data.records;
      });
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        this.userList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getTreeList() {
      treeList().then((response) => {
        console.log(response);
        this.deptArr = response.data[0].children;
      });
    },
    getdeviceData() {
      deviceData({}).then((response) => {
        this.deviceObj = response.data;
      });
    },
    /** 查询部门下拉树结构 */
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deviceType = data.value;
      this.handleQuery();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deviceId: undefined,
        deviceName: undefined,
        deviceType: undefined,
        organizationId: undefined,
        deviceModel: undefined,
        monitor: undefined,
        principal: undefined,
        phone: undefined,
        griddingId: undefined,
        location: undefined,
        emergencyType: undefined,
        threshold: undefined,
        photo: undefined,
        status: "4010501",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deviceType = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);

      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增设备";
      this.disabled = false;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "关联摄像头";
      this.form = row;
      this.disabled = true;
    },
    diaSerch() {
      this.diaFrom.current = 1;
      if (this.diaFrom.dateRange.length > 0) {
        this.diaFrom.startTime = this.diaFrom.dateRange[0];
        this.diaFrom.endTime = this.diaFrom.dateRange[1];
      }
      if (this.btnDia == "1") {
        this.pageStatus();
      } else if (this.btnDia == "2") {
        this.pageDevice();
      } else {
        this.pageEmergency();
      }
    },
    diaGitList() {
      if (this.btnDia == "1") {
        this.pageStatus();
      } else if (this.btnDia == "2") {
        this.pageDevice();
      } else {
        this.pageEmergency();
      }
    },
    resetTbableDia() {
      this.diaReset();
      this.diaGitList();
    },
    diaReset() {
      this.diaFrom = {
        startTime: undefined,
        endTime: undefined,
        // 运行数据
        param: undefined,
        value: undefined,
        id: this.diaId,
        current: 1,
        size: 10,
        dateRange: [],
        // 报警数据
        emergencyType: undefined,
        emergencyStatus: undefined,
        //
        status: undefined, //
      };
    },
    diaDeatilBtn(ind) {
      this.btnDia = ind;
      this.diaReset();
      this.diaGitList();
    },
    handleDetail(row) {
      this.detailOpen = true;
      this.diaId = row.id;
      this.diaReset();
      // 设备数据
      this.diaDeviceObj = row;
      this.pageDevice();
    },
    //
    pageDevice() {
      this.loadingDia = true;
      this.tableData = [];
      this.diaFrom.deviceId = this.diaDeviceObj.deviceId
      console.log(this.diaFrom, this.diaDeviceObj);
      pageDevice(this.diaFrom).then((response) => {
        console.log(response);
        this.tableData = response.data.records;
        this.totalDia = response.data.total;
        this.loadingDia = false;
      });
    },
    //
    pageEmergency() {
      this.loadingDia = true;
      this.tableData = [];
      pageEmergency(this.diaFrom).then((response) => {
        this.tableData = response.data.records;
        this.totalDia = response.data.total;
        this.loadingDia = false;
      });
    },
    //
    pageStatus() {
      this.loadingDia = true;
      this.tableData = [];

      pageStatus(this.diaFrom).then((response) => {
        this.tableData = response.data.records;
        this.totalDia = response.data.total;
        this.loadingDia = false;
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            update({ firecontrolDevices: [this.form] }).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            save(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 修改状态
    deleMory() {
      let arr = [];
      this.ids.forEach((item) => {
        arr.push({
          id: item,
          isDeleted: 1,
        });
      });
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return update({ firecontrolDevices: arr });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    statMory() {
      let arr = [];
      this.ids.forEach((item) => {
        arr.push({
          id: item,
          status: "4010501",
        });
      });
      this.$modal
        .confirm("是否确认修改当前状态")
        .then(function () {
          return update({ firecontrolDevices: arr });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("修改成功");
        })
        .catch(() => { });
    },
    clockMory() {
      let arr = [];
      this.ids.forEach((item) => {
        arr.push({
          id: item,
          status: "4010505",
        });
      });
      console.log(arr);
      this.$modal
        .confirm("是否确认修改当前状态")
        .then(function () {
          return update({ firecontrolDevices: arr });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("修改成功");
        })
        .catch(() => { });
    },
    hanldClock(row) {
      this.$modal
        .confirm("是否确认修改当前状态")
        .then(function () {
          return update({
            firecontrolDevices: [{ id: row.id, status: "4010505" }],
          });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("修改成功");
        })
        .catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认修改当前状态")
        .then(function () {
          return update({ firecontrolDevices: [{ id: row.id, isDeleted: 1 }] });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },

    // 弹窗处理
    getdiaDetail(row) {
      this.openDetail = true;
      this.tableDataDetail = [];
      this.detailDiadeviceObj = {};
      this.detailDiaemergencyObj = {};
      diaDetail({ id: row.id }).then((response) => {
        this.tableDataDetail = response.data.deals;
        this.detailDiadeviceObj =
          response.data.device == null ? {} : response.data.device;
        this.detailDiaemergencyObj = response.data.emergency;
      });
    },
    cancelDetail() {
      this.openDetail = false;
      this.tableDataDetail = [];
      this.detailDiadeviceObj = {};
      this.detailDiaemergencyObj = {};
    },
    // 告警处理
    // 取消按钮
    cancelgaojin() {
      this.openDetail = false;
      this.resetgaojin();
    },
    getchukli(row) {
      this.opengaojin = true;
      this.resetgaojin();

      diaDetail({ id: row.id }).then((response) => {
        this.detailDiadeviceObj =
          response.data.device == null ? {} : response.data.device;
      });
      this.formgaojin.emergencyId = row.id;
      console.log(this.formgaojin);
    },
    // 表单重置
    resetgaojin() {
      this.formgaojin = {
        emergencyId: undefined,
        emergencyType: undefined,
        handleType: undefined,
        description: undefined,
        photo: undefined,
      };
      this.resetForm("formgaojin");
    },
    submitFormgaojin() {
      console.log(this.formgaojin);
      this.$refs["formgaojin"].validate((valid) => {
        if (valid) {
          saveDevice(this.formgaojin).then((response) => {
            this.$modal.msgSuccess("处理成功");
            this.opengaojin = false;
            this.diaSerch();
          });
        }
      });
    },
    handleRemovegaojin(file, fileList) {
      console.log(file, fileList);
    },
    handlePreviewgaojin(file) {
      console.log(file);
    },
    //
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "设备导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "system/user/importTemplate",
        {},
        `user_template_${new Date().getTime()}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;

  p {
    font-size: 20px;
    font-weight: bold;
  }
}

.top_device {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  column-gap: 10px;
  row-gap: 10px;
  /* 或者使用gap属性统一设置，一下代码等同于上两行代码 */
  gap: 10px;
  margin-bottom: 20px;

  .top_title {
    color: rgba(128, 128, 128, 1);
    font-size: 18px;
    font-weight: bold;
    padding-bottom: 10px;
  }

  .top_num {
    color: rgba(42, 130, 228, 1);
    font-size: 36px;
    text-align: right;
    font-weight: bold;
  }
}

.diadevice_top {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}
</style>