<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <!-- <el-form-item label="编号" prop="qy">
        <el-input
          v-model="queryParams.qy"
          placeholder="请输入编号"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="所属区域" prop="griddingId">
        <el-select
          v-model="queryParams.griddingId"
          placeholder="请选择所属区域"
        >
          <el-option
            v-for="dict in areaArr"
            :key="dict.griddingId"
            :label="dict.griddingName"
            :value="dict.griddingId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="位置关键字" prop="areaName">
        <el-input
          v-model="queryParams.areaName"
          placeholder="请输入位置关键字"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="紧急联系人" prop="emergency">
        <el-input
          v-model="queryParams.emergency"
          placeholder="请输入紧急联系人"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系人" prop="principal">
        <el-input
          v-model="queryParams.principal"
          placeholder="请输入联系人"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:role:add']"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <!-- <el-table-column
        label="微型消防站编号"
        :show-overflow-tooltip="true"
        prop="roleId"
        align="center"
      /> -->
      <el-table-column label="所属区域" prop="griddingName" align="center" />
      <el-table-column label="所属位置" prop="areaName" align="center" />
      <el-table-column label="紧急联系人" prop="emergency" align="center" />
      <el-table-column
        label="紧急联系方式"
        prop="emergencyPhone"
        align="center"
      />
      <el-table-column label="联系人" prop="principal" align="center" />
      <el-table-column label="联系方式" prop="phone" align="center" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:role:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:role:edit']"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handlePersonnel(scope.row)"
            v-hasPermi="['system:role:edit']"
            >人员配备</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleEquipment(scope.row)"
            v-hasPermi="['system:role:edit']"
            >器材装备</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属区域" prop="griddingId">
              <el-select
                v-model="form.griddingId"
                placeholder="请选择所属区域"
                ref="selectDept"
                @change="getAreaName()"
              >
                <el-option
                  v-for="dict in areaArr"
                  :key="dict.griddingId"
                  :label="dict.griddingName"
                  :value="dict.griddingId"
                /> </el-select></el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="所属位置" prop="areaName">
              <el-input
                v-model="form.areaName"
                readonly
                placeholder="请输入所属位置"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="紧急联系人" prop="emergency">
              <el-input
                v-model="form.emergency"
                placeholder="请输入紧急联系人"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="紧急联系电话" prop="emergencyPhone">
              <el-input
                v-model="form.emergencyPhone"
                placeholder="请输入紧急联系电话"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="principal">
              <el-input
                v-model="form.principal"
                placeholder="请输入联系人"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入联系电话"
              /> </el-form-item
          ></el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  areaPage,
  save,
  update,
} from "@/api/fireManagement/resourceManagement/fireStation/index";
export default {
  name: "FireStation",
  data() {
    var checkPhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入绑定的手机号码"));
      } else if (
        !/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(
          value
        )
      ) {
        callback(new Error("请输入正确的手机号码"));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [{}],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        griddingId: undefined,
        areaName: undefined,
        emergency: undefined,
        principal: undefined,
      },
      // 表单参数
      form: {},
      dateRange: [],
      // 表单校验
      rules: {
        griddingId: [
          { required: true, message: "请选择所属区域", trigger: "change" },
        ],
        principal: [
          { required: true, message: "请选择联系人", trigger: "blur" },
        ],
        phone: [{ validator: checkPhone, required: true, trigger: "blur" }],
        emergency: [
          { required: true, message: "请选择紧急联系人", trigger: "blur" },
        ],
        emergencyPhone: [
          { validator: checkPhone, required: true, trigger: "blur" },
        ],
      },
      areaArr: [],
    };
  },
  created() {
    this.getList();
    this.getAreaPage();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        this.roleList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    getAreaPage() {
      areaPage({ current: 1, size: 1000 }).then((response) => {
        this.areaArr = response.data.records;
      });
    },
    getAreaName() {
      let obj = this.areaArr.find(
        (item) => item.griddingId == this.form.griddingId
      );
      this.form.areaName = obj.areaName;
      this.form.griddingName = obj.griddingName;
    },
    // 表单重置
    reset() {
      this.form = {
        griddingId: undefined,
        areaName: undefined,
        griddingName: undefined,
        principal: undefined,
        emergencyPhone: undefined,
        emergency: undefined,
        phone: undefined,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增消防站";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "修改消防站";
      this.form = row;
    },
    handlePersonnel(row) {
      this.$router
        .push({
          path: "/vue-fireControl/fireManagement/personnel",
          query: {
            deviceId: row.id,
          },
        })
        .catch(() => {});
    },
    handleEquipment(row) {
      this.$router
        .push({
          path: "/vue-fireControl/fireManagement/equipment",
          query: {
            deviceId: row.id,
          },
        })
        .catch(() => {});
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            update(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            save(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return update({ id: row.id, isDeleted: 1 });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;
  p {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>