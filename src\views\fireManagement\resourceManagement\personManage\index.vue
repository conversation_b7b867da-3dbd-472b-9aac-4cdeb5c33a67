<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>数据筛选</span>
      </div>
      <div class="topBottom">
        <div class="descriptions">
          <el-descriptions :column="4">
            <el-descriptions-item>
              <div slot="label" class="labelStyle">单位名称</div>
              <el-input
                v-model="queryParams.protectionUnitName"
                placeholder="请输入单位名称"
                style="width: 10vw"
                @keyup.enter.native="handleQuery"
              ></el-input>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="tabButton">
          <el-button class="queryBtn" icon="el-icon-refresh" @click="resetQuery"
            >重置</el-button
          >
          <el-button
            class="queryBtn"
            type="primary"
            icon="el-icon-search"
            @click="handleQuery"
            >搜索</el-button
          >
        </div>
      </div>
    </el-card>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>消防人员信息列表</span>
        <el-button
          type="primary"
          plain
          @click="handleAdd"
          class="queryBtnT"
          icon="el-icon-plus"
          >新增消防人员</el-button
        >
      </div>
      <el-table
        :cell-style="{ padding: '0px' }"
        :row-style="{ height: '48px' }"
        v-loading="loading"
        :data="tableData"
      >
        <el-table-column type="index" width="50">
          <template slot-scope="scope">
            <span>{{
              (queryParams.current - 1) * queryParams.size + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="所属消防单位"
          align="center"
          prop="protectionUnitName"
        />
        <el-table-column label="姓名" align="center" prop="name" />
        <el-table-column label="性别" align="center" prop="sex">
          <template slot-scope="scope">
            <div>
              {{
                dict.type.sex.find((ele) => ele.value == scope.row.sex).label ||
                ""
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="联系电话" align="center" prop="phone" />
        <el-table-column label="人员类型" align="center" prop="type">
          <template slot-scope="scope">
            <div>
              {{
                dict.type.firecontrol_firefighter_type.find(
                  (ele) => ele.value == scope.row.type
                ).label || ""
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleLook(scope.row)"
              >详情</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-tickets"
              @click="handleRecord(scope.row)"
              >信息变更记录</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.current"
        :limit.sync="queryParams.size"
        @pagination="getList"
      />
    </el-card>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="560px">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="120px"
      >
        <el-form-item label="所属消防单位" prop="protectionUnitId">
          <el-select
            v-model="ruleForm.protectionUnitId"
            style="width: 245px"
            placeholder="所属消防单位"
            :disabled="disabled"
          >
            <el-option
              v-for="dict in unitList"
              :key="dict.id"
              :label="dict.protectionUnitName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="人员类型" prop="type">
          <el-select
            v-model="ruleForm.type"
            placeholder="请选择人员类型"
            clearable
            style="width: 245px"
          >
            <el-option
              v-for="dict in dict.type.firecontrol_firefighter_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="ruleForm.name"
            placeholder="请输入姓名"
            style="width: 245px"
            maxlength="32"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-radio-group v-model="ruleForm.sex" :disabled="disabled">
            <el-radio
              v-for="dict in dict.type.sex"
              :key="dict.value"
              :label="dict.value"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="民族" prop="nation">
          <el-input
            v-model="ruleForm.nation"
            placeholder="请输入民族"
            style="width: 245px"
            maxlength="32"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="政治面貌" prop="politicCountenance">
          <el-radio-group v-model="ruleForm.politicCountenance">
            <el-radio
              v-for="dict in dict.type.firecontrol_politic_countenance"
              :key="dict.value"
              :label="dict.value"
              :value="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input
            placeholder="请输入身份证号"
            v-model="ruleForm.idCard"
            style="width: 245px"
            :disabled="disabled"
            maxlength="32"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input
            placeholder="请输入手机号码"
            v-model="ruleForm.phone"
            style="width: 245px"
            maxlength="32"
          ></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            placeholder="请输入邮箱"
            v-model="ruleForm.email"
            style="width: 245px"
            maxlength="32"
          ></el-input>
        </el-form-item>
        <el-form-item label="紧急联系方式" prop="emergencyContact">
          <el-input
            placeholder="请输入紧急联系方式"
            v-model="ruleForm.emergencyContact"
            style="width: 245px"
            maxlength="32"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="unitSubmit">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="消防人员详情" :visible.sync="detailDialog" width="560px">
      <el-descriptions :column="2">
        <el-descriptions-item label="所属消防单位">{{
          detail.protectionUnitName
        }}</el-descriptions-item>
        <el-descriptions-item label="人员类型">{{
          detail.type
        }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{
          detail.name
        }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{
          detail.sex
        }}</el-descriptions-item>
        <el-descriptions-item label="民族">{{
          detail.nation
        }}</el-descriptions-item>
        <el-descriptions-item label="政治面貌">{{
          detail.politicCountenance
        }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{
          detail.idCard
        }}</el-descriptions-item>
        <el-descriptions-item label="手机号码">{{
          detail.phone
        }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{
          detail.email
        }}</el-descriptions-item>
        <el-descriptions-item label="紧急联系人">{{
          detail.emergencyContact
        }}</el-descriptions-item>
      </el-descriptions>
      <el-button
        type="primary"
        icon="el-icon-tickets"
        style="margin-top: 10px"
        @click="handleRecord"
        >信息变更记录</el-button
      >
    </el-dialog>
    <el-dialog title="信息变更记录" :visible.sync="recordDialog" width="560px">
      <el-timeline :reverse="false">
        <el-timeline-item
          v-for="(record, index) in records"
          :key="index"
          :timestamp="record.updateTime"
          placement="top"
        >
          <div
            v-for="(item, index) in record.firefighterChangeRecordDetailList"
            :key="index"
            style="margin-bottom: 20px"
          >
            变更内容：{{ item.field }}
            <br />
            {{ item.beforeValue }} <i class="el-icon-right"></i>
            {{ item.afterValue }}
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
  update,
  remove,
  detail,
  unitList,
  changeRecord,
} from "@/api/fireManagement/resourceManagement/personManage/index";
export default {
  name: "policyConfiguration",
  dicts: [
    "firecontrol_firefighter_type",
    "sex",
    "firecontrol_politic_countenance",
  ],
  data() {
    return {
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableData: null,
      // 是否显示弹出层
      open: false,
      status: true,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        protectionUnitName: undefined,
      },
      optionSeen: [],
      // 消防单位信息弹框
      title: "",
      disabled: false,
      ruleForm: {
        protectionUnitId: "",
        type: "",
        name: "",
        sex: "",
        nation: "",
        politicCountenance: "",
        idCard: "",
        phone: "",
        email: "",
        emergencyContact: "",
      },
      rules: {
        protectionUnitId: [
          { required: true, message: "请选择所属消防单位", trigger: "change" },
        ],
        type: [
          { required: true, message: "请选择人员类型", trigger: "change" },
        ],
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
        idCard: [
          { required: true, message: "请输入身份证号", trigger: "blur" },
        ],
        phone: [{ required: true, message: "请输入手机号码", trigger: "blur" }],
      },
      dialogVisible: false,
      // 所属消防单位
      unitList: [],
      // 消防人员详情
      detailDialog: false,
      detail: {},
      // 信息变更记录
      recordDialog: false,
      records: [],
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.getList();
  },
  computed: {},
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        // console.log(response, "response");
        if (response.data != null) {
          this.tableData = response.data.records;
          this.total = response.data.total;
          unitList().then((res) => {
            // console.log(res.data);
            this.unitList = res.data;
            response.data.records.forEach((element) => {
              res.data.forEach((ele) => {
                if (element.protectionUnitId == ele.id) {
                  element.protectionUnitName = ele.protectionUnitName;
                }
              });
            });
          });
        }
        this.loading = false;
      });
    },
    // 消防人员新增
    handleAdd() {
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.resetFields();
      }
      this.title = "新增消防人员";
      this.dialogVisible = true;
      this.disabled = false;
    },
    // 消防人员编辑
    handleUpdate(row) {
      this.title = "编辑消防人员";
      this.dialogVisible = true;
      detail({ id: row.id }).then((res) => {
        // console.log(res.data);
        this.ruleForm = JSON.parse(JSON.stringify(res.data));
        this.ruleForm.protectionUnitId =
          this.ruleForm.protectionUnitId.toString();
      });
      this.disabled = true;
    },
    // 消防人员详情
    handleLook(row) {
      this.detailDialog = true;
      detail({ id: row.id }).then((res) => {
        // console.log(res.data);
        this.unitList.forEach((element) => {
          if (res.data.protectionUnitId == element.id) {
            res.data.protectionUnitName = element.protectionUnitName;
          }
        });
        res.data.type = this.dict.type.firecontrol_firefighter_type.find(
          (ele) => ele.value == res.data.type
        ).label;
        res.data.sex = this.dict.type.sex.find(
          (ele) => ele.value == res.data.sex
        ).label;
        res.data.politicCountenance =
          this.dict.type.firecontrol_politic_countenance.find(
            (ele) => ele.value == res.data.politicCountenance
          ).label;
        this.detail = JSON.parse(JSON.stringify(res.data));
      });
    },
    // 关联消控室监控点按钮
    handleRecord(row) {
      this.recordDialog = true;
      this.records = [];
      if (row.id) {
        changeRecord({ id: row.id }).then((res) => {
          // console.log(res.data);
          res.data.forEach((element) => {
            if (element.firefighterChangeRecordDetailList.length > 0) {
              element.firefighterChangeRecordDetailList.forEach((item) => {
                if (item.field == "人员类型") {
                  item.beforeValue =
                    this.dict.type.firecontrol_firefighter_type.find(
                      (ele) => ele.value == item.beforeValue
                    ).label || "";
                  item.afterValue =
                    this.dict.type.firecontrol_firefighter_type.find(
                      (ele) => ele.value == item.afterValue
                    ).label || "";
                } else if (item.field == "政治面貌") {
                  item.beforeValue =
                    this.dict.type.firecontrol_politic_countenance.find(
                      (ele) => ele.value == item.beforeValue
                    ).label || "";
                  item.afterValue =
                    this.dict.type.firecontrol_politic_countenance.find(
                      (ele) => ele.value == item.afterValue
                    ).label || "";
                }
              });
            }
          });
          this.records = res.data;
        });
      } else {
        changeRecord({ id: this.detail.id }).then((res) => {
          // console.log(res.data);
          res.data.forEach((element) => {
            if (element.firefighterChangeRecordDetailList.length > 0) {
              element.firefighterChangeRecordDetailList.forEach((item) => {
                if (item.field == "人员类型") {
                  item.beforeValue =
                    this.dict.type.firecontrol_firefighter_type.find(
                      (ele) => ele.value == item.beforeValue
                    ).label || "";
                  item.afterValue =
                    this.dict.type.firecontrol_firefighter_type.find(
                      (ele) => ele.value == item.afterValue
                    ).label || "";
                } else if (item.field == "政治面貌") {
                  item.beforeValue =
                    this.dict.type.firecontrol_politic_countenance.find(
                      (ele) => ele.value == item.beforeValue
                    ).label || "";
                  item.afterValue =
                    this.dict.type.firecontrol_politic_countenance.find(
                      (ele) => ele.value == item.afterValue
                    ).label || "";
                }
              });
            }
          });
          this.records = res.data;
        });
      }
    },
    // 消防人员确认提交
    unitSubmit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          console.log(this.ruleForm);
          if (this.ruleForm.id) {
            update(this.ruleForm).then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess("编辑人员成功！");
                this.dialogVisible = false;
                this.getList();
              }
            });
          } else {
            save(this.ruleForm).then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess("新增人员成功！");
                this.dialogVisible = false;
                this.getList();
              }
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 消防人员删除
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前人员")
        .then(function () {
          return remove({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.name = "";
      this.handleQuery();
    },
  },
};
</script>
<style lang="scss" scoped>
.open_close {
  font-size: 14px;
  color: #448ef7;
  cursor: pointer;
}
.title_rule {
  font-size: 17px;
  font-weight: 400;
  color: #000;
  margin: 10px 0 10px 0;
}
.body {
  width: 100%;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

::v-deep.box-card .topBottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}

::v-deep.box-card .topBottom .descriptions {
  -webkit-box-flex: 4;
  -ms-flex: 4;
  flex: 4;
}

::v-deep.box-card .topBottom .tabButton {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

::v-deep.box-card .topBottom .tabButton button {
  float: right;
  margin: 0 5px;
}

.card_wrap {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c_item {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 300px;
  margin: 0 0 0 0;
  width: calc(20% - 2px);
  min-width: calc(20% - 2px);
  max-width: calc(20% - 2px);
}

.c_item :nth-child(5n) {
  margin-right: 0;
}

.el-descriptions-item__container .el-descriptions-item__content {
  text-align: left !important;
}

.el-card.is-always-shadow {
  -webkit-box-shadow: inset 0 -1px 0 #ebebeb;
  box-shadow: inset 0 -1px 0 #ebebeb;
}

.el-pagination.is-background .el-pager li:not(.disabled) {
  border: 1px solid #ccc;
  border-radius: 2px;
  background-color: #fff;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  border: 1px solid #188cff;
  border-radius: 2px;
  background-color: #fff;
  color: #188cff;
}

.unregisteredText {
  font-size: 14px;
}

.el-radio-button__inner {
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  letter-spacing: 0.04em;
  color: #666;
}

.tabButton .button {
  width: 88px;
  height: 32px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 2px;
}

.tabButton .primary {
  width: 88px;
  height: 32px;
  border: 1px solid #ccc;
  border-radius: 2px;
  background: #188cff;
}

::v-deep.el-table .el-table__fixed-header-wrapper th,
::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.el-table--medium .el-table__cell {
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #666;
}

.buttonAdd {
  width: 88px;
  height: 32px;
  background: #1a8cff;
  border-radius: 2px;
  float: right;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 5px 8px;
  gap: 10px;
}

::v-deep .el-card__header {
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 26px;
  color: #333;
}

.labelStyle {
  width: 60px;
  height: 14px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  letter-spacing: 0.04em;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -moz-text-align-last: justify;
  text-align-last: justify;
  margin: auto;
}

.button {
  width: 88px;
  height: 32px;
  border-radius: 2px;
}

.el-input--medium .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.el-descriptions--medium:not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 20px;
}

.el-descriptions-item__label {
  line-height: 35px;
  margin-left: 10px;
}
</style>