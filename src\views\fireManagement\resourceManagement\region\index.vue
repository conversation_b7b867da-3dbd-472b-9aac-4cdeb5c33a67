<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <!-- <el-form-item label="区域ID">
        <el-input
          v-model="queryParams.areaId"
          placeholder="请输入发布名称"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="区域名称" prop="areaName">
        <el-input
          v-model="queryParams.areaName"
          placeholder="请输入区域名称"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 230px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="联系人" prop="principal">
        <el-input
          v-model="queryParams.principal"
          placeholder="请输入联系人"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系电话"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:role:add']"
          >新增区域</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <el-table-column
        label="ID"
        :show-overflow-tooltip="true"
        prop="id"
        align="center"
      />
      <el-table-column
        label="消防区域网格"
        prop="griddingName"
        align="center"
      />
      <el-table-column label="区域范围" prop="areaName" align="center" />
      <el-table-column label="责任人" prop="principal" align="center" />
      <el-table-column label="联系电话" prop="phone" align="center" />
      <el-table-column label="创建人" prop="createUser" align="center" />
      <el-table-column label="创建日期" prop="createTime" align="center" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:role:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:role:edit']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <div class="diaTil">
          <p>基础信息</p>

          <el-dialog
            width="600px"
            title="关联区域"
            :visible.sync="innerVisible"
            append-to-body
          >
            <el-tree
              :data="treeData"
              show-checkbox
              default-expand-all
              node-key="areaId"
              ref="tree"
              highlight-current
              :props="defaultProps"
              check-strictly
            >
            </el-tree>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="determine()" size="mini"
                >确 定</el-button
              >
            </div>
          </el-dialog>
        </div>
        <el-row :gutter="20">
          <!-- <el-col :span="12">
            <el-form-item label="区域ID" prop="griddingId">
              <el-input
                v-model="form.griddingId"
                placeholder="请输入区域ID"
              /> </el-form-item
          ></el-col> -->
          <el-col :span="12">
            <el-form-item label="区域名称" prop="griddingName">
              <el-input
                v-model="form.griddingName"
                placeholder="请输入区域名称"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-button
          type="primary"
          @click="innerVisibleOpen()"
          size="mini"
          :disabled="disabled"
          >关联区域</el-button
        >
        <div>{{ areaName }}</div>
        <div class="diaTil">
          <p>责任人</p>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="责任人" prop="principal">
              <el-input
                v-model="form.principal"
                placeholder="请输入责任人"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入联系电话"
              /> </el-form-item
          ></el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
  areaList,
  update,
} from "@/api/fireManagement/resourceManagement/region/index";
export default {
  name: "Region",
  data() {
    var checkPhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入绑定的手机号码"));
      } else if (
        !/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(
          value
        )
      ) {
        callback(new Error("请输入正确的手机号码"));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [{}],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      disabled: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        griddingId: undefined,
        areaName: undefined,
        principal: undefined,
        phone: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      // 表单参数
      form: {},
      dateRange: [],
      innerVisible: false,
      treeData: [],
      defaultProps: {
        children: "children",
        label: "name",
      },
      areaName: "",
      // 表单校验
      rules: {
        griddingId: [
          { required: true, message: "请输入区域ID", trigger: "blur" },
        ],
        griddingName: [
          { required: true, message: "请输入区域名称", trigger: "blur" },
        ],
        principal: [
          { required: true, message: "请输入责任人", trigger: "blur" },
        ],
        phone: [{ validator: checkPhone, required: true, trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
    this.getAreaList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        this.roleList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getAreaList() {
      areaList().then((response) => {
        response.data.forEach((item) => {
          item.areaName = item.name;
        });
        this.addName(response.data);
        this.treeData = response.data;
      });
    },
    determine() {
      this.areaName = "";
      this.form.areaNameArr = [];
      this.form.areaIdArr = [];
      let arr = this.$refs.tree.getCheckedNodes();
      arr.forEach((res) => {
        this.form.areaNameArr.push(res.name);
        this.form.areaIdArr.push(res.areaId);
        this.areaName += res.areaName + " , ";
      });
      this.innerVisible = false;
    },
    // 区域树递归添加展示名称
    addName(data) {
      data.forEach((item) => {
        if (item.children) {
          item.children.forEach((element) => {
            element.areaName = item.areaName + "-" + element.name;
          });
          this.addName(item.children);
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    innerVisibleOpen() {
      this.innerVisible = true;
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        areaIdArr: [],
        areaNameArr: [],
        areaId: undefined,
        areaName: undefined,
        griddingName: undefined,
        griddingId: undefined,
        principal: undefined,
        phone: undefined,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      if (this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增区域";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "修改区域";
      this.form = row;
      this.disabled = true;
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            update(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.areaName = this.form.areaNameArr.join();
            this.form.areaId = this.form.areaIdArr.join();
            this.$refs.tree.setCheckedKeys([]);
            save(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return update({ id: row.id, isDeleted: 1 });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch((error) => {
          console.log(error);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;
  p {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>