<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
    >
      <el-form-item label="联系人" prop="principal">
        <el-input
          v-model="queryParams.principal"
          placeholder="请输入联系人"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系电话"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="灭火剂种类" prop="type">
        <el-select
          v-model="queryParams.type"
          style="width: 230px"
          placeholder="请选择灭火剂种类"
        >
          <el-option
            v-for="item in dict.type.resource_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增灭火药剂</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
        >导出使用记录</el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        @queryTable="getList"
      />
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="所属区域" prop="griddingName" align="center" />
      <el-table-column
        label="详细位置"
        :show-overflow-tooltip="true"
        prop="detailLocation"
        align="center"
      />
      <el-table-column label="灭火剂种类" prop="type" align="center">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.resource_type"
            :value="scope.row.type"
            :type="1"
          />
        </template>
      </el-table-column>
      <el-table-column label="储量" prop="amount" align="center" />
      <el-table-column label="剩余量" prop="remain" align="center" />
      <el-table-column label="联系人" prop="principal" align="center" />
      <el-table-column label="联系方式" prop="phone" align="center" />
      <el-table-column label="创建人" prop="createUser" align="center" />
      <el-table-column
        label="创建日期"
        prop="createTime"
        align="center"
        width="150"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:role:edit']"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['system:role:edit']"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="left"
      >
        <div class="diaTil">
          <p>基础信息</p>
        </div>
        <el-form-item label="所属区域" prop="griddingId">
          <el-select
            v-model="form.griddingId"
            placeholder="请选择所属区域"
            style="width: 100%"
            @change="getAreaName()"
          >
            <el-option
              v-for="dict in areaArr"
              :key="dict.id"
              :label="dict.griddingName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="详细位置" prop="detailLocation">
          <el-input
            v-model="form.detailLocation"
            placeholder="请输入详细位置"
          />
          <!-- <div
            style="
              width: 100%;
              height: 140px;
              background: #333;
              margin-top: 10px;
              color: #fff;
              text-align: center;
              line-height: 140px;
            "
          >
            暂无地图
          </div> -->
        </el-form-item>
        <el-form-item label="灭火剂种类" prop="type">
          <el-select
            v-model="form.type"
            style="width: 100%"
            placeholder="请选择灭火剂种类"
          >
            <el-option
              v-for="item in dict.type.resource_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="储量" prop="amount">
          <el-input v-model="form.amount" placeholder="请输储量" />
        </el-form-item>

        <el-form-item label="联系人" prop="principal">
          <el-input v-model="form.principal" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div v-show="title == '修改灭火药剂'">
        <div class="diaTil">
          <p>使用信息</p>
          <el-button
            type="primary"
            plain
            size="mini"
            style="height: 30px; margin-left: 10px"
            @click="openinnerVisible"
          >登录使用记录</el-button>
        </div>
        <el-table :data="tableData" height="250" border style="width: 100%">
          <el-table-column type="index" width="50" />
          <el-table-column prop="useTime" label="使用时间" align="center" />
          <!-- <el-table-column prop="name" label="物料名称" align="center">
          </el-table-column> -->
          <el-table-column prop="useAmount" label="使用量" align="center" />
        </el-table>
      </div>
      <el-dialog
        width="500px"
        title="新增使用记录"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-form
          ref="formDia"
          :model="formDia"
          :rules="rulesDia"
          label-width="120px"
          label-position="left"
        >
          <!-- <el-form-item label="物料名称" prop="name">
            <el-input v-model="formDia.name" placeholder="请输入物料名称" />
          </el-form-item> -->
          <el-form-item label="使用量" prop="useAmount">
            <el-input
              v-model.number="formDia.useAmount"
              placeholder="请输入使用量"
            />
          </el-form-item>
          <el-form-item label="使用时间" prop="useTime">
            <el-date-picker
              v-model="formDia.useTime"
              style="width: 100%"
              type="datetime"
              placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitDiaForm">确 定</el-button>
          <el-button @click="cancelDia">取 消</el-button>
        </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  pageResource,
  saveResource,
  updateResource,
  areaPage,
  pageRecord,
  saveRecord
} from '@/api/fireManagement/resourceManagement/rescueForce/index'
export default {
  name: 'FirewaterSource',
  dicts: ['resource_type'],
  data() {
    var checkPhone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入绑定的手机号码'))
      } else if (
        !/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(
          value
        )
      ) {
        callback(new Error('请输入正确的手机号码'))
      } else {
        callback()
      }
    }
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [{}],
      tableData: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        principal: undefined,
        phone: undefined,
        type: undefined
      },
      // 表单参数
      form: {},
      dateRange: [],
      activeNames: ['1'],
      // 表单校验
      rules: {
        griddingId: [
          { required: true, message: '请选择所属区域', trigger: 'change' }
        ],
        detailLocation: [
          { required: true, message: '请输入详细位置', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择灭火剂种类', trigger: 'blur' }
        ],
        amount: [{ required: true, message: '请输入储量', trigger: 'blur' }],
        principal: [
          { required: true, message: '请输入联系人', trigger: 'blur' }
        ],
        phone: [{ validator: checkPhone, required: true, trigger: 'blur' }]
      },
      areaArr: [],
      innerVisible: false,
      formDia: {},
      rulesDia: {
        name: [{ required: true, message: '请输入物料名称', trigger: 'blur' }],
        useAmount: [
          { required: true, message: '请输入使用量', trigger: 'blur' }
        ],
        useTime: [
          { required: true, message: '请输入使用时间', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getAreaPage()
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true
      pageResource(this.queryParams).then((response) => {
        this.roleList = response.data.records
        this.total = response.data.total
        this.loading = false
      })
    },
    getAreaPage() {
      areaPage({ current: 1, size: 1000 }).then((response) => {
        this.areaArr = response.data.records
      })
    },
    getAreaName() {
      const obj = this.areaArr.find((item) => item.id == this.form.griddingId)
      this.form.griddingName = obj.griddingName
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },

    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        griddingName: undefined,
        griddingId: undefined,
        detailLocation: undefined,
        type: undefined,
        principal: undefined,
        phone: undefined,
        amount: undefined
      }
      this.resetForm('form')
    },
    openinnerVisible() {
      this.resetDia()
      this.innerVisible = true
    },
    resetDia() {
      this.formDia = {
        useTime: undefined,
        name: undefined,
        resourceId: this.form.id,
        useAmount: null
      }
      this.resetForm('form')
    },
    cancelDia() {
      this.resetDia()
      this.innerVisible = false
    },
    /** 提交按钮 */
    submitDiaForm: function() {
      this.$refs['formDia'].validate((valid) => {
        if (valid) {
          saveRecord(this.formDia).then((response) => {
            this.$modal.msgSuccess('新增成功')
            this.innerVisible = false
            this.getDiaTbale()
          })
        }
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1
      if (this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.endTime = this.dateRange[1]
      }
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '新增灭火药剂'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.open = true
      this.title = '修改灭火药剂'
      this.form = row

      this.getDiaTbale()
    },
    getDiaTbale() {
      pageRecord({ resourceId: this.form.id }).then((response) => {
        this.tableData = response.data
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            this.form.remain = null
            updateResource(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            saveResource(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId
      this.$modal
        .confirm('是否确认删除当前数据')
        .then(function() {
          return updateResource({ id: row.id, isDeleted: 1 })
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.diaTil {
  display: flex;
  align-items: center;
  p {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>
