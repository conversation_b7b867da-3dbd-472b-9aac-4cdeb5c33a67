<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="联系人" prop="principal">
        <el-input
          v-model="queryParams.principal"
          placeholder="请输入联系人"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系电话"
          clearable
          style="width: 230px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:role:add']"
          >新增天然水源</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roleList">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="所属区域" prop="griddingName" align="center" />
      <el-table-column
        label="详细位置"
        :show-overflow-tooltip="true"
        prop="detailLocation"
        align="center"
      />
      <el-table-column label="储量" prop="amount" align="center" />
      <el-table-column label="联系人" prop="principal" align="center" />
      <el-table-column label="联系方式" prop="phone" align="center" />
      <el-table-column label="创建人" prop="createUser" align="center" />
      <el-table-column label="创建日期" prop="createTime" align="center" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:role:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:role:edit']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="所属区域" prop="griddingId">
          <el-select
            v-model="form.griddingId"
            placeholder="请选择所属区域"
            style="width: 100%"
            @change="getAreaName()"
          >
            <el-option
              v-for="dict in areaArr"
              :key="dict.id"
              :label="dict.griddingName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="详细位置" prop="detailLocation">
          <el-input
            v-model="form.detailLocation"
            placeholder="请输入详细位置"
          />
          <!-- <div
            style="
              width: 100%;
              height: 140px;
              background: #333;
              margin-top: 10px;
            "
          ></div> -->
        </el-form-item>
        <el-form-item label="储量" prop="amount">
          <el-input v-model="form.amount" placeholder="请输管径" />
        </el-form-item>
        <el-form-item label="联系人" prop="principal">
          <el-input v-model="form.principal" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  pageNature,
  areaPage,
  saveNature,
  updateNature,
} from "@/api/fireManagement/resourceManagement/rescueForce/index";
export default {
  name: "FirewaterSource",
  data() {
    var checkPhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入绑定的手机号码"));
      } else if (
        !/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/.test(
          value
        )
      ) {
        callback(new Error("请输入正确的手机号码"));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        principal: undefined,
        phone: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      // 表单参数
      form: {},
      dateRange: [],
      // 表单校验
      rules: {
        griddingId: [
          { required: true, message: "请选择所属区域", trigger: "change" },
        ],
        detailLocation: [
          { required: true, message: "请输入详细位置", trigger: "blur" },
        ],
        amount: [{ required: true, message: "请输入储量", trigger: "blur" }],
        principal: [
          { required: true, message: "请输入联系人", trigger: "blur" },
        ],
        phone: [{ validator: checkPhone, required: true, trigger: "blur" }],
      },
      areaArr: [],
    };
  },
  created() {
    this.getList();
    this.getAreaPage();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      pageNature(this.queryParams).then((response) => {
        this.roleList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getAreaPage() {
      areaPage({ current: 1, size: 1000 }).then((response) => {
        this.areaArr = response.data.records;
      });
    },
    getAreaName() {
      let obj = this.areaArr.find((item) => item.id == this.form.griddingId);
      this.form.griddingName = obj.griddingName;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        griddingName: undefined,
        griddingId: undefined,
        detailLocation: undefined,
        amount: undefined,

        principal: undefined,
        phone: undefined,
        remark: undefined,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      if (this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增天然水源";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "修改天然水源";
      this.form = row;
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            updateNature(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            saveNature(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId;
      this.$modal
        .confirm("是否确认删除当前数据")
        .then(function () {
          return updateNature({ id: row.id, isDeleted: 1 });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
</style>