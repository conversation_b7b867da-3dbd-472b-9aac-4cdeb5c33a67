<template>
  <div class="app-container">
    <el-menu
      :default-active="activeIndex"
      class="el-menu-demo"
      mode="horizontal"
      @select="handleSelect"
      active-text-color="#409EFF"
    >
      <el-menu-item index="1">消防水源</el-menu-item>
      <el-menu-item index="2">天然水源</el-menu-item>
      <el-menu-item index="3">灭火药剂</el-menu-item>
    </el-menu>
    <firewaterSource v-if="activeIndex == '1'"></firewaterSource>
    <waterSource v-if="activeIndex == '2'"></waterSource>
    <fireFighting v-if="activeIndex == '3'"></fireFighting>
  </div>
</template>

<script>
import firewaterSource from "./components/firewaterSource";
import waterSource from "./components/waterSource";
import fireFighting from "./components/fireFighting";
export default {
  name: "RescueForce",
  components: {
    firewaterSource: firewaterSource,
    waterSource: waterSource,
    fireFighting: fireFighting,
  },
  data() {
    return {
      activeIndex: "1",
    };
  },
  created() {},
  methods: {
    handleSelect(key, keyPath) {
      this.activeIndex = key;
      console.log(key, keyPath);
    },
  },
};
</script>
<style lang="scss" scoped>
</style>