<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>数据筛选</span>
      </div>
      <div class="topBottom">
        <div class="descriptions">
          <el-descriptions :column="4">
            <el-descriptions-item>
              <div slot="label" class="labelStyle">单位名称</div>
              <el-input v-model="queryParams.protectionUnitName" placeholder="请输入单位名称" style="width: 10vw"
                @keyup.enter.native="handleQuery"></el-input>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="tabButton">
          <el-button class="queryBtn" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button class="queryBtn" type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        </div>
      </div>
    </el-card>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>消防单位展示列表</span>
        <el-button type="primary" @click="handleAdd" class="queryBtnT" icon="el-icon-plus">新增消防单位</el-button>
      </div>
      <el-table :cell-style="{ padding: '0px' }" :row-style="{ height: '48px' }" v-loading="loading" :data="tableData">
        <el-table-column type="index" width="50">
          <template slot-scope="scope">
            <span>{{
              (queryParams.current - 1) * queryParams.size + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="单位名称" align="center" prop="protectionUnitName" />
        <el-table-column label="单位属性" align="center" prop="attribute" />
        <el-table-column label="联系人" align="center" prop="contacts" />
        <el-table-column label="联系电话" align="center" prop="phone" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleLook(scope.row)">详情</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
        @pagination="getList" />
    </el-card>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="720px" append-to-body :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
        <el-form-item label="单位名称" prop="protectionUnitName">
          <el-input v-model="ruleForm.protectionUnitName" maxlength="32" style="width: 245px"
            :disabled="disabled"></el-input>
          <el-button type="primary" icon="el-icon-plus" style="margin-left: 20px"
            @click="connectRoom">关联消控室监控点</el-button>
        </el-form-item>
        <el-form-item label="单位属性" prop="attribute">
          <el-input type="textarea" v-model="ruleForm.attribute" style="width: 245px" maxlength="512"
            :disabled="disabled"></el-input>
        </el-form-item>
        <el-form-item label="联系人" prop="contacts">
          <el-input v-model="ruleForm.contacts" style="width: 245px" maxlength="32"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="ruleForm.phone" style="width: 245px" maxlength="32"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="unitSubmit">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="关联消控室监控点" :visible.sync="monitorDialog" width="560px" :show-close="false"
      :close-on-press-escape="false" :close-on-click-modal="false" append-to-body>
      <!-- <el-input
        v-model="fireControlName"
        placeholder="请输入消控室名称"
        style="margin-bottom: 10px"
      ></el-input> -->
      <el-table ref="multipleTable" :data="fireControlData" tooltip-effect="dark" style="width: 100%" height="360"
        @selection-change="handleSelectionChange" :row-key="(row) => {
          return row.id;
        }
          " v-loading="roomLoading">
        <el-table-column type="selection" :reserve-selection="true" width="100"></el-table-column>
        <el-table-column prop="principal" align="center" label="负责人"></el-table-column>
        <el-table-column prop="name" align="center" label="消控室名称" show-overflow-tooltip>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="monitorDialog = false">确 定</el-button>
        <!-- <el-button @click="monitorDialog = false">取 消</el-button> -->
      </span>
    </el-dialog>
    <el-dialog title="消防单位详情" :visible.sync="detailDialog" width="720px" :close-on-press-escape="false"
      :close-on-click-modal="false" append-to-body>
      <el-descriptions :column="2">
        <el-descriptions-item label="单位名称">{{
          detail.protectionUnitName
        }}</el-descriptions-item>
        <el-descriptions-item label="单位属性">{{
          detail.attribute
        }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{
          detail.contacts
        }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{
          detail.phone
        }}</el-descriptions-item>
      </el-descriptions>
      <p>关联消控室监控点</p>
      <el-table :data="detailRoomList" tooltip-effect="dark" style="width: 100%" height="360"
        @selection-change="handleSelectionChange" :row-key="(row) => {
          return row.id;
        }
          " v-loading="roomLoading">
        <el-table-column prop="principal" align="center" label="负责人"></el-table-column>
        <el-table-column prop="name" align="center" label="消控室名称" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewRoom(scope.row)">查看消控室</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  page,
  save,
  update,
  remove,
  detail,
  roomList,
} from "@/api/fireManagement/resourceManagement/unitsManage/index";
export default {
  name: "policyConfiguration",
  dicts: ["event_handle_type", "effective_status", "event_priority"],
  data() {
    let checkPhone = (rule, value, callback) => {
      let reg = /^1[345789]\d{9}$/;
      if (!reg.test(value)) {
        callback(new Error("请输入11位手机号"));
      } else {
        callback();
      }
    };
    return {
      // 遮罩层d
      loading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      tableData: null,
      // 是否显示弹出层
      open: false,
      status: true,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        protectionUnitName: undefined,
      },
      optionSeen: [],
      // 消防单位id
      protectionUnitId: "",
      // 消防单位信息弹框
      title: "",
      disabled: false,
      ruleForm: {
        protectionUnitName: "",
        attribute: "",
        contacts: "",
        phone: "",
      },
      rules: {
        protectionUnitName: [
          { required: true, message: "请输入单位名称", trigger: "blur" },
        ],
        attribute: [
          { required: true, message: "请输入单位属性", trigger: "blur" },
        ],
        contacts: [
          { required: true, message: "请输入联系人", trigger: "blur" },
        ],
        phone: [
          {
            type: "number",
            validator: checkPhone,
            message: "请输入正确的手机号",
            trigger: "change",
            required: true,
          },
        ],
      },
      dialogVisible: false,
      // 关联消控室监控点
      monitorDialog: false,
      roomLoading: false,
      fireControlName: "",
      fireControlData: [],
      multipleSelection: [],
      // 监控点详情
      detailDialog: false,
      detail: {},
      detailRoomList: [],
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  computed: {},
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      page(this.queryParams).then((response) => {
        // console.log(response, "response");
        if (response.data != null) {
          this.tableData = response.data.records;
          this.total = response.data.total;
        }
        this.loading = false;
      });
    },
    // 消防单位新增
    handleAdd() {
      this.protectionUnitId = "";
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.resetFields();
      }
      this.title = "新增消防单位";
      this.dialogVisible = true;
      this.disabled = false;
    },
    // 消防单位编辑
    handleUpdate(row) {
      this.title = "编辑消防单位";
      this.dialogVisible = true;
      detail({ id: row.id }).then((res) => {
        // console.log(res.data);
        this.protectionUnitId = res.data.id;
        this.ruleForm = JSON.parse(JSON.stringify(res.data));
        if (res.data.firecontrolProtectionUnitAssociationRoomList.length > 0) {
          const arr = [];
          res.data.firecontrolProtectionUnitAssociationRoomList.forEach(
            (element) => {
              arr.push({ id: element.roomId });
            }
          );
          this.multipleSelection = arr;
        }
      });
      this.disabled = true;
    },
    // 消防单位详情
    handleLook(row) {
      this.detailDialog = true;
      detail({ id: row.id }).then((res) => {
        console.log(res.data);
        this.detail = JSON.parse(JSON.stringify(res.data));
        this.detailRoomList = res.data.firecontrolRoomList;
      });
    },
    // 跳转消控室查岗
    handleViewRoom(row) {
      console.log(row);
      this.dialogVisible = false;
      this.$router.push({
        path: 'fireManagement/fireControlRoom/fireControl',
        name: 'FireControl',
        query: {
          id: row.id
        }
      })
    },
    // 关联消控室监控点按钮
    connectRoom() {
      this.monitorDialog = true;
      this.roomLoading = true;
      roomList(null).then((res) => {
        // console.log(res.data);
        this.fireControlData = res.data;
        this.roomLoading = false;
        if (this.multipleSelection.length > 0) {
          this.multipleSelection.map((row) => {
            this.$refs.multipleTable.toggleRowSelection(row, true);
          });
        }
      });
    },
    // 关联消控室监控点多选
    handleSelectionChange(val) {
      this.multipleSelection = val;
      if (this.protectionUnitId) {
        this.ruleForm.firecontrolProtectionUnitAssociationRoomList = [];
        val.forEach((ele) => {
          this.ruleForm.firecontrolProtectionUnitAssociationRoomList.push({
            roomId: ele.id,
            protectionUnitId: this.protectionUnitId,
          });
        });
      } else {
        this.ruleForm.firecontrolProtectionUnitAssociationRoomList = [];
        val.forEach((ele) => {
          this.ruleForm.firecontrolProtectionUnitAssociationRoomList.push({
            roomId: ele.id,
          });
        });
      }
    },
    // 消防单位确认提交
    unitSubmit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          console.log(this.ruleForm);
          if (this.protectionUnitId) {
            update(this.ruleForm).then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess("单位编辑成功");
                this.dialogVisible = false;
                this.getList();
              }
            });
          } else {
            save(this.ruleForm).then((res) => {
              if (res.code == 200) {
                this.$modal.msgSuccess("单位新增成功");
                this.dialogVisible = false;
                this.getList();
              }
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 消防单位删除
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除当前单位")
        .then(function () {
          return remove({ id: row.id });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.name = "";
      this.handleQuery();
    },
    /* 格式化  一级场景*/
    formScene(cellValue) {
      let scene = "";
      this.optionSeen.map((item) => {
        if (cellValue.parentSceneType == item.id) {
          scene = item.sceneName;
        }
      });
      return scene;
    },
    /* 格式化  状态*/
    formtype(cellValue) {
      // console.log(this.dict.type.effective_status);
      let stt = "";
      this.dict.type.effective_status.map((item) => {
        if (cellValue.status == item.value) {
          stt = item.label;
        }
      });
      return stt;
    },
  },
};
</script>
<style lang="scss" scoped>
.open_close {
  font-size: 14px;
  color: #448ef7;
  cursor: pointer;
}

.title_rule {
  font-size: 17px;
  font-weight: 400;
  color: #000;
  margin: 10px 0 10px 0;
}

.body {
  width: 100%;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.box-card-bottom {
  margin: 20px;
}

.box-card {
  margin-bottom: 20px;
  z-index: 2;
}

::v-deep.box-card .topBottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}

::v-deep.box-card .topBottom .descriptions {
  -webkit-box-flex: 4;
  -ms-flex: 4;
  flex: 4;
}

::v-deep.box-card .topBottom .tabButton {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

::v-deep.box-card .topBottom .tabButton button {
  float: right;
  margin: 0 5px;
}

.card_wrap {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c_item {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 300px;
  margin: 0 0 0 0;
  width: calc(20% - 2px);
  min-width: calc(20% - 2px);
  max-width: calc(20% - 2px);
}

.c_item :nth-child(5n) {
  margin-right: 0;
}

.el-descriptions-item__container .el-descriptions-item__content {
  text-align: left !important;
}

.el-card.is-always-shadow {
  -webkit-box-shadow: inset 0 -1px 0 #ebebeb;
  box-shadow: inset 0 -1px 0 #ebebeb;
}

.el-pagination.is-background .el-pager li:not(.disabled) {
  border: 1px solid #ccc;
  border-radius: 2px;
  background-color: #fff;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  border: 1px solid #188cff;
  border-radius: 2px;
  background-color: #fff;
  color: #188cff;
}

.unregisteredText {
  font-size: 14px;
}

.el-radio-button__inner {
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  letter-spacing: 0.04em;
  color: #666;
}

.tabButton .button {
  width: 88px;
  height: 32px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 2px;
}

.tabButton .primary {
  width: 88px;
  height: 32px;
  border: 1px solid #ccc;
  border-radius: 2px;
  background: #188cff;
}

::v-deep.el-table .el-table__fixed-header-wrapper th,
::v-deep.el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.el-table--medium .el-table__cell {
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #666;
}

.buttonAdd {
  width: 88px;
  height: 32px;
  background: #1a8cff;
  border-radius: 2px;
  float: right;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 5px 8px;
  gap: 10px;
}

::v-deep .el-card__header {
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 26px;
  color: #333;
}

.labelStyle {
  width: 60px;
  height: 14px;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  letter-spacing: 0.04em;
  color: #333;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -moz-text-align-last: justify;
  text-align-last: justify;
  margin: auto;
}

.button {
  width: 88px;
  height: 32px;
  border-radius: 2px;
}

.el-input--medium .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.el-descriptions--medium:not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 20px;
}

.el-descriptions-item__label {
  line-height: 35px;
  margin-left: 10px;
}
</style>