<template>
    <el-dialog
      title="地图"
      v-if="mapVisible"
      :visible.sync="mapVisible"
      :before-close="handleCloseMap"
      :close-on-click-modal="false"
      width="800px"
      append-to-body
    >
      <!-- 高德地图  -->
      <div id="gao-de-map">
        <!-- 技术支持和联系方式  -->
        <input
          style="z-index: 10; position: relative"
          type="text"
          id="tipinput"
          placeholder="搜索"
          autocomplete="off"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('mapCancellation')">取 消</el-button>
        <el-button type="primary" @click="confirmLocation">确 定</el-button>
      </span>
    </el-dialog>
  </template>
  
  <script>
  // import AMapLoader from "@amap/amap-jsapi-loader";
  // 设置安全密钥
  window._AMapSecurityConfig = {
    securityJsCode: "072a0e6e84770c0ca585ffee9c3c5f5b",
  };
  export default {
    props: {
      ranksForm: Object, //新增的数据对象，用于判断是否渲染经纬度
      mapVisible: Boolean, //是否弹出地图
      url: String, //地图打点图标地址
      disabled: Boolean,
    },
    data() {
      return {
        map: null,
        lng: null,
        lat: null,
        mouseTool: null,
        overlays: [],
        auto: null,
        placeSearch: null,
        lnglat: null,
        markers1: null,
        addressInfo: null, // 用于存储地址信息
      };
    },
    mounted() {
      console.log(this.lng, this.lat, "测试111");
    },
    methods: {
      handleCloseMap() {
        AMapLoader.reset();
        this.$emit("mapCancellation");
      },
      initMap() {
        this.cleanLatAndLng()
        // if(this.ranksForm.id){
        //   this.disabled=false
        // }else{
        //   this.disabled=true
        // }
        console.log(window.AMap, this.ranksForm, "window.AMap");
        if (window.__POWERED_BY_QIANKUN__) {
          AMapLoader.load({
            key: "f5b5f1ec4cfa2ff350df220e82acd09e",
            version: "2.0", // 申请好的Web端开发者Key，首次调用 load 时必填
            plugins: [
              "AMap.AutoComplete", // 输入提示插件
              "AMap.PlaceSearch", // POI搜索插件
              "AMap.Scale", // 右下角缩略图插件 比例尺
              "AMap.OverView", // 地图鹰眼插件
              "AMap.ToolBar", // 地图工具条
              "AMap.MapType", // 类别切换控件，实现默认图层与卫星图、实施交通图层之间切换的控制
              "AMap.PolyEditor", // 编辑 折线多，边形
              "AMap.CircleEditor", // 圆形编辑器插件
              "AMap.Geolocation", // 定位控件，用来获取和展示用户主机所在的经纬度位置
              "AMap.Geocoder",
              "AMap.Heatmap",
              "AMap.SimpleMarker",
              "AMap.PolygonEditor",
              "AMap.ControlBar",
              "AMap.MouseTool",
              "AMap.MarkerCluster",
            ],
            AMapUI: {
              // 是否加载 AMapUI，缺省不加载
              version: "1.1", // AMapUI 缺省 1.1
              plugins: ["misc/PositionPicker", "overlay/SimpleMarker"], // 需要加载的 AMapUI ui插件
            },
          })
            .then((AMap) => {
              this.map = new AMap.Map("gao-de-map", {
                viewMode: "2D", //  是否为3D地图模式
                zoom: 13, // 初始化地图级别
                center: [115.159226, 37.606716], //中心点坐标  郑州
                resizeEnable: true,
              });
              let that = this;
              console.log(that.map, "sssssssssssswwwwww");
              that.placeSearch = new AMap.PlaceSearch({
                map: that.map,
                panel: "panel", // 结果列表将在此容器中进行展示。
                // city: "010", // 兴趣点城市
                autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
                extensions: "base", //返回基本地址信息
              });
              that.auto = new AMap.AutoComplete({
                input: "tipinput", // 搜索框的id
              });
              console.log(that.auto, "sdww");
              that.auto.on("select", this.select);
              this.handleClick();
            })
            .catch((e) => {
              console.log(e);
            });
        } else {
          this.AMapLoader.load({
            key: "f5b5f1ec4cfa2ff350df220e82acd09e",
            version: "2.0", // 申请好的Web端开发者Key，首次调用 load 时必填
            plugins: [
              "AMap.AutoComplete", // 输入提示插件
              "AMap.PlaceSearch", // POI搜索插件
              "AMap.Scale", // 右下角缩略图插件 比例尺
              "AMap.OverView", // 地图鹰眼插件
              "AMap.ToolBar", // 地图工具条
              "AMap.MapType", // 类别切换控件，实现默认图层与卫星图、实施交通图层之间切换的控制
              "AMap.PolyEditor", // 编辑 折线多，边形
              "AMap.CircleEditor", // 圆形编辑器插件
              "AMap.Geolocation", // 定位控件，用来获取和展示用户主机所在的经纬度位置
              "AMap.Geocoder",
              "AMap.Heatmap",
              "AMap.SimpleMarker",
              "AMap.PolygonEditor",
              "AMap.ControlBar",
              "AMap.MouseTool",
              "AMap.MarkerCluster",
            ],
            AMapUI: {
              // 是否加载 AMapUI，缺省不加载
              // version: "1.1", // AMapUI 缺省 1.1
              plugins: ["misc/PositionPicker", "overlay/SimpleMarker"], // 需要加载的 AMapUI ui插件
            },
          })
            .then((AMap) => {
              this.map = new AMap.Map("gao-de-map", {
                viewMode: "2D", //  是否为3D地图模式
                zoom: 13, // 初始化地图级别
                center: [115.159226, 37.606716], //中心点坐标  郑州
                resizeEnable: true,
              });
              let that = this;
              console.log(that.map, "sssssssssssswwwwww");
              that.placeSearch = new AMap.PlaceSearch({
                map: that.map,
                panel: "panel", // 结果列表将在此容器中进行展示。
                // city: "010", // 兴趣点城市
                autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
                extensions: "base", //返回基本地址信息
              });
              that.auto = new AMap.AutoComplete({
                input: "tipinput", // 搜索框的id
              });
              console.log(that.auto, "sdww");
              that.auto.on("select", that.select);
              this.handleClick();
            })
            .catch((e) => {
              console.log(e);
            });
        }
      },
      // 鼠标点击获取经纬度并显示点标记
      handleClick() {
        const _this = this;
        // 图标地址
        console.log(this.ranksForm);
        if (this.ranksForm.id !== undefined) {
          this.marker(this.ranksForm.longitude, this.ranksForm.latitude, this.url);
        }
        if (this.disabled == false) {
          this.map.on("click", function (e) {
            console.log("经度", e.lnglat.lng);
            console.log("纬度", e.lnglat.lat);
            _this.clearMap();
            _this.clearMap1();
            _this.marker(e.lnglat.lng, e.lnglat.lat, _this.url);
          });
        }
      },
      marker(lng, lat, src) {
        console.log(lng, lat, src);
        // 配置marker
        this.markers = new AMap.Marker({
          icon: new AMap.Icon({
            image: src,
            imageSize: new AMap.Size(20, 25),
          }),
          position: [lng, lat],
          offset: new AMap.Pixel(0, 0),
        });
        this.map.add(this.markers); //将marker添加到map中
        this.map.setFitView();
        // 保存经纬度
        this.lat = lat;
        this.lng = lng;
        
        // 获取地址信息
        this.getAddressInfo(lng, lat);
      },
      // 删除地图上已有的点标记
      clearMap() {
        if (this.markers) {
          this.map.remove(this.markers);
        }
      },
      clearMap1() {
        if (this.markers1) {
          this.map.remove(this.markers1);
        }
      },
      // 清除经纬度信息
      cleanLatAndLng() {
        if (this.disabled == false) {
          this.lat = null;
          this.lng = null;
        }
      },
      // 获取地址信息的方法
      getAddressInfo(lng, lat) {
        const geocoder = new AMap.Geocoder();
        const _this = this;
        
        geocoder.getAddress([lng, lat], (status, result) => {
          console.log(status, result, 'ceshi');
          if (status === 'complete' && result.info === 'OK') {
            const address = result.regeocode.addressComponent;
            const addressInfo = {
              province: address.province,
              city: address.city,
              district: address.district,
              address: result.regeocode.formattedAddress
            };
            
            // 保存地址信息
            _this.addressInfo = addressInfo;
          }
        });
      },
      select(e) {
        console.log(e);
        if (e.poi.location === "") {
          this.$message("请输入具体位置");
        } else {
          let _this = this;
          let lng = e.poi.location.lng;
          let lat = e.poi.location.lat;
          
          // 清除之前的标记并添加新标记
          this.clearMap();
          this.clearMap1();
          this.markers1 = new AMap.Marker({
            icon: new AMap.Icon({
              image: this.url,
              imageSize: new AMap.Size(20, 25),
            }),
            position: [lng, lat],
            offset: new AMap.Pixel(0, 0),
          });
          
          _this.map.add(this.markers1);
          _this.map.setFitView();
          
          // 保存经纬度
          this.lat = lat;
          this.lng = lng;
          
          // 获取地址信息
          this.getAddressInfo(lng, lat);
        }
      },
      confirmLocation() {
        console.log('mapConfirm', this.lng, this.lat, this.addressInfo);
        let address= ''
        if(this.addressInfo&&this.addressInfo.province&&this.addressInfo.city&&this.addressInfo.district){
          address=this.addressInfo.province+this.addressInfo.city+this.addressInfo.district;
        }
        if (this.lng && this.lat) {
          this.$emit('mapConfirm', this.lng, this.lat, address);
        } else {
          this.$message("请选择位置");
        }
      },
    },
  };
  </script>
  
  <style scoped>
  #gao-de-map {
    overflow: hidden;
    width: 100%;
    height: 500px;
    margin: 0;
    font-family: "微软雅黑";
  }
  </style>
  <style>
  /* 隐藏高德logo  */
  .amap-logo {
    display: none !important;
  }
  
  /* 隐藏高德版权  */
  .amap-copyright {
    display: none !important;
  }
  .amap-sug-result {
    z-index: 2999 !important;
  }
  </style>
  
