<template>
  <div class="body">
    <el-card>
      <el-card>
        <div slot="header">
          <span>数据筛选</span>
        </div>
        <div class="center">
          <div class="scarchIpt">
            <el-form :inline="true" :model="formInline" class="demo-form-inline">
              <el-form-item label="月份">
                <el-date-picker v-model="formInline.monthDate" type="month"
                                placeholder="选择月"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="班组">
                <el-select v-model="formInline.serviceGroupId" placeholder="考勤组" filterable clearable>
                  <el-option :label="item.name" :value="item.id" v-for="(item, i) in AllFindList"
                             :key="i"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="姓名">
                <el-input v-model="formInline.userName" placeholder="姓名" clearable :maxlength="20"></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="tabButton">
            <el-button icon="el-icon-search" type="primary" class="searchBtn" @click="onSubmit">搜索</el-button>
            <el-button icon="el-icon-refresh" type="primary" class="searchBtn" @click="reset">重置</el-button>
          </div>
        </div>
      </el-card>
      <el-card class="tab_card">
        <div slot="header">
          <span>全员排班表</span>
        </div>
        <div class="table-body">
          <div class="table-title">
            <div class="table-name">姓名</div>
            <div class="table-head" v-for="(item, index) in dateListHead" :key="index">
              <div class="bor height501">
                {{ item.day }}
                <div style="color: red">{{ item.holidayName }}</div>
              </div>

              <div class="height50" :class="{ red: item.week == '六' || item.week == '日' }">{{ item.week }}
              </div>
            </div>
          </div>
          <div class="title-center">
            <div class="table-line" v-for="(fitem, findex) in dateListInfo" :key="findex">
              <div class="table-name">{{ fitem.userName }}</div>
              <div class="table-type" v-for="(sitem, sindex) in fitem.memberWorkVos" :key="sindex">
                <el-tooltip :disabled="!sitem.arrangementName"  :content="sitem.arrangementName" effect="dark" placement="top" popper-class="tooltip-width">
                  <div :class="sitem.arrangementId == -1
									? 'ffffff'
									: sitem.arrangementId == 0
										? 'c0c0c0'
										: sitem.colourIndex < 0
											? 'c0c0c0'
											: sitem.colourIndex == 0
												? 'ED9121'
												: sitem.colourIndex == 1
													? 'c843900'
													: sitem.colourIndex == 2
														? 'c817936'
														: sitem.colourIndex == 3
															? 'cc7a252'
															: sitem.colourIndex == 4
																? 'c494e8f'
																: sitem.colourIndex == 5
																	? 'c121a2a'
																	: 'ffffff'
									"
                  >
                    {{
                      sitem.arrangementId == -1 ? '-' : sitem.arrangementId == 0 ? '休' :
                        sitem.arrangementName
                    }}
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
        <pagination v-show="total > 1" :limit.sync="pageSize" :page.sync="pageNum" :total="total"
                    @pagination="getShowDatas"
        />
      </el-card>
    </el-card>
  </div>
</template>

<script>
import { getAllShowData, getAllfindList } from '@/api/scheduling/scheduling'

var dayjs = require('dayjs')
export default {
  name: '',
  // 获取父级的值
  props: {},
  // 数据
  data() {
    return {
      total: 0,
      formInline: {
        monthDate: dayjs(new Date()).format('YYYY-MM'),
        serviceGroupId: '',//  服务组id
        userName: ''//  人员姓名
      },
      isShowTooltip: false,
      pageNum: 1,
      pageSize: 10,
      isSearch: false,
      // 遮罩层
      loading: false,
      monthDateApi: dayjs(new Date()).format('YYYY-MM'),
      dateListInfo: [],
      dateListHead: [],
      AllFindList: [],
      openDrawer: false
    }
  },

  // 实例创建完成后被立即调用
  created() {
    this.getShowDatas()
    getAllfindList().then(({ data }) => {
      this.AllFindList = data
    })
  },
  // 混入到 Vue 实例中
  methods: {
    onMouseOver() {
      console.log(this.$refs.remark.parentNode,"this.$refs.remark")
      const parentWidth = this.$refs.remark.parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs.remark.offsetWidth // 获取元素可视宽度
      console.log(parentWidth,contentWidth,"测试")
      this.isShowTooltip = contentWidth <= parentWidth
    },
    // 查询
    onSubmit() {
      this.monthDateApi = this.filterTimess(this.formInline.monthDate)
      this.pageNum = 1
      this.isSearch = true
      this.getShowDatas()
    },
    reset() {
      this.formInline = {
        monthDate: dayjs(new Date()).format('YYYY-MM'),
        serviceGroupId: '',//  服务组id
        userName: ''//  人员姓名
      },
        this.pageNum = 1
      this.isSearch = false
      this.monthDateApi = this.filterTimess(this.formInline.monthDate)
      this.getShowDatas()
    },
    /** 排班管理-班次管理分页查询列表 */
    getShowDatas() {
      this.loading = true
      let params = {}
      if (this.isSearch) {
        params = {
          monthStr: this.monthDateApi,
          serviceGroupId: this.formInline.serviceGroupId,
          userName: this.formInline.userName,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
      } else {
        params = {
          monthStr: this.monthDateApi,
          // serviceGroupId: this.formInline.serviceGroupId,
          // userName: this.formInline.userName,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
      }
      console.log(params, 'params')
      getAllShowData(params).then(res => {
        this.dateListInfo = res.data.infoList.list
        this.total = res.data.infoList.total
        this.dateListHead = res.data.header
        this.dateListHead.forEach(item => {
          item.isAll = 0
        })
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .remark {
    width:100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
.body {
  font-size: 12px;
  padding: 16px;
}

// @import './index.scss';
.demo-form-inline {
  background: #ffffff;
}

.width50 {
  width: 50px !important;
}

.width90 {
  width: 90px !important;
}

.width100 {
  width: 100px !important;
}

.width120 {
  width: 120px !important;
}

.width200 {
  width: 200px !important;
}

.width300 {
  width: 300px !important;
}

.flexs {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.up-btn {
  text-align: end;
  width: 300px;
}

.height501 {
  height: 50%;
}

.height50 {
  line-height: 35px;
  height: 50%;
}

.ED9121 {
  // background-color: #ed9121;
  color: #FA9938;
  // color: #ffffff;
}

.height40 {
  height: 40px !important;
  line-height: 40px !important;
}

.cef5b9c {
  background-color: #ef5b9c;
  color: #ffffff;
}

.cf47920 {
  background-color: #f47920;
  color: #ffffff;
}

.cca8687 {
  background-color: #ca8687;
  color: #ffffff;
}

.c843900 {
  // background-color: ;
  color: #843900;
}

.c817936 {
  color: #817936;
}

.cc7a252 {
  color: #c7a252;
}

.c494e8f {
  color: #494e8f;
}

.c121a2a {
  color: #121a2a;
}

.ffffff {
  background-color: #ffffff;
  color: #333333;
}

.c367459 {
  background-color: #367459;
}

.over {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.centers {
  padding: 24px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.centers-left {
  display: flex;
  align-items: center;
}

.body {
  width: 100%;
  // height: calc(100vh - 86px);
  height: 100%;
  margin: 0;
  font-size: 12px;
}

.add {
  background-color: #ffffff;
  padding: 24px;
}

.center-btn {
  background-color: #ffffff;
  display: flex;
}

.c0c0c0 {
  color: rgba(251, 81, 81, 1);
}

.caozuo {
  color: #3399ff;
}

.caozuo {
  color: #3399ff;
  margin-right: 20px;
}

.alarmUp {
  margin-bottom: 24px;

  .alarmTips {
    font-size: 16px;
    margin-right: 24px;
  }
}

.alarmDown {

  // display: flex;
  // align-items: center;
  .alarmTips {
    font-size: 16px;
    margin-right: 24px;
  }

  .alarmRight {
    margin-left: 92px;
    margin-top: -20px;

    .alarminp {
      display: flex;
      align-items: center;

      // margin-left: 80px;
      .alarminp-left {
        width: 72px;
        margin-right: 8px;
        margin-bottom: 8px;
      }

      .alarminp-right {
        width: 72px;
        margin-right: 8px;
        margin-bottom: 8px;
      }

      .alarminp-center {
        width: 72px;
        margin-right: 8px;
        margin-bottom: 8px;
        height: 36px;
        border-radius: 4px;
        line-height: 36px;
        font-size: 18px;
        text-align: center;
      }

      .blue {
        background: #e6f7ff;
        border: 1px solid #91d5ff;
        color: #1890ff;
      }

      .orange {
        background: #fff7e6;
        border: 1px solid #ffd591;
        color: #d46b08;
      }

      .red {
        background: #fff2f0;
        border: 1px solid #ffccc7;
        color: #ff4d4f;
      }
    }
  }
}

.addtitle {
  color: #333333;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.addbody {
  padding: 40px 160px;
}

.checkbox {
  padding: 0 0 0 24px;
}

.table-name {
  width: inherit;
  height: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-body {
  background-color: #ffffff;
  font-size: 12px !important;

  .table-title {
    display: flex;
    align-items: center;
    width: 100%;
    height: 70px;
    background-color: #199FFF26;
    border: solid 1px #f2f2f2;

    .table-name {
      line-height: 70px;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .table-head {
      // line-height: 40px;
      text-align: center;
      height: inherit;
      width: inherit;
      border-bottom: 1px solid #f2f2f2;

      .height50 {
        background: #fff;
      }

      .bor {
        color: #007BAF;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .height50.red {
        color: red;
      }
    }
  }

  .title-center {
    .table-line {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #f2f2f2;
      border-left: 1px solid #f2f2f2;
      border-right: 1px solid #f2f2f2;

      .table-name {
        text-align: center;
        line-height: 40px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .table-type {
        line-height: 40px;
        text-align: center;
        height: inherit;
        overflow: hidden;
        text-overflow: ellipsis;
        width: inherit;
        border-bottom: 1px solid #f2f2f2;
      }
    }
  }
}

.center {
  display: flex;

  ::v-deep .el-input {
    width: 10vw;
  }

  ::v-deep .el-date-editor {
    width: 10vw;
  }

  .scarchIpt {
    -webkit-box-flex: 6;
    flex: 6 1 0%;
  }

  .tabButton {
    -webkit-box-flex: 1;
    flex: 1 1 0%;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;

  }

  .el-form-item {
    white-space: nowrap;
    width: 17vw;
  }

  ::v-deep .el-form-item__label {
    width: 40px;
  }
}

::v-deep .el-form {
  padding-left: 0;
}


::v-deep .el-card__header {
  height: 56px;
  font-size: 18px;
  font-weight: 400;
  padding: 16px;
}

::v-deep .el-card__body {
  padding: 10px;
}

.tab_card {
  ::v-deep .el-card__body {
    padding: 16px 0px;
  }

  .tab_card_header {
    display: flex;
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
    }
  }
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  border: 1px solid #188cff;
  border-radius: 2px;
  background-color: #fff;
  color: #188cff;
}

.el-card {
  margin-bottom: 20px;
}
</style>
