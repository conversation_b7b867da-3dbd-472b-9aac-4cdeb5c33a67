.body {
  width: 100%;
  // height: calc(100vh - 86px);
  height: 100%;
  margin: 0;
  font-size: 12px;
  padding: 16px;
}
.add {
  background-color: #ffffff;
  padding: 24px;
}
.center {
  border-bottom: 1px solid #f5f5f5;
}
.center-btn {
  background-color: #ffffff;
  padding: 10px 24px 10px 24px;
  display: flex;
  justify-content: space-between;
}
.caozuo {
  color: #3399ff;
}
.caozuo {
  color: #3399ff;
  margin-right: 20px;
}
.alarmUp {
  margin-bottom: 24px;
  .alarmTips {
    font-size: 16px;
    margin-right: 24px;
  }
}
.alarmDown {
  // display: flex;
  // align-items: center;
  .alarmTips {
    font-size: 16px;
    margin-right: 24px;
  }
  .alarmRight {
    margin-left: 92px;
    margin-top: -20px;
    .alarminp {
      display: flex;
      align-items: center;
      // margin-left: 80px;
      .alarminp-left {
        width: 72px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
      .alarminp-right {
        width: 72px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
      .alarminp-center {
        width: 72px;
        margin-right: 8px;
        margin-bottom: 8px;
        height: 36px;
        border-radius: 4px;
        line-height: 36px;
        font-size: 18px;
        text-align: center;
      }
      .blue {
        background: #e6f7ff;
        border: 1px solid #91d5ff;
        color: #1890ff;
      }
      .orange {
        background: #fff7e6;
        border: 1px solid #ffd591;
        color: #d46b08;
      }
      .red {
        background: #fff2f0;
        border: 1px solid #ffccc7;
        color: #ff4d4f;
      }
    }
  }
}

.addtitle {
  color: #333333;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}
.addbody {
  padding: 40px 160px;
}
.checkbox {
  padding: 0 0 0 24px;
}

.table-name {
  width: inherit;
  height: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.table-body {
  padding: 10px;
  background-color: #ffffff;
  .table-title {
    display: flex;
    align-items: center;
    width: 100%;
    height: 70px;
    background-color: #199fff26;
    border-top: 1px solid #f2f2f2;
    border-bottom: 1px solid #f2f2f2;
    border-right: 1px solid #f2f2f2;
    .table-name {
      line-height: 70px;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .table-head {
      // line-height: 40px;
      text-align: center;
      height: inherit;
      width: inherit;
      border-bottom: 1px solid #f2f2f2;
      .bor {
        color: #007BAF;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      .height50{
        background: #fff;
      }
      .height50.red{
        color: red;
      }
    }
  }
  .title-center {
    .table-line {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #f2f2f2;
      border-left: 1px solid #f2f2f2;
      border-right: 1px solid #f2f2f2;
      .table-name {
        text-align: center;
        line-height: 40px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .table-type {
        line-height: 40px;
        text-align: center;
        height: inherit;
        overflow: hidden;
        text-overflow: ellipsis;
        width: inherit;
        border-bottom: 1px solid #f3f2f2;
      }
    }
  }
}
