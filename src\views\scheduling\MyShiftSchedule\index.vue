<template>
  <div class="body">
    <el-card class="out">
      <div class="content">
        <div class="content_left">
          <el-card>
            <MyCalendar :timeStemp="timeStemp1" :isYear="true" :nowDataList="dateListInfo" @getId="getDayId"
            ></MyCalendar>
          </el-card>
        </div>
        <div class="content_right">
          <el-card>
            <div slot="header">
              {{ clickDayTime }}
            </div>
            <div class="work-time">
              <div class="sign" v-if="dayList.arrangementTypeId==1">固定班次</div>
              <div class="sign sign-two" v-else-if="dayList.arrangementTypeId==2">弹性班次</div>
              <div class="sign sign-three" v-else-if="dayList.arrangementTypeId==3">签到班次</div>
              <div class="sign sign-three" v-else-if="dayList.arrangementTypeId==0">今日休息</div>
              <div class="sign sign-three" v-else-if="dayList.arrangementTypeId==-1">暂未排班</div>
              <el-empty v-if="dayList.arrangementTypeId==-1" description="暂未排班"></el-empty>
              <el-empty v-if="dayList.arrangementTypeId==0" description="今日休息"></el-empty>
              <div v-if="dayList.arrangementTypeId==1" class="work-con"
                   v-for="(item,index) in dayList.arrangementTimeInfoList" :key="item.id"
              >
                <div class="work-tit">班段{{ index + 1 }}</div>
                <div class="work-out-line">
                  <div class="line-tit">工作时间</div>
                  <div class="go_work_time">
                    <p>上班时间：</p>
                    <p>
                      {{item.startWorkDayType=='2'?'次日':''}}
                      {{ item.startWorkTime }}
                    </p>
                  </div>
                  <div class="go_work_time">
                    <p>下班时间：</p>
                    <p>
                      {{item.endWorkDayType=='2'?'次日':''}}
                      {{ item.endWorkTime }}
                    </p>
                  </div>
                </div>
                <div class="work-out-line">
                  <div class="line-tit">打卡时间</div>
                  <div class="go_work_time">
                    <p>最早打卡时间：</p>
                    <p>
                      {{item.startEarliestDayType=='2'?'次日':''}}
                      {{ item.startEarliestWorkTime }}
                    </p>
                  </div>
                  <div class="go_work_time">
                    <p>最晚打卡时间：</p>
                    <p>
                      {{item.endLatestDayType=='2'?'次日':''}}
                      {{ item.endLatestWorkTime }}
                    </p>
                  </div>
                </div>
              </div>
              <div v-else-if="dayList.arrangementTypeId==2" class="work-con"
                   v-for="(item,index) in dayList.arrangementTimeInfoList" :key="item.id"
              >
                <div class="work-tit">班段{{ index + 1 }}</div>
                <div class="work-out-line">
                  <div class="line-tit">工作时长</div>
                  <div class="go_work_time">
                    <p>必须工作时长为：{{dayList.workTimeAmount}} 小时，计为 {{dayList.ycqts}} 天出勤</p>
                  </div>
                </div>
                <div class="work-out-line">
                  <div class="line-tit">打卡时间</div>
                  <div class="go_work_time">
                    <p>最早打卡时间：</p>
                    <p>{{ item.startEarliestWorkTime }}</p>
                  </div>
                  <div class="go_work_time">
                    <p>最晚打卡时间：</p>
                    <p>{{ item.endLatestWorkTime }}</p>
                  </div>
                </div>
              </div>
              <div v-else-if="dayList.arrangementTypeId==3" class="work-con"
                   v-for="(item,index) in dayList.arrangementTimeInfoList" :key="item.id"
              >
                <div class="work-tit">班段{{ index + 1 }}</div>
                <div class="work-out-line">
                  <div class="line-tit">打卡时间</div>
                  <div class="go_work_time">
                    <p>最早打卡时间：</p>
                    <p>{{ item.startEarliestWorkTime }}</p>
                  </div>
                  <div class="go_work_time">
                    <p>最晚打卡时间：</p>
                    <p>{{ item.endLatestWorkTime }}</p>
                  </div>
                </div>
              </div>

            </div>
            <div class="work-btm-time">
              <div class="work-con">
                <div class="work-out-line"  v-if="dayList.arrangementTypeId!=0&&dayList.arrangementTypeId!=-1">
                  <div class="line-tit">加班设置</div>
                  <div class="go_work_time">
                    <p v-if="dayList.overtime">{{dayList.overtime}}之后算加班</p>
                    <p v-else>无</p>
                  </div>
                </div>
                <div class="work-out-line" v-if="dayList.arrangementTypeId==1">
                  <div class="line-tit">弹性设置</div>
                  <div class="go_work_time">
                    <div>
                      <el-checkbox v-model="addShift.checkbox1" true-label="1"
                                   false-label="0" disabled
                      >
                      </el-checkbox> <span style="margin-left: 8px">晚到、早走几分钟不记为异常</span>
                      <div class="checkbox" v-if="addShift.checkbox1 == 1">
                        <div>
                          上班最多可晚到：
                          <el-select class="width90" v-model="addShift.checkbox1Up" placeholder="请选择"  disabled>
                            <el-option v-for="item in Dateoptions" :key="item.value" :label="item.label"
                                       :value="item.value"
                            >
                            </el-option>
                          </el-select>
                          内不算迟到
                        </div>
                        <div>
                          下班最多可早走：
                          <el-select class="width90" v-model="addShift.checkbox1Down" placeholder="请选择"  disabled>
                            <el-option v-for="item in Dateoptions" :key="item.value" :label="item.label"
                                       :value="item.value"
                            >
                            </el-option>
                          </el-select>
                          内不算早退
                        </div>
                      </div>
                    </div>
                    <div>
                      <el-checkbox v-model="addShift.checkbox2" true-label="1"
                                   false-label="0" disabled
                      >
                      </el-checkbox><span style="margin-left: 10px">下班晚走，第二天可晚到</span>
                      <div class="checkbox" v-if="addShift.checkbox2 == 1">
                        <div>
                          <el-radio v-model="addShift.radio1" label="1" disabled>仅下班的内勤打卡计算为晚走</el-radio>
                          <el-radio v-model="addShift.radio1" label="2" disabled>下班的内勤，外勤打卡均计算为晚走</el-radio>
                        </div>
                        <div>
                          第一天下班后晚走
                          <el-select class="width90" v-model="addShift.checkbox2Up" placeholder="请选择" disabled>
                            <el-option v-for="item in DateoptionsHour" :key="item.value" :label="item.label"
                                       :value="item.value"
                            >
                            </el-option>
                          </el-select>

                          ，第二天上班可以晚到

                          <el-select class="width90" v-model="addShift.checkbox2Down" placeholder="请选择" disabled>
                            <el-option v-for="item in DateoptionsHour" :key="item.value" :label="item.label"
                                       :value="item.value"
                            >
                            </el-option>
                          </el-select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mySchedule } from '@/api/scheduling/scheduling'
import { bcDetail } from '@/api/scheduling/scheduling'
import MyCalendar from '@/views/components/MyCalendar'

var dayjs = require('dayjs')
export default {
  name: '',
  // 获取父级的值
  props: {},
  //组件注册
  components: {
    MyCalendar
  },

  // 数据
  data() {
    return {
      name: '环境数据',
      // 遮罩层
      loading: false,
      monthDate: dayjs(new Date()).format('YYYY-MM'),
      monthDateApi: dayjs(new Date()).format('YYYY-MM'),
      dateListInfo: [],
      dateListHead: [],
      dayList: [],
      timeStemp1: dayjs(new Date()).format('YYYY-MM'),
      clickDayTime: '',
      isDataNull: false,
      Dateoptions: [
        {
          value: 15,
          label: '15分钟'
        },
        {
          value: 30,
          label: '30分钟'
        },
        {
          value: 45,
          label: '45分钟'
        },
        {
          value: 60,
          label: '60分钟'
        }
      ],
      DateoptionsHour: [
        {
          value: 1,
          label: '1小时'
        },
        {
          value: 2,
          label: '2小时'
        },
        {
          value: 3,
          label: '3小时'
        },
        {
          value: 4,
          label: '4小时'
        }
      ],
      addShift: {
        name: '', // type1、 2、 3、 4
        type: 1, // type1、 2、 3、 4
        typeName: '', // type4
        days: '', // type1、 2
        overtime: '',
        tableData: [
          {
            name: '',
            startWorkTime: '',
            endWorkTime: '',
            endWorkDayType: '',
            startEarliestWorkTime: '',
            endEarliestWorkTime: '',
            endEarliestDayType: '',
            startLatestWorkTime: '',
            endLatestDayType: '',
            endLatestWorkTime: '',
            restStartTime: '',
            restEndTime: '',
            restLength: '',
            overtime: ''
          }
        ],
        checkbox1: '0', // type1
        checkbox2: '0', // type1
        checkbox1Up: '', // type1
        checkbox1Down: '', // type1
        radio1: '0', // type1
        checkbox2Up: '', // type1
        checkbox2Down: '' // type1
      },
    }
  },

  // 实例创建完成后被立即调用
  created() {
    this.getShowDatas()
  },

  // 挂载实例后调用
  mounted() {
  },

  // 监控
  watch: {},

  // 过滤器
  filters: {},

  // 计算属性
  computed: {},

  // 混入到 Vue 实例中
  methods: {
    /** 排班管理-班次管理分页查询列表 */
    getShowDatas() {
      this.loading = true
      let params = {
        monthStr: this.monthDateApi,
        // userId: 17
      }
      console.log(params, '时间')
      mySchedule(params).then(res => {
        this.dateListInfo = res.data.infoList[0].memberWorkVos
        this.dateListHead = res.data.header
        this.dateListHead.forEach(item => {
          item.isAll = 0
        })
        this.loading = false
      })
    },
    //获取本天的排班情况
    getDayId(day) {
      console.log(day)
      if (day.arrangementId == '0') {
        this.dayList.arrangementTypeId = '0'
        this.clickDayTime = day.thisDayTime
        this.isDataNull = true
      } else if (day.arrangementId == '-1') {
        this.dayList.arrangementTypeId = '-1'
        this.clickDayTime = day.thisDayTime
        this.isDataNull = true
      } else {
        this.isDataNull = false
        this.clickDayTime = day.thisDayTime
        if (day.arrangementId !== 0 && day.arrangementId !== -1) {
          let params = {
            id: day.arrangementId
          }
          bcDetail(params).then(res => {
            this.dayList = res.data
            this.addShift.checkbox1 = this.dayList.optionOne
            this.addShift.checkbox2 = this.dayList.optionTwo
            this.addShift.checkbox1Up = this.dayList.oneBelateLength
            this.addShift.checkbox1Down = this.dayList.oneLeaveEarlyLength
            this.addShift.radio1 = this.dayList.twoType
            this.addShift.checkbox2Up = this.dayList.twoStayLateLength
            this.addShift.checkbox2Down = this.dayList.twoArriveLateLength
          })
        }
      }

    }
  }
}
</script>
<style lang="scss" scoped>
.body {
  .table-body {
    font-size: 12px !important;
  }
}

@import './index.scss';

.demo-form-inline {
  background: #ffffff;
  padding: 24px;
}

.width50 {
  width: 50px !important;
}

.width90 {
  width: 90px !important;
}

.width100 {
  width: 100px !important;
}

.width120 {
  width: 120px !important;
}

.width200 {
  width: 200px !important;
}

.width300 {
  width: 300px !important;
}

.flexs {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.selectW {
  width: 100%;
}

.selectW1 {
  width: 300px;
}

.up-btn {
  text-align: end;
  width: 300px;
}

.height501 {
  height: 50%;
}

.height50 {
  line-height: 35px;
  height: 50%;
}

.ED9121 {
  background-color: #fff;
  color: #1A8CFF;
}

.height40 {
  height: 40px !important;
  line-height: 40px !important;
}

.cef5b9c {
  background-color: #ef5b9c;
  color: #ffffff;
}

.cf47920 {
  background-color: #f47920;
  color: #ffffff;
}

.cca8687 {
  background-color: #ca8687;
  color: #ffffff;
}

.c843900 {
  background-color: #843900;
  color: #ffffff;
}

.c817936 {
  background-color: #817936;
  color: #ffffff;
}

.cc7a252 {
  background-color: #c7a252;
  color: #ffffff;
}

.c494e8f {
  background-color: #494e8f;
  color: #ffffff;
}

.c121a2a {
  background-color: #121a2a;
  color: #ffffff;
}

.ffffff {
  background-color: #ffffff;
  color: #333333;
}

.c367459 {
  background-color: #367459;
}

.over {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.centers {
  padding: 24px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.centers-left {
  display: flex;
  align-items: center;
}

::v-deep .el-dropdown {
  width: -webkit-fill-available;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-dialog {
  width: 40% !important;
}

::v-deep .el-form-item__label {
  width: 130px !important;
}

::v-deep .el-dialog__body {
  padding: 20px 20px 20px 40px;
}

::v-deep .el-form-item {
  margin-bottom: 0px;
  width: 30%;
}

::v-deep .el-form-item__content {
  width: 100%;
}

::v-deep .el-form-item {
  width: 600px;
  margin-bottom: 20px;
}

::v-deep .el-form-item__content {
  width: 500px;
}

::v-deep .el-row {
  display: flex;
  justify-content: flex-end;
}

::v-deep .el-table__row {
  height: 50px;
}

::v-deep .out {
  > .el-card__body {
    min-height: 80vh;
  }
}

.c0c0c0 {
  color: #FB5151;
}

.center {
  display: flex;

  ::v-deep .el-input {
    width: 10vw;
  }

  ::v-deep .el-date-editor {
    width: 10vw;
  }

  .scarchIpt {
    -webkit-box-flex: 6;
    flex: 6 1 0%;
  }

  .tabButton {
    -webkit-box-flex: 1;
    flex: 1 1 0%;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;

  }

  .el-form-item {
    width: 17vw;
  }

  ::v-deep .el-form-item__label {
    width: 85px;
  }
}

::v-deep .el-form {
  padding-left: 0;
}


::v-deep .el-card__header {
  height: 60px;
  font-size: 18px;
  font-weight: 400;
  padding: 16px;
}

.tab_card {
  ::v-deep .el-card__body {
    padding: 16px 0px;
  }

  .tab_card_header {
    display: flex;
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
    }
  }
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  border: 1px solid #188cff;
  border-radius: 2px;
  background-color: #fff;
  color: #188cff;
}

.el-card {
  margin-bottom: 20px;
}

.content {
  display: flex;
  height: 100%;
}

.content_right {
  flex: 1;
  padding-left: 16px;

  .el-card {
    height: 100%;
  }

  ::v-deep .el-card__body {
    padding-top: 0px;
  }

  ::v-deep .el-card__header {
    padding: 0px 0px 0px 16px;
    font-size: 24px;
    font-weight: 500;
    display: flex;
    align-items: center;
    border-bottom: none;
  }

  .work-te {
    width: 100%;
    border-radius: 5px;
    position: relative;
    min-height: 50px;
    margin-bottom: 20px;

    .sign {
      width: 32px;
      height: 40px;
      background: url("../../../assets/images/rectangle-one.png") center no-repeat;
      background-size: 100%;
      position: absolute;
      right: 20px;
      top: 0px;
      text-align: center;
      color: #fff;
      font-size: 10px;
    }

    .sign-two {
      background: url("../../../assets/images/rectangle-two.png") top center no-repeat;
      background-size: 100%;
    }

    .sign-three {
      background: url("../../../assets/images/rectangle-three.png") top center no-repeat;
      background-size: 100%;
    }

    .work-con {
      box-sizing: border-box;
      padding: 16px;

      .work-tit {
        font-size: 16px;
        font-weight: 600;
      }

      .work-out-line {
        display: flex;
        margin-top: 15px;
        margin-bottom: 15px;

        p {
          margin-block-start: 0;
          margin-block-end: 0;
        }

        .line-tit {
          width: 86px;
          height: 24px;
          background: rgba(25, 159, 255, 0.27);
          border-radius: 2px;
          text-align: center;
          line-height: 24px;
          font-size: 14px;
        }

        .go_work_time {
          display: flex;
          //height: 24px;
          //width: 220px;
          flex-wrap: wrap;
          margin-left: 30px;
          font-size: 12px;

          p {
            line-height: 24px;
          }
        }
      }

    }
  }
  .work-time{
    width: 100%;
    border: solid 1px #EBEBEB;
    border-radius: 5px;
    position: relative;
    min-height: 50px;
    margin-bottom: 20px;

    .sign {
      width: 32px;
      height: 40px;
      background: url("../../../assets/images/rectangle-one.png") center no-repeat;
      background-size: 100%;
      position: absolute;
      right: 20px;
      top: 0px;
      text-align: center;
      color: #fff;
      font-size: 10px;
    }

    .sign-two {
      background: url("../../../assets/images/rectangle-two.png") top center no-repeat;
      background-size: 100%;
    }

    .sign-three {
      background: url("../../../assets/images/rectangle-three.png") top center no-repeat;
      background-size: 100%;
    }

    .work-con {
      box-sizing: border-box;
      padding: 16px;

      .work-tit {
        font-size: 16px;
        font-weight: 600;
      }

      .work-out-line {
        display: flex;
        margin-top: 15px;
        margin-bottom: 15px;

        p {
          margin-block-start: 0;
          margin-block-end: 0;
        }

        .line-tit {
          width: 86px;
          height: 24px;
          background: rgba(25, 159, 255, 0.27);
          border-radius: 2px;
          text-align: center;
          line-height: 24px;
          font-size: 14px;
        }

        .go_work_time {
          display: flex;
          //height: 24px;
          //width: 220px;
          margin-left: 30px;
          font-size: 12px;

          p {
            line-height: 24px;
          }
        }
      }
    }
  }
  .work-btm-time{
    width: 100%;
    border-radius: 5px;
    position: relative;
    min-height: 50px;
    margin-bottom: 20px;
    .sign {
      width: 32px;
      height: 40px;
      background: url("../../../assets/images/rectangle-one.png") center no-repeat;
      background-size: 100%;
      position: absolute;
      right: 20px;
      top: 0px;
      text-align: center;
      color: #fff;
      font-size: 10px;
    }

    .sign-two {
      background: url("../../../assets/images/rectangle-two.png") top center no-repeat;
      background-size: 100%;
    }

    .sign-three {
      background: url("../../../assets/images/rectangle-three.png") top center no-repeat;
      background-size: 100%;
    }

    .work-con {
      box-sizing: border-box;
      .work-tit {
        font-size: 16px;
        font-weight: 600;
      }
      .work-out-line {
        display: flex;
        margin-top: 15px;
        margin-bottom: 15px;
        p {
          margin-block-start: 0;
          margin-block-end: 0;
        }
        .line-tit {
          width: 86px;
          height: 24px;
          background: rgba(25, 159, 255, 0.27);
          border-radius: 2px;
          text-align: center;
          line-height: 24px;
          font-size: 14px;
        }

        .go_work_time {
          //display: flex;
          //height: 24px;
          //width: 220px;
          flex-wrap: wrap;
          margin-left: 30px;
          font-size: 12px;
          >div{
            margin-bottom: 10px;
            .is-checked{
              margin-bottom: 15px;
            }
            >div{
              >div{
                margin-bottom: 10px;
              }
            }
          }
          p {
            line-height: 24px;
          }
        }
      }
    }
  }
}
</style>
