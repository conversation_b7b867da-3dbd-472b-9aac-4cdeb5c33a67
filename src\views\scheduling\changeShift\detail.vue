<template>
    <div class="body flexs">
        <el-card>
            <div class="body-left">
                <div class="left-line">
                    <div class="left-lineTitle">员工信息</div>
                    <div class="left-lineBody">
                        <div class="left-txt">
                            <p>员工姓名：</p><span>{{ detailList.applicantUserName }}</span>
                        </div>
                        <div class="left-txt">
                            <p>所属部门：</p><span>{{ detailList.applicantDeptName }}</span>
                        </div>
                        <div class="left-txt">
                            <p>所属班组：</p><span>{{ detailList.serviceGroupName }}</span>
                        </div>
                    </div>
                </div>
                <div class="left-line">
                    <div class="left-lineTitle">调班信息</div>
                    <div class="left-lineBody">
                        <div class="left-txt">调班方式：<span>{{ detailList.typeName }}</span></div>
                        <div class="left-txt" v-if="detailList.type == '104003'"> <!--同事换班-->
                            <p>调班时间：</p><span>{{ detailList.workDatetime }}</span>
                        </div>
                        <div class="left-txt" v-else>
                          <p>调班时间：</p><span>{{ detailList.restDatetime }}</span>
                        </div>
                        <div class="left-txt maxW" v-if="detailList.type == '104001'">
                            <p>补班时间：</p> <span>{{ detailList.workDatetime }}</span>
                        </div>
                        <div class="left-txt maxW" v-if="detailList.type == '104002'">
                            <p>代班人：</p> <span>{{ detailList.applicantUserName }}</span>
                        </div>
                        <div class="left-txt" v-if="detailList.type == '104003'">
                            <p>代班人：</p> <span>{{ detailList.applicantUserName }}</span>
                        </div>
                        <!-- <div class="left-txt" v-if="detailList.type == '104003'">
                            <p>还班时间：</p> <span>{{ detailList.givebackDatetime }}</span>
                        </div> -->
                        <div class="left-txt maxW">
                            <p>调班原因：</p> <span>{{ detailList.reason }}</span>
                        </div>
                    </div>
                </div>
                <div class="left-down">
                    <!-- <div class="left-down-up">附件：这里是附件</div> -->
                    <!-- <div class="left-down-down">
                    <div class="left-down-downtxt">审批意见：</div>
                    <el-input
                        type="textarea"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                        placeholder="请输入审批意见"
                        v-model="opinion"
                    >
                    </el-input>
                </div> -->
                </div>
            </div>
            <!-- <el-timeline-item v-for="(item, index) in processList" :key="index"
                    :color="item.duration ? '#0bbd87' : '#FF0000'">
                    <el-card>
                        <div class="cards cardsm">
                            <span class="cards-left">时间：</span>
                            <span>{{ item.createTime }}</span>
                        </div>
                        <div class="cards cardsm">
                            <span class="cards-left">人员：</span>
                            <span>{{ item.assigneeName }}</span>
                        </div>
                        <div class="cards">
                            <span class="cards-left">流程状态：</span>
                            <div v-for="(zitem, zindex) in item.commentList" :key="zindex">
                                <span>{{ zitem.message }}</span>
                                <span class="cards-dian" :style="`background-color: ${item.duration
                                    ? '#0bbd87'
                                    : '#FF0000'
                                    }`"></span>
                                <span>{{
                                    zitem.type == 1 ? '通过' : '驳回'
                                }}</span>
                            </div>
                        </div>
                    </el-card>
                </el-timeline-item> -->
            <div class="body-right" v-if="processList.length>0">
                <div class="timeline">
                    <div class="time-item" v-for="(item, index) in processList" :key="index">
                        <div class="time-line">
                            <svg v-if="item.duration" t="1686707594449" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="3557" width="28" height="28">
                                <path d="M852.8 261.6l2.3 1.2-2.3-1.2z" fill="#68BB8D" p-id="3558"></path>
                                <path
                                    d="M514.2 99.9c-228.5 0-413.7 185.2-413.7 413.7s185.2 413.7 413.7 413.7S927.8 742 927.8 513.5 742.5 99.9 514.2 99.9zM712 430.7L553 587l-77 75.3c-0.3 0.4-0.7 0.8-1.2 1.3-0.6 0.6-1.3 1.2-2 1.8-4.8 4.6-11.1 7.1-17.8 7.1h-1.1c-7 0-13.5-2.6-18.3-7.4-0.7-0.6-1.3-1.2-1.9-1.7-0.4-0.4-0.7-0.7-1-1.1L304.3 533.9c-10.4-10.4-9.7-28 1.5-39.2 5.7-5.7 13.3-8.9 21-8.9 7 0 13.5 2.6 18.3 7.4l109.4 109.4 58.1-56.8 159.1-156.3c4.8-4.7 11.2-7.2 18.1-7.2 7.8 0 15.5 3.3 21.2 9.1 11 11.4 11.6 29 1 39.3z"
                                    fill="#68BB8D" p-id="3559"></path>
                            </svg>
                            <svg v-else t="1686707548580" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="2273" width="28" height="28">
                                <path
                                    d="M511.563 65.144c-246.87 0-446.998 200.128-446.998 446.998S264.693 959.14 511.563 959.14 958.56 759.012 958.56 512.141 758.433 65.144 511.563 65.144z m178.524 582.517c11.716 11.716 11.716 30.71 0 42.426-5.857 5.858-13.535 8.787-21.213 8.787s-15.355-2.929-21.213-8.787L513.535 555.961 379.41 690.087c-5.858 5.858-13.536 8.787-21.213 8.787s-15.355-2.929-21.213-8.787c-11.715-11.716-11.715-30.71 0-42.426L471.11 513.535 336.983 379.41c-11.715-11.716-11.715-30.711 0-42.427 11.716-11.716 30.711-11.716 42.427 0l134.126 134.126 134.126-134.126c11.715-11.716 30.711-11.716 42.426 0 11.716 11.716 11.716 30.711 0 42.427L555.961 513.535l134.126 134.126z"
                                    fill="#d81e06" p-id="2274"></path>
                            </svg>
                            <el-divider v-if="index!==processList.length-1"></el-divider>
                        </div>
                        <div class="time-con">
                          <div v-if="item.activityType=='startEvent' || item.activityType=='endEvent'">
                            <div class="cards cardsm">
                              <span class="cards-left">时间：</span>
                              <span>{{ item.createTime }}</span>
                            </div>
                            <div class="cards">
                              <span class="cards-left">流程状态：</span>
                              <span>{{item.activityType=='startEvent'? '开始':'结束'}}</span>
                            </div>

                          </div>
                          <div v-else>
                            <div class="cards cardsm">
                              <span class="cards-left">时间：</span>
                              <span>{{ item.createTime }}</span>
                            </div>
                            <div class="cards cardsm">
                              <span class="cards-left">人员：</span>
                              <span>{{ item.assigneeName }}</span>
                            </div>
                            <div v-if="!item.commentList || item.commentList.length < 2">
                              <div class="cards cardsm">
                                <span class="cards-left">审批意见：</span>
                                <div v-for="(zitem, zindex) in item.commentList" style="text-align: left" :key="zindex">
                                  <span>{{ zitem.message }}</span>
                                </div>
                              </div>
                              <div class="cards">
                                <span class="cards-left">流程状态：</span>
                                <div v-for="(zitem, zindex) in item.commentList" style="text-align: left" :key="zindex">
                                  <span v-if="zitem.type == 0" class="cards-dian" style="background-color: #FF0000"/>
                                  <span v-if="zitem.type == 1" class="cards-dian" style="background-color: #0bbd87"/>
                                  <span v-if="zitem.type == 2" class="cards-dian" style="background-color: #ffae00"/>
                                  <span v-if="zitem.type == 3" class="cards-dian" style="background-color: #FF0000"/>
                                  <span v-if="zitem.type == 4" class="cards-dian" style="background-color: #1890ff"/>
                                  <span v-if="zitem.type == 5" class="cards-dian" style="background-color: #13ce66"/>
                                  <span v-if="zitem.type == 0">驳回</span>
                                  <span v-if="zitem.type == 1">通过</span>
                                  <span v-if="zitem.type == 2">退回</span>
                                  <span v-if="zitem.type == 3">拒绝</span>
                                  <span v-if="zitem.type == 4">委派</span>
                                  <span v-if="zitem.type == 5">转办</span>
                                </div>
                              </div>
                            </div>
                            </div>
                            <div v-if="item.commentList && item.commentList.length >1">
                              <el-timeline :reverse="reverse">
                                <el-timeline-item
                                  v-for="(activity, index) in item.commentList"
                                  :key="index"
                                  :icon="activity.icon"
                                  :type="activity.type"
                                  :color="`${activity.type == 1 ? '#0bbd87' :
                                             activity.type == 2 ? '#ffae00' :
                                             activity.type == 3 ? '#FF0000' :
                                             activity.type == 4 ? '#1890ff' :
                                             activity.type == 5 ? '#13ce66' : ''}`"
                                  :size="activity.size"
                                  :timestamp="activity.time">
                                  {{activity.type == 1 ? '通过' :
                                    activity.type == 2 ? '退回' :
                                    activity.type == 3 ? '拒绝' :
                                    activity.type == 4 ? '委派' :
                                    activity.type == 5 ? '转办' : ''}} ：{{activity.message}}
                                </el-timeline-item>
                              </el-timeline>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script>
import { tbglDetail, tbgllcDetail } from '@/api/scheduling/scheduling'

export default {
    name: '',
    // 获取父级的值
    props: {},
    // 数据
    data() {
        return {
            detailList: {},
            processList: [],
            opinion: '',
            reverse: true,
            activities: [
                {
                    name: '马振兵',
                    state: '提交',
                    stateRight: '通过',
                    timestamp: '2018-04-12 20:46',
                    color: '#1890ff'
                },
                {
                    name: '马振兵',
                    state: '提交',
                    stateRight: '通过',
                    timestamp: '2018-04-03 20:46',
                    color: '#0bbd87'
                },
                {
                    name: '马振兵',
                    state: '提交',
                    stateRight: '驳回',
                    timestamp: '2018-04-03 20:46',
                    color: '#FF0000'
                },
                {
                    name: '马振兵',
                    state: '提交',
                    stateRight: '通过',
                    timestamp: '2018-04-03 20:46'
                },
                {
                  content: '支持使用图标',
                  timestamp: '2018-04-12 20:46',
                  size: 'large',
                  type: 'primary',
                  icon: 'el-icon-more'
                }, {
                  content: '支持自定义颜色',
                  timestamp: '2018-04-03 20:46',
                  color: '#0bbd87'
                }, {
                  content: '支持自定义尺寸',
                  timestamp: '2018-04-03 20:46',
                  size: 'large'
                }, {
                  content: '默认样式的节点',
                  timestamp: '2018-04-03 20:46'
                }
            ]
        }
    },

    // 实例创建完成后被立即调用
    created() {
        this.tbglDetails()
    },

    // 挂载实例后调用
    mounted() { },

    // 监控
    watch: {},

    // 过滤器
    filters: {},

    // 定义模板
    components: {},

    // 计算属性
    computed: {},

    // 混入到 Vue 实例中
    methods: {
        /** 设备管理删除 */
        tbglDetails() {
            this.loading = true
            console.log(this.$route.query)
            let ids = this.$route.query.id
            // JSON.stringify(this.targetList)
            let params = {
                id: ids
            }
            console.log(params, 'params')
            tbglDetail(params).then((res) => {
                console.log(res)
                this.detailList = res.data
                this.loading = false
                if (this.detailList.procinsId) {
                    this.tbgllcDetails()
                }
            })
        },
        tbgllcDetails() {
            this.loading = true
            console.log(this.$route.query)
            let ids = this.$route.query.id
            // JSON.stringify(this.targetList)
            let params = {
                workAdjustmentId: ids
            }
            console.log(params, 'params')
            tbgllcDetail(params).then((res) => {
                console.log(res)
              res.data.historyProcNodeList.forEach(item => {
                this.processList.unshift(item);
              });
                // this.processList = res.data.historyProcNodeList
                this.loading = false
            })
        }
    }
}
</script>
<style lang='scss' scoped>
@import './index.scss';

::v-deep .el-timeline-item {
    padding-bottom: 39px;
}

::v-deep .el-card {
    width: 100%;
    min-height: 670px;
}
::v-deep .el-timeline {
  padding-inline-start: 10px;
}
</style>
