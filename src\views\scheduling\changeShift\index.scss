.body{
    width: 100%;
    // height: calc(100vh - 86px);
    height: 100%;
    margin: 0;
    padding: 12px;
    font-size: 12px;
}
.body-left{
    background-color: #ffffff;
    width: 100%;
    .left-line{
        border: solid 1px #ebebeb;
        display: flex;
        flex-direction: column;
        margin-bottom: 32px;
        .left-lineTitle{
            height: 50px;
        }

        .left-lineTitle{
            font-size: 16px;
            border-bottom: 1px solid #ebebeb;
            display: flex;
            align-items: center;
            font-size: 18px;
            box-sizing: border-box;
            padding-left: 16px;
        }
        .left-lineBody{
            font-size: 16px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            box-sizing: border-box;
            padding-left: 16px;
            .left-txt{
                display: flex;
                align-items: center;
                margin-right: 30px;
                span{
                    display: inline-block;
                    box-sizing: border-box;
                    padding: 5px 10px;
                    border: solid 1px #ebebeb;
                    min-width: 200px;
                }
            }
        }

    }
    .left-down{
        font-size: 16px;
        .left-down-up{
            margin-top: 20px;
        }
        .left-down-down{
            margin-top: 40px;
            display: flex;
            align-items: inherit;
            .left-down-downtxt{
                width: 100px;
            }
        }
    }
}
.body-right{
    //height: 240px;
    background-color: #ffffff;
    width: 100%;
    border: solid 1px #ebebeb;
    margin-bottom: 30px;
    display: flex;
    padding-left: 20px;
    // justify-content: center;
    overflow-x: scroll;
    box-sizing: border-box;
    // padding-top: 30px;
    align-items: center;
    .timeline{
        display: flex;
        .time-item{
            display: flex;
            flex-direction: column;
            min-width: 350px;
            margin-right: 15px;
            .time-line{
                display: flex;
                align-items: center;
                height: 50px;
                svg{
                    margin-right: 15px;
                }
            }
        }
    }

}
.body-right::-webkit-scrollbar {
    /*滚动条整体样式*/
    /*高宽分别对应横竖滚动条的尺寸*/
    width: 5px;
    height: 10px;
}

.body-right::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: rgb(214, 214, 214);
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.body-right::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(199, 199, 199, 0.2);
    background: #ededed;
    border-radius: 10px;
}
.flexs{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cards{
    display: flex;
    align-items: center;
    .cards-left{
        margin-right: 10px;
        line-height: 26px;
    }
}
.cards-dian{
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    //margin-left: 30px;
    margin-right: 6px;
}
.caozuo{
    color: #3399FF;
    margin-right: 20px;
}
.cardsm{
    margin-bottom: 12px;
}
.add{
    background-color: #ffffff;
    padding: 24px;
}
.center-btn{
    background-color: #ffffff;
    padding: 10px 24px 10px 24px;
    display: flex;
    justify-content: space-between;
}
.caozuo{
    color: #3399FF;
}
.alarmUp{
    margin-bottom: 24px;
    .alarmTips{
        font-size: 16px;
        margin-right: 24px;
    }
}
.alarmDown{
    // display: flex;
    // align-items: center;
    .alarmTips{
        font-size: 16px;
        margin-right: 24px;
    }
    .alarmRight{
        margin-left: 92px;
        margin-top: -20px;
        .alarminp{
            display: flex;
            align-items: center;
            // margin-left: 80px;
            .alarminp-left{
                width: 72px;
                margin-right: 8px;
                margin-bottom: 8px;
            }
            .alarminp-right{
                width: 72px;
                margin-right: 8px;
                margin-bottom: 8px;
            }
            .alarminp-center{
                width: 72px;
                margin-right: 8px;
                margin-bottom: 8px;
                height: 36px;
                border-radius: 4px;
                line-height: 36px;
                font-size: 18px;
                text-align: center;
            }
            .blue{
                background: #E6F7FF;
                border: 1px solid #91D5FF;
                color: #1890FF;
            }
            .orange{
                background: #FFF7E6;
                border: 1px solid #FFD591;
                color: #D46B08;
            }
            .red{
                background: #FFF2F0;
                border: 1px solid #FFCCC7;
                color: #FF4D4F;
            }
        }
    }
}

.addtitle{
    color: #333333;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
}
.addbody{
    padding: 40px 160px;
}
.checkbox{
    padding: 0 0 0 24px;
}
