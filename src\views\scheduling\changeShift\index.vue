<template>
  <div class="body">
    <el-card>
      <el-card>
        <div slot="header">
          <span>数据筛选</span>
        </div>
        <div class="center">
          <div class="scarchIpt">
            <el-form :inline="true" :model="formInline" class="demo-form-inline">
              <el-form-item label="申请员工：" class="marb">
                <el-input v-model="formInline.workName" placeholder="请输入姓名" clearable :maxlength="20"></el-input>
              </el-form-item>
              <el-form-item label="所属班组：" class="marb">
                <el-select class="selectW" v-model="formInline.workClass" placeholder="请选择班组">
                  <el-option v-for="item in serviceArr" :key="item.id" :label="item.name"
                             :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="调班方式：" class="marb">
                <el-select class="selectW" v-model="formInline.workType" placeholder="请选择调班方式">
                  <el-option v-for="item in dictList.dict.TBGL_type" :key="item.value" :label="item.label"
                             :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="审批状态：" class="marb">
                <el-select class="selectW" v-model="formInline.workState" placeholder="请选择审批状态">
                  <el-option v-for="item in dictList.dict.ACT_status" :key="item.value"
                             :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="申请时间：" class="marb">
                <el-date-picker class="selectW" v-model="workTime" type="datetimerange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期" align="left" @change="chooseDateM"
                                value-format="yyyy-MM-dd HH:mm:ss">
                </el-date-picker>
              </el-form-item>
            </el-form>
          </div>
          <div class="tabButton">
            <div>
              <el-button icon="el-icon-search" type="primary" class="searchBtn" @click="findList">搜索</el-button>
              <el-button icon="el-icon-refresh" @click="resetList" class="searchBtn">重置</el-button>
            </div>
          </div>
        </div>
      </el-card>
      <el-card class="tab_card">
        <div slot="header">
          <div class="tab_card_header">
                        <span>
                            调班管理展示列表
                        </span>
            <div class="btns">
              <el-button v-hasRoleList="['ROLE_ADMIN']" icon="el-icon-plus" type="success" @click="openDrawerBtn" plain
                         class="searchBtn">代理申请
              </el-button>
              <el-button icon="el-icon-plus" type="primary" @click="openDrawerBtnYG" class="searchBtn">员工申请
              </el-button>
            </div>
          </div>
        </div>
        <el-table v-loading="loading" :data="tableData" style="width: 100%" :highlight-current-row="true">
          <el-table-column prop="applicantUserName" label="申请员工" width="200" align="center" header-align="center"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="serviceGroupName" label="所属班组" width="200" align="left" header-align="left"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="typeName" label="调班方式" align="center" header-align="center"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="substituteMemberName" label="确认员工" align="center" header-align="center"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="approveStatusName" label="调班状态" align="center" header-align="center"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="applyDatetime" label="申请时间" align="center" header-align="center" show-overflow-tooltip
                           min-width="150">
          </el-table-column>
          <el-table-column label="操作" align="center" header-align="center" min-width="150">
            <template slot-scope="scope">
              <div>
                <!--                              applicantUserName-->
                <span class="caozuo pointer" @click="goDetail(scope.$index, scope.row)"><i
                  class="el-icon-view"></i>查看</span>
                <span class="caozuo pointer" @click="agree(scope.$index, scope.row)"
                      v-if="(scope.row.approveStatus == '106'&&realName==scope.row.substituteMemberName)||(scope.row.approveStatus=='106'&&(isAdmin||serviceGroupRole=='2'))"><i
                  class="el-icon-circle-check"></i>同意</span>
                <span class="caozuo pointer" @click="noAgree(scope.$index, scope.row)"
                      v-if="(scope.row.approveStatus == '106'&&realName==scope.row.substituteMemberName)||(scope.row.approveStatus=='106'&&(isAdmin||serviceGroupRole=='2'))"><i
                  class="el-icon-circle-close"></i>不同意</span>
                <span class="caozuo pointer" @click="editData(scope.$index, scope.row)" v-if="((scope.row.approveStatus == '101' || scope.row.approveStatus == '109')&&scope.row.applicantUserName==realName)
                                                    ||((scope.row.approveStatus == '101'  || scope.row.approveStatus == '109')&&(isAdmin||serviceGroupRole=='2'))
                                    "><i class="el-icon-edit"></i>编辑</span>
                <span class="caozuo pointer" @click="submit(scope.$index, scope.row)" v-if="scope.row.approveStatus == '101' || scope.row.approveStatus == '109'
                                    "><i class="el-icon-edit-outline"></i>提交</span>
                <span class="caozuo pointer" @click="withdraw(scope.$index, scope.row)" v-if="((scope.row.approveStatus == '102' ||
                     scope.row.approveStatus == '103')&&scope.row.applicantUserName==realName)||((scope.row.approveStatus == '102' ||
                     scope.row.approveStatus == '103')&&(isAdmin||serviceGroupRole=='2'))
                     "><i class="el-icon-tickets"></i>撤回</span>
                <span class="caozuo pointer" @click="deleteList(scope.$index, scope.row)" v-if="scope.row.approveStatus == '101' || scope.row.approveStatus == '109'
                                    "><i class="el-icon-delete"></i>删除</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :limit.sync="pageSize" :page.sync="pageNum" :total="total"
                    @pagination="pageLists"/>
      </el-card>
    </el-card>

    <el-dialog :title="dialogTit" :visible.sync="openDrawer" :before-close="handleClose" append-to-body>
      <el-form :label-position="labelPosition" label-width="100px" :model="siteList" :rules="rules" ref="siteListOne">
        <el-form-item label="所属班组" prop="shiftGroup">
          <el-select v-model="siteList.shiftGroup" placeholder="请选择所属班组" @change="chooseFWZ">
            <el-option v-for="item in serviceArr" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请员工" prop="shiftMan">
          <el-select v-model="siteList.shiftMan" placeholder="请选择申请员工">
            <el-option v-for="item in manArr" :key="item.id" :label="item.userName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="调班方式" prop="shiftType">
          <el-select v-model="siteList.shiftType" placeholder="请选择调班方式">
            <el-option v-for="item in dictList.dict.TBGL_type" :key="item.value" :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="调班时间" prop="restDatetime" v-if="siteList.shiftType != '104003'">
          <el-date-picker v-model="siteList.restDatetime" type="date" align="right"
                          value-format="yyyy-MM-dd" placeholder="请选择调班时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="siteList.shiftType == '104003'
                    ? '调班时间'
                    : '补班时间'
                    " v-if="siteList.shiftType != '104002'" prop="workDatetime">
          <el-date-picker v-model="siteList.workDatetime" type="date" align="right"
                          value-format="yyyy-MM-dd" placeholder="补班时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="代班人" v-if="siteList.shiftType != '104001'" prop="shiftPeo">
          <el-select v-model="siteList.shiftPeo" placeholder="请选择代班人">
            <el-option v-for="item in manArr" :key="item.id" :label="item.userName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="调班理由" prop="shiftCause">
          <el-input class="marl" type="textarea" :rows="2" placeholder="请填写调班理由" v-model="siteList.shiftCause" :maxlength="200">
          </el-input>
        </el-form-item>
        <el-form-item>
          <div class="up-btn">
            <el-button type="primary" @click="submitList('siteListOne')">确定</el-button>
            <el-button @click="closeDialog">取消</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="新增调班申请" :visible.sync="openDrawerYG" :before-close="handleClose" append-to-body>
      <el-form :label-position="labelPosition" label-width="100px" :model="siteList" :rules="rules" ref="siteListTwo">
        <el-form-item label="申请员工" prop="shiftManName">
          <el-input v-model="siteList.shiftManName" placeholder="请输入姓名" disabled :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="调班方式" prop="shiftType">
          <el-select v-model="siteList.shiftType" placeholder="请选择调班方式">
            <el-option v-for="item in dictList.dict.TBGL_type" :key="item.value" :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="调班时间" prop="restDatetime" v-if="siteList.shiftType != '104003'">
          <el-date-picker v-model="siteList.restDatetime" type="date" align="right"
                          value-format="yyyy-MM-dd" placeholder="请选择调班时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="siteList.shiftType == '104003'
                    ? '调班时间'
                    : '补班时间'
                    " v-if="siteList.shiftType != '104002'" prop="workDatetime">
          <el-date-picker v-model="siteList.workDatetime" type="date" align="right"
                          value-format="yyyy-MM-dd" placeholder="请选择调班工作时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="代班人" v-if="siteList.shiftType != '104001'" prop="shiftPeo">
          <el-select v-model="siteList.shiftPeo" placeholder="请选择代班人">
            <el-option v-for="item in manArr" :key="item.id" :label="item.userName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="调班理由" prop="shiftCause">
          <el-input class="marl" type="textarea" :rows="2" placeholder="请填写调班理由" v-model="siteList.shiftCause" :maxlength="200" show-word-limit>
          </el-input>
        </el-form-item>
        <el-form-item>
          <div class="up-btn">
            <el-button type="primary" @click="submitList('siteListTwo')">确定</el-button>
            <el-button @click="closeFormTwo">取消</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  pageList,
  addsave,
  findList,
  findListMan,
  submitAct,
  tbUpdate,
  tbglDeleteByIds,
  getLoginMemberInfo,
  approvalOperation, getServiceGroupSelectList, withdrawAct
} from '@/api/scheduling/scheduling'

  export default {
    name: '',
    // 获取父级的值
    props: {},
    // 数据
    data() {
      return {
        serviceGroupRole: '',
        isAdmin: false,
        realName: '',
        rules: {
          shiftMan: [
            {
              required: true,
              message: '请选择申请人',
              trigger: 'blur'
            }
          ],
          shiftGroup: [
            {
              required: true,
              message: '请选择服务组',
              trigger: 'blur'
            }
          ],
          shiftPeo: [
            {
              required: true,
              message: '请选择代班人',
              trigger: 'blur'
            }
          ],
          shiftCause: [
            {
              required: true,
              message: '请输入调班理由',
              trigger: 'blur'
            }
          ],
          restDatetime: [
            {
              required: true,
              message: '请选择调班休息时间',
              trigger: 'blur'
            }
          ],
          workDatetime: [
            {
              required: true,
              message: '请选择调班工作时间',
              trigger: 'blur'
            }
          ],
          givebackDatetime: [
            {
              required: true,
              message: '请选择还班时间',
              trigger: 'blur'
            }
          ],
          shiftType: [
            {
              required: true,
              message: '请选择调班方式',
              trigger: 'blur'
            }
          ]
        },
        name: '环境数据',
        formInline: {
          workName: '',
          workClass: '',
          workType: '',
          workState: '',
          workbeginDate: '',
          workendDate: ''
        },
        workTime: '',
        // 遮罩层
        loading: false,
        tableData: [],
        pageSize: 10,
        pageNum: 1,
        total: 0,
        targetList: [
          {
            name: '轻度',
            degree: '102001',
            startNum: '',
            startType: '1',
            endNum: '',
            endType: '1'
          },
          {
            name: '中度',
            degree: '102002',
            startNum: '',
            startType: '1',
            endNum: '',
            endType: '1'
          },
          {
            name: '重度',
            degree: '102003',
            startNum: '',
            startType: '1',
            endNum: '',
            endType: '1'
          }
        ],
        openDrawer: false,
        openDrawerYG: false,
        labelPosition: 'right',
        siteList: {
          shiftManName: '',
          shiftMan: '',
          shiftGroup: '',
          shiftType: '',
          shiftPeo: '',
          shiftCause: '',
          restDatetime: '',
          workDatetime: '',
          givebackDatetime: ''
        },
        serviceArr: [],
        manArr: [],
        isAdd: 1,
        vegeid: '',
        userList: {},
        dialogTit: '新增调班申请' // 编辑弹框与新增弹框标题更改
      }
    },

    // 实例创建完成后被立即调用
    created() {
      this.getDict()
      this.findLists()
      this.pageLists()
    },

    // 挂载实例后调用
    mounted() {
      this.getLoginMemberInfos()
      this.serviceGroupRole = this.$store.getters.serviceGroupRole
      this.realName = this.$store.getters.realName
      this.isAdmin = this.$store.getters.roleCodeList instanceof Array && this.$store.getters.roleCodeList.includes('ROLE_ADMIN')
    },

    // 监控
    watch: {},

    // 过滤器
    filters: {},

    // 定义模板
    components: {},

    // 计算属性
    computed: {
      dictList() {
        return this.$store.state.dict
      },
    },

    // 混入到 Vue 实例中
    methods: {
      // 选择时间
      chooseDateM(val) {
        this.formInline.workbeginDate = val[0]
        this.formInline.workendDate = val[1]
      },
      /** 获取当前登录人的信息 */
      getLoginMemberInfos() {
        this.loading = true
        let params = {}
        getLoginMemberInfo(params).then((res) => {
          this.userList = res.data
          this.siteList.shiftMan = this.userList.id
          this.siteList.shiftGroup = this.userList.serviceGroupId
          this.siteList.shiftManName = this.userList.userName
          this.findListMans(this.userList.serviceGroupId)
          this.loading = false
        })
      },

      /** 设备管理删除 */
      tbglDeleteByIdss(val) {
        this.loading = true
        // JSON.stringify(this.targetList)
        let params = {
          ids: val
        }
        tbglDeleteByIds(params).then((res) => {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.pageLists()
          this.loading = false
        })
      },
      deleteList(index, row) {
        this.tbglDeleteByIdss(row.id)
      },
      editData(index, row) {
        console.log(row, '表格一行数据')
        this.dialogTit = '编辑调班申请'
        this.isAdd = 2
        this.openDrawer = true
        this.siteList.restDatetime = row.restDatetime
        this.siteList.workDatetime = row.workDatetime
        this.siteList.givebackDatetime = row.givebackDatetime

        this.siteList.shiftMan = row.applicantMemberId
        this.siteList.shiftGroup = row.serviceGroupId
        this.siteList.shiftType = row.type
        this.siteList.shiftPeo = row.substituteMemberId
        this.siteList.shiftCause = row.reason
        this.vegeid = row.id
      },
      findListMans(id) {
        let params = {
          serviceGroupId: id
        }
        findListMan(params).then((res) => {
          this.manArr = res.data
        })
      },
      chooseFWZ(value) {
        this.findListMans(value)
      },
      handleClose(done) {
        this.$confirm('确认关闭？')
          .then((_) => {
            done()
          })
          .catch((_) => {
          })
      },
      // 详情接口
      goDetail(index, row) {
        this.$router.push({
          // path: '/vue-equipment/scheduling/shiftmanaDetail',
          name: 'changeShiftDetail',
          query: row
        })
      },
      // 提交接口
      submit(index, row) {
        this.submitActs(row.id)
      },
      findLists() {
        let params = {}
        getServiceGroupSelectList(params).then((res) => {
          this.serviceArr = res.data
        })
      },
      // 新增接口
      submitList(siteList) {
        this.$refs[siteList].validate((valid) => {
          if (valid) {
            if (this.isAdd == 1) {
              this.addRoles()
            } else {
              this.tbUpdates()
            }
          } else {
            return false
          }
        })
      },
      submitActs(ids) {
        this.loading = true
        // JSON.stringify(this.targetList)
        let params = {
          id: ids
        }
        submitAct(params).then((res) => {
          this.$message({
            message: '提交成功',
            type: 'success'
          })
          setTimeout(() => {
            this.loading = false
            this.openDrawer = false
            this.pageLists()
          }, 1000)
        })
      },
      //撤回
      withdraw(index, row) {
        this.loading = true
        // JSON.stringify(this.targetList)
        let params = {
          workAdjustmentId: row.id
        }
        withdrawAct(params).then((res) => {
          this.$message({
            message: '撤回成功',
            type: 'success'
          })
          setTimeout(() => {
            this.openDrawer = false
            this.pageLists()
          }, 1000)
        })
        this.loading = false
      },

      tbUpdates() {
        this.loading = true
        let params = {
          applicantMemberId: this.siteList.shiftMan,
          serviceGroupId: this.siteList.shiftGroup,
          type: this.siteList.shiftType,
          substituteMemberId: this.siteList.shiftPeo,
          reason: this.siteList.shiftCause,
          restDatetime: this.siteList.restDatetime,
          workDatetime: this.siteList.workDatetime,
          givebackDatetime: this.siteList.givebackDatetime,
          id: this.vegeid
        }
        tbUpdate(params).then((res) => {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.loading = false
          setTimeout(() => {
            this.openDrawer = false
            this.pageLists()
          }, 1000)
        })
      },
      addRoles() {
        this.loading = true
        // JSON.stringify(this.targetList)
        let params = {
          applicantMemberId: this.siteList.shiftMan, //申请员工
          serviceGroupId: this.siteList.shiftGroup, //班组名称
          type: this.siteList.shiftType,
          substituteMemberId: this.siteList.shiftPeo,
          reason: this.siteList.shiftCause,
          restDatetime: this.siteList.restDatetime,
          workDatetime: this.siteList.workDatetime,
          givebackDatetime: this.siteList.givebackDatetime
        }
        addsave(params).then((res) => {
          this.$message({
            message: '新增成功',
            type: 'success'
          })
          setTimeout(() => {
            this.loading = false
            this.openDrawer = false
            this.openDrawerYG = false
            this.resetForm()
            this.pageLists()
          }, 1000)
        }).catch(err => {
          this.loading = false
        })
      },
      //重置提交表单
      resetForm() {
        this.siteList.shiftType = ""
        this.siteList.restDatetime = ""
        this.siteList.shiftPeo = ""
        this.siteList.shiftCause = ""
        this.siteList.workDatetime = ""
        this.siteList.givebackDatetime = ""
      },
      /** 告警中心分页查询列表 */
      pageLists() {
        this.loading = true
        console.log(this.formInline.workbeginDate,"console.log(this.formInline.workbeginDate)")
        let params = {
          applicantUserName: this.formInline.workName,
          serviceGroupId: this.formInline.workClass,
          type: this.formInline.workType,
          approveStatus: this.formInline.workState,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          applyDatetimeBg: this.formInline.workbeginDate?this.formInline.workbeginDate:'',
          applyDatetimeEd: this.formInline.workendDate?  this.formInline.workendDate:''
        }

        pageList(params).then((res) => {
          this.tableData = res.data.list
          this.total = res.data.total
          console.log(this.tableData, '表格列表')
          // this.userList = response.rows;
          // this.total = response.total;
          this.loading = false
        })
      },
      getDict() {
        this.$store.dispatch('dict/setDict', {})
      },
      findList() {
        this.pageNum = 1;
        this.pageLists()
      },
      resetList() {
        console.log("打印一下")
        this.tableData = []
        this.pageNum = 1
        this.pageSize = 10
        this.workTime = ''
        // this.formInline.workbeginDate = ''
        // this.formInline.workendDate = ''
        this.formInline = {}
        this.pageLists()
      },
      openDrawerBtn() {
        this.resetForm()
        this.siteList.shiftMan = ""
        this.siteList.shiftGroup = ""
        this.siteList.shiftManName = ""
        this.isAdd = 1
        this.getLoginMemberInfos()
        this.dialogTit = '新增调班申请'
        this.openDrawer = true
      },
      openDrawerBtnYG() {
        this.resetForm()
        this.siteList.shiftMan = ""
        this.siteList.shiftGroup = ""
        this.siteList.shiftManName = ""
        this.isAdd = 1
        this.getLoginMemberInfos()
        this.openDrawerYG = true
      },
      approvalOperations(params) {
        this.loading = true
        approvalOperation(params).then((res) => {
          this.$message({
            message: '状态更新成功',
            type: 'success'
          })
          this.pageLists()
          this.loading = false
        }).catch(err => {
          this.loading = false
        })
      },
      //同意
      agree(index, row) {
        let params = {
          id: row.id,
          operationType: 1
        }
        this.approvalOperations(params)
      },
      // 不同意
      noAgree(index, row) {
        let params = {
          id: row.id,
          operationType: 2
        }
        this.approvalOperations(params)
      },
      closeDialog() {
        this.resetFormOne()
        this.openDrawer = false
      },
      resetFormOne() {
        this.siteList = {
          shiftManName: '',
          shiftMan: '',
          shiftGroup: '',
          shiftType: '',
          shiftPeo: '',
          shiftCause: '',
          restDatetime: '',
          workDatetime: '',
          givebackDatetime: ''
        }
        this.$refs.siteListOne.resetFields();
      },
      resetFormTwo() {
        this.$refs.siteListTwo.resetFields();
      },
      closeFormTwo() {
        this.resetFormTwo()
        this.openDrawerYG = false
      }
    }
  }
</script>
<style lang='scss' scoped>
  @import './index.scss';


  .selectW {
    width: 100%;
  }

  .selectW1 {
    width: 300px;
  }

  .selectW100 {
    width: 100%;
  }

  .marb {
    margin-bottom: 20px !important;
  }

  .up-btn {
    display: flex;

  }

  ::v-deep .el-dialog__header {
    border-bottom: 1px solid #f1f1f1;
  }

  ::v-deep .el-dialog__body {
    // padding: 32px 30px 5px 30px;

    .el-form-item {
      width: 100% !important;
    }
  }

  ::v-deep .el-dialog__footer {
    border-top: 1px solid #f1f1f1;
  }

  ::v-deep .el-form-item {
    margin-bottom: 0px;
    width: 30%;
  }

  ::v-deep .el-row {
    display: flex;
    justify-content: flex-end;
  }

  ::v-deep .el-table__row {
    height: 50px;
  }

  ::v-deep .el-form-item {
    margin-bottom: 0px;
    width: 520px;
    margin-bottom: 20px;
  }

  ::v-deep .el-card__header {
    padding: 15px 24px;
    font-size: 18px;
  }

  ::v-deep .table_box {
    padding: 16px;
    background-color: #ffffff;
  }

  ::v-deep .center-btn {
    padding: 16px;
  }

  ::v-deep .pagination-container .el-pagination {
    right: 16px;
  }

  ::v-deep .el-card__body {
    padding: 16px;
  }


  ::v-deep .el-form-item {
    margin-bottom: 0px;
    width: 390px;
    margin-bottom: 20px;
  }

  ::v-deep .el-form-item {
    margin-bottom: 0px;
    width: 390px;
    margin-bottom: 20px;
  }

  ::v-deep .el-table .el-table__header-wrapper th {
    background: rgba(25, 159, 255, 0.15);
    font-family: Noto Sans SC;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #007baf;
  }

  .center {
    display: flex;

    ::v-deep .el-input {
      width: 10vw;
    }

    ::v-deep .el-date-editor {
      width: 19vw;
    }

    .scarchIpt {
      -webkit-box-flex: 6;
      flex: 6 1 0%;
    }

    .tabButton {
      -webkit-box-flex: 1;
      flex: 1 1 0%;
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;

    }

    .el-form-item {
      white-space: nowrap;
      width: 17vw;
    }

    ::v-deep .el-form-item__label {
      width: 85px;
    }
  }

  ::v-deep .el-form {
    padding-left: 0;
  }


  ::v-deep .el-card__header {
    height: 56px;
    font-size: 18px;
    font-weight: 400;
    padding: 16px;
  }

  .tab_card {
    ::v-deep .el-card__body {
      padding: 16px 0px;
    }

    .tab_card_header {
      display: flex;
      justify-content: space-between;

      > span {
        display: flex;
        align-items: center;
      }
    }
  }

  ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    border: 1px solid #188cff;
    border-radius: 2px;
    background-color: #fff;
    color: #188cff;
  }

  .el-card {
    margin-bottom: 20px;
  }

  ::v-deep .el-dialog {
    width: 560px;

    .el-input {
      width: 245px;
    }

    .el-textarea__inner {
      width: 245px;
    }
  }
</style>
