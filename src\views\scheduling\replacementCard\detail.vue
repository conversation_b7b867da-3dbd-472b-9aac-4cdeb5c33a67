<template>
    <div class="body flexs">
        <div class="body-left">
            <div class="left-line">
                <div class="left-lineTitle">员工信息</div>
                <div class="left-lineBody">
                    <p class="left-txt">姓名：{{ detailList.userName }}</p>
                    <p class="left-txt">所属部门：{{ detailList.deptName }}</p>
                    <p class="left-txt">
                        服务组：{{ detailList.serviceGroupName }}
                    </p>
                </div>
            </div>
            <div class="left-line">
                <div class="left-lineTitle">调班信息</div>
                <div class="left-lineBody">
                    <!-- <p class="left-txt">调班方式：{{detailList.userName}}</p>
                    <p class="left-txt">调班休息时间：{{detailList.userName}}</p> -->
                    <p class="left-txt maxW">
                        补卡时间：{{ detailList.repairAttendDatetime }}
                    </p>
                    <p class="left-txt maxW">
                        补卡类型：{{ detailList.repairAttendTypeName }}
                    </p>
                    <!-- <p class="left-txt">代班人：赵铁柱</p> -->
                    <!-- <p class="left-txt">还班时间：2023-02-26 15:00:00</p> -->
                    <p class="left-txt maxW">
                        补卡原因：{{ detailList.reason }}
                    </p>
                </div>
            </div>
            <div class="left-down">
                <div class="left-down-up">
                    附件：
                    <div>
                        <div
                            class="attachment"
                            v-for="(item, index) in detailList.files"
                            :key="index"
                        >
                            <div class="attachmentDetail" @click="upLoad(item)">
                                {{ item.name }}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="left-down-down">
                    <div class="left-down-downtxt">审批意见：</div>
                    <el-input
                        type="textarea"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                        placeholder="请输入审批意见"
                        v-model="opinion"
                    >
                    </el-input>
                </div> -->
            </div>
        </div>
        <div class="body-right">
            <div class="block">
                <el-timeline>
                    <el-timeline-item
                        v-for="(item, index) in processList"
                        :key="index"
                        :color="item.duration ? '#0bbd87' : '#FF0000'"
                    >
                        <el-card>
                            <div class="cards cardsm">
                                <span class="cards-left">时间：</span>
                                <span>{{ item.createTime }}</span>
                            </div>
                            <div class="cards cardsm">
                                <span class="cards-left">人员：</span>
                                <span>{{ item.assigneeName }}</span>
                            </div>
                            <div class="cards">
                                <span class="cards-left">流程状态：</span>
                                <div
                                    v-for="(zitem, zindex) in item.commentList"
                                    :key="zindex"
                                >
                                    <span>{{ zitem.message }}</span>
                                    <span
                                        class="cards-dian"
                                        :style="`background-color: ${
                                            item.duration
                                                ? '#0bbd87'
                                                : '#FF0000'
                                        }`"
                                    ></span>
                                    <span>{{
                                        zitem.type == 1 ? '通过' : '驳回'
                                    }}</span>
                                </div>
                            </div>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
            </div>
        </div>
    </div>
</template>

<script>
import { bkDetail, bksqlcDetail } from '@/api/scheduling/scheduling'
export default {
    name: '',
    // 获取父级的值
    props: {},
    // 数据
    data() {
        return {
            processList: [],
            detailList: {},
            opinion: '',
            activities: [
                {
                    name: '马振兵',
                    state: '提交',
                    stateRight: '通过',
                    timestamp: '2018-04-12 20:46',
                    color: '#1890ff'
                },
                {
                    name: '马振兵',
                    state: '提交',
                    stateRight: '通过',
                    timestamp: '2018-04-03 20:46',
                    color: '#0bbd87'
                },
                {
                    name: '马振兵',
                    state: '提交',
                    stateRight: '驳回',
                    timestamp: '2018-04-03 20:46',
                    color: '#FF0000'
                },
                {
                    name: '马振兵',
                    state: '提交',
                    stateRight: '通过',
                    timestamp: '2018-04-03 20:46'
                }
            ]
        }
    },

    // 实例创建完成后被立即调用
    created() {
        this.bkDetails()
        this.bksqlcDetails()
    },

    // 挂载实例后调用
    mounted() {},

    // 监控
    watch: {},

    // 过滤器
    filters: {},

    // 定义模板
    components: {},

    // 计算属性
    computed: {},

    // 混入到 Vue 实例中
    methods: {
        bkDetails() {
            let ids = this.$route.query.id
            let params = {
                id: ids
            }
            bkDetail(params).then((res) => {
                this.detailList = res.data
            })
        },
        bksqlcDetails() {
            this.loading = true
            console.log(this.$route.query)
            let ids = this.$route.query.id
            // JSON.stringify(this.targetList)
            let params = {
                workAdjustmentId: ids
            }
            console.log(params, 'params')
            bksqlcDetail(params).then((res) => {
                console.log(res)
                this.processList = res.data.historyProcNodeList
                this.loading = false
            })
        },
        upLoad(val) {
            // var blob = new Blob([val.url], {
            //     type: 'text/plain;charset=utf-8'
            // })
            // console.log(blob)
            // // 存在浏览器兼容性
            // let href = URL.createObjectURL(val.url)
            // console.log(href)
            let alink = document.createElement('a')
            alink.style.display = 'none'
            alink.download = val.name //下载后文件名
            alink.href = val.url
            document.body.appendChild(alink)
            alink.click()
        }
    }
}
</script>
<style lang='scss' scoped>
@import './index.scss';
::v-deep .el-timeline-item {
    padding-bottom: 39px;
}
.attachment {
    margin-bottom: 20px;
    margin-left: 50px;
    .attachmentDetail {
        cursor: pointer;
        color: #478eff;
    }
}
</style>