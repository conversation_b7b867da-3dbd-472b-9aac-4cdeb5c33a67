.body{
    width: 100%;
    // height: calc(100vh - 86px);
    height: 100%;
    margin: 0;
    font-size: 12px;
    padding: 24px;
}
.body-left{
    height: 830px;
    background-color: #ffffff;
    width: 70%;
    padding: 30px 30px 50px 50px;
    .left-line{
        margin-top: 40px;
        .left-lineTitle{
            color: #1890FF;
            font-size: 16px;
        }
        .left-lineBody{
            margin-top: 20px;
            font-size: 16px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            .left-txt{
                width: 50%;
            }
            .maxW{
                width: 100% !important;
            }
        }

        .left-lineTitle::before{
            content: "";
            display: inline-block;
            width: 6px;
            height: 15px;
            margin-right: 6px;
            background-color: #1890FF;
        }
    }
    .left-down{
        font-size: 16px;
        .left-down-up{
            display: flex;
            align-items: flex-start;
            margin-top: 20px;
        }
        .left-down-down{
            margin-top: 40px;
            display: flex;
            align-items: inherit;
            .left-down-downtxt{
                width: 100px;
            }
        }
    }
}
.body-right{
    height: 830px;
    background-color: #ffffff;
    width: 30%;
    padding: 30px;
}
.flexs{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cards{
    display: flex;
    align-items: center;
    .cards-left{
        width: 100px;
    }
}
.cards-dian{
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 30px;
    margin-right: 6px;
}
.cardsm{
    margin-bottom: 12px;
}
.add{
    background-color: #ffffff;
    padding: 24px;
}
.center{
    border-bottom: 1px solid #f5f5f5;
}
.center-btn{
    background-color: #ffffff;
    padding: 10px 24px 10px 24px;
    display: flex;
    justify-content: space-between;
}
.caozuo{
    color: #3399FF;
}
.alarmUp{
    margin-bottom: 24px;
    .alarmTips{
        font-size: 16px;
        margin-right: 24px;
    }
}
.alarmDown{
    // display: flex;
    // align-items: center;
    .alarmTips{
        font-size: 16px;
        margin-right: 24px;
    }
    .alarmRight{
        margin-left: 92px;
        margin-top: -20px;
        .alarminp{
            display: flex;
            align-items: center;
            // margin-left: 80px;
            .alarminp-left{
                width: 72px;
                margin-right: 8px;
                margin-bottom: 8px;
            }
            .alarminp-right{
                width: 72px;
                margin-right: 8px;
                margin-bottom: 8px;
            }
            .alarminp-center{
                width: 72px;
                margin-right: 8px;
                margin-bottom: 8px;
                height: 36px;
                border-radius: 4px;
                line-height: 36px;
                font-size: 18px;
                text-align: center;
            }
            .blue{
                background: #E6F7FF;
                border: 1px solid #91D5FF;
                color: #1890FF;
            }
            .orange{
                background: #FFF7E6;
                border: 1px solid #FFD591;
                color: #D46B08;
            }
            .red{
                background: #FFF2F0;
                border: 1px solid #FFCCC7;
                color: #FF4D4F;
            }
        }
    }
}
.caozuo{
    color: #3399FF;
    margin-right: 20px;
}
.addtitle{
    color: #333333;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
}
.addbody{
    padding: 40px 160px;
}
.checkbox{
    padding: 0 0 0 24px;
}
