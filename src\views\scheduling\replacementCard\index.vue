<template>
    <div class="body">
        <div>
            <div class="center">
                <el-form
                    :inline="true"
                    :model="formInline"
                    class="demo-form-inline"
                >
                    <el-form-item label="补卡申请人：" class="marb">
                        <el-input
                            v-model="formInline.workName"
                            placeholder="请输入姓名"
                            clearable
                            :maxlength="20"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="所属服务组：" class="marb">
                        <el-select
                            class="selectW"
                            v-model="formInline.workServer"
                            placeholder="请选择服务组"
                        >
                            <el-option
                                v-for="item in serviceArr"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="补卡类型：" class="marb">
                        <el-select
                            class="selectW"
                            v-model="formInline.workType"
                            placeholder="请选择补卡类型"
                        >
                            <el-option
                                v-for="item in dictList.dict.KQDK_type"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="审批状态：">
                        <el-select
                            class="selectW marl"
                            v-model="formInline.workState"
                            placeholder="请选择审批状态"
                        >
                            <el-option
                                v-for="item in dictList.dict.ACT_status"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="补卡时间：">
                        <el-date-picker
                            class="selectW marl"
                            v-model="workTime"
                            type="datetimerange"
                            :picker-options="pickerOptions"
                            @change="chooseDateM"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            align="right"
                            value-format="yyyy-MM-dd HH:mm:ss"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </div>
            <div class="center-btn">
                <div>
                    <el-button @click="openDrawerBtn">新增审批</el-button>
                    <!-- <el-button @click="openDrawerBtn">批量导入</el-button> -->
                    <el-button @click="openDrawerBtn">批量导出</el-button>
                </div>
                <div>
                    <el-button type="primary" @click="findList">查询</el-button>
                    <el-button @click="resetList">重置</el-button>
                </div>
            </div>
        </div>
        <div>
            <el-table
                v-loading="loading"
                :data="tableData"
                style="width: 100%"
                :highlight-current-row="true"
            >
                <el-table-column
                    type="selection"
                    width="55"
                    align="center"
                    header-align="center"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    prop="userName"
                    label="员工"
                    width="200"
                    align="center"
                    header-align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="deptName"
                    label="所属部门"
                    align="center"
                    header-align="center"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    prop="serviceGroupName"
                    label="所属服务组"
                    align="center"
                    header-align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="repairAttendTypeName"
                    label="补卡类型"
                    align="center"
                    header-align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="repairAttendDatetime"
                    label="补卡时间"
                    width="200"
                    align="center"
                    header-align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="applyDatetime"
                    label="申请时间"
                    width="200"
                    align="center"
                    header-align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="approveStatusName"
                    label="审批状态"
                    width="200"
                    align="center"
                    header-align="center"
                >
                </el-table-column>
                <el-table-column
                    width="240"
                    label="操作"
                    align="center"
                    header-align="center"
                >
                    <template slot-scope="scope">
                        <div>
                            <span
                                class="caozuo"
                                @click="goDetail(scope.$index, scope.row)"
                                >查看详情</span
                            >
                            <span
                                class="caozuo"
                                @click="editData(scope.$index, scope.row)"
                                v-if="
                                    scope.row.approveStatus == '101' ||
                                    scope.row.approveStatus == '105'
                                "
                                >编辑</span
                            >
                            <span
                                class="caozuo"
                                @click="submit(scope.$index, scope.row)"
                                v-if="
                                    scope.row.approveStatus == '101' ||
                                    scope.row.approveStatus == '105'
                                "
                                >提交</span
                            >
                            <span
                                class="caozuo"
                                @click="withdraw(scope.$index, scope.row)"
                                v-if="
                                    scope.row.approveStatus == '102' ||
                                    scope.row.approveStatus == '103'
                                "
                                >撤回</span
                            >
                            <span
                                class="caozuo"
                                @click="deleteList(scope.$index, scope.row)"
                                v-if="
                                    scope.row.approveStatus == '101' ||
                                    scope.row.approveStatus == '105'
                                "
                                >删除</span
                            >
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <el-dialog
            title="新建审批"
            :visible.sync="openDrawer"
            :before-close="handleClose"
            append-to-body
        >
            <el-form
                :label-position="labelPosition"
                label-width="100px"
                :model="siteList"
                :rules="rules"
                ref="siteList"
            >
                <el-form-item label="员工" prop="shiftMan">
                    <el-select
                        class="selectW1"
                        v-model="siteList.shiftMan"
                        placeholder="请选择员工"
                    >
                        <el-option
                            v-for="item in manArr"
                            :key="item.id"
                            :label="item.userName"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="服务组" prop="shiftGroup">
                    <el-select
                        class="selectW1"
                        v-model="siteList.shiftGroup"
                        placeholder="请选择服务组"
                        @change="chooseFWZ"
                    >
                        <el-option
                            v-for="item in serviceArr"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="补卡类型" prop="shiftType">
                    <el-select
                        class="selectW1"
                        v-model="siteList.shiftType"
                        placeholder="请选择补卡类型"
                    >
                        <el-option
                            v-for="item in dictList.dict.KQDK_type"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="补卡时间" prop="restDatetime">
                    <el-date-picker
                        class="selectW1"
                        v-model="siteList.restDatetime"
                        type="datetime"
                        align="right"
                        value-format="yyyy-MM-dd hh:mm:ss"
                        placeholder="请选择补卡时间"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="附件上传">
                    <el-upload
                        ref="upload"
                        class="upload-demo"
                        :action="uploadUrl"
                        :headers="headers"
                        accept
                        :on-remove="handleRemove"
                        :on-success="handleSuccess"
                        :limit="10"
                        :file-list="fileList"
                    >
                        <el-button size="small" type="primary"
                            >点击上传</el-button
                        >
                    </el-upload>
                </el-form-item>
                <el-form-item label="调班理由" prop="shiftCause">
                    <el-input
                        type="textarea"
                        :rows="2"
                        placeholder="请填写调班理由"
                        v-model="siteList.shiftCause"
                        :maxlength="20"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <div class="up-btn">
                        <el-button @click="openDrawer = false">取消</el-button>
                        <el-button
                            type="primary"
                            @click="submitList('siteList')"
                            >确定</el-button
                        >
                    </div>
                </el-form-item>
            </el-form>
        </el-dialog>
        <pagination
            v-show="total > 0"
            :limit.sync="pageSize"
            :page.sync="pageNum"
            :total="total"
            @pagination="bkPageLists"
        />
        <!-- @pagination="getAlarmLists" -->
    </div>
</template>

<script>
import {
    bkPageList,
    bkaddsave,
    findList,
    findListMan,
    bksubmitAct,
    bkUpdate,
    bkDeleteByIds,
    bkDetail
} from '@/api/scheduling/scheduling'
import { getToken } from '@/utils/auth'
export default {
    name: '',
    // 获取父级的值
    props: {},
    // 数据
    data() {
        return {
            rules: {
                shiftMan: [
                    {
                        required: true,
                        message: '请选择员工',
                        trigger: 'change'
                    }
                ],
                shiftGroup: [
                    {
                        required: true,
                        message: '请选择服务组',
                        trigger: 'change'
                    }
                ],
                shiftType: [
                    {
                        required: true,
                        message: '请选择补卡类型',
                        trigger: 'change'
                    }
                ],
                shiftCause: [
                    {
                        required: true,
                        message: '请输入调班理由',
                        trigger: 'blur'
                    }
                ],
                restDatetime: [
                    {
                        required: true,
                        message: '请选择补卡时间',
                        trigger: 'change'
                    }
                ]
            },
            uploadUrl:
                process.env.VUE_APP_BASE_API + 'attendance/file/uploadFile', // 上传附件
            headers: {
                Authorization: getToken()
            },
            fileList: [],
            name: '环境数据',
            tabPosition: '1',
            formInline: {
                workName: '',
                workServer: '',
                workType: '',
                workState: '',
                workbeginDate: '',
                workendDate: ''
            },
            workTime: '',
            // 遮罩层
            loading: false,
            tableData: [],
            pageSize: 10,
            pageNum: 1,
            total: 0,
            alarm: '',
            targetList: [
                {
                    name: '轻度',
                    degree: '102001',
                    startNum: '',
                    startType: '1',
                    endNum: '',
                    endType: '1'
                },
                {
                    name: '中度',
                    degree: '102002',
                    startNum: '',
                    startType: '1',
                    endNum: '',
                    endType: '1'
                },
                {
                    name: '重度',
                    degree: '102003',
                    startNum: '',
                    startType: '1',
                    endNum: '',
                    endType: '1'
                }
            ],
            symbolList: [
                {
                    value: '1',
                    label: '<'
                },
                {
                    value: '2',
                    label: '≤'
                }
                // {
                //   value: '3',
                //   label: '>'
                // }, {
                //   value: '4',
                //   label: '≥'
                // },
            ],
            openDrawer: false,
            labelPosition: 'right',
            manArr: [],
            serviceArr: [],
            siteList: {
                shiftMan: '',
                shiftGroup: '',
                shiftType: '',
                shiftCause: '',
                restDatetime: ''
            },
            pickerOptions: {
                shortcuts: [
                    {
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 7
                            )
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 30
                            )
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date()
                            const start = new Date()
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 90
                            )
                            picker.$emit('pick', [start, end])
                        }
                    }
                ]
            },
            vegeid: '',
            isAdd: 1,
            detailList: {}
        }
    },

    // 实例创建完成后被立即调用
    created() {
        this.bkPageLists()
        this.getDict()
        this.findLists()
    },

    // 挂载实例后调用
    mounted() {},

    // 监控
    watch: {},

    // 过滤器
    filters: {},

    // 定义模板
    components: {},

    // 计算属性
    computed: {
        dictList() {
            return this.$store.state.dict
        }
    },

    // 混入到 Vue 实例中
    methods: {
        /** 设备管理删除 */
        bkDeleteByIdss(val) {
            this.loading = true
            // JSON.stringify(this.targetList)
            let params = {
                ids: val
            }
            console.log(params, 'params')
            bkDeleteByIds(params).then((res) => {
                console.log(res)
                this.$message({
                    message: '删除成功',
                    type: 'success'
                })
                this.bkPageLists()
                this.loading = false
            })
        },
        deleteList(index, row) {
            console.log(index, row)
            this.bkDeleteByIdss(row.id)
        },
        // 删除文件
        handleRemove(file, List) {
            console.log(file, List)
            this.fileList = []
            List.forEach((item) => {
                this.fileList.push({
                    name: item.name,
                    url: item.response ? item.response.viewPath : item.url
                })
            })
            console.log(this.fileList)
        },
        // 文件上传成功
        handleSuccess(file, List) {
            console.log(List)
            this.fileList.push({
                name: List.name,
                url: List.response.viewPath,
                id: List.response.fileId
            })
            console.log(this.fileList)
        },

        // 提交接口
        submit(index, row) {
            this.bksubmitActs(row.id)
        },
        bksubmitActs(ids) {
            this.loading = true
            // JSON.stringify(this.targetList)
            let params = {
                id: ids
            }
            console.log(params, 'params')
            bksubmitAct(params).then((res) => {
                this.$message({
                    message: '提交成功',
                    type: 'success'
                })
                setTimeout(() => {
                    this.loading = false
                    this.openDrawer = false
                    this.bkPageLists()
                }, 1000)
            })
        },
        // 新增接口
        submitList(siteList) {
            console.log(siteList)
            this.$refs[siteList].validate((valid) => {
                console.log(valid)
                if (valid) {
                    if (this.isAdd == 1) {
                        this.bkaddsaves()
                    } else {
                        this.bkUpdates()
                    }
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },

        bkUpdates() {
            this.loading = true
            // JSON.stringify(this.targetList)
            let fileIdsArr = []
            this.fileList.forEach((item, index) => {
                fileIdsArr.push(item.id)
            })
            console.log(fileIdsArr)
            let params = {
                memberId: this.siteList.shiftMan,
                serviceGroupId: this.siteList.shiftGroup,
                repairAttendType: this.siteList.shiftType,
                repairAttendDatetime: this.siteList.restDatetime,
                reason: this.siteList.shiftCause,
                fileIds: fileIdsArr.join(','),
                id: this.vegeid
            }
            console.log(params, 'params')

            bkUpdate(params).then((res) => {
                this.$message({
                    message: '修改成功',
                    type: 'success'
                })
                setTimeout(() => {
                    this.loading = false
                    this.openDrawer = false
                    this.bkPageLists()
                }, 1000)
            })
        },
        bkaddsaves() {
            this.loading = true
            // JSON.stringify(this.targetList)
            let fileIdsArr = []
            this.fileList.forEach((item, index) => {
                fileIdsArr.push(item.id)
            })
            console.log(fileIdsArr)
            let params = {
                memberId: this.siteList.shiftMan,
                serviceGroupId: this.siteList.shiftGroup,
                repairAttendType: this.siteList.shiftType,
                repairAttendDatetime: this.siteList.restDatetime,
                reason: this.siteList.shiftCause,
                fileIds: fileIdsArr.join(',')
            }
            console.log(params, 'params')
            bkaddsave(params).then((res) => {
                this.$message({
                    message: '新增成功',
                    type: 'success'
                })
                setTimeout(() => {
                    this.loading = false
                    this.openDrawer = false
                    this.bkPageLists()
                }, 1000)
            })
        },
        // 选择时间
        chooseDateM(val) {
            console.log(val, 'val')
            this.formInline.workbeginDate = val[0]
            this.formInline.workendDate = val[1]
            console.log(this.formInline.workbeginDate)
        },
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then((_) => {
                    done()
                })
                .catch((_) => {})
        },
        bkDetails(ids, row) {
            let params = {
                id: ids
            }
            bkDetail(params).then((res) => {
                this.detailList = res.data
                this.fileList = this.detailList.files
                this.isAdd = 2
                console.log(row, ' rowrow')
                this.siteList.shiftMan = row.memberId
                this.siteList.shiftGroup = row.serviceGroupId
                this.siteList.shiftType = row.repairAttendType
                this.siteList.shiftCause = row.reason
                this.siteList.restDatetime = row.repairAttendDatetime
                this.vegeid = row.id
                this.openDrawer = true
            })
        },
        editData(index, row) {
            this.bkDetails(row.id, row)
        },
        goDetail(index, row) {
            this.$router.push({
                // path: '/vue-equipment/scheduling/shiftmanaDetail',
                name: 'replacementCardDetail',
                query: row
            })
        },
        onSubmit() {
            console.log('submit!')
        },
        findListMans(id) {
            let params = {
                serviceGroupId: id
            }
            findListMan(params).then((res) => {
                this.manArr = res.data
            })
        },
        chooseFWZ(value) {
            console.log(value)
            this.findListMans(value)
        },
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then((_) => {
                    done()
                })
                .catch((_) => {})
        },
        findLists() {
            let params = {}
            findList(params).then((res) => {
                this.serviceArr = res.data
            })
        },
        /** 告警中心分页查询列表 */
        bkPageLists() {
            this.loading = true
            let params = {
                repairAttendType: this.formInline.workType,
                approveStatus: this.formInline.workState,
                userName: this.formInline.workName,
                serviceGroupId: this.formInline.workServer,
                repairAttendDatetimeBg: this.formInline.workbeginDate,
                repairAttendDatetimeEd: this.formInline.workendDate,
                pageNum: this.pageNum,
                pageSize: this.pageSize
            }
            bkPageList(params).then((res) => {
                this.tableData = res.data.list
                this.total = res.data.total
                console.log(this.tableData, 'this.res')
                // this.userList = response.rows;
                // this.total = response.total;
                this.loading = false
            })
        },
        getDict() {
            this.$store.dispatch('dict/setDict', {})
        },
        findList() {
            this.bkPageLists()
        },
        resetList() {
            this.tableData = []
            this.pageNum = 1
            this.pageSize = 10
            this.formInline = {}
            this.bkPageLists()
        },
        openDrawerBtn() {
            this.openDrawer = true
            this.isAdd = 1
            this.siteList.shiftMan = ''
            this.siteList.shiftGroup = ''
            this.siteList.shiftType = ''
            this.siteList.shiftCause = ''
            this.siteList.restDatetime = ''
            this.fileList = []
        }
    }
}
</script>
<style lang='scss' scoped>
@import './index.scss';
.demo-form-inline {
    background: #ffffff;
    padding: 24px;
}
.selectW {
    width: 100%;
}
.selectW1 {
    width: 300px;
}
.marl {
    margin-left: 14px !important;
}
.marb {
    margin-bottom: 20px !important;
}
.up-btn {
    text-align: end;
    width: 300px;
}
::v-deep .el-dialog {
    width: 40% !important;
}
::v-deep .el-dialog__body {
    padding: 20px 20px 20px 100px;
}
::v-deep .el-form-item {
    margin-bottom: 0px;
    width: 30%;
}
::v-deep .el-form-item__content {
    width: 80%;
}
::v-deep .el-row {
    display: flex;
    justify-content: flex-end;
}
::v-deep .el-table__row {
    height: 50px;
}
::v-deep .el-form-item {
    margin-bottom: 0px;
    width: 520px;
    margin-bottom: 20px;
}
::v-deep .el-form-item__content {
    width: 372px;
}
</style>
