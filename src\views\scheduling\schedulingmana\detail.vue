<template>
    <div class="body">
        <el-card>
            <div>
                <el-card>
                    <div slot="header">
                        员工筛选
                    </div>
                    <div class="center">
                        <div class="scarchIpt">
                            <span>员工：</span>
                            <div>
                                <el-select class="selectW" v-model="formInline.classMember" placeholder="请选择员工">
                                    <el-option v-for="item in manArr" :key="item.id" :label="item.userName"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <div>
                            <el-button class="searchBtn" type="primary" @click="findList">查询</el-button>
                            <el-button class="searchBtn" @click="resetList">重置</el-button>
                        </div>
                        <!-- <el-form
                    :inline="true"
                    :model="formInline"
                    class="demo-form-inline"
                >
                    <el-form-item label="员工：">
                        <el-select
                            class="selectW"
                            v-model="formInline.classType"
                            placeholder="请选择员工"
                        >
                            <el-option
                                v-for="item in dictList.dict.GJZX_type"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item> </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="findList"
                            >查询</el-button
                        >
                        <el-button @click="resetList">重置</el-button>
                    </el-form-item>
                </el-form> -->
                    </div>
                </el-card>
            </div>
            <el-card class="tab_card">
                <div slot="header">
                    <div class="center-btn">
                        <div class="center_left">
                            <!-- <div class="block"> -->
                            <el-date-picker v-model="monthDate" @change="chooseDate" type="month" placeholder="选择月">
                            </el-date-picker>
                            <!-- </div> -->
                            <el-button @click="openDrawerBtn">自动排班</el-button>
                            <el-button @click="cleanList">清空</el-button>
                        </div>
                        <div style="display: flex">
                            <el-button class="searchBtn" type="primary" @click="findList"><i class="el-icon-refresh-left"></i> 恢复</el-button>
                            <el-button class="searchBtn" @click="submitTable"><i class="el-icon-s-claim"></i> 保存</el-button>
                            <el-button class="searchBtn" @click="deriveTable"><i class="el-icon-download"></i> 导出Excel</el-button>
                            <el-upload class="upload-demo" action="/schedule/schedule/import"
                                :headers="headerObj" :limit="1" :show-file-list="false" :file-list="fileList"
                                :on-change="changeListState">
                                <el-button class="searchBtn"><i class="el-icon-upload"></i> 导入Excel</el-button>
                            </el-upload>
                        </div>
                    </div>
                </div>
                <!-- <el-table
                ref="refreshTable"
                v-loading="loading"
                :data="dateList"
                :header-cell-style="{ background: '#007F69', color: '#fff' }"
                highlight-current-row
            >
                <el-table-column
                    fixed
                    width="170px"
                    label="姓名"
                    align="center"
                    prop="userName"
                />
                <el-table-column label="降雨日期(mm)" align="center">
                    <template v-for="(item, index) in dateList">
                        <el-table-column
                            align="center"
                            :prop="item.name"
                            :label="item.house"
                            :key="index"
                            v-if="item.name != 'id'"
                        ></el-table-column>
                    </template>
                </el-table-column>
            </el-table> -->
                <div class="table-body" v-loading="loading">
                    <div class="table-title" v-if="dateListInfo.length">
                        <div class="table-name">姓名</div>
                        <div class="table-head" v-for="(item, index) in dateListHead" :key="index">
                            <div class="bor height501">
                                {{ item.day }}
                                <div style="color: red">
                                    {{ item.holidayName }}
                                </div>
                            </div>

                            <div class="height50">{{ item.week }}</div>
                            <!-- <div>{{ item.day }}</div> -->
                        </div>
                    </div>
                    <div class="title-center" >
                        <div class="table-line" v-for="(fitem, findex) in dateListInfo" :key="findex">
                            <div  class="table-name">{{ fitem.userName }}</div>
                            <div class="table-type" v-for="(sitem, sindex) in fitem.memberWorkVos" :key="sindex">
                                <!-- <el-popover
                                placement="right"
                                width="400"
                                trigger="click"
                            >
                                <div
                                    v-for="(item, index) in gridData"
                                    :key="index"
                                >
                                    <div
                                        @click="
                                            determine(
                                                sitem,
                                                item,
                                                sindex,
                                                index
                                            )
                                        "
                                    >
                                        {{ item.name }}
                                    </div>
                                </div>
                                <div slot="reference" @click="openChoose">
                                    {{ sitem.type }}
                                </div>
                            </el-popover> -->
                                <div :class="sitem.arrangementId == -1
                                    ? 'ffffff'
                                    : sitem.arrangementId == 0
                                        ? 'c0c0c0'
                                        : sitem.colourIndex < 0
                                            ? 'c0c0c0'
                                            : sitem.colourIndex == 0
                                                ? 'ED9121'
                                                : sitem.colourIndex == 1
                                                    ? 'c843900'
                                                    : sitem.colourIndex == 2
                                                        ? 'c817936'
                                                        : sitem.colourIndex == 3
                                                            ? 'cc7a252'
                                                            : sitem.colourIndex == 4
                                                                ? 'c494e8f'
                                                                : sitem.colourIndex == 5
                                                                    ? 'c121a2a'
                                                                    : 'ffffff'
                                    ">
                                    <!--                                :class="-->
                                    <!--                                    sitem.arrangementId == -1-->
                                    <!--                                        ? 'ffffff'-->
                                    <!--                                        : sitem.arrangementId == 0-->
                                    <!--                                        ? 'cf47920'-->
                                    <!--                                        : sitem.arrangementId == 1-->
                                    <!--                                        ? 'cca8687'-->
                                    <!--                                        : sitem.arrangementId == 2-->
                                    <!--                                        ? 'c843900'-->
                                    <!--                                        : sitem.arrangementId == 3-->
                                    <!--                                        ? 'c817936'-->
                                    <!--                                        : sitem.arrangementId == 4-->
                                    <!--                                        ? 'cc7a252'-->
                                    <!--                                        : sitem.arrangementId == 5-->
                                    <!--                                        ? 'c494e8f'-->
                                    <!--                                        : sitem.arrangementId == 6-->
                                    <!--                                        ? 'c121a2a'-->
                                    <!--                                        : 'ffffff'-->
                                    <!--                                "-->


                                    <el-dropdown trigger="click" :disabled="!sitem.canUpdate">
                                        <el-tooltip  :disabled="!sitem.arrangementName" effect="dark" :content="sitem.arrangementName"
                                            placement="top-end">
                                            <span class="over" :class="sitem.arrangementId == -1
                                                ? 'ffffff'
                                                : sitem.arrangementId == 0
                                                    ? 'cf47920'
                                                    : sitem.colourIndex < 0 ?
                                                        'ffffff'
                                                        : sitem.colourIndex == 0
                                                            ? 'cca8687'
                                                            : sitem.colourIndex == 1
                                                                ? 'c843900'
                                                                : sitem.colourIndex == 2
                                                                    ? 'c817936'
                                                                    : sitem.colourIndex == 3
                                                                        ? 'cc7a252'
                                                                        : sitem.colourIndex == 4
                                                                            ? 'c494e8f'
                                                                            : sitem.colourIndex == 5
                                                                                ? 'c121a2a'
                                                                                : 'ffffff'
                                                ">
                                                <!--                                            :class="-->
                                                <!--                                                sitem.arrangementId == -1-->
                                                <!--                                                    ? 'ffffff'-->
                                                <!--                                                    : sitem.arrangementId == 0-->
                                                <!--                                                    ? 'cf47920'-->
                                                <!--                                                    : sitem.arrangementId == 1-->
                                                <!--                                                    ? 'cca8687'-->
                                                <!--                                                    : sitem.arrangementId == 2-->
                                                <!--                                                    ? 'c843900'-->
                                                <!--                                                    : sitem.arrangementId == 3-->
                                                <!--                                                    ? 'c817936'-->
                                                <!--                                                    : sitem.arrangementId == 4-->
                                                <!--                                                    ? 'cc7a252'-->
                                                <!--                                                    : sitem.arrangementId == 5-->
                                                <!--                                                    ? 'c494e8f'-->
                                                <!--                                                    : sitem.arrangementId == 6-->
                                                <!--                                                    ? 'c121a2a'-->
                                                <!--                                                    : 'ffffff'-->
                                                <!--                                            "-->

                                                {{
                                                    sitem.arrangementId == -1
                                                    ? '-'
                                                    : sitem.arrangementId == 0
                                                        ? '休'
                                                        : sitem.arrangementName
                                                }}
                                            </span>
                                        </el-tooltip>

                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item
                                                v-for="(item, index) in scheduleWayIds == 2 ? gridDatatwo : gridData"
                                                :key="index">
                                                <div @click="
                                                    determine(
                                                        sitem,
                                                        item,
                                                        sindex,
                                                        index
                                                    )
                                                    ">
                                                    {{ item.name }}
                                                </div>
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-body">
                    <!-- <div class="table-title height40">
                    <div class="table-name height40">班次</div>
                    <div
                        class="table-head"
                        v-for="(item, index) in dateListHead"
                        :key="index"
                    >
                        <div>{{ item.day }}</div>
                        <div>{{ item.week }}</div>
                        <div>{{ item.day }}</div>
                    </div>
                </div> -->
                    <div class="title-center" v-if="dateListInfo.length">
                        <div class="table-line" v-for="(fitem, findex) in gridData" :key="findex">
                            <el-tooltip class="item" effect="dark" :content="fitem.name" placement="top-end">
                                <div class="table-name">{{ fitem.name}}</div>
                            </el-tooltip>

                            <div class="table-type" v-for="(sitem, sindex) in fitem.memberWorkVos" :key="sindex">
                                {{ sitem.isAll}}
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>
            <el-dialog title="自动排班" :visible.sync="openDrawer" :before-close="handleClose" append-to-body>
                <el-tabs v-model="siteList.activeName" @tab-click="handleClick">
                    <el-tab-pane label="按天排班" name="1"></el-tab-pane>
                    <el-tab-pane :disabled="scheduleWayIds == 2 ? true : false" label="周期排班" name="2"></el-tab-pane>
                </el-tabs>
                <el-form :label-position="labelPosition" label-width="100px" :model="siteList">
                    <div v-if="siteList.activeName == 1">
                        <el-form-item label="班次：">
                            <el-select class="selectW1" v-model="siteList.classTpye" placeholder="请选择班次" filterable multiple
                                collapse-tags>
                                <el-option v-for="item in gridData" :key="item.id" :label="item.name" :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                      <el-form-item v-if="scheduleWayIds == 2" label="人员选择：">
                        <el-transfer
                          :titles="['待选择人员', '已选择人员']"
                          filter-placeholder="请输入人员名称"
                          v-model="siteList.autoArrangeMember"
                          :data="personnelSorting"
                          target-order="push"
                          :props="{
                                key: 'userId',
                                label: 'userName'
                          }"
                        >
                        </el-transfer>
                      </el-form-item>

                        <el-form-item label="节假日自动排休：">
                            <el-switch v-model="siteList.isRest" active-value="1" inactive-value="0" active-color="#13ce66"
                                inactive-color="#ff4949">
                            </el-switch>
                        </el-form-item>
                    </div>
                    <div v-if="siteList.activeName == 2">
                        <el-form-item label="周期天数：" class="width120">
                            <div class="flexs">
                                以<el-input class="width120" v-model="siteList.input" @input="inputChange"
                                    placeholder="请输入天数" :maxlength="20"></el-input>
                                天为周期进行循环,最大周期天数为{{ maxDays }}天
                            </div>
                        </el-form-item>
                        <el-form-item label="设置班次：">
                            <div v-if="siteList.classList.length > 0">
                                <div v-for="(item, index) in siteList.classList" :key="index" class="bcItem">
                                    第{{ index + 1 }}天
                                    <el-select class="selectW1" v-model="item.id" placeholder="请选择班次">
                                        <el-option v-for="item in gridData" :key="item.id" :label="item.name"
                                            :value="item.id">
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div v-else>暂无可设置的班次</div>
                        </el-form-item>
                        <el-form-item label="节假日自动排休：">
                            <el-switch v-model="siteList.isRest" active-value="1" inactive-value="0" active-color="#13ce66"
                                inactive-color="#ff4949">
                            </el-switch>
                        </el-form-item>
                    </div>

                    <el-form-item>
                        <div class="up-btn">
                            <el-button type="primary" @click="submitList">保存</el-button>
                            <el-button @click="openDrawer = false">取消</el-button>
                        </div>
                    </el-form-item>
                </el-form>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
import {
    getShowData,
    bcfindList,
    autoSetSchedule,
    exportExcel,
    saveEntitys,
    importExcel,
    findListMan
} from '@/api/scheduling/scheduling'
import { getToken } from '@/utils/auth';
var dayjs = require('dayjs')
export default {
    name: '',
    // 获取父级的值
    props: {},
    // 数据
    data() {
        return {
           userdataList: "",
           value: [],

            url:'http://************:9007/schedule/schedule/import',//用于导入Excel表格
            headerObj: {
                Authorization: getToken()
            },
            name: '环境数据',
            tabPosition: '1',
            formInline: {
                classMember: ''
            },
            siteList: {
                activeName: '1',
                classTpye: '',
                isRest: '',
                input: '',
                autoArrangeMember: [],
                classList: []
            },
            // 遮罩层
            loading: false,
            tableData: [],
            pageSize: 10,
            pageNum: 1,
            total: 0,
            alarm: '',
            isAdd: 1,
            monthDate: dayjs(new Date()).format('YYYY-MM'),
            monthDateApi: dayjs(new Date()).format('YYYY-MM'),
            dataList: [
                {
                    name: '2021-12-01',
                    house: '01'
                },
                {
                    name: '2021-12-02',
                    house: '02'
                },
                {
                    name: '2021-12-03',
                    house: '03'
                }
            ],
            listData: [
                {
                    name: '1#站点',
                    '2021-12-01': 4266.7,
                    '2021-12-02': 3574.9,
                    '2021-12-03': 4313.3
                },
                {
                    name: '2#站点',
                    '2021-12-13': 4266.7,
                    '2021-12-14': 3574.9,
                    '2021-12-15': 4313.3
                },
                {
                    name: '3#站点',
                    '2021-12-01': 4266.7,
                    '2021-12-02': 3574.9,
                    '2021-12-03': 4313.3
                }
            ],
            manArr: [],
            dateListInfo: [],
            dateListHead: [],
            gridData: [
                // {
                //     id: -1,
                //     name: '清空'
                // },
                // {
                //     id: 0,
                //     name: '休'
                // }
            ],
            gridDatatwo: [],
            openDrawer: false,
            labelPosition: 'right',
            maxDays: '',
            isUpdata: 1,
            fileList: [],
            personnelSorting: [],//人员排序
            scheduleWayIds: '',
            nowDate:new Date()
        }
    },

    // 实例创建完成后被立即调用
    created() {
        this.scheduleWayIds = this.$route.query.scheduleWay
        console.log(this.scheduleWayIds, 'what is this')
        this.bcfindLists()
        // this.getShowDatas()
        this.findListMans()
        this.getDict()
        console.log(process.env,'urlurlurl')
    },

    // 挂载实例后调用
    mounted() { },

    // 监控
    watch: {},

    // 过滤器
    filters: {},

    // 定义模板
    components: {},

    // 计算属性
    computed: {
        dictList() {
            return this.$store.state.dict
        },
        setIconStyle() {
            return function (index) {
                console.log(index)
                return index === 0 ? 'color: #333' : 'color: #000'
            }
        }
    },

    // 混入到 Vue 实例中
    methods: {
      filterMethod(query, item) {
        // return item.pinyin.indexOf(query) > -1;
      },
        // 保存提交接口
        submitTable() {
            let params = []
            this.dateListInfo.forEach((zitem, zindex) => {
                zitem.memberWorkVos.forEach((sitem, sindex) => {
                    params.push(sitem)
                })
            })
            this.loading = true
            saveEntitys(params).then((res) => {
                this.$message({
                    message: '保存成功',
                    type: 'success'
                })
                console.log('更新之后调用接口')
                setTimeout(() => {
                    this.getShowDatas()
                })
                this.loading = false
            })
        },
        // 导出接口
        deriveTable() {
            this.loading = true
            let ids = this.$route.query.id
            let params = {
                monthStr: this.monthDateApi,
                scheduleId: ids
            }
            exportExcel(params).then((res) => {
                const link = document.createElement('a')
                let blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                link.style.display = 'none'
                link.href = URL.createObjectURL(blob)
                link.setAttribute('download', '排班表')
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
                this.loading = false
            })
        },
        // 导入接口
        changeListState(file, fileList) {
            console.log(file, fileList)
            this.getShowDatas()
        },
        // 清空数据
        cleanList() {
            this.dateListInfo.forEach((zitem, zindex) => {
                zitem.memberWorkVos.forEach((sitem, sindex) => {
                    sitem.arrangementId = -1
                })
            })
            this.initializationGet()
        },
        inputChange(value) {
            if (+value <= this.maxDays) {
                this.siteList.classList = []
                // console.log('value')
                for (let i = 0; i < +value; i++) {
                    this.siteList.classList.push({ id: '' })
                }
                // console.log(this.siteList.classList)
            } else {
                this.$message({
                    message: '输入天数不能超过每月最大天数！'
                })
            }
        },
        handleClick(tab, event) {
            // console.log(tab, event)
        },
        // 提交接口
        submitList() {
            // siteList: {
            //     activeName: '1',
            //     classTpye: '',
            //     isRest: '',
            //     input: '',
            //     classList: []
            // },
            let ids = this.$route.query.id
            let classLists = this.siteList.classList.map((item) => {
                return item.id
            })
            let params = {
                arrangementIdList:
                    this.siteList.activeName == 1
                        ? this.siteList.classTpye
                        : classLists,
                autoArrangeMember:this.siteList.autoArrangeMember? this.siteList.autoArrangeMember.toString():"",
                monthStr: this.monthDateApi,
                scheduleId: ids,
                shunHoliday: this.siteList.isRest,
                type: this.siteList.activeName
            }
            if (!params.arrangementIdList.length) {

              return this.$message.error("请选择班次");
            }
            for (let i = 0; i < params.arrangementIdList.length; i++) {
              let arrangementId = params.arrangementIdList[i];
              if (!arrangementId && arrangementId !== 0) {
                return this.$message.error("选择班次不能为空");
              }
            }
            this.isUpdata = 2
            this.autoSetSchedules(params)
            // this.getShowDatas()
        },
        autoSetSchedules(params) {
            this.loading = true
            // console.log(params, 'params')
            autoSetSchedule(params).then((res) => {

                this.dateListInfo = res.data
                console.log(this.dateListInfo,'自动排班后')
                this.$message({
                    message: '保存成功',
                    type: 'success'
                })
                //自动排班后重新获取排班数量
                this.initializationGet()
                // if (this.isUpdata == 1) {
                // this.bcfindLists()
                // }
                setTimeout(() => {
                    this.loading = false
                    this.openDrawer = false
                }, 1000)
            })
        },
        chooseDate(value) {
            this.monthDateApi = this.filterTimess(value)
            this.getShowDatas()
        },
        //打开排班选择器
        openChoose() { },
        // 确定选择
        determine(sitem, item, sindex, index) {
            // debugger
            console.log(sitem, item, sindex, index)
            // console.log(sitem,"============")
            sitem.arrangementName = item.name
            sitem.arrangementId = item.id
            if (sitem.type !== 0 && sitem.type !== -1) {
                sitem.colourIndex = index
            }
            let arr = []
            this.dateListInfo.forEach((xitem, xindex) => {
                console.log(xitem.memberWorkVos[sindex], "-=-=-=-=-==-")
                arr.push(xitem.memberWorkVos[sindex].arrangementId)
                // arr.push(xitem.memberWorkVos[sindex].colourIndex)
            })
            this.gridData[index].memberWorkVos[sindex].isAll = 0
            this.gridData.forEach((zitem, zindex) => {
                zitem.memberWorkVos[sindex].isAll =
                    this.isRepeat(arr, zitem.id) || 0
            })
        },
        // 初始化获取班次的数量。
        initializationGet() {
            // console.log(this.dateListInfo, this.gridData)
            let newarr = []
            this.dateListHead.forEach((item, index) => {
                let allArr = []
                this.dateListInfo.forEach((zitem, zindex) => {
                    zitem.memberWorkVos[index].isAll = 0
                    allArr.push(zitem.memberWorkVos[index].arrangementId)
                })

                newarr.push(allArr)
            })
            // console.log(this.dateListHead, '汇总的数据列表')
            this.gridData.forEach((qitem, qindex) => {
                qitem.memberWorkVos.forEach((aitem, aindex) => {
                    // console.log(
                    //     qitem.id,
                    //     '出现了',
                    //     this.isRepeat(newarr[aindex], qitem.id)
                    // )
                    aitem.isAll = this.isRepeat(newarr[aindex], qitem.id) || 0
                })
            })

            console.log(newarr, 'push后的数组')
        },
        // console.log(item.memberWorkVos, index, 'item.memberWorkVos')
        //         allArr.push(item.memberWorkVos[index].arrangementId)
        //         console.log(allArr, '汇总的数据列表')
        /** 设备管理删除 */
        setdeleteByIdss(val) {
            this.loading = true
            // JSON.stringify(this.targetList)
            let params = {
                ids: val
            }
            // console.log(params, 'params')
            deleteByIds(params).then((res) => {
                // console.log(res)
                this.$message({
                    message: '删除成功',
                    type: 'success'
                })
                this.getPageLists()
                this.loading = false
            })
        },
        deleteList(index, row) {
            // console.log(index, row)
            this.setdeleteByIdss(row.id)
        },
        openDrawerBtn() {
            this.openDrawer = true
        },
        onSubmit() {
            console.log('submit!')
        },
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then((_) => {
                    done()
                })
                .catch((_) => { })
        },
        /** 排班管理-班次管理分页查询列表 */
        getShowDatas() {
            this.loading = true
            let ids = this.$route.query.id
            let params = {
                monthStr: this.monthDateApi,
                scheduleId: ids,
                memberId: this.formInline.classMember
            }
            getShowData(params).then((res) => {
                this.dateListInfo = res.data.infoList
                this.dateListHead = res.data.header
                this.maxDays = this.dateListHead.length
                this.dateListHead.forEach((item) => {
                    item.isAll = 0
                })
                this.gridData.forEach((item) => {
                    item.memberWorkVos = JSON.parse(
                        JSON.stringify(this.dateListHead)
                    )
                })
              console.log(this.dateListInfo,'修改完信息')
                // console.log(this.dateListHead, 'dateListdateList')
                this.initializationGet()
                this.loading = false
            })
        },
        rgb() {
            //rgb颜色随机
            const r = Math.floor(Math.random() * 256)
            const g = Math.floor(Math.random() * 256)
            const b = Math.floor(Math.random() * 256)
            return `rgb(${r},${g},${b})`
        },
        /** 排班管理-班次 */
        bcfindLists() {
            this.loading = true
            let ids = this.$route.query.id
            let params = {
                scheduleId: ids
            }
            //统一是1 轮换是2
            bcfindList(params).then((res) => {
                let arrone = [{
                    id: -1,
                    name: '清空'
                },
                {
                    id: 0,
                    name: '休'
                }]
                if (this.scheduleWayIds == 1) {
                    this.gridData.push(...res.data, ...arrone)
                    // this.gridData.push(...res.data)
                } else {
                    this.gridData = res.data
                    this.gridDatatwo.push(...this.gridData, ...arrone)
                }
                this.gridData.forEach((item) => {
                    item.rbg = this.rgb()
                })

                console.log(this.gridData, "排版此时")
                this.getShowDatas()
                this.loading = false
            })
        },
        getDict() {
            this.$store.dispatch('dict/setDict', {})
        },
        findList() {
            this.getShowDatas()
        },
        resetList() {
            this.formInline.classMember = ''
            this.getShowDatas()
        },
        findListMans() {
            let ids = this.$route.query.serviceGroupId
            let params = {
                serviceGroupId: ids
            }
            findListMan(params).then((res) => {
                this.manArr = res.data
                this.personnelSorting=res.data
                // res.data.forEach((item,index)=>{
                //   this.personnelSorting.push({
                //     label:item.userName,
                //     key:index,
                //     userId:res.data[index].userId
                //   })
                // })
              console.log(this.personnelSorting,"this.personnelSorting")
            })
        },
        isRepeat(result, valueNum) {
            //判断数组中重复元素的个数
            var arr = []
            result.sort()
            for (var i = 0; i < result.length;) {
                var count = 0
                for (var j = i; j < result.length; j++) {
                    if (result[i] === result[j]) {
                        count++
                    }
                }
                arr.push({
                    value: result[i],
                    count: count
                })
                i += count
            }
            var countNum
            for (var k = 0; k < arr.length; k++) {
                if (arr[k].value == valueNum) {
                    countNum = arr[k].count
                }
            }
            console.log(countNum, "countNum")
            return countNum
        }
    }
}
</script>
<style lang='scss' scoped>
@import './index.scss';

.body {
    font-size: 12px;
}

.table-body {
    font-size: 12px !important;
}

.demo-form-inline {
    background: #ffffff;
    padding: 24px;
}

.width50 {
    width: 50px !important;
}

.width90 {
    width: 90px !important;
}

.width100 {
    width: 100px !important;
}

.width120 {
    width: 120px !important;
}

.width200 {
    width: 200px !important;
}

.width300 {
    width: 300px !important;
}

.flexs {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.selectW {
    width: 100%;
}

.selectW1 {
    width: 300px;
}

.up-btn {
    text-align: end;
    width: 300px;
}

.height501 {
    height: 50%;
}

.height50 {
    line-height: 35px;
    height: 50%;
}

.height40 {
    height: 40px !important;
    line-height: 40px !important;
}

.cef5b9c {
    background-color: #ef5b9c;
    color: #ffffff;
}

.cf47920 {
    color: #FB5151;
}

.cca8687 {
    /*background-color: #ca8687;*/
    color: #1A8CFF;
}

.c843900 {
  color: #843900;
}

.c0c0c0 {
    background-color: #ffffff;
    /*color: #ffffff;*/
    color: red;
}

.ED9121 {
    //   background-color: #ED9121;
    color: #1A8CFF;
}

::v-deep .table-body {
    border: solid 1px #fbfbfb;
}

.c817936 {
  color: #817936;
}

.cc7a252 {
  color: #c7a252;
}

.c494e8f {
    /*background-color: #494e8f;*/
    color: #494e8f;
}

.c121a2a {
    /*background-color: #121a2a;*/
    color: #121a2a;
}

.ffffff {
    /*background-color: #ffffff;*/
    color: #333333;
}

.c367459 {
    background-color: #367459;
}

.over {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    // text-align: center;
    // color: #1A8CFF;
}

.centers {
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.centers-left {
    display: flex;
    align-items: center;
}
.upload-demo{
    margin-left: 10px;
}
::v-deep .el-dropdown {
    width: -webkit-fill-available;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background: none !important;
}
::v-deep .el-form-item__content{
    margin-left: 135px !important;
}
::v-deep .el-dialog {
    /*width: 40% !important;*/
  width: 920px;
}
::v-deep .el-transfer {
    /*width: 40% !important;*/
  width: 600px;
}

::v-deep .el-form-item__label {
    width: 130px !important;
}

::v-deep .el-dialog__body {
    padding: 20px 20px 20px 40px;
}

::v-deep .el-form-item {
    margin-bottom: 0px;
    width: 30%;
}

::v-deep .el-form-item__content {
    width: 100%;
}

::v-deep .el-form-item {
    width: 600px;
    margin-bottom: 20px;
}

::v-deep .el-form-item__content {
    width: 500px;
}

::v-deep .el-row {
    display: flex;
    justify-content: flex-end;
}

::v-deep .el-table__row {
    height: 50px;
}

.center {
    display: flex;

    ::v-deep .el-input {
        width: 10vw;
    }

    ::v-deep .el-date-editor {
        width: 10vw;
    }

    .scarchIpt {
        -webkit-box-flex: 6;
        flex: 6 1 0%;
        display: flex;
        align-items: center;
        span{
            font-size: 16px;
        }
    }

    .tabButton {
        -webkit-box-flex: 1;
        flex: 1 1 0%;
        display: flex;
        align-items: flex-start;
        justify-content: flex-end;

    }

    .el-form-item {
        width: 17vw;
    }

    ::v-deep .el-form-item__label {
        width: 85px;
    }
}

::v-deep .el-form {
    padding-left: 0;
}



::v-deep .el-card__header {
    height: 56px;
    font-size: 18px;
    font-weight: 400;
    padding: 16px;
}

.tab_card {
    ::v-deep .el-card__body {
        padding: 16px 0px;
    }
    ::v-deep .el-card__header{
        height: 95px;
    }
    .tab_card_header {
        display: flex;
        justify-content: space-between;

        >span {
            display: flex;
            align-items: center;
        }
    }
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    border: 1px solid #188cff;
    border-radius: 2px;
    background-color: #fff;
    color: #188cff;
}

.el-card {
    margin-bottom: 20px;
}
.center_left{
    :nth-child(1){
        margin-right: 10px;
    }
}
.bcItem{
    margin-bottom: 5px;
}
</style>
