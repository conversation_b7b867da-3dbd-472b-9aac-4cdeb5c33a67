.body {
  width: 100%;
  // height: calc(100vh - 86px);
  height: 100%;
  margin: 0;
  font-size: 12px;
  padding: 16px;
}
.add {
  background-color: #ffffff;
  padding: 24px;
}
.center-btn {
  background-color: #ffffff;
  padding: 16px 0px;
  display: flex;
  justify-content: space-between;
}
.caozuo {
  color: #3399ff;
}
.caozuo {
  color: #3399ff;
  margin-right: 20px;
}
.delete_btn {
  color: #ff3c32;
}
.alarmUp {
  margin-bottom: 24px;
  .alarmTips {
    font-size: 16px;
    margin-right: 24px;
  }
}
.alarmDown {
  // display: flex;
  // align-items: center;
  .alarmTips {
    font-size: 16px;
    margin-right: 24px;
  }
  .alarmRight {
    margin-left: 92px;
    margin-top: -20px;
    .alarminp {
      display: flex;
      align-items: center;
      // margin-left: 80px;
      .alarminp-left {
        width: 72px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
      .alarminp-right {
        width: 72px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
      .alarminp-center {
        width: 72px;
        margin-right: 8px;
        margin-bottom: 8px;
        height: 36px;
        border-radius: 4px;
        line-height: 36px;
        font-size: 18px;
        text-align: center;
      }
      .blue {
        background: #e6f7ff;
        border: 1px solid #91d5ff;
        color: #1890ff;
      }
      .orange {
        background: #fff7e6;
        border: 1px solid #ffd591;
        color: #d46b08;
      }
      .red {
        background: #fff2f0;
        border: 1px solid #ffccc7;
        color: #ff4d4f;
      }
    }
  }
}

.addtitle {
  color: #333333;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}
.addbody {
  padding: 40px 160px;
}
.checkbox {
  padding: 0 0 0 24px;
}

.table-name {
  width: inherit;
  height: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.table-body {
  // padding-bottom: 10px;
  margin-bottom: 10px;
  background-color: #ffffff;
  border: solid 1px #ebebeb;
  .table-title {
    display: flex;
    align-items: center;
    width: 100%;
    height: 88px;
    
    .table-name {
      line-height: 88px;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #007BAF;
      background-color: rgba(25, 159, 255, 0.15);
    }
    .table-head {
      // line-height: 40px;
      text-align: center;
      height: inherit;
      width: inherit;
      .bor {
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        background-color: rgba(25, 159, 255, 0.15);
        color: #007BAF;
      }
    }
  }
  .title-center {
    .table-line {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #e1e1e1;
      .table-name {
        text-align: center;
        line-height: 40px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-bottom: 1px solid #e1e1e1;
      }
      .table-type {
        line-height: 40px;
        text-align: center;
        height: inherit;
        overflow: hidden;
        text-overflow: ellipsis;
        width: inherit;
        border-bottom: 1px solid #e1e1e1;
        // border: 1px solid #dfdede;
      }
    }
  }
}
