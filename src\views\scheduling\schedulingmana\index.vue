<template>
  <div class="body">
    <el-card class="top_box">
      <el-card>
        <div slot="header" class="clearfix">
          <span>数据筛选</span>
        </div>
        <div class="center">
          <div class="scarchIpt">
            <el-form :inline="true" :model="formInline">
              <el-form-item label="排班名称：">
                <el-input v-model="formInline.className" placeholder="请输入排班名称" :maxlength="20"></el-input>
              </el-form-item>
              <el-form-item label="班次名称：">
                <el-select v-model="formInline.classType" placeholder="请选择班次类型">
                  <el-option v-for="item in bctypeArr" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="排班班组：">
                <el-select v-model="formInline.GroupName" placeholder="请选择排班班组">
                  <el-option v-for="item in serviceArr" :key="item.id" :label="item.name"
                             :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="tabButton">
            <el-button icon="el-icon-search" class="searchBtn" type="primary" @click="findList" style="font-size:13px" size="mini">搜索</el-button>
            <el-button icon="el-icon-refresh" class="searchBtn" @click="resetList" style="font-size:13px" size="mini">重置</el-button>
          </div>
        </div>
      </el-card>
      <el-card class="tab_card">
        <div slot="header">
          <div class="tab_card_header">
                        <span>
                            排班管理展示列表
                        </span>
            <div class="btns">
              <el-button icon="el-icon-plus" type="primary" @click="openDrawerBtn" class="searchBtn" size="mini"
                         
              >新建排班
              </el-button>
            </div>
          </div>
        </div>
        <el-table v-loading="loading" :data="tableData" style="width: 100%" :highlight-current-row="true">
          <el-table-column type="selection" width="55" align="center" header-align="center" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="name" label="排班名称" width="200" align="left" header-align="left"
                           show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column prop="memberTotal" label="排班人数" align="center" header-align="center"
                           show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column prop="arrangementsName" label="排班名次" align="left" header-align="left"
                           show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column prop="serviceGroupName" label="排班班组" align="left" header-align="left"
                           show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column prop="scheduleWayName" label="排班方式" align="center" header-align="center"
                           show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column label="操作" align="center" header-align="center">
            <template slot-scope="scope">
              <div>
                                <span class="caozuo pointer" @click="editData(scope.$index, scope.row)">排班</span>
                <span class="delete_btn pointer" @click="deleteList(scope.$index, scope.row)"><i
                  class="el-icon-delete"
                ></i>删除</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :limit.sync="pageSize" :page.sync="pageNum" :total="total"
                    @pagination="pbglPageLists"
        />
      </el-card>
    </el-card>
    <el-dialog title="新建排班" :visible.sync="openDrawer" :before-close="handleClose" append-to-body>
      <el-form :label-position="labelPosition" label-width="100px" :model="siteList" :rules="rules" ref="siteList">
        <el-form-item label="排班名称" prop="addClassName">
          <el-input v-model="siteList.addClassName" placeholder="请输入名称" clearable :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="班次名称" prop="addClassType">
          <el-select v-model="siteList.addClassType" placeholder="请选择班次" filterable multiple
                     collapse-tags
          >
            <el-option v-for="item in bctypeArr" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排班班组" prop="addClassSer">
          <el-select v-model="siteList.addClassSer" placeholder="请选择服务组">
            <el-option v-for="item in serviceArr" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排班方式">
          <el-radio-group v-model="siteList.addpbType">
            <el-radio :label="1">统一排班</el-radio>
            <el-radio :label="2">轮换排班(考勤组不少于两人)</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="up-btn">
        <div class="up-btn">
          <el-button type="primary" @click="submitList('siteList')">确定</el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  pbglPageList,
  findList,
  bcfindList,
  addpb,
  pbdeleteByIds,
  getServiceGroupSelectList
} from '@/api/scheduling/scheduling'

export default {
  name: '',
  // 获取父级的值
  props: {},
  // 数据
  data() {
    return {
      rules: {
        addClassName: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ],
        addClassType: [
          {
            required: true,
            message: '请选择班次',
            trigger: 'blur'
          }
        ],
        addClassSer: [
          {
            required: true,
            message: '请选择服务组',
            trigger: 'change'
          }
        ]
      },
      labelPosition: 'right',
      name: '环境数据',
      formInline: {
        className: '',
        classType: '',
        GroupName: ''
      },
      // 遮罩层
      loading: false,
      tableData: [],
      pageSize: 10,
      pageNum: 1,
      isSearch: false,
      total: 0,
      targetList: [
        {
          name: '轻度',
          degree: '102001',
          startNum: '',
          startType: '1',
          endNum: '',
          endType: '1'
        },
        {
          name: '中度',
          degree: '102002',
          startNum: '',
          startType: '1',
          endNum: '',
          endType: '1'
        },
        {
          name: '重度',
          degree: '102003',
          startNum: '',
          startType: '1',
          endNum: '',
          endType: '1'
        }
      ],
      serviceArr: [],
      bctypeArr: [],
      openDrawer: false,
      siteList: {
        addClassName: '',
        addClassType: '',
        addClassSer: '',
        addpbType: 1
      }
    }
  },

  // 实例创建完成后被立即调用
  created() {
    this.findLists()
    this.bcfindLists()
    this.pbglPageLists()
    this.getDict()
    this.getTeamsGroups()
  },

  // 挂载实例后调用
  mounted() {
  },

  // 监控
  watch: {},

  // 过滤器
  filters: {},

  // 定义模板
  components: {},

  // 计算属性
  computed: {
    dictList() {
      return this.$store.state.dict
    }
  },

  // 混入到 Vue 实例中
  methods: {
    // 新增接口
    addpbs() {
      this.loading = true
      let params = {
        arrangementIds: this.siteList.addClassType.join(','),
        serviceGroupId: this.siteList.addClassSer,
        name: this.siteList.addClassName,
        scheduleWay: this.siteList.addpbType
      }
      console.log(params, 'params')
      addpb(params).then((res) => {
        this.$message({
          message: '新增成功',
          type: 'success'
        })
        setTimeout(() => {
          this.loading = false
          this.openDrawer = false
          this.pbglPageLists()
        }, 1000)
      })
    },
    submitList(siteList) {
      console.log(siteList)
      this.$refs[siteList].validate((valid) => {
        console.log(valid)
        if (valid) {
          this.addpbs()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    openDrawerBtn() {
      this.isAdd = 1
      this.openDrawer = true
      this.siteList.addClassName = ''
      this.siteList.addClassType = ''
      this.siteList.addClassSer = ''
      this.siteList.addpbType = 1
    },
    goDetail() {
      this.$router.push({
        // path: '/vue-equipment/scheduling/shiftmanaDetail',
        name: 'schedulingmanaDetail',
        query: {}
      })
    },
    editData(index, row) {
      console.log(row)
      this.$router.push({
        // path: '/vue-equipment/scheduling/shiftmanaDetail',
        name: 'schedulingmanaDetail',
        query: row
      })
    },

    onSubmit() {
      console.log('submit!')
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          done()
        })
        .catch((_) => {
        })
    },
    // 获取排班名称
    findLists() {
      let params = {}
      getServiceGroupSelectList(params).then((res) => {
        this.serviceArr = res.data
      })
    },
    //获取班次名称
    bcfindLists() {
      let params = {}
      bcfindList(params).then((res) => {
        this.bctypeArr = res.data
      })
    },
    // 获取排班班组   (暂时没有)
    getTeamsGroups() {

    },

    /** 告警中心分页查询列表 */
    pbglPageLists() {
      this.loading = true
      let params = {}
      if (this.isSearch) {
        params = {
          serviceGroupId: this.formInline.GroupName,
          arrangementIdQuery: this.formInline.classType,
          name: this.formInline.className,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
      } else {
        params = {
          // serviceGroupId: this.formInline.GroupName,
          // arrangementIdQuery: this.formInline.classType,
          // name: this.formInline.className,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
      }
      pbglPageList(params).then((res) => {
        this.tableData = res.data.list
        this.total = res.data.total
        console.log(this.tableData, 'this.res')
        // this.userList = response.rows;
        // this.total = response.total;
        this.loading = false
      })
    },
    // 删除
    deleteList(index, row) {
      console.log(index, row)
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pbdeleteByIdss(row.id)
      })
    },
    /** 设备管理删除 */
    pbdeleteByIdss(val) {
      this.loading = true
      // JSON.stringify(this.targetList)
      let params = {
        ids: val
      }
      console.log(params, 'params')
      if (this.tableData.length == 1 && this.pageNum !== 1) {
        this.pageNum = this.pageNum - 1
      }
      pbdeleteByIds(params).then((res) => {
        console.log(res)
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        this.pbglPageLists()
        this.loading = false
      })
    },
    getDict() {
      this.$store.dispatch('dict/setDict', {})
    },
    findList() {
      this.pageNum = 1
      this.isSearch = true
      this.pbglPageLists()
    },
    resetList() {
      this.tableData = []
      this.pageNum = 1
      this.pageSize = 10
      this.formInline = {}
      this.isSearch = false
      this.pbglPageLists()
    },
    closeDialog() {
      this.$refs.siteList.resetFields()
      this.openDrawer = false
    }
  }
}
</script>
<style lang="scss" scoped>
@import './index.scss';

.demo-form-inline {
  background: #ffffff;
  padding: 24px;
}

.selectW {
  width: 100%;
}

.selectW1 {
  width: 300px;
}

.selectW100 {
  width: 100%;
}

.up-btn {
  display: flex;
  justify-content: flex-end;
}

::v-deep .el-dialog__header {
  border-bottom: 1px solid #f1f1f1;
}

::v-deep .el-dialog__body {
  // padding: 32px 30px 5px 30px;

  .el-form-item {
    width: 100% !important;
  }

}

::v-deep .el-dialog__footer {
  border-top: 1px solid #f1f1f1;
}

::v-deep .el-form-item {
  margin-bottom: 0px;
  width: 30%;
}

.demo-form-inline {
  padding: 16px;
}

.table_box {
  padding: 16px;
  background: #ffffff;
}

::v-deep .el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}


::v-deep .el-form-item {
  width: 390px;
  margin-bottom: 20px;
}


::v-deep .el-row {
  display: flex;
  justify-content: flex-end;
}

::v-deep .el-table__row {
  height: 50px;
}

::v-deep .el-card__header {
  padding: 15px 24px;
  font-size: 18px;
}

::v-deep .table_box {
  padding: 16px;
  background-color: #ffffff;
}

::v-deep .pagination-container .el-pagination {
  right: 16px;
}

// ::v-deep .el-form-item {
// 	margin-bottom: 0px;
// 	width: 30%;
// }


::v-deep .el-row {
  // display: flex;
  // justify-content: flex-end;
}

::v-deep .el-table__row {
  height: 50px;
}

::v-deep .el-card__body {
  padding: 16px;
}

.center {
  display: flex;

  .scarchIpt {
    -webkit-box-flex: 5;
    flex: 5 1 0%;
  }

  .tabButton {
    -webkit-box-flex: 1;
    flex: 1 1 0%;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
  }

  ::v-deep .demo-form-inline {
    padding: 16px;
  }
}

::v-deep .el-form-item {
  margin-bottom: 0px;
  width: 390px;
  margin-bottom: 20px;
}


::v-deep .el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.center {
  display: flex;

  ::v-deep .el-input {
    width: 10vw;
  }

  ::v-deep .el-date-editor {
    width: 10vw;
  }

  .scarchIpt {
    -webkit-box-flex: 6;
    flex: 6 1 0%;
  }

  .tabButton {
    -webkit-box-flex: 1;
    flex: 1 1 0%;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;

  }

  .el-form-item {
    white-space: nowrap;
    width: 17vw;
  }

  ::v-deep .el-form-item__label {
    width: 85px;
    font-weight: 400;
  }
}

::v-deep .el-form {
  padding-left: 0;
}


::v-deep .el-card__header {
  height: 56px;
  font-size: 16px;
  font-weight: 400;
  padding: 16px;
}

.tab_card {
  ::v-deep .el-card__body {
    padding: 16px 0px;
  }

  .tab_card_header {
    display: flex;
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
    }
  }
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  border: 1px solid #188cff;
  border-radius: 2px;
  background-color: #fff;
  color: #188cff;
}

.el-card {
  margin-bottom: 20px;
}

::v-deep .el-dialog {
  width: 560px;


  .el-input {
    width: 245px;
  }
}

::v-deep .el-table__cell {
  > .cell {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

::v-deep .el-scrollbar {
  width: 10vw !important;
}
</style>
