<template>
  <div class="body">
    <div class="add">
      <p class="addtitle">基本设置</p>
      <div class="addbody">
        <el-form ref="form" :model="addShift" label-width="100px">
          <el-form-item label="班次名称：">
            <el-input class="width455" v-model="addShift.name" :maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="班次类型：">
            <el-radio-group v-model="addShift.type" @input="changeRadio">
              <el-radio :label="1">固定班次</el-radio>
              <el-radio :label="2">弹性班次</el-radio>
              <el-radio :label="3">签到班次</el-radio>
              <el-radio :label="0">自定义
                <el-input style="width: 167px; height: 32px; margin-left: 5px" v-model="addShift.typeName"
                          placeholder="请输入班次类型名称"
                          :maxlength="20"
                ></el-input>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="addShift.type == 1 || addShift.type == 0">
            <el-form-item label="上下班时间：">
              <div class="flexs">
                <el-button type="primary" @click="addList()">新增班段</el-button>
              </div>
              <div class="flexs">
                工作时间总计：
                <el-select class="width90" v-model="addShift.hour" placeholder="请选择">
                  <el-option v-for="item in houroptions" :key="item.value" :label="item.label"
                             :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
              <div>
                <el-table :data="addShift.tableData" border style="width: 100%; margin-top: 20px">
                  <el-table-column label="班段" width="140" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div>
                        <el-input class="width120" v-model="scope.row.name" :maxlength="20"></el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="工作时间" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div class="oneceil">
                        <div style="border-bottom: 1px solid #f2f2f2">
                          上班：
                          <span v-if="addShift.tableData[scope.$index].goToFollowingDay">次日</span>
                          <span v-else>当天</span>
                          <el-time-picker v-model="addShift.tableData[scope.$index].startWorkTime
                                                        " value-format="HH:mm" format="HH:mm" @change="(val) => { upClassTime(val, scope.$index, scope.row, 'sb'); }
        " class="width120" placeholder="上班时间"
                          >
                          </el-time-picker>
                        </div>
                        <div>
                          下班：
                          <span v-if="addShift.tableData[scope.$index].afterFollowingDay">
                            次日
                          </span>
                          <span v-else>当天</span>
                          <el-time-picker v-model="addShift.tableData[scope.$index].endWorkTime
                                                        " value-format="HH:mm" format="HH:mm" @change="(val) => { upClassTime(val, scope.$index, scope.row, 'xb'); }
        " class="width120" placeholder="下班时间"
                          >
                          </el-time-picker>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="最早打卡时间" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div class="oneceil padLeftNone">
                        <div>
                          提前
                          <el-input type="number" min="0" v-model="addShift.tableData[scope.$index].goToEarlyHour"
                                    style="width: 50px;margin-right: 3px;margin-left: 5px" placeholder="时"
                                    @input="computeTime(scope.row,scope.$index,'goToEarlyMinutes')"
                                    :maxlength="20"
                          ></el-input>
                          小时
                          <el-input type="number" min="0" v-model="addShift.tableData[scope.$index].goToEarlyMinutes"
                                    style="width: 50px;margin-right: 3px;margin-left: 3px" placeholder="分"
                                    @input="computeTime(scope.row,scope.$index,'goToEarlyMinutes')"
                                    :maxlength="20"
                          ></el-input>
                          分钟打卡
                        </div>
                        <div>
                          提前
                          <el-input type="number" min="0" v-model="addShift.tableData[scope.$index].afterEarlyHours"
                                    style="width: 50px;margin-right: 3px;margin-left: 5px" placeholder="时"
                                    @input="computeTime(scope.row,scope.$index,'afterEarlyMinutes')"
                                    :maxlength="20"
                          ></el-input>
                          小时
                          <el-input type="number" min="0" v-model="addShift.tableData[scope.$index].afterEarlyMinutes"
                                    style="width: 50px;margin-right: 3px;margin-left: 3px" placeholder="分"
                                    @input="computeTime(scope.row,scope.$index,'afterEarlyMinutes')"
                                    :maxlength="20"
                          ></el-input>
                          分钟打卡
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="最晚打卡时间" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div class="oneceil padLeftNone">
                        <div>
                          延迟
                          <el-input type="number" min="0" v-model="addShift.tableData[scope.$index].goToLateHour"
                                    style="width: 50px;margin-right: 3px;margin-left: 5px" placeholder="时"
                                    @input="computeTime(scope.row,scope.$index,'goToLateMinutes')"
                                    :maxlength="20"
                          ></el-input>
                          小时
                          <el-input type="number" min="0" v-model="addShift.tableData[scope.$index].goToLateMinutes"
                                    style="width: 50px;margin-right: 3px;margin-left: 3px" placeholder="分"
                                    @input="computeTime(scope.row,scope.$index,'goToLateMinutes')"
                                    :maxlength="20"
                          ></el-input>
                          分钟打卡
                          <!--                          <el-time-picker v-model="addShift.tableData[scope.$index]-->
                          <!--                                                        .startLatestWorkTime-->
                          <!--                                                        " value-format="HH:mm" format="HH:mm" @change="(val) => { upClassTime(val, scope.$index, scope.row); }-->
                          <!--        " class="width120" placeholder="下班时间"-->
                          <!--                          >-->
                          <!--                          </el-time-picker>-->
                        </div>
                        <div>
                          延迟
                          <el-input type="number" min="0" v-model="addShift.tableData[scope.$index].afterLateHours"
                                    style="width: 50px;margin-right: 3px;margin-left: 5px" placeholder="时"
                                    @input="computeTime(scope.row,scope.$index,'afterLateMinutes')"
                                    :maxlength="20"
                          ></el-input>
                          小时
                          <el-input type="number" min="0" v-model="addShift.tableData[scope.$index].afterLateMinutes"
                                    style="width: 50px;margin-right: 3px;margin-left: 3px" placeholder="分"
                                    @input="computeTime(scope.row,scope.$index,'afterLateMinutes')"
                                    :maxlength="20"
                          ></el-input>
                          分钟打卡
                          <!--                          最晚打卡：-->
                          <!--                          <el-select class="width90" v-model="addShift.tableData[scope.$index].endLatestDayType-->
                          <!--                                                        " placeholder="请选择"-->
                          <!--                          >-->
                          <!--                            <el-option v-for="item in options" :key="item.value"-->
                          <!--                                       :label="item.label" :value="item.value"-->
                          <!--                            >-->
                          <!--                            </el-option>-->
                          <!--                          </el-select>-->
                          <!--                          <el-time-picker v-model="addShift.tableData[scope.$index].endLatestWorkTime-->
                          <!--                                                        " value-format="HH:mm" format="HH:mm" @change="(val) => { upClassTime(val, scope.$index, scope.row); }-->
                          <!--        " class="width120" placeholder="下班时间"-->
                          <!--                          >-->
                          <!--                          </el-time-picker>-->
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="休息时间" width="210" align="center">
                    <template slot-scope="scope">
                      <div class="oneceil">
                        <div>
                          休息开始：
                          <el-time-picker v-model="addShift.tableData[scope.$index].restStartTime
                                                        " value-format="HH:mm" format="HH:mm" @change="(val) => { upClassTime(val, scope.$index, scope.row); }
        " class="width120" placeholder="休息开始"
                          >
                          </el-time-picker>
                        </div>
                        <div>
                          休息结束：
                          <el-time-picker v-model="addShift.tableData[scope.$index].restEndTime
                                                        " value-format="HH:mm" format="HH:mm" @change="(val) => { upClassTime(val, scope.$index, scope.row); }
        " class="width120" placeholder="休息结束"
                          >
                          </el-time-picker>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div @click="deleteList(scope.$index, scope.row)">
                        <span>删除</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-form-item>
            <el-form-item label="弹性打卡：">
              <div>
                <el-checkbox v-model="addShift.checkbox1" true-label="1"
                             false-label="0"
                >晚到、早走几分钟不记为异常
                </el-checkbox>
                <div class="checkbox" v-if="addShift.checkbox1 == 1">
                  <div>
                    上班最多可晚到：
                    <el-select class="width90" v-model="addShift.checkbox1Up" placeholder="请选择">
                      <el-option v-for="item in Dateoptions" :key="item.value" :label="item.label"
                                 :value="item.value"
                      >
                      </el-option>
                    </el-select>
                    内不算迟到
                  </div>
                  <div>
                    下班最多可早走：
                    <el-select class="width90" v-model="addShift.checkbox1Down" placeholder="请选择">
                      <el-option v-for="item in Dateoptions" :key="item.value" :label="item.label"
                                 :value="item.value"
                      >
                      </el-option>
                    </el-select>
                    内不算早退
                  </div>
                </div>
              </div>
              <div>
                <el-checkbox v-model="addShift.checkbox2" true-label="1"
                             false-label="0"
                >下班晚走，第二天可晚到
                </el-checkbox>
                <div class="checkbox" v-if="addShift.checkbox2 == 1">
                  <div>
                    <el-radio v-model="addShift.radio1" label="1">仅下班的内勤打卡计算为晚走</el-radio>
                    <el-radio v-model="addShift.radio1" label="2">下班的内勤，外勤打卡均计算为晚走</el-radio>
                  </div>
                  <div>
                    第一天下班后晚走
                    <el-select class="width90" v-model="addShift.checkbox2Up" placeholder="请选择">
                      <el-option v-for="item in DateoptionsHour" :key="item.value" :label="item.label"
                                 :value="item.value"
                      >
                      </el-option>
                    </el-select>

                    ，第二天上班可以晚到

                    <el-select class="width90" v-model="addShift.checkbox2Down" placeholder="请选择">
                      <el-option v-for="item in DateoptionsHour" :key="item.value" :label="item.label"
                                 :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </div>
                  <div style="font-size: 12px;color: #999999;">
<!--                    第一天19:00下班，第二天10:00上班不算迟到-->
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
          <div v-if="addShift.type == 2">
            <el-form-item label="工作时长">
              <div class="flexs">
                必须工作时长为：
                <el-input class="width100" v-model="addShift.hour" :maxlength="20"></el-input>
                小时，计为
                <el-input
                  class="width100" v-model="addShift.days"
                  :maxlength="20"
                ></el-input>
                天出勤
              </div>
            </el-form-item>
            <el-form-item label="打卡时间">
              <div>
                <el-table :data="addShift.tableData" border style="width: 100%; margin-top: 20px">
                  <el-table-column label="班段" width="300" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div>
                        <el-input class="width120" v-model="scope.row.name" :maxlength="20"></el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="最早打卡时间" width="300" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div>
                        <div>
                          最早打卡：当天
                          <el-time-picker v-model="addShift.tableData[scope.$index].startEarliestWorkTime
                                                        " value-format="HH:mm" format="HH:mm" @change="(val) => { upClassTime(val, scope.$index, scope.row,'txSb'); }
        " class="width120" placeholder="上班时间"
                          >
                          </el-time-picker>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="最晚打卡时间" width="300" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div>
                        <div>
                          最晚打卡：
                          <span v-if="addShift.tableData[scope.$index].endLatestDayType=='2'">
                            次日
                          </span>
                          <span v-else>当天</span>
                          <el-time-picker v-model="addShift.tableData[scope.$index].endLatestWorkTime
                                                        " value-format="HH:mm" format="HH:mm" @change="(val) => { upClassTime(val, scope.$index, scope.row,'txXb'); }
        " class="width120" placeholder="下班时间"
                          >
                          </el-time-picker>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="休息时间" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div>
                        <div>
                          休息时间：
                          <el-select class="width90" v-model="scope.row.restLength"
                                     placeholder="请选择"
                          >
                            <el-option v-for="item in DateoptionsHour" :key="item.value"
                                       :label="item.label" :value="item.value"
                            >
                            </el-option>
                          </el-select>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
<!--                  <el-table-column label="操作" width="150" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div @click="deleteList(scope.$index, scope.row)">
                        <span>删除</span>
                      </div>
                    </template>
                  </el-table-column>-->
                </el-table>
              </div>
            </el-form-item>
            <!-- <el-form-item label="值班提醒">
                <el-switch
                    v-model="addShift.switch1"
                    active-value="1"
                    inactive-value="0"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                >
                </el-switch>
                <div class="flexs" v-if="addShift.switch1 == '1'">
                    值班前<el-input
                        class="width100"
                        v-model="addShift.zbHour"
                    ></el-input
                    >小时,提醒值班。提醒方式为：
                    <el-select
                        class="width90"
                        v-model="addShift.tipsType"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in dictList.dict
                                .BCGL_reminderType"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item> -->
          </div>
          <div v-if="addShift.type == 3">
            <el-form-item label="上下班时间">
              <div class="flexs">
                <el-button type="primary" @click="addList()"><i class="el-icon-plus"
                                                                style="margin-right: 8px"
                ></i>添加班段
                </el-button>
              </div>
              <div>
                <el-table :data="addShift.tableData" border style="width: 100%; margin-top: 20px">
                  <el-table-column label="班段" width="300" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div>
                        <el-input class="width120" v-model="scope.row.name" :maxlength="20"></el-input>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="最早打卡时间" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div>
                        <div>
                          最早打卡：
                          <span v-if="addShift.tableData[scope.$index].startEarliestDayType=='2'">次日</span>
                          <span v-else>当天</span>
                          <el-time-picker v-model="addShift.tableData[scope.$index].startEarliestWorkTime
                                                        " value-format="HH:mm" format="HH:mm" @change="(val) => { upClassTime(val, scope.$index, scope.row,'qdStart'); }
        " class="width120" placeholder="上班时间"
                          >
                          </el-time-picker>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="最晚打卡时间" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div>
                        <div>
                          最晚打卡：
                          <span v-if="addShift.tableData[scope.$index].endLatestDayType=='2'">次日</span>
                          <span v-else>当天</span>
                          <el-time-picker v-model="addShift.tableData[scope.$index].endLatestWorkTime"
                                          value-format="HH:mm" format="HH:mm"
                                          @change="(val) => { upClassTime(val, scope.$index, scope.row,'qdEnd'); }"
                                          class="width120" placeholder="下班时间"
                          >
                          </el-time-picker>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150" align="center" header-align="center">
                    <template slot-scope="scope">
                      <div @click="deleteList(scope.$index, scope.row)">
                        <span>删除</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-form-item>

            <!-- <el-form-item label="值班提醒">
                <el-switch
                    v-model="addShift.switch1"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                >
                </el-switch>
                <div class="flexs" v-if="addShift.switch1 == '1'">
                    值班前<el-input
                        class="width100"
                        v-model="addShift.zbHour"
                    ></el-input
                    >小时,提醒值班。提醒方式为：
                    <el-select
                        class="width90"
                        v-model="addShift.tipsType"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in dictList.dict
                                .BCGL_reminderType"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item> -->
          </div>
<!--          <el-form-item label="加班设置：">
            <el-time-picker v-model="addShift.overtime" value-format="HH:mm" format="HH:mm" class="width120"
                            placeholder="00:00"
            ></el-time-picker>
            之后工作时长算加班
          </el-form-item>-->
          <el-form-item>
            <div class="btns">
              <el-button @click="goList">返回</el-button>
              <el-button type="primary" style="margin-left: 25px;" @click="onSubmit">保存</el-button>
            </div>

          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { addRole, bcDetail, bcUpdate } from '@/api/scheduling/scheduling'

export default {
  name: '',
  // 获取父级的值
  props: {},
  // 数据
  data() {
    return {
      typeId: '',
      defaultClasses: '',  //记录默认的班次类型，避免切换时回显消失
      addShift: {
        name: '', // type1、 2、 3、 4
        type: 1, // type1、 2、 3、 4
        typeName: '', // type4
        hour: '', // type1、 2
        days: '', // type1、 2
        overtime: '',
        tableData: [
          {
            name: '',
            startWorkTime: '',
            endWorkTime: '',
            endWorkDayType: '',
            startEarliestWorkTime: '',
            endEarliestWorkTime: '',
            endEarliestDayType: '',
            startLatestWorkTime: '',
            endLatestDayType: '',
            endLatestWorkTime: '',
            restStartTime: '',
            restEndTime: '',
            restLength: '',
            overtime: '',
            afterFollowingDay: false,//下班时间是否是次日
            goToFollowingDay: false,//上班时间是否是次日
            goToEarlyHour: 0,
            goToEarlyMinutes: 0,
            goToLateHour: 0,
            goToLateMinutes: 0,
            afterEarlyHours: 0,
            afterEarlyMinutes: 0,
            afterLateHours: 0,
            afterLateMinutes: 0
          }
        ],
        // type1
        // tableData2: [
        //     {
        //         name: '',
        //         tableSec: '',
        //         tableThiUp: '',
        //         tableThiDown: '',
        //         tableFour: ''
        //     }
        // ], // type2
        // tableData3: [
        //     {
        //         name: '',
        //         tableSec: '',
        //         tableThiUp: '',
        //         tableThiDown: ''
        //     }
        // ], // type3
        checkbox1: '0', // type1
        checkbox2: '0', // type1
        checkbox1Up: '', // type1
        checkbox1Down: '', // type1
        radio1: '0', // type1
        checkbox2Up: '', // type1
        checkbox2Down: '' // type1
        // switch1: '0', // type2、 3
        // zbHour: '', // type2、3
        // tipsType: '' // type2、3
      },
      copyList: {
        name: '',
        startWorkTime: '',
        endWorkTime: '',
        endWorkDayType: '',
        startEarliestWorkTime: '',
        endEarliestWorkTime: '',
        startLatestWorkTime: '',
        startEarliestDayType: '1',
        endEarliestDayType: '',
        endLatestDayType: '',
        endLatestWorkTime: '',
        restStartTime: '',
        restEndTime: '',
        restLength: '',
        goToEarlyHour: 0,
        goToEarlyMinutes: 0,
        goToLateHour: 0,
        goToLateMinutes: 0,
        afterEarlyHours: 0,
        afterEarlyMinutes: 0,
        afterLateHours: 0,
        afterLateMinutes: 0
      },
      options: [
        {
          value: '1',
          label: '当天'
        },
        {
          value: '2',
          label: '次日'
        }
      ],
      Dateoptions: [
        {
          value: 15,
          label: '15分钟'
        },
        {
          value: 30,
          label: '30分钟'
        },
        {
          value: 45,
          label: '45分钟'
        },
        {
          value: 60,
          label: '60分钟'
        }
      ],
      DateoptionsHour: [
        {
          value: 0,
          label: '不休息'
        },
        {
          value: 1,
          label: '1小时'
        },
        {
          value: 2,
          label: '2小时'
        },
        {
          value: 3,
          label: '3小时'
        },
        {
          value: 4,
          label: '4小时'
        }
      ],
      houroptions: [
        {
          value: 8,
          label: '8小时'
        },
        {
          value: 12,
          label: '12小时'
        }
      ],
      typeoptions: [
        {
          value: '1',
          label: '系统消息'
        },
        {
          value: '2',
          label: '短信'
        }
      ],
      submmitSwitch: {
        hourAdd: 0,
        nameJY: 1,
        startWorkTimeJY: 1,
        endWorkTimeJY: 1,
        endWorkDayTypeJY: 1,
        endEarliestWorkTimeJY: 1,
        startLatestWorkTimeJY: 1,
        endEarliestDayTypeJY: 1,
        endLatestDayTypeJY: 1,
        endLatestWorkTimeJY: 1,
        restStartTimeJY: 1,
        restEndTimeJY: 1,
        restLengthJY: 1,
        startEarliestMinuteJY: 1,
        goToLateHourJY: 1
      }
    }
  },

  // 实例创建完成后被立即调用
  created() {

  },

  // 挂载实例后调用
  mounted() {
    if (this.$route.query.id) {
      this.bcDetails()
    }
    let a = this.getNowDate() + ' ' + '12:06:11'
  },

  // 监控
  watch: {},

  // 过滤器
  filters: {},

  // 定义模板
  components: {},

  // 计算属性
  computed: {
    dictList() {
      return this.$store.state.dict
    }
  },

  // 混入到 Vue 实例中
  methods: {
    deleteList(index, row) {
      console.log(111)
      console.log(index, row)
      this.addShift.tableData.splice(index, 1)
      this.addShift.tableData.forEach((zitem, zindex) => {
        this.addShift.hour +=
          this.getDateDiff(
            new Date(this.getNowDate() + ' ' + zitem.startWorkTime),
            new Date(this.getNowDate() + ' ' + zitem.endWorkTime),
            'hour'
          ) -
          this.getDateDiff(
            new Date(this.getNowDate() + ' ' + zitem.restStartTime),
            new Date(this.getNowDate() + ' ' + zitem.restEndTime),
            'hour'
          )
      })
    },
    changeRadio(value) {
      console.log(value, '点击切换班次类型')
      let copyObj=JSON.parse(JSON.stringify(this.copyList))
      if (this.defaultClasses && value == this.defaultClasses) {
        this.bcDetails()
      } else {
        this.addShift.hour = ''
        this.addShift.checkbox1 = ''
        this.addShift.checkbox1Up = ''
        this.addShift.checkbox1Down = ''
        this.addShift.overtime = ''
        this.addShift.tableData = [
          // {
          //   name: '',
          //   startWorkTime: '',
          //   endWorkTime: '',
          //   endWorkDayType: '',
          //   startEarliestWorkTime: '',
          //   endEarliestWorkTime: '',
          //   startLatestWorkTime: '',
          //   endEarliestDayType: '',
          //   startEarliestDayType: '1',
          //   endLatestDayType: '',
          //   endLatestWorkTime: '',
          //   restStartTime: '',
          //   restEndTime: '',
          //   restLength: '',
          //   afterFollowingDay: false,//下班时间是否是次日
          //   goToFollowingDay: false//上班时间是否是次日
          // }
          copyObj
        ]
      }
    },
    addList() {
      let newObj = JSON.parse(JSON.stringify(this.copyList))
      if (this.addShift.tableData.length > 0) {
        newObj.goToFollowingDay = this.addShift.tableData[this.addShift.tableData.length - 1].afterFollowingDay
        newObj.afterFollowingDay = newObj.goToFollowingDay
        newObj.startWorkDayType = this.addShift.tableData[this.addShift.tableData.length - 1].endWorkDayType
        newObj.endWorkDayType = newObj.startWorkDayType
      }
      this.addShift.tableData.push(newObj)
    },
    bcDetails() {
      this.loading = true
      let ids = this.$route.query.id
      let params = {
        id: ids
      }
      bcDetail(params).then((res) => {
        let bcList = res.data
        // 记录数据库中此id的班次类型
        this.defaultClasses = res.data.arrangementTypeName
        this.addShift.name = bcList.name
        if (bcList.arrangementTypeId > 3) {
          this.addShift.type = 0
          this.typeId = bcList.arrangementTypeId
          this.defaultClasses = 0
        } else {
          this.addShift.type = bcList.arrangementTypeId
          this.typeId = bcList.arrangementTypeId
          this.defaultClasses = bcList.arrangementTypeId
        }
        this.addShift.typeName = bcList.arrangementTypeId > 3 ? bcList.arrangementTypeName : ''
        this.addShift.hour = bcList.workTimeAmount
        this.addShift.days = bcList.ycqts
        this.addShift.overtime = bcList.overtime
        this.addShift.tableData = []
        bcList.arrangementTimeInfoList.forEach((item, index) => {
          let gotoEarlyhour = Math.floor(item.startEarliestMinute / 60)
          let gotoEarlyminutes = item.startEarliestMinute % 60
          let afterEarlyhour = Math.floor(item.endEarliestMinute / 60)
          let afterEarlyminutes = item.endEarliestMinute % 60
          let goToLateHour = Math.floor(item.startLatestMinute / 60)
          let goToLateMinutes = item.startLatestMinute % 60
          let afterLateHours = Math.floor(item.endLatestMinute / 60)
          let afterLateMinutes = item.endLatestMinute % 60
          var arr = {}
          arr.id = item.id
          arr.name = item.name
          arr.startWorkTime = item.startWorkTime
          arr.endWorkTime = item.endWorkTime
          arr.startEarliestWorkTime = item.startEarliestWorkTime
          arr.endEarliestWorkTime = item.endEarliestWorkTime
          arr.startLatestWorkTime = item.startLatestWorkTime
          arr.startWorkDayType = item.startWorkDayType
          arr.endWorkDayType = item.endWorkDayType
          arr.endEarliestDayType = item.endEarliestDayType
          arr.endLatestDayType = item.endLatestDayType
          arr.endLatestWorkTime = item.endLatestWorkTime
          arr.restStartTime = item.restStartTime
          arr.restEndTime = item.restEndTime
          arr.restLength = String(item.restLength)
          item.startWorkDayType == '1' ? arr.goToFollowingDay = false : arr.goToFollowingDay = true
          item.endWorkDayType == '1' ? arr.afterFollowingDay = false : arr.afterFollowingDay = true
          arr.goToEarlyHour = gotoEarlyhour
          arr.goToEarlyMinutes = gotoEarlyminutes
          arr.goToLateHour = goToLateHour
          arr.goToLateMinutes = goToLateMinutes
          arr.afterEarlyHours = afterEarlyhour
          arr.afterEarlyMinutes = afterEarlyminutes
          arr.afterLateHours = afterLateHours
          arr.afterLateMinutes = afterLateMinutes
          if ((this.addShift.type == 2 || this.addShift.type == 3) && !item.startEarliestDayType) {
            arr.startEarliestDayType = '1'
          } else {
            arr.startEarliestDayType = item.startEarliestDayType
          }
          this.addShift.tableData.push(arr)
          console.log(this.addShift.tableData, '最终反显数据')
        })
        this.addShift.checkbox1 = bcList.optionOne
        this.addShift.checkbox2 = bcList.optionTwo
        this.addShift.checkbox1Up = bcList.oneBelateLength
        this.addShift.checkbox1Down = bcList.oneLeaveEarlyLength
        this.addShift.radio1 = bcList.twoType
        this.addShift.checkbox2Up = bcList.twoStayLateLength
        this.addShift.checkbox2Down = bcList.twoArriveLateLength
        console.log(this.addShift.tableData, 'this.tableData')
        console.log(this.addShift.type, 'this.addShift.type22')
      })
    },
    //计算最早最晚打卡时间
    computeTime(value, index, type) {
      this.initNumber(index)
      if (type == 'goToEarlyMinutes') {
        if (!value.startWorkTime) {
          this.$message({
            showClose: true,
            message: `请输入上班时间`,
            type: 'error'
          })
          this.addShift.tableData[index].goToEarlyHour = '0'
          this.addShift.tableData[index].goToEarlyMinutes = '0'
        } else {
          //若当前修改的为第一个班段的上班时间
          if (index == 0) {
            let acquiesceMinutes = 8 * 60
            let allMinutes = Number(value.goToEarlyHour) * 60 + Number(value.goToEarlyMinutes)
            let needAllMinutes = this.timeToMinutes(value.startWorkTime)
            //00：00距离上班时间大于8小时
            if (acquiesceMinutes < Number(needAllMinutes)) {
              //输入的最高打卡时间大于8小时
              if (Number(allMinutes) > acquiesceMinutes) {
                let hour = acquiesceMinutes / 60
                let minutes = acquiesceMinutes % 60
                this.$message({
                  showClose: true,
                  message: `最早不可超过${hour}时${minutes}分`,
                  type: 'error'
                })
                this.addShift.tableData[index].goToEarlyHour = '0'
                this.addShift.tableData[index].goToEarlyMinutes = '0'
              }
            } else {
              //00：00距离上班时间小于8小时 && 输入的最早打卡时间大于距离00:00的时间
              if (Number(allMinutes) > Number(needAllMinutes)) {
                let hour = Number(needAllMinutes) / 60
                let minutes = Number(needAllMinutes) % 60
                this.$message({
                  showClose: true,
                  message: `最早不可超过${value.startWorkTime.split(':')[0]}时${value.startWorkTime.split(':')[1]}分`,
                  type: 'error'
                })
                this.addShift.tableData[index].goToEarlyHour = '0'
                this.addShift.tableData[index].goToEarlyMinutes = '0'
              }
            }
          } else {  //不为第一个班段时的情况
            if (this.addShift.tableData[index - 1].afterFollowingDay) {
              let topMinutes = this.timeToMinutes(this.addShift.tableData[index - 1].endWorkTime)
              let nowStartMinutes = this.timeToMinutes(value.startWorkTime)
              let nowEarly = Number(value.goToEarlyHour * 60) + Number(value.goToEarlyMinutes)
              let maxMinutes = nowStartMinutes - topMinutes
              if (maxMinutes < nowEarly) {
                this.addShift.tableData[index].goToEarlyHour = '0'
                this.addShift.tableData[index].goToEarlyMinutes = '0'
                let hour = Math.floor(maxMinutes / 60)
                let minutes = maxMinutes % 60
                this.$message({
                  showClose: true,
                  message: `最早不可超过${hour}时${minutes}分`,
                  type: 'error'
                })
              }
            } else if (!this.addShift.tableData[index - 1].afterFollowingDay && this.addShift.tableData[index].goToFollowingDay) {
              let nowEarly = Number(value.goToEarlyHour * 60) + Number(value.goToEarlyMinutes)
              let ZzMinutes = 23 * 60 + 59
              let topMinutes = this.timeToMinutes(this.addShift.tableData[index - 1].endWorkTime)
              let yesterdayMinutes = ZzMinutes - topMinutes
              let nowStartMinutes = this.timeToMinutes(value.startWorkTime)
              let allMinutes = yesterdayMinutes + nowStartMinutes
              let hour = Math.floor(allMinutes / 60)
              let minutes = allMinutes % 60
              if (nowEarly > allMinutes) {
                this.$message({
                  showClose: true,
                  message: `最早不可超过${hour}时${minutes}分`,
                  type: 'error'
                })
                this.addShift.tableData[index].goToEarlyHour = '0'
                this.addShift.tableData[index].goToEarlyMinutes = '0'
              }
            } else {
              let topMinutes = this.timeToMinutes(this.addShift.tableData[index - 1].endWorkTime)
              let nowStartMinutes = this.timeToMinutes(value.startWorkTime)
              let nowEarly = Number(value.goToEarlyHour * 60) + Number(value.goToEarlyMinutes)
              let maxMinutes = nowStartMinutes - topMinutes
              if (maxMinutes < nowEarly) {
                this.addShift.tableData[index].goToEarlyHour = '0'
                this.addShift.tableData[index].goToEarlyMinutes = '0'
                let hour = Math.floor(maxMinutes / 60)
                let minutes = maxMinutes % 60
                this.$message({
                  showClose: true,
                  message: `最早不可超过${hour}时${minutes}分`,
                  type: 'error'
                })
              }
            }
          }
        }
      } else if (type == 'goToLateMinutes') {
        // debugger;
        if (!value.startWorkTime || !value.endWorkTime) {
          this.$message({
            showClose: true,
            message: `请先输入上班与下班时间`,
            type: 'error'
          })
          this.addShift.tableData[index].goToLateHour = '0'
          this.addShift.tableData[index].goToLateMinutes = '0'
        } else {
          //当前上班为次日或当前上班为当日，下班为当日
          if (this.addShift.tableData[index].goToFollowingDay || (!this.addShift.tableData[index].goToFollowingDay && !this.addShift.tableData[index].afterFollowingDay)) {
            let minMinutes = this.timeToMinutes(value.startWorkTime)
            let maxMinutes = this.timeToMinutes(value.endWorkTime)
            let allMinutes = Number(value.goToLateHour) * 60 + Number(value.goToLateMinutes)
            let comMinutes = maxMinutes - minMinutes
            if (allMinutes < 0 || allMinutes > comMinutes) {
              let hour = Math.floor(comMinutes / 60)
              let minutes = comMinutes % 60
              this.$message({
                showClose: true,
                message: `延迟不可超过${hour}时${minutes}分`,
                type: 'error'
              })
              this.addShift.tableData[index].goToLateHour = '0'
              this.addShift.tableData[index].goToLateMinutes = '0'
            }
          } else {
            let minMinutes = this.timeToMinutes(value.startWorkTime)
            let maxMinutes = this.timeToMinutes(value.endWorkTime)
            let allMinutes = Number(value.goToLateHour) * 60 + Number(value.goToLateMinutes)
            let comMinutes = 24 * 60 - minMinutes + maxMinutes
            if (allMinutes < 0 || allMinutes > comMinutes) {
              let hour = Math.floor(comMinutes / 60)
              let minutes = comMinutes % 60
              this.$message({
                showClose: true,
                message: `延迟不可超过${hour}时${minutes}分`,
                type: 'error'
              })
              this.addShift.tableData[index].goToLateHour = '0'
              this.addShift.tableData[index].goToLateMinutes = '0'
            }
          }

        }
      } else if (type == 'afterEarlyMinutes') {
        if (!value.startWorkTime || !value.endWorkTime) {
          this.$message({
            showClose: true,
            message: `请先输入上班与下班时间`,
            type: 'error'
          })
          this.addShift.tableData[index].afterEarlyHours = '0'
          this.addShift.tableData[index].afterEarlyMinutes = '0'
        } else {
          let minMinutes = this.timeToMinutes(value.startWorkTime)
          if (this.addShift.tableData[index].goToFollowingDay) {
            minMinutes = 24 * 60 + minMinutes
          }
          let maxMinutes = this.timeToMinutes(value.endWorkTime)
          if (this.addShift.tableData[index].afterFollowingDay) {
            maxMinutes = 24 * 60 + maxMinutes
          }
          let allMinutes = Number(value.afterEarlyHours) * 60 + Number(value.afterEarlyMinutes)
          let comMinutes = maxMinutes - minMinutes

          if (allMinutes < 0 || allMinutes > comMinutes) {
            let hour = Math.floor(comMinutes / 60)
            let minutes = comMinutes % 60
            this.$message({
              showClose: true,
              message: `延迟不可超过${hour}时${minutes}分`,
              type: 'error'
            })
            this.addShift.tableData[index].afterEarlyHours = '0'
            this.addShift.tableData[index].afterEarlyMinutes = '0'
          }
        }
      } else if (type == 'afterLateMinutes') {
        if (!value.endWorkTime) {
          this.$message({
            showClose: true,
            message: `请先输入下班时间`,
            type: 'error'
          })
          this.addShift.tableData[index].afterLateHours = '0'
          this.addShift.tableData[index].afterLateMinutes = '0'
        } else {
          if (index == 0 && this.addShift.tableData.length == 1 && !this.addShift.tableData[index].afterFollowingDay) {
            let endMinutes = this.timeToMinutes(value.endWorkTime)
            let LastMinutes = 23 * 60 + 59 - endMinutes
            let nowLateMinutes = Number(value.afterLateHours * 60) + Number(value.afterLateMinutes)
            if (nowLateMinutes > LastMinutes) {
              let twentyFour = 23 * 60 + 59 - endMinutes
              let hour = Math.floor(twentyFour / 60)
              let minutes = twentyFour % 60
              this.$message({
                showClose: true,
                message: `延迟不可超过${hour}时${minutes}分`,
                type: 'error'
              })
              this.addShift.tableData[index].afterLateHours = '0'
              this.addShift.tableData[index].afterLateMinutes = '0'
            }
          } else if ((index == 0 && this.addShift.tableData.length > 0) || (index != 0 && this.addShift.tableData.length > 0 && index != this.addShift.tableData.length - 1)) {
            if (this.addShift.tableData[index].afterFollowingDay) {
              let bigMinutes = this.timeToMinutes(this.addShift.tableData[index + 1].startWorkTime)
              let smallMinutes = this.timeToMinutes(this.addShift.tableData[index].endWorkTime)
              let MaxMinutes = bigMinutes - smallMinutes
              let nowLateMinutes = Number(value.afterLateHours * 60) + Number(value.afterLateMinutes)
              if (nowLateMinutes > MaxMinutes) {
                let hour = Math.floor(MaxMinutes / 60)
                let minutes = MaxMinutes % 60
                this.$message({
                  showClose: true,
                  message: `延迟不可超过${hour}时${minutes}分`,
                  type: 'error'
                })
                this.addShift.tableData[index].afterLateHours = '0'
                this.addShift.tableData[index].afterLateMinutes = '0'
              }
            } else if (!this.addShift.tableData[index].afterFollowingDay && this.addShift.tableData[index + 1].goToFollowingDay) {
              let nowLateMinutes = Number(value.afterLateHours * 60) + Number(value.afterLateMinutes)
              let yesterdayMinutes = this.timeToMinutes(this.addShift.tableData[index].endWorkTime)
              let bigMinutes = this.timeToMinutes(this.addShift.tableData[index + 1].startWorkTime)
              let MaxMinutes = 24 * 60 - yesterdayMinutes + bigMinutes
              if (nowLateMinutes > MaxMinutes) {
                let hour = Math.floor(MaxMinutes / 60)
                let minutes = MaxMinutes % 60
                this.$message({
                  showClose: true,
                  message: `延迟不可超过${hour}时${minutes}分`,
                  type: 'error'
                })
                this.addShift.tableData[index].afterLateHours = '0'
                this.addShift.tableData[index].afterLateMinutes = '0'
              }
            } else {
              let bigMinutes = this.timeToMinutes(this.addShift.tableData[index + 1].startWorkTime)
              let smallMinutes = this.timeToMinutes(this.addShift.tableData[index].endWorkTime)
              let MaxMinutes = bigMinutes - smallMinutes
              let nowLateMinutes = Number(value.afterLateHours * 60) + Number(value.afterLateMinutes)
              if (nowLateMinutes > MaxMinutes) {
                let hour = Math.floor(MaxMinutes / 60)
                let minutes = MaxMinutes % 60
                this.$message({
                  showClose: true,
                  message: `延迟不可超过${hour}时${minutes}分`,
                  type: 'error'
                })
                this.addShift.tableData[index].afterLateHours = '0'
                this.addShift.tableData[index].afterLateMinutes = '0'
              }
            }
          } else if (index != 0 && index == this.addShift.tableData.length - 1) {
            if (this.addShift.tableData[index].afterFollowingDay) {
              let bigMinutes = this.timeToMinutes(this.addShift.tableData[0].startWorkTime)
              let smallMinutes = this.timeToMinutes(this.addShift.tableData[index].endWorkTime)
              let LastMinutes = bigMinutes - smallMinutes
              let nowLateMinutes = Number(value.afterLateHours * 60) + Number(value.afterLateMinutes)
              if (nowLateMinutes > LastMinutes) {
                let hour = Math.floor(LastMinutes / 60)
                let minutes = LastMinutes % 60
                this.$message({
                  showClose: true,
                  message: `延迟不可超过${hour}时${minutes}分`,
                  type: 'error'
                })
                this.addShift.tableData[index].afterLateHours = '0'
                this.addShift.tableData[index].afterLateMinutes = '0'
              }
            } else if (!this.addShift.tableData[index].afterFollowingDay) {
              let nowLateMinutes =  Number(value.afterLateHours * 60) + Number(value.afterLateMinutes)
              let endMinutes = this.timeToMinutes(value.endWorkTime)
              let LastMinutes = 23 * 60 + 59 - endMinutes
              if (nowLateMinutes > LastMinutes) {
                let hour = Math.floor(LastMinutes / 60)
                let minutes = LastMinutes % 60
                this.$message({
                  showClose: true,
                  message: `延迟不可超过${hour}时${minutes}分`,
                  type: 'error'
                })
                this.addShift.tableData[index].afterLateHours = '0'
                this.addShift.tableData[index].afterLateMinutes = '0'
              }
            }
          }
        }
      }
    },
    // 新增班次
    addRoles(params) {
      this.loading = true
      // JSON.stringify(this.targetList)
      console.log(params, 'params')
      addRole(params).then((res) => {
        this.$message({
          message: '新增成功',
          type: 'success'
        })
        setTimeout(() => {
          this.loading = false
        }, 1000)
        this.goList()
      })
    },
    // 修改班次
    bcUpdates(params) {
      this.loading = true
      // JSON.stringify(this.targetList)
      console.log(params, 'params')
      bcUpdate(params).then((res) => {
        this.$message({
          message: '修改成功',
          type: 'success'
        })
        setTimeout(() => {
          this.loading = false
        }, 1000)
        this.goList()
      })
    },
    getDict() {
      this.$store.dispatch('dict/setDict', {})
    },


    //提取公共逻辑代码

    //上班时间转分钟
    timeToMinutes(time){
      return Number(time.split(':')[0]) * 60 + Number(time.split(':')[1])
    },



    upClassTime(value, index, row, tips) {
      //固定班次------上班工作时间
      if (tips == 'xb') {
        //----------------------------------------------修改当前下班时间,与当前班段上班时间对比------------------------------------------
        let afterWork = value.split(':')
        //将小时*60+分钟总和去比较时间大小:上班时间
        let goToWorkAllTime = this.timeToMinutes(this.addShift.tableData[index].startWorkTime)
        //将此班段下班时间分割为数组格式
        let afterWorkAllTime = this.timeToMinutes(value)
        //下班时间变为次日需满足两个个条件： 1.当前班段上班时间为今日，下班时间小于上班时间 2. 当前班段上班时间为次日
        if (Number(afterWorkAllTime) < Number(goToWorkAllTime) || this.addShift.tableData[index].goToFollowingDay) {
          //下班时间小于上班时间或上班时间为次日，直接将下班时间变为次日
          this.addShift.tableData[index].afterFollowingDay = true
          if (Number(afterWorkAllTime) < Number(goToWorkAllTime) && this.addShift.tableData[index].goToFollowingDay) {
            //如果上班时间为次日，并企鹅下班时间比上班时间小，给出提示
            this.addShift.tableData[index].endWorkTime = ''
            this.$message.error('不可以比上一个次日更早')
          }
        } else {
          // 其他情况为 1. 上班时间为当日,下班时间大于上班时间
          this.addShift.tableData[index].afterFollowingDay = false
        }
        // -------------------------------------- 修改当前下班时间，与下个班次上班时间进行校验---------------------------------------
        //获取当前修改的下班时间---下班时间转分钟
        let timeToMinutes = this.timeToMinutes(this.addShift.tableData[index].endWorkTime)
        //如果是次日，在基础上加24小时
        if (this.addShift.tableData[index].afterFollowingDay) {
          timeToMinutes = timeToMinutes + 24 * 60
        }
        let timeToNextMinutes, nextWorkTime

        //不为最后一个班段的情况
        if (index !== this.addShift.tableData.length - 1) {
          //获取下一班段的上班时间
          //时间转分钟
          timeToNextMinutes = this.timeToMinutes(this.addShift.tableData[index + 1].startWorkTime)
          if (this.addShift.tableData[index + 1].goToFollowingDay) {
            timeToNextMinutes = timeToNextMinutes + 24 * 60
          }
          //当前班段下班时间大于下一班段上班时间
          if (timeToMinutes > timeToNextMinutes) {
            this.addShift.tableData[index].endWorkTime = ''
            this.$message.error('不可以比下个班段的上班时间晚')
          }
        } else {
          //为最后一个班段的情况
          //获取下一班段的上班时间
          //时间转分钟
          timeToNextMinutes = this.timeToMinutes(this.addShift.tableData[0].startWorkTime)

          //最后一个班段下班时间大于此班次最早上班时间
          if (this.addShift.tableData[index].afterFollowingDay) {
            timeToMinutes = timeToMinutes - 24 * 60

            if (timeToMinutes > timeToNextMinutes) {
              this.addShift.tableData[index].endWorkTime = ''
              this.$message.error('不可以迟于第一个班段的上班时间')
            }
          }
        }
      } else if (tips == 'sb') {
        //--------------------------------------------------修改上班时间------------------------------------------------------
        if (index == 0) {
          //上班为当天
          if (!this.addShift.tableData[index].goToFollowingDay) {
            //下班为当天
            if (!this.addShift.tableData[index].afterFollowingDay) {
              let startMinutes = this.timeToMinutes(this.addShift.tableData[index].startWorkTime)
              let endMinutes = this.timeToMinutes(this.addShift.tableData[index].endWorkTime)
              //上班时间小于下班时间
              if (endMinutes < startMinutes) {
                this.addShift.tableData[index].startWorkTime = ''
                this.$message({
                  showClose: true,
                  message: '不可以比下班时间早',
                  type: 'error'
                })
              }
            }
          } else if (this.addShift.tableData[index].afterFollowingDay) {
            // 下班为次日
            let startMinutes = this.timeToMinutes(this.addShift.tableData[index].startWorkTime)
            let endMinutes = this.timeToMinutes(this.addShift.tableData[index].endWorkTime)
            if (endMinutes < startMinutes) {
              this.$message({
                showClose: true,
                message: '不可以比上个班段的下班时间早',
                type: 'error'
              })
            }
          }
        } else { //不为第一天
          let lastNumEndMinutes = this.timeToMinutes(this.addShift.tableData[index - 1].endWorkTime)
          let endMinutes = this.timeToMinutes(this.addShift.tableData[index].endWorkTime)
          //当前上班为今日
          if (!this.addShift.tableData[index-1].goToFollowingDay) {
            if (lastNumEndMinutes<endMinutes) {
              this.addShift.tableData[index].goToFollowingDay=false
            } else {
              this.addShift.tableData[index].goToFollowingDay=true
            }
          } else {
            //当前上班为次日
            if (lastNumEndMinutes<endMinutes) {
              this.addShift.tableData[index].goToFollowingDay=false
            } else {
              this.$message.error('不可以比上个班段下班时间早')
              this.addShift.tableData[index].goToFollowingDay=false
            }
          }
        }
        if (index !== 0) {
          //设置上班时间时，
          //获取当前设置的时间
          let goToWork = value.split(':')
          //获取上个班段下班时间
          let lastIndexafterWork = this.addShift.tableData[index - 1].endWorkTime.split(':')
          // 1 需要look上个班段的下班时间是否是次日
          // 2 是否是比上个班段的时间小
          let goToWorkAllTime = this.timeToMinutes(value)
          let afterWorkAllTime = this.timeToMinutes(this.addShift.tableData[index - 1].endWorkTime)
          if (this.addShift.tableData[index - 1].afterFollowingDay || Number(goToWorkAllTime) < Number(afterWorkAllTime)) {
            this.addShift.tableData[index].goToFollowingDay = true
            this.addShift.tableData[index].afterFollowingDay = true
            if (this.addShift.tableData[index - 1].afterFollowingDay && Number(goToWorkAllTime) < Number(afterWorkAllTime)) {
              this.addShift.tableData[index].startWorkTime = ''
              this.$message.error('不可以比上一个次日更早')
            }
          } else {
            this.addShift.tableData[index].goToFollowingDay = false
            this.addShift.tableData[index].afterFollowingDay = false
          }
        }
        //获取下班时间 转分钟
        let IndexgoToWorkAllTime2 = this.timeToMinutes(this.addShift.tableData[index].endWorkTime)
        if (this.addShift.tableData[index].afterFollowingDay) { //下班时间类型为次日
          IndexgoToWorkAllTime2 = IndexgoToWorkAllTime2 + 24 * 60
        }
        //当前班段上班 转分钟
        let startWork = this.addShift.tableData[index].startWorkTime.split(':')
        let startWork2 = this.timeToMinutes(this.addShift.tableData[index].startWorkTime)
        if (this.addShift.tableData[index].goToFollowingDay) {
          startWork2 = startWork2 + 24 * 60
        }
        if (startWork2 > IndexgoToWorkAllTime2) {
          this.addShift.tableData[index].startWorkTime = ''
          this.$message.error('不可以比上个班段的下班时间早')
        }
      } else if (tips == 'txSb') {
        this.addShift.tableData[index].endLatestWorkTime = ''
      } else if (tips == 'txXb') {
        let startTime = this.timeToMinutes(this.addShift.tableData[index].startEarliestWorkTime)
        let endTime = this.timeToMinutes(this.addShift.tableData[index].endLatestWorkTime)
        if (endTime < startTime) {
          this.addShift.tableData[index].endLatestDayType = '2'
        } else {
          this.addShift.tableData[index].endLatestDayType = '1'
        }
      } else if (tips == 'qdStart') {
        if (index !== 0) {
          //设置上班时间时，
          //获取当前设置的时间

          let goToWorkAllTime = this.timeToMinutes(value)

          // 1 需要look上个班段的下班时间是否是次日
          // 2 是否是比上个班段的时间小

          let afterWorkAllTime = this.timeToMinutes(this.addShift.tableData[index - 1].endLatestWorkTime)
          console.log(this.addShift.tableData[index - 1].endLatestDayType)
          if (this.addShift.tableData[index - 1].endLatestDayType == '2') {
            this.addShift.tableData[index].startEarliestDayType = '2'
            this.addShift.tableData[index].endLatestDayType = '2'
            if (Number(goToWorkAllTime) < Number(afterWorkAllTime)) {
              this.addShift.tableData[index].startEarliestDayType = '2'
              this.addShift.tableData[index].startEarliestWorkTime = ''
              this.$message.error('不可以比上一个次日更早')
            }
          }
          if (this.addShift.tableData[index - 1].endLatestDayType == '1') {
            if (Number(goToWorkAllTime) > Number(afterWorkAllTime)) {
              this.addShift.tableData[index].startEarliestDayType = '1'
            } else {
              this.addShift.tableData[index].startEarliestDayType = '2'
            }
          }
          console.log(this.addShift.tableData[index].startEarliestDayType, '次日')
        } else {
          this.addShift.tableData[index].startEarliestDayType = '1'
        }
        //获取当前时间
        //当前时间转分钟
        let nowTimeMinutes = this.timeToMinutes(this.addShift.tableData[index].startEarliestWorkTime)
        //当前选中时间是否是次日
        if (this.addShift.tableData[index].startEarliestDayType == '2') {
          nowTimeMinutes = nowTimeMinutes + 24 * 60
        }
        if (this.addShift.tableData[index].endLatestWorkTime) {
          //获取当前时间段的最晚打卡时间
          let afterTimeMinutes = this.timeToMinutes(this.addShift.tableData[index].endLatestWorkTime)
          if (this.addShift.tableData[index].endWorkDayType == '2') {
            afterTimeMinutes = afterTimeMinutes + 24 * 60
          }
          if (nowTimeMinutes > afterTimeMinutes) {
            this.addShift.tableData[index].startEarliestWorkTime = ''
            this.$message.error('上班时间不可晚于下班时间')
          }
        }
      } else if (tips == 'qdEnd') {
        let endTime = this.timeToMinutes(this.addShift.tableData[index].endLatestWorkTime)
        let startTime = this.timeToMinutes(this.addShift.tableData[index].startEarliestWorkTime)
        if (this.addShift.tableData[index].startEarliestDayType == '1' && endTime < startTime) {
          this.addShift.tableData[index].endLatestDayType = '2'
        } else if (this.addShift.tableData[index].startEarliestDayType == '2' && endTime < startTime) {
          this.$message.error('不可以比上一个次日更早')
          this.addShift.tableData[index].endLatestDayType = '2'
          this.addShift.tableData[index].endLatestWorkTime = ''
        } else if (this.addShift.tableData[index].startEarliestDayType == '1' && endTime > startTime) {
          this.addShift.tableData[index].endLatestDayType = '1'
        } else {
          this.addShift.tableData[index].endLatestDayType = '2'
        }
        //  获取当前班段下班时间
        let nowAfterTimeMinutes = this.timeToMinutes(this.addShift.tableData[index].endLatestWorkTime)
        if (this.addShift.tableData[index].endLatestDayType == '2') {
          nowAfterTimeMinutes = nowAfterTimeMinutes + 24 * 60
        }
        let nextStartTime, nextStartTimeMinutes

        //  到此分两种情况，是否是最后一个班段以及不是最后一个班段
        //  获取下个班段
        if (index == this.addShift.tableData.length - 1) {
          //获取下个班段最早打卡时间
          nextStartTimeMinutes = this.timeToMinutes(this.addShift.tableData[0].startEarliestWorkTime)
          if (this.addShift.tableData[index].endLatestDayType == '2') {
            nowAfterTimeMinutes = nowAfterTimeMinutes - 24 * 60
          }
          if (this.addShift.tableData[index].endLatestDayType == '2' && nowAfterTimeMinutes > nextStartTimeMinutes) {
            this.addShift.tableData[index].endLatestWorkTime = ''
            this.$message.error('不可以比此班次最早打卡时间晚')
          }
        } else {
          nextStartTimeMinutes = this.timeToMinutes(this.addShift.tableData[index + 1].startEarliestWorkTime)
          if (this.addShift.tableData[index + 1].startEarliestDayType == '2') {
            nextStartTimeMinutes = nextStartTimeMinutes + 24 * 60
          }
          if (nowAfterTimeMinutes > nextStartTimeMinutes) {
            this.addShift.tableData[index].endLatestWorkTime = ''
            this.$message.error('不可以比下个班段上班时间晚')
          }
        }
      }
      this.hourAdd = 0
      this.addShift.tableData.forEach((zitem, zindex) => {
        this.hourAdd +=
          this.getDateDiff(
            new Date(this.getNowDate() + ' ' + zitem.startWorkTime),
            new Date(this.getNowDate() + ' ' + zitem.endWorkTime),
            'hour'
          ) -
          this.getDateDiff(
            new Date(this.getNowDate() + ' ' + zitem.restStartTime),
            new Date(this.getNowDate() + ' ' + zitem.restEndTime),
            'hour'
          )
      })
    },
    //将空字段赋值0
    initNumber(index){
      if(!this.addShift.tableData[index].goToEarlyMinutes) this.addShift.tableData[index].goToEarlyMinutes=0
      if(!this.addShift.tableData[index].goToLateMinutes) this.addShift.tableData[index].goToLateMinutes=0
      if(!this.addShift.tableData[index].afterEarlyMinutes) this.addShift.tableData[index].afterEarlyMinutes=0
      if(!this.addShift.tableData[index].afterLateMinutes) this.addShift.tableData[index].afterLateMinutes=0
      if(!this.addShift.tableData[index].goToEarlyHour) this.addShift.tableData[index].goToEarlyHour=0
      if(!this.addShift.tableData[index].goToLateHour) this.addShift.tableData[index].goToLateHour=0
      if(!this.addShift.tableData[index].afterEarlyHours) this.addShift.tableData[index].afterEarlyHours=0
      if(!this.addShift.tableData[index].afterLateHours) this.addShift.tableData[index].afterLateHours=0
    },

    onSubmit() {
      console.log(this.addShift.type, 'type')
      this.submmitSwitch = {
        hourAdd: 0,
        nameJY: 1,
        startWorkTimeJY: 1,
        endWorkTimeJY: 1,
        endWorkDayTypeJY: 1,
        endEarliestWorkTimeJY: 1,
        startLatestWorkTimeJY: 1,
        endEarliestDayTypeJY: 1,
        endLatestDayTypeJY: 1,
        endLatestWorkTimeJY: 1,
        restStartTimeJY: 1,
        restEndTimeJY: 1,
        restLengthJY: 1,
        startEarliestMinuteJY: 1,
        goToLateHourJY: 1,
        startEarliestWorkTimeJY: 1
      }
      if (!this.addShift.name) {
        this.$message({
          message: '请输入班次名称'
        })
        return
      }

      //非空判断
      this.addShift.tableData.forEach((item) => {
        if (!item.name) this.submmitSwitch.nameJY = 2
        if (!item.startWorkTime) this.submmitSwitch.startWorkTimeJY = 2
        if (!item.endWorkTime) this.submmitSwitch.endWorkTimeJY = 2
        this.submmitSwitch.endWorkDayTypeJY = 1
        if (!item.startEarliestWorkTime && (this.addShift.type == 2 || this.addShift.type == 3)) this.submmitSwitch.startEarliestWorkTimeJY = 2
        if (!item.endEarliestWorkTime) this.submmitSwitch.endEarliestWorkTimeJY = 2
        if (!item.startLatestWorkTime) this.submmitSwitch.startLatestWorkTimeJY = 2
        this.submmitSwitch.endEarliestDayTypeJY = 1
        if (!item.endLatestDayType && (this.addShift.type == 2 || this.addShift.type == 3)) this.submmitSwitch.endLatestDayTypeJY = 2
        if (!item.endLatestWorkTime && (this.addShift.type == 2 || this.addShift.type == 3)) this.submmitSwitch.endLatestWorkTimeJY = 2
        if (!item.restStartTime) this.submmitSwitch.restStartTimeJY = 2
        if (!item.restEndTime) this.submmitSwitch.restEndTimeJY = 2
        this.submmitSwitch.restLengthJY = 2
        if ((item.goToEarlyHour==0 && item.goToEarlyMinutes==0) || (item.afterEarlyHours==0 && item.afterEarlyMinutes==0)) this.submmitSwitch.startEarliestMinuteJY = 2
        if ((item.goToLateHour==0 && item.goToLateMinutes==0) || (item.afterLateHours==0 && item.afterLateMinutes==0)) this.submmitSwitch.goToLateHourJY = 2
      })
      console.log(this.submmitSwitch)
      if (this.submmitSwitch.nameJY == 2) {
        this.$message({
          message: '请输入班段名称'
        })
        return
      }
      if (this.submmitSwitch.startEarliestWorkTimeJY == 2) {
        console.log("弹性、签到 最早打卡时间为空")
        this.$message({
          message: '请选择最早打卡时间'
        })
        return
      }
      if (this.submmitSwitch.endLatestWorkTimeJY == 2) {
        this.$message({
          message: '请选择最晚打卡时间'
        })
        return
      }
      if (this.addShift.type == 0 || this.addShift.type == 1) {
        if (this.submmitSwitch.startWorkTimeJY == 2) {
          this.$message({
            message: '请选择上班时间'
          })
          return
        }
        if (this.submmitSwitch.endWorkTimeJY == 2) {
          this.$message({
            message: '请选择下班时间'
          })
          return
        }
        if (this.submmitSwitch.startEarliestMinuteJY == 2) {
          this.$message({
            message: '请选择最早打卡时间'
          })
          return
        }
        if (this.submmitSwitch.goToLateHourJY == 2) {
          this.$message({
            message: '请选择最晚打卡时间'
          })
          return
        }

        // if (this.endEarliestWorkTimeJY == 2) {
        //   this.$message({
        //     message: '请选择最早打卡下班时间'
        //   })
        //   return
        // }
        // if (this.startLatestWorkTimeJY == 2) {
        //   this.$message({
        //     message: '请选择最晚打卡上班时间'
        //   })
        //   return
        // }
      }

      // if (this.addShift.type == 2) {
      //   if (this.restLengthJY == 2) {
      //     this.$message({
      //       message: '请选择休息时间'
      //     })
      //     return
      //   }
      // }

      // if (this.startEarliestWorkTimeJY == 2) {
      //   this.$message({
      //     message: '请选择最早打卡上班时间'
      //   })
      //   return
      // }

      // if (this.endLatestDayTypeJY == 2) {
      //   this.$message({
      //     message: '请选择最晚打卡下班天次'
      //   })
      //   return
      // }
      // if (this.endLatestWorkTimeJY == 2) {
      //   this.$message({
      //     message: '请选择最晚打卡下班时间'
      //   })
      //   return
      // }

      this.addShift.tableData.forEach((item, index) => {
        //上班最早打卡分钟数
        item.startEarliestMinute = Number(item.goToEarlyHour) * 60 + Number(item.goToEarlyMinutes)
        //上班最晚打卡分钟数
        item.startLatestMinute = Number(item.goToLateHour) * 60 + Number(item.goToLateMinutes)
        if (this.addShift.type == 1 || this.addShift.type == 0) {
          //下班打卡类型
          item.endWorkDayType = item.afterFollowingDay ? '2' : '1'
          //上班打卡类型
          item.startWorkDayType = item.goToFollowingDay ? '2' : '1'
        }
        //下班最早打卡分钟数
        item.endEarliestMinute = Number(item.afterEarlyHours) * 60 + Number(item.afterEarlyMinutes)
        //下班最晚打卡分钟数
        item.endLatestMinute = Number(item.afterLateHours) * 60 + Number(item.afterLateMinutes)

      })

      let params = {
        name: this.addShift.name, //班次名称
        arrangementTypeId: this.addShift.type, //班次类型Id
        customTypeName: this.addShift.typeName, //自定义班次类型名称
        workTimeAmount: this.addShift.hour, //工作时长总计(单位:h)
        ycqts: this.addShift.days, //出勤天数总计
        arrangementTimeInfoList: [...this.addShift.tableData],
        optionOne: this.addShift.checkbox1, //是否允许 晚到早走 1:是
        oneBelateLength: this.addShift.checkbox1Up, //	允许晚到时长(单位:分钟)
        oneLeaveEarlyLength: this.addShift.checkbox1Down, //允许早退时长(单位:分钟)
        optionTwo: this.addShift.checkbox2, //是否允许 晚走晚到 1:是
        twoType: this.addShift.radio1, //1:仅内勤 2:内勤和外勤
        twoStayLateLength: this.addShift.checkbox2Up, //第一天晚走时长
        twoArriveLateLength: this.addShift.checkbox2Down, //第二天晚到时长
        overtime: this.addShift.overtime

        // dutyReminder: this.addShift.switch1, //值班提醒 1:是
        // reminderAdvanceLength: this.addShift.zbHour, //值班前多长时间提醒
        // reminderType: this.addShift.tipsType //	提醒方式(字典项)
      }
      console.log(this.$route.query)
      let ids = this.$route.query.id
      if (ids) {
        params.id = ids
        console.log('修改')
        this.bcUpdates(params)
      } else {
        console.log('新增')
        this.addRoles(params)
      }
    },
    goList() {
      this.$router.push({
        name: 'shiftmana',
        query: {}
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "./index.scss";

.width50 {
  width: 50px !important;
}

.width90 {
  width: 90px !important;
}

.width100 {
  width: 100px !important;
}

.width120 {
  width: 120px !important;
}

.width200 {
  width: 200px !important;
}

.width300 {
  width: 300px !important;
}

.width455 {
  width: 455px !important;
  height: 32px !important;
}

.flexs {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.colorF59 {
  color: #f59a23;
}

::v-deep .el-form-item__content {
  margin-left: 100px !important;
}

::v-deep .el-input {
  // width: 400px;
}

.addtitle {
  padding-bottom: 16px;
  border-bottom: 1px solid #f2f2f2;
}

::v-deep .el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

::v-deep .el-table__body .el-table__cell {
  padding: 0;
  height: 140px;
}

::v-deep .has-gutter .el-table--medium .el-table__cell {
  padding: 0;
  height: 80px;
}

::v-deep .el-table__body .cell {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.oneceil {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  > div:first-child {
    border-bottom: 1px solid #f2f2f2;
  }

  > div {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

::v-deep .el-table__row .el-table_cell {
  border-right: none;
}

::v-deep .el-table--border .el-table__cell {
  border-right: none;
}

.btns {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.padLeftNone{
  ::v-deep .el-input__inner{
    width: 100%;
    padding-right: 0px;
    padding-left: 10px;
  }
}
</style>
