<template>
  <div class="body">
    <el-card>
      <el-card>
        <div slot="header">
          <span>数据筛选</span>
        </div>
        <div class="center">
          <div class="scarchIpt">
            <el-form :inline="true" :model="formInline" class="demo-form-inline">
              <el-form-item label="班次名称：">
                <el-input v-model="formInline.className" placeholder="请输入班次名称" clearable :maxlength="20"></el-input>
              </el-form-item>
              <el-form-item label="班次类型：">
                <el-select v-model="formInline.classType" placeholder="请选择班次类型">
                  <el-option v-for="item in bctypeArr" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="tabButton">
            <div>
              <el-button icon="el-icon-search" class="searchBtn" type="primary" size="mini" style="font-size:13px"
                         @click="findList"
              >搜索
              </el-button>
              <el-button icon="el-icon-refresh" class="searchBtn" @click="resetList" style="font-size:13px" size="mini">重置</el-button>
            </div>
          </div>
        </div>
      </el-card>
      <el-card class="tab_card">
        <div slot="header">
          <div class="tab_card_header">
                        <span>
                            班次管理展示列表
                        </span>
            <div class="btns">
              <el-button icon="el-icon-plus" type="primary" @click="goDetail" class="searchBtn" size="mini"
                         
              >新建班次
              </el-button>
              <el-radio-group v-model="SelectModel" style="margin-left: 15px" @input="changeSelect">
                <el-radio-button label="列表式"></el-radio-button>
                <el-radio-button label="卡片式"></el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </div>

        <el-table v-show="SelectModel=='列表式'" v-loading="loading" :data="tableData" style="width: 100%"
                  :highlight-current-row="true"
        >
          <el-table-column type="selection" width="55" align="center" header-align="center" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="name" label="班次名称" width="150" align="left" header-align="left"
                           show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column prop="arrangementTypeName" width="150" label="班次类型" align="center" header-align="center"
                           show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column prop="workTimeStr" label="工作时间" min-width="600" align="center" header-align="center">
            <template slot="header">
              <div>
                <div>
                  工作时间
                </div>
                <div class="time_line">
                  <div v-for="(item,index) in timeline" :key="item.name" class="one_little"
                       :style="{'left':item.value*4.16+'%'}"
                  ></div>
                  <div v-for="(item,index) in timeline" :key="item.value" class="name_little"
                       :class="{'last_name_little':item.value/3==8}"
                       v-if="item.value/3==0||item.value/3==2||item.value/3==4||item.value/3==6||item.value/3==8"
                       :style="{'left':item.value*4.16+'%','transform': 'translateX(-50%)'}"
                  >
                    {{ item.name }}
                  </div>
                </div>
              </div>
            </template>
            <template slot-scope="scope">
              <div class="work-time" :style="{ whiteSpace: 'normal', overflow: 'visible' }">
                <el-tooltip v-for="(item,index) in scope.row.arrangementTimeInfoList" content="点击关闭 tooltip 功能"
                            placement="top" effect="light"
                >
                  <div slot="content">
                    {{ item.workTimeStr }}
                  </div>
                  <div
                    class="one-work-line"
                    :class="`line${item.code}`"
                    :style="getstyle(item)"
                  >
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="workTimeAmount" label="状态" align="center" header-align="center" width="150">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-color="#13ce66"
                inactive-color="#ff4949"
                active-value="1"
                inactive-value="0"
                @change="(value)=>{changeStatus(value,scope.row)}"
              >
              </el-switch>
            </template>
          </el-table-column>
<!--          <el-table-column prop="ycqts" label="计出勤天数" width="100" align="center" header-align="center">-->
<!--          </el-table-column>-->
          <el-table-column label="操作" align="center" header-align="center" width="250">
            <template slot-scope="scope">
              <div>
                                <span class="caozuo pointer" @click="editData(scope.$index, scope.row)"><i
                                  class="el-icon-edit"
                                ></i>编辑</span>
                <span class="delete_btn pointer" @click="deleteList(scope.$index, scope.row)"><i
                  class="el-icon-delete"
                ></i>删除</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="showLIST" v-show="SelectModel=='卡片式'">
          <div class="out_card" v-for="(item,index) in tableData" :key="index">
            <div class="card_con">
              <div class="group_logo">
                {{ item.arrangementTypeName }}
              </div>
              <div class="con_tit">
                {{ item.name }}
              </div>
              <div class="con_member_num">
                <div class="member_tit">
                  工作时间
                </div>
                <div class="memberNum">
                  {{ item.workTimeStr }}
                </div>
              </div>
              <div class="con_member_num">
                <div class="member_tit">
                  状态
                </div>
                <div class="memberNum">
                  <el-switch
                    v-model="item.status"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    active-value="1"
                    inactive-value="0"
                    @change="(value)=>{changeStatus(value,item)}"
                  >
                  </el-switch>
                </div>
              </div>
              <div class="card-btns">
                <el-button type="text" @click="editData(index,item)">编辑</el-button>
                <el-button type="text" style="color:#8F97A2" @click="deleteList(index,item)">删除</el-button>
              </div>
            </div>
          </div>
        </div>

        <pagination v-show="total > 0" :limit.sync="pageSize" :page.sync="pageNum" :total="total"
                    @pagination="getPageLists"
        />
      </el-card>
    </el-card>
  </div>
</template>

<script>
import {
  getPageList,
  getArrangementTypeList,
  deleteByIds,
  changeClasseStatus
} from '@/api/scheduling/scheduling'

export default {
  name: '',
  // 获取父级的值
  props: {},
  // 数据
  data() {
    return {
      SelectModel: '列表式',
      name: '环境数据',
      tabPosition: '1',
      formInline: {
        className: '',
        classType: ''
      },
      // 遮罩层
      loading: false,
      tableData: [],
      pageSize: 10,
      pageNum: 1,
      isSearch: false,
      total: 0,
      isAdd: 1,
      bctypeArr: [],
      minutesWidth: '',//工作时间分钟宽度
      timeline: [
        {
          name: '00:00',
          value: 0
        },
        {
          name: '03:00',
          value: 3
        },
        {
          name: '06:00',
          value: 6
        },
        {
          name: '09:00',
          value: 9
        },
        {
          name: '12:00',
          value: 12
        },
        {
          name: '15:00',
          value: 15
        },
        {
          name: '18:00',
          value: 18
        },
        {
          name: '21:00',
          value: 21
        },
        {
          name: '24:00',
          value: 24
        }
      ]
    }
  },

  // 实例创建完成后被立即调用
  created() {

  },

  // 挂载实例后调用
  mounted() {
    this.getPageLists()
    this.getArrangementTypeLists()
    this.getDict()
    setTimeout(() => {
      this.getPageLists()
    }, 10)
  },

  // 监控
  watch: {},

  // 过滤器
  filters: {},

  // 定义模板
  components: {},

  // 计算属性
  computed: {
    dictList() {
      return this.$store.state.dict
    }
  },

  // 混入到 Vue 实例中
  methods: {
    //展示形式发生变化
    changeSelect(value) {
      this.getPageLists()
    },
    getstyle(value) {
      let that = this
      let widths = document.querySelectorAll('.work-time')
      let onLineWidth
      let minutesWidth
      if (widths.length > 0) {
        onLineWidth = widths[0].clientWidth
        minutesWidth = onLineWidth / 1440
      }
      return {
        width: `${minutesWidth * value.width}px`,
        left: `${minutesWidth * value.startLocal}px`
      }
    },
    //计算工作时间一分钟的宽度
    /** 设备管理删除 */
    setdeleteByIdss(val) {
      this.loading = true
      // JSON.stringify(this.targetList)
      let params = {
        ids: val
      }
      if (this.tableData.length == 1 && this.pageNum !== 1) {
        this.pageNum = this.pageNum - 1
      }
      deleteByIds(params).then((res) => {
        console.log(res)
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        this.getPageLists()
        this.loading = false
      }).catch(error=>{
        this.loading = false
      })
    },
    //获取班次类型
    getArrangementTypeLists() {
      let params = {}
      getArrangementTypeList(params).then((res) => {
        this.bctypeArr = res.data
      })
    },
    deleteList(index, row) {
      console.log(index, row)
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.setdeleteByIdss(row.id)
      })
    },
    editData(index, row) {
      this.$router.push({
        // path: '/vue-equipment/scheduling/shiftmanaDetail',
        name: 'shiftmanaDetail',
        query: row
      })
    },
    goDetail() {
      this.$router.push({
        // path: '/vue-equipment/scheduling/shiftmanaDetail',
        name: 'shiftmanaDetail',
        query: {}
      })
    },
    /** 排班管理-班次管理分页查询列表 */
    getPageLists(type) {
      this.loading = true
      let params = {}
      if(this.SelectModel=='列表式'){
        if (this.isSearch) {
          params = {
            styleType: '1',
            name: this.formInline.className,
            arrangementTypeId: this.formInline.classType,
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        } else {
          params = {
            // name: this.formInline.className,
            // arrangementTypeId: this.formInline.classType,
            styleType: '1',
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        }
      }else{
        if (this.isSearch) {
          params = {
            styleType: '2',
            name: this.formInline.className,
            arrangementTypeId: this.formInline.classType,
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        } else {
          params = {
            // name: this.formInline.className,
            // arrangementTypeId: this.formInline.classType,
            styleType: '2',
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        }
      }

      getPageList(params).then((res) => {
        this.tableData = res.data.list
        this.total = res.data.total
        console.log(this.tableData, 'this.res')
        this.tableData.forEach((item, index) => {
          item.arrangementTimeInfoList.forEach((event, number) => {
            let end = event.maxEndWorkTime.split(':')
            let start = event.minStartWorkTime.split(':')
            event.startLocal = Number(start[0]) * 60 + Number(start[1])
            event.endLocal = Number(end[0]) * 60 + Number(end[1])
            event.width = event.endLocal - event.startLocal
          })
        })
        console.log(this.tableData)
        this.loading = false
      })
    },
    getDict() {
      this.$store.dispatch('dict/setDict', {})
    },
    findList() {
      this.pageNum = 1
      this.isSearch = true
      this.getPageLists()
    },
    resetList() {
      this.pageNum = 1
      this.pageSize = 10
      this.formInline = {}
      this.isSearch = false
      this.getPageLists()
    },
    //修改开关状态
    changeStatus(value,row){
      let data={
        arrangementId:row.id
      }
      changeClasseStatus(data).then(res=>{
        this.getPageLists()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import './index.scss';

.selectW {
  width: 100%;
}

::v-deep .el-form-item {
  margin-bottom: 0px;
  width: 30%;
}


::v-deep .el-row {
  display: flex;
  justify-content: flex-end;
}

::v-deep .el-table__row {
  height: 50px;
}

::v-deep .el-card__header {
  padding: 15px 24px;
  font-size: 18px;
}

::v-deep .table_box {
  padding: 16px;
  background-color: #ffffff;
}

::v-deep .pagination-container .el-pagination {
  right: 16px;
}

.center {
  display: flex;

  .scarchIpt {
    -webkit-box-flex: 5;
    flex: 5 1 0%;
  }

  .tabButton {
    -webkit-box-flex: 1;
    flex: 1 1 0%;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
  }
}

::v-deep .el-form-item {
  margin-bottom: 0px;
  width: 390px;
  margin-bottom: 20px;
}

::v-deep .el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
  padding: 0px;

}


.center {
  display: flex;

  ::v-deep .el-input {
    width: 10vw;
  }

  ::v-deep .el-date-editor {
    width: 10vw;
  }

  .scarchIpt {
    -webkit-box-flex: 5;
    flex: 5 1 0%;
  }

  .tabButton {
    -webkit-box-flex: 1;
    flex: 1 1 0%;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;

  }

  .el-form-item {
    white-space: nowrap;
    width: 17vw;
  }

  ::v-deep .el-form-item__label {
    width: 85px;
    font-weight: 400;
  }
}

::v-deep .el-form {
  padding-left: 0;
}


::v-deep .el-card__header {
  height: 56px;
  font-size: 16px;
  font-weight: 400;
  padding: 16px;
}

::v-deep .el-card__body {
  padding: 16px;
}

.tab_card {
  ::v-deep .el-card__body {
    padding: 16px 0px;
  }

  .tab_card_header {
    display: flex;
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
    }
  }
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  border: 1px solid #188cff;
  border-radius: 2px;
  background-color: #fff;
  color: #188cff;

}


.el-card {
  margin-bottom: 20px;
}

::v-deep .el-scrollbar {
  width: 100% !important;
}

.delete_btn {
  color: #ff3c32;
}

.work-time {
  width: 100%;
  height: 100%;
  //display: flex;
  position: relative;

  .one-work-line {
    height: 6px;
    background: #188CFF;
    border-radius: 6px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: all .5s;
    cursor: pointer;
  }
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  height: 40px;
}

::v-deep .el-table .cell {
  height: 30px !important;
  line-height: 30px;
}

::v-deep .el-table thead {
  height: 50px;

  .th.el-table__cell {
    overflow: visible;
  }

  .cell {
    height: 100% !important;
    overflow: visible;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

.time_line {
  border-top: solid 1px #188CFF;
  position: relative;
  margin-top: 8px;

  .one_little {
    height: 8px;
    border-left: solid 1px #188CFF;
    position: absolute;
    top: 0;
  }

  .name_little {
    height: 8px;
    position: absolute;
    top: -24px;
  }

  .last_name_little {
    width: 55px;
  }
}

::v-deep .el-table th.el-table__cell {
  overflow: visible;
}

::v-deep .el-radio-button--medium .el-radio-button__inner {
  padding: 8px 20px;
}

.showLIST {
  box-sizing: border-box;
  padding: 16px;
  display: flex;
  flex-wrap: wrap;

  .out_card {
    display: flex;
    width: 20%;
    margin-bottom: 20px;

    .card_con {
      width: 300px;
      height: 192px;
      border: solid 1px rgba(26, 140, 255, 0.3);
      background: rgba(219, 237, 255, 0.5);
      position: relative;
      box-sizing: border-box;
      padding: 20px 16px 0px 16px;

      .con_tit {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        max-width: 140px;
        overflow: hidden;
        white-space:nowrap;
        text-overflow: ellipsis;
      }

      .con_member_num {
        display: flex;
        font-size: 14px;
        box-sizing: border-box;
        padding-top: 5px;
        padding-bottom: 5px;

        .member_tit {
          width: 85px;
          //height: 18px;
          line-height: 18px;
        }
        .memberNum {
          height: 30px;
          line-height: 18px;
        }
      }
      .group_logo {
        position: absolute;
        text-align: center;
        width: 32px;
        height: 40px;
        right: 14px;
        top: 0px;
        color: #fff;
        font-size: 10px;
        background: url("../../../assets/images/rectangle-one.png") top center no-repeat;
        background-size: 100%;
      }

      .card-btns {
        width: 100%;
        height: 45px;
        position: absolute;
        bottom: 0px;
        left: 0px;
        border-top: solid 1px rgba(26, 140, 255, 0.3);
        display: flex;

        > button {
          flex: 1;
        }

        > :last-child::before {
          content: '';
          width: 0px;
          height: 15px;
          border-left: solid 1px #D8D8D8;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translateY(-50%);
        }
      }

    }
  }
}
</style>
