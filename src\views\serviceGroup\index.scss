.body{
    width: 100%;
    // height: calc(100vh - 86px);
    height: 100%;
    margin: 0;
    padding: 16px;
    font-size: 12px;
}
.add{
    background-color: #ffffff;
    padding: 24px;
}
.center-btn{
    background-color: #ffffff;
    padding: 16px;
    display: flex;
    justify-content: space-between;
}
.caozuo{
    color: #3399FF;
}
.delete_btn{
    color: #FF3C32;
}
.alarmUp{
    margin-bottom: 24px;
    .alarmTips{
        font-size: 16px;
        margin-right: 24px;
    }
}
.alarmDown{
    // display: flex;
    // align-items: center;
    .alarmTips{
        font-size: 16px;
        margin-right: 24px;
    }
    .alarmRight{
        margin-left: 92px;
        margin-top: -20px;
        .alarminp{
            display: flex;
            align-items: center;
            // margin-left: 80px;
            .alarminp-left{
                width: 72px;
                margin-right: 8px;
                margin-bottom: 8px;
            }
            .alarminp-right{
                width: 72px;
                margin-right: 8px;
                margin-bottom: 8px;
            }
            .alarminp-center{
                width: 72px;
                margin-right: 8px;
                margin-bottom: 8px;
                height: 36px;
                border-radius: 4px;
                line-height: 36px;
                font-size: 18px;
                text-align: center;
            }
            .blue{
                background: #E6F7FF;
                border: 1px solid #91D5FF;
                color: #1890FF;
            }
            .orange{
                background: #FFF7E6;
                border: 1px solid #FFD591;
                color: #D46B08;
            }
            .red{
                background: #FFF2F0;
                border: 1px solid #FFCCC7;
                color: #FF4D4F;
            }
        }
    }
}
.caozuo{
    color: #3399FF;
    margin-right: 20px;
}
.addtitle{
    color: #333333;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
}
.addbody{
    padding: 40px 160px;
}
.checkbox{
    padding: 0 0 0 24px;
}