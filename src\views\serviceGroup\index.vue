<template>
  <div class="body">
    <el-card>
      <div>
        <el-card>
          <div slot="header">
            <span>数据筛选</span>
          </div>
          <div class="center">
            <div class="scarchIpt">
              <el-form :inline="true" :model="formInline" class="demo-form-inline">
                <el-form-item label="班组名称：">
                  <el-input v-model="formInline.className" placeholder="请输入班组名称" clearable :maxlength="20"></el-input>
                </el-form-item>
                <el-form-item label="人员名称：">
                  <el-input v-model="formInline.classManName" placeholder="请输入人员名称" clearable :maxlength="20"></el-input>
                </el-form-item>
                <!-- <el-form-item label="班组类型：">
                  <el-select class="selectW" v-model="formInline.classType" placeholder="请选择班组类型">
                    <el-option v-for="item in dictList.dict.FWZGL_type" :key="item.value"
                               :label="item.label" :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item> -->
              </el-form>
            </div>
            <div class="tabButton">
              <el-button icon="el-icon-search" class="searchBtn" type="primary"  style="font-size:13px" size="mini"
                         @click="findList"
              >搜索
              </el-button>
              <el-button icon="el-icon-refresh" class="searchBtn" @click="resetList" style="font-size:13px" size="mini">重置</el-button>
            </div>
          </div>
        </el-card>
        <el-card class="tab_card">
          <div slot="header">
            <div class="tab_card_header">
                            <span>
                                班组管理展示列表
                            </span>
              <div class="btns">
                <el-button icon="el-icon-plus" type="primary" @click="openDrawerBtn" class="searchBtn" size="mini"
                           
                >新建班组
                </el-button>
                <!--<el-button icon="el-icon-delete" type="danger" @click="moreDelete" class="searchBtn"
                           plain
                >批量删除
                </el-button>-->
                <el-radio-group v-model="SelectModel" style="margin-left: 15px" @input="changeSelect">
                  <el-radio-button label="列表式"></el-radio-button>
                  <el-radio-button label="卡片式"></el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </div>
          <el-table v-if="SelectModel=='列表式'" v-loading="loading" :data="tableData" style="width: 100%"
                    @selection-change="handleSelectionChange" :highlight-current-row="true"
          >
            <el-table-column type="selection" width="55" align="center" header-align="center"
                             show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column prop="name" label="班组名称" width="200" align="center" header-align="center"
                             show-overflow-tooltip
            >
            </el-table-column>
            <!-- <el-table-column prop="typeName" label="班组类型" align="center" header-align="center"
                             show-overflow-tooltip
            >
            </el-table-column> -->
            <el-table-column prop="memberTotal" label="班组人数" align="center" header-align="center">
            </el-table-column>
            <el-table-column prop="memberLeadsName" label="班组组长" align="center" header-align="center">
            </el-table-column>
            <el-table-column prop="membersName" label="班组成员" align="center" header-align="center"
                             show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column prop="remind"  label="消息提醒" align="center" header-align="center" width="150">
              <template slot-scope="scope">
                <el-switch
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  :value="getSwitchValue(scope.row.remind)"
                  @change="(value)=>changeStatus(value,scope.row)"
                >
                </el-switch>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" header-align="center">
              <template slot-scope="scope">
                <div>
                                              <span class="caozuo pointer" @click="editData(scope.$index, scope.row)"><i
                                                class="el-icon-edit"
                                              ></i>编辑</span>
                  <span class="delete_btn pointer" @click="deleteList(scope.$index, scope.row)"><i
                    class="el-icon-delete"
                  ></i>删除</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div v-if="SelectModel=='卡片式'" class="showLIST">
            <div class="out_card" v-for="(item,index) in tableData" :key="index">
              <div class="card_con">
                <div class="group_logo">
                  {{ item.typeName }}
                </div>
                <div class="con_tit">
                  {{ item.name }}
                </div>
                <div class="con_member_num">
                  <div class="member_tit">
                    班组人数
                  </div>
                  <div class="memberNum">
                    {{ item.memberTotal }}
                  </div>
                </div>
                <div class="con_member_num">
                  <div class="member_tit">
                    班组组长
                  </div>
                  <div class="memberNum">
                    {{ item.memberLeadsName }}
                  </div>
                </div>
                <div class="con_member_num">
                  <div class="member_tit">
                    班组组员
                  </div>
                  <div class="memberNum">
                    {{ item.membersName }}
                  </div>
                </div>
                <div class="con_member_num">
                  <div class="member_tit">
                    消息提醒
                  </div>
                  <div class="memberNum">
                    <el-switch
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                      :value="getSwitchValue(item.remind)"
                      @change="(value)=>{changeStatus(value,item)}"
                    >
                    </el-switch>
                  </div>
                </div>
                <div class="card-btns">
                  <el-button type="text" @click="editData(index,item)">编辑</el-button>
                  <el-button type="text" style="color:#8F97A2" @click="deleteList(index,item)">删除</el-button>
                </div>
              </div>
            </div>
          </div>
          <pagination v-show="total > 0" :limit.sync="pageSize" :page.sync="pageNum" :total="total"
                      @pagination="getPageLists"
          />
        </el-card>
      </div>

    </el-card>
    <el-dialog :title="banzu" :visible.sync="openDrawer" :before-close="handleClose" append-to-body>
      <el-form :label-position="labelPosition" label-width="100px" :model="siteList" :rules="rules" ref="ListForm">
        <el-form-item label="班组名称" prop="addClassName">
          <el-input v-model="siteList.addClassName" placeholder="请输入班组名称" clearable :maxlength="20"></el-input>
        </el-form-item>
        <!-- <el-form-item label="班组类型" prop="addClassType">
          <el-select v-model="siteList.addClassType" placeholder="请选择班组类型">
            <el-option v-for="item in dictList.dict.FWZGL_type" :key="item.value" :label="item.label"
                       :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="班组主管" prop="addCard">
          <el-select filterable multiple v-model="siteList.addCard" placeholder="请选择班组主管" disabled>
            <el-option v-for="item in dictList.userList" :key="item.userId" :label="item.realname"
                       :value="item.userId"
            >
            </el-option>
          </el-select>
          <el-button type="text" @click="alertChooseRole('supervisor')">
            选择人员
          </el-button>
        </el-form-item>
        <el-form-item label="人员选择" prop="addClassMan">
          <el-select filterable multiple v-model="siteList.addClassMan" placeholder="请选择员工" disabled>
            <el-option v-for="item in dictList.userList" :key="item.userId" :label="item.realname"
                       :value="item.userId"
            >
            </el-option>
          </el-select>
          <el-button type="text" @click="alertChooseRole('selection')">
            选择人员
          </el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="up-btn">
        <el-button type="primary" @click="submitList('ListForm')">确定</el-button>
        <el-button @click="closeDialog">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="选择人员" width="720px" :visible.sync="chooseRole" :before-close="closeChoose" append-to-body>
      <el-container>
        <el-header class="header_box">
          <div class="header_btn">
            <el-button type="danger" size="mini" @click="empty">清空已选</el-button>
          </div>
        </el-header>
        <el-container>
          <el-aside width="230px">
            <el-tree :data="deptList" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
          </el-aside>
          <el-main>
            <el-checkbox
              v-model="checkAll"
              label="全选"
              @change="handleCheckAllChangeSales"
            ></el-checkbox>
            <div style="margin: 15px 0;"></div>

            <el-checkbox-group v-model="checkList" >

              <!--<span v-for="(item,index) in personnel">
                <el-checkbox  :label="item.realname" :key="item.userId" @change="(data)=>selectMan(data,item)"></el-checkbox>
              </span>-->
              <span v-for="(item,index) in personnel">
              <el-checkbox  :label="item.userId" :key="item.userId" @change="(data)=>selectMan(data,item)">{{item.realname}}</el-checkbox>
            </span>

            </el-checkbox-group>
          </el-main>
        </el-container>

      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="iptAddMan">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPageList,
  addServiceGroup,
  deleteByIds,
  login,
  updates,
  getDeptList,
  getDeptByUserList
} from '@/api/serviceGroup/serviceGroup'
import {getLoginMemberInfo, updateRemind} from '@/api/scheduling/scheduling'

export default {
  name: '',
  // 获取父级的值
  props: {},
  // 数据
  data() {
      return {
        banzu:'新建班组',
      //右侧人员id集合
      treeManlist:[],
      //当前修改是主管还是人员
      modifyType:'',
      checkList:[], //选中的人员
      selectedItems: [], // 存储选中的值
      //替换输入框人员
      checckListToid:[],
      isALL: false,
      checkAll: false,
      chooseRole:false,
      deptList:null,//部门
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      personnel:[], //点击部门所对应的人
      SelectModel: '卡片式',
      rules: {
        addClassName: [
          {
            required: true,
            message: '请输入班组名称',
            trigger: 'blur'
          }
        ],
        addClassType: [
          {
            required: true,
            message: '请选择班组类型',
            trigger: 'change'
          }
        ],
        addClassMan: [
          { required: true, message: '请选择员工', trigger: 'blur' }
        ],
        addCard: [
          {
            required: true,
            message: '请选班组主管',
            trigger: 'blur'
          }
        ]
      },
      name: '环境数据',
      formInline: {
        className: '',
        classManName: '',
        classType: ''
      },
      // 遮罩层
      loading: false,
      tableData: [],
      pageSize: 10,
      pageNum: 1,
      total: 0,
      isSearch: false,
      targetList: [
        {
          name: '轻度',
          degree: '102001',
          startNum: '',
          startType: '1',
          endNum: '',
          endType: '1'
        },
        {
          name: '中度',
          degree: '102002',
          startNum: '',
          startType: '1',
          endNum: '',
          endType: '1'
        },
        {
          name: '重度',
          degree: '102003',
          startNum: '',
          startType: '1',
          endNum: '',
          endType: '1'
        }
      ],
      symbolList: [
        {
          value: '1',
          label: '<'
        },
        {
          value: '2',
          label: '≤'
        }
        // {
        //   value: '3',
        //   label: '>'
        // }, {
        //   value: '4',
        //   label: '≥'
        // },
      ],
      openDrawer: false,
      labelPosition: 'right',
      siteList: {
        addClassName: '',
        addClassType: '',
        addClassMan: '',
        addCard: '',
        addleaderNames:[],
        addmemberNames:[]
      },
      moreList: [],
      deleteMoreList: [],
      multipleSelection: [],
      isAdd: 1,
      vegeid: ''
    }
  },

  // 实例创建完成后被立即调用
  created() {
    this.getPageLists()
    this.getDict()
    // this.logins()
    // 
  },

  // 挂载实例后调用
  mounted() {
    // this.getLoginMemberInfos()
  },

  // 监控
  watch: {},

  // 过滤器
  filters: {},

  // 定义模板
  components: {},

  // 计算属性
  computed: {
    dictList() {
      return this.$store.state.dict
    },
    isIndeterminate() {

      return Array.from(new Set(this.selectedItems)).length < this.personnel.length;
    }
    // userList() {
    //   return this.$store.state.userList
    // }
  },

  // 混入到 Vue 实例中
  methods: {
    //消息提醒数值更改为boolean
    getSwitchValue(remind) {
      return remind === 1;
    },
    handleCheckAll() {
      this.selectedItems = Array.from(new Set(this.selectedItems)); // 去重
      console.log(this.selectedItems,"this.selectedItems")
    },
    handleCheckedCitiesChange(value) {
      console.log(value,"value")
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.personnel.length;
      this.isALL = checkedCount > 0 && checkedCount < this.personnel.length;
    },
    //全选校验
    isSelectAll(haystack,arrIds){
        let flag=true
        haystack.forEach((item,index)=>{
          if(arrIds.indexOf(item) == -1){
            console.log('进入')
            flag=false
          }
        })
        console.log(haystack,arrIds)
        return flag
    },
    //全选
    handleCheckAllChangeSales(val) {
      let checkedsItem = val ? this.personnel : [];
      if (checkedsItem.length > 0) {
        checkedsItem.forEach((item) => {
          // this.checkList.push(item.realname);
          this.checkList.push(item.userId);
          if (this.checckListToid.indexOf(item.userId) == -1){
            // this.checckListToid.push(item.userId);
            this.checckListToid.push(item.realname);
          }
        });
      } else {
        let ids =[]
        let nameList =[]
        this.personnel.forEach((item) => {
          nameList.push(item.realname);
          ids.push(item.userId);
        });
        // this.checkList =  this.setarr(this.checkList,nameList)
        // this.checckListToid =   this.setarr(this.checckListToid,ids)
        this.checkList =  this.setarr(this.checkList,ids)
        this.checckListToid =   this.setarr(this.checckListToid,nameList)
        // this.checkList = [];
        // this.checckListToid = [];
      }
      this.isALL = false;
    },
    //清空已选数据
    empty(){
      if(this.modifyType=='supervisor'){
        this.checkList=[]
        this.siteList.addCard=[]
        this.siteList.addleaderNames =[]
        this.checckListToid=[]
      }else{
        //人员选择
        this.siteList.addClassMan=[]
        this.checkList=[]
        this.siteList.addmemberNames =[]
        this.checckListToid =[]
      }
    },
    //去重
    setarr(arr, subarr){
      return arr.filter((x) => !subarr.includes(x));
    },
    //重置选择人员弹框
    initAddMember(){
      this.checkList=[]
      this.checckListToid=[]
      this.checkAll=false
    },
    //关闭选择人员弹框
    closeChoose(){
      this.initAddMember()
      this.chooseRole=false
    },
    setchcke(){
      if(this.personnel){
        this.personnel.forEach((item,index)=>{
          this.checckListToid.push(item.userId)
        })
      }
    },
    //选中人员时候
    selectMan(select,data){
      console.log(select,data,'单选===--=-=')
      if(select){
        if(this.checkList.indexOf(data.userId) == -1){
          this.checkList.push(data.userId)
        }
      }else{
        this.checkList.forEach((item,index)=>{
          if(item==data.userId){
            this.checkList.splice(index,1)
          }
        })
      }
      this.checkAll=this.isSelectAll(this.treeManlist,this.checkList)
    },
    //确认添加选中人员
    iptAddMan(){
      //班组主管
      if(this.modifyType=='supervisor'){
         /* this.siteList.addCard=this.checckListToid
          this.siteList.addleaderNames = this.checkList*/
          this.siteList.addCard=this.checkList
          this.siteList.addleaderNames = this.checckListToid
      }else{
      //人员选择
        /*this.siteList.addClassMan=this.checckListToid
        this.siteList.addmemberNames = this.checkList*/
        this.siteList.addClassMan=this.checkList
        this.siteList.addmemberNames = this.checckListToid
      }
      this.chooseRole=false
      this.initAddMember()
    },
    //点击树节点
    handleNodeClick(data){
      console.log(data)
      let params={
        deptId:data.id
      }
      getDeptByUserList(params).then(res=>{

        this.personnel=res.data
        // this.checkAll = false
        this.treeManlist = this.personnel.map((item)=>{
          // return item.realname
          return item.userId
        })

        if (this.isSelectAll(this.treeManlist,this.checkList)){
          this.checkAll= true
        } else {
          this.checkAll= false
        }
        console.log(this.isSelectAll(this.treeManlist,this.checkList),'选中状态')
      })
    },
    //选择部门及人员
    alertChooseRole(type){
      if(type=='supervisor'){
        console.log(this.siteList.addCard,"this.siteList.addCard",this.siteList.addleaderNames)
      /*  this.checkList=JSON.parse(JSON.stringify(this.siteList.addleaderNames))
        this.checckListToid=JSON.parse(JSON.stringify(this.siteList.addCard))*/
        if (this.siteList.addCard){
          this.checkList=JSON.parse(JSON.stringify(this.siteList.addCard))
        } else {
          this.siteList.addCard=[]
          this.checkList=JSON.parse(JSON.stringify(this.siteList.addCard))
        }
        if (this.siteList.addleaderNames){
          this.checckListToid=JSON.parse(JSON.stringify(this.siteList.addleaderNames))
        }else {
          this.siteList.addleaderNames=[]
          this.checckListToid=JSON.parse(JSON.stringify(this.siteList.addleaderNames))
        }
      }else{
        console.log(this.siteList.addClassMan,"this.siteList.addClassMan",this.siteList.addmemberNames)
       /* this.checkList=JSON.parse(JSON.stringify(this.siteList.addmemberNames))
        this.checckListToid=JSON.parse(JSON.stringify(this.siteList.addClassMan))*/
        if (this.siteList.addClassMan){
          this.checkList=JSON.parse(JSON.stringify(this.siteList.addClassMan))
        }else {
          this.siteList.addClassMan=[]
          this.checkList=JSON.parse(JSON.stringify(this.siteList.addClassMan))
        }
        if (this.siteList.addmemberNames){
          this.checckListToid=JSON.parse(JSON.stringify(this.siteList.addmemberNames))
        }else {
          this.siteList.addmemberNames=[]
          this.checckListToid=JSON.parse(JSON.stringify(this.siteList.addmemberNames))
        }


      }
      this.modifyType=type
      //查看所有部门
      getDeptList().then(res=>{
        this.deptList=res.data
        let params={
          deptId:res.data[0].id
        }
        getDeptByUserList(params).then(res=>{
          this.personnel=res.data
          this.treeManlist = this.personnel.map((item)=>{
            return item.realname
          })
        })
      })
      this.chooseRole = true
    },
    //获取用户信息
    getLoginMemberInfos() {
      this.loading = true
      let params = {}
      getLoginMemberInfo(params).then((res) => {
        console.log(res)
        this.userList = res.data
        this.siteList.shiftMan = this.userList.id
        this.siteList.shiftGroup = this.userList.serviceGroupId
        this.siteList.shiftManName = this.userList.userName
        this.findListMans(this.userList.serviceGroupId)
        this.loading = false
      })
    },
    changeSelect(value) {
      this.getPageLists()
    },
    // 获取token
    logins() {
      let params = {
        username: 'chennan',
        password: 'WmRzYyExMjM='
      }

      login(params).then((res) => {
        console.log(res, 'tokne')
        localStorage.setItem('Authorization', res.access_token)
      })
    },
    editData(index, row) {
      // console.log(row,'编辑传过来的数据')
      this.isAdd = 2
      // console.log(row, " rowrow");
      this.banzu = '编辑班组'
      this.openDrawer = true
      this.siteList.addClassName = row.name
      this.siteList.addClassType = row.type
      this.siteList.addClassMan = row.membersId
        ? row.membersId.split(',').map(Number)
        : []
      this.siteList.addCard = row.memberLeadsId
        ? row.memberLeadsId.split(',').map(Number)
        : []
      this.siteList.addleaderNames=row.memberLeadsName?row.memberLeadsName.split(','):[]
      this.siteList.addmemberNames=row.membersName?row.membersName.split(','):[]
      // this.siteList.addSettlement = row.attendanceSettlementMethod
      // console.log(this.siteList,'赋值后的数据')
      this.vegeid = row.id
    },
    deleteList(index, row) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.setdeleteByIdss(row.id)
      })
    },
    /** 设备管理删除 */
    setdeleteByIdss(val) {
      this.loading = true
      // JSON.stringify(this.targetList)
      let params = {
        ids: val
      }
      if (this.tableData.length == this.moreList.length && this.pageNum !== 1) {
        this.pageNum = this.pageNum - 1
      }
      deleteByIds(params)
        .then((res) => {
          if (res.code == 200) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.getPageLists()
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then((_) => {
            done()
            this.resetForm()
        })
        .catch((_) => {
        })
    },
    /** 服务组管理分页查询列表 */
    getPageLists(type) {
      this.loading = true
      let params = {}
      if (this.isSearch) {
        params = {
          memberName: this.formInline.classManName,
          name: this.formInline.className,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          type: this.formInline.classType
        }
      } else {
        params = {
          // memberName: this.formInline.classManName,
          // name: this.formInline.className,
          pageNum: this.pageNum,
          pageSize: this.pageSize
          // type: this.formInline.classType,
        }
      }
      getPageList(params).then((res) => {
        this.tableData = res.data.list
        this.total = res.data.total
        console.log(this.tableData, 'this.res')
        // this.userList = response.rows;
        // this.total = response.total;
        this.loading = false
      })
    },
    getDict() {
      this.$store.dispatch('dict/setDict', {})
      this.$store.dispatch('dict/setUserList', {})
    },
    findList() {
      this.pageNum = 1
      this.isSearch = true
      this.getPageLists()
    },
    resetList() {
      this.tableData = []
      this.pageNum = 1
      this.pageSize = 10
      this.formInline = {}
      this.isSearch = false
      this.getPageLists()
    },
      openDrawerBtn() {
        this.banzu = '新建班组'
      this.isAdd = 1
      this.openDrawer = true
      // this.siteList = {};
    },
    // 提交接口
    submitList(siteList) {
      console.log(siteList)
      this.$refs[siteList].validate((valid) => {
        console.log(valid)
        if (valid) {
          if (this.isAdd == 1) {
            this.addServiceGroups()
          } else {
            this.updatess()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 新增接口
    addServiceGroups() {
      this.loading = true
      // JSON.stringify(this.targetList)
      let params = {
        membersId: this.siteList.addClassMan,
        name: this.siteList.addClassName,
        memberLeadsId: this.siteList.addCard,
        type: this.siteList.addClassType
        // attendanceSettlementMethod: this.siteList.addSettlement
      }
      console.log(params, 'params')
      addServiceGroup(params).then((res) => {
        this.$message({
          message: '新增成功',
          type: 'success'
        })
        setTimeout(() => {
          this.resetForm()
          this.loading = false
          this.openDrawer = false
          this.getPageLists()
        }, 1000)
      }).catch(err => {
        this.loading = false
        this.getPageLists()
      })
    },
    updatess() {
      // JSON.stringify(this.targetList)
      let params = {
        id: this.vegeid,
        membersId: this.siteList.addClassMan,
        name: this.siteList.addClassName,
        memberLeadsId: this.siteList.addCard,
        type: this.siteList.addClassType
        // attendanceSettlementMethod: this.siteList.addSettlement
      }
      console.log(params, 'params')
      updates(params).then((res) => {
        this.loading = true
        this.$message({
          message: '编辑成功',
          type: 'success'
        })
        setTimeout(() => {
          this.resetForm()
          this.loading = false
          this.openDrawer = false
          this.getPageLists()
        }, 1000)
      })
    },
    handleSelectionChange(val) {
      console.log(val, 'va')
      this.multipleSelection = val
    },
    moreDelete() {
      this.moreList = []
      this.multipleSelection.forEach((item) => {
        this.moreList.push(item.id)
      })
      this.deleteMoreList = this.moreList.toString()
      if (this.deleteMoreList) {
        this.setdeleteByIdss(this.deleteMoreList)
      } else {
        this.$message({
          message: '请至少选择一项！'
        })
      }
    },
    closeDialog() {
      this.resetForm()
      this.openDrawer = false
    },
    // 重置表单
    resetForm() {
      this.siteList = {
        addClassName: '',
        addClassType: '',
        addClassMan: '',
        addCard: ''
      }
      // 重置表单及校验
      this.$refs.ListForm.resetFields()
    },
    //修改消息提醒开关
    changeStatus(value,row){
      if(value){
        value =1
      }else{
        value=0
      }
      let data={
        id:row.id,
        remind:value
      }
      updateRemind(data).then(res=>{
        this.getPageLists()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "./index.scss";


.selectW {
  width: 100%;
}
.header_box{
  height: 40px !important;
  .header_btn{
    text-align: right;
    width: 100%;
    height: 100%;
  }
}
.table_box {
  padding: 16px;
  // padding-top: 10px;
  background-color: #ffffff;
}

.selectW1 {
  width: 300px;
}

.selectW100 {
  width: 100%;
}

.center {
  display: flex;

  .scarchIpt {
    -webkit-box-flex: 5;
    flex: 5 1 0%;
  }
}

::v-deep .el-card__body {
  padding: 16px;
}

::v-deep .clearfix {
  // height: 56px;
}

.up-btn {
  display: flex;
  justify-content: flex-end;
}

::v-deep .el-dialog__header {
  border-bottom: 1px solid #f1f1f1;
}

::v-deep .el-dialog__body {
  // display: flex;
  // justify-content: center;
  // padding: 32px 30px 5px 0px;
   padding: 16px;
  .el-form-item {
    width: 100% !important;
  }
}

::v-deep .el-dialog__footer {
  border-top: 1px solid #f1f1f1;
}

::v-deep .el-form-item {
  margin-bottom: 0px;
  width: 30%;
}


::v-deep .el-form-item {
  margin-bottom: 0px;
  width: 390px;
  margin-bottom: 20px;
}

::v-deep .el-row {
  display: flex;
  justify-content: flex-end;
}

::v-deep .el-table__row {
  height: 50px;
}

::v-deep .el-table .el-table__header-wrapper th {
  background: rgba(25, 159, 255, 0.15);
  font-family: Noto Sans SC;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #007baf;
}

.showLIST {
  box-sizing: border-box;
  padding: 16px;
  display: flex;
  flex-wrap: wrap;
  .out_card {
    display: flex;
    width: 20%;
    margin-top: 20px;
    .card_con {
      width: 300px;
      height: 220px;
      border: solid 1px rgba(26, 140, 255, 0.3);
      background: rgba(219, 237, 255, 0.5);
      position: relative;
      box-sizing: border-box;
      padding: 20px 16px 0px 16px;

      .con_tit {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
      }

      .con_member_num {
        display: flex;
        font-size: 14px;
        box-sizing: border-box;
        padding-top: 5px;
        padding-bottom: 5px;

        .member_tit {
          width: 70px;
          height: 18px;
          line-height: 18px;
        }

        .memberNum {
          height: 18px;
          line-height: 18px;
        }
      }

      .group_logo {
        position: absolute;
        text-align: center;
        width: 32px;
        height: 40px;
        right: 14px;
        top: 0px;
        color: #fff;
        font-size: 10px;
        background: url("../../assets/images/rectangle-one.png") top center no-repeat;
        background-size: 100%;
      }

      .card-btns {
        width: 100%;
        height: 45px;
        position: absolute;
        bottom: 0px;
        left: 0px;
        border-top: solid 1px rgba(26, 140, 255, 0.3);
        display: flex;

        > button {
          flex: 1;
        }

        > :last-child::before {
          content: '';
          width: 0px;
          height: 15px;
          border-left: solid 1px #D8D8D8;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translateY(-50%);
        }
      }

    }
  }
}


::v-deep .pagination-container .el-pagination {
  right: 16px;
}


.center {
  display: flex;

  ::v-deep .el-input {
    width: 10vw;
  }

  ::v-deep .el-date-editor {
    width: 10vw;
  }

  .scarchIpt {
    -webkit-box-flex: 6;
    flex: 6 1 0%;
  }

  .tabButton {
    -webkit-box-flex: 1;
    flex: 1 1 0%;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;

  }

  .el-form-item {
    white-space: nowrap;
    width: 17vw;
  }

  ::v-deep .el-form-item__label {
    width: 85px;
    font-weight: 400;
  }
}

::v-deep .el-form {
  padding-left: 0;
}


::v-deep .el-card__header {
  height: 56px;
  font-size: 16px;
  font-weight: 400;
  padding: 16px;
}

.tab_card {
  ::v-deep .el-card__body {
    padding: 16px 0px;
  }

  .tab_card_header {
    display: flex;
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
    }
  }
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  border: 1px solid #188cff;
  border-radius: 2px;
  background-color: #fff;
  color: #188cff;
}

.el-card {
  margin-bottom: 20px;
}

::v-deep .el-dialog {
  width: 560px;

  .el-input {
    width: 245px;
  }
}

::v-deep .el-table__cell {
  > .cell {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
::v-deep .el-checkbox{
  width: 40%;
  margin:3px 0 3px 5%;
}

::v-deep .el-radio-button--medium .el-radio-button__inner {
  padding: 8px 20px;
}
::v-deep .el-container{
  min-height: 400px;
}
::v-deep .el-main{
  height: 400px;
}
::v-deep .el-aside{
  height: 400px;
  /*width: 250px;*/
  overflow-x: auto;
}
aside{
  margin-bottom: 0px;
  background: #fff;
  border-right: solid 1px #eef1f6;
}
::v-deep .el-checkbox{
  min-width: 100px;
}
</style>
