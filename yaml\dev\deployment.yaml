kind: Deployment
apiVersion: apps/v1
metadata:
  name: ningjin-emergency-web-2-v1
  namespace: ningjin-park-dev
  labels:
    app: ningjin-emergency-web-2
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ningjin-emergency-web-2
      version: v1
  template:
    metadata:
      labels:
        app: ningjin-emergency-web-2
        version: v1
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
      containers:
        - name: container-a6ia3v
          image: 'local.harbor.com/park-project/$IMAGE_NAME:$IMAGE_TAG'
          ports:
            - name: http-80
              containerPort: 80
              protocol: TCP
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      affinity: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
