kind: Service
apiVersion: v1
metadata:
  name: ningjin-emergency-web-2
  namespace: ningjin-park-dev
  labels:
    app: ningjin-emergency-web-2
    version: v1
spec:
  ports:
    - name: http-80
      protocol: TCP
      port: 80
      targetPort: 80
      nodePort: 14426
  selector:
    app: ningjin-emergency-web-2
  clusterIP: *************
  type: NodePort
  sessionAffinity: None
  externalTrafficPolicy: Cluster
